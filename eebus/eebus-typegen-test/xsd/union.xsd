<?xml version="1.0" encoding="UTF-8" ?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="value" type="FeatureSpecificUsageEnumType" />
    <xs:simpleType name="FeatureSpecificUsageEnumType">
        <xs:union
            memberTypes="ns_p:FeatureDirectControlSpecificUsageEnumType ns_p:FeatureHvacSpecificUsageEnumType ns_p:FeatureMeasurementSpecificUsageEnumType ns_p:FeatureSetpointSpecificUsageEnumType ns_p:FeatureSmartEnergyManagementPsSpecificUsageEnumType"
        />
    </xs:simpleType>
    <xs:simpleType name="FeatureDirectControlSpecificUsageEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="History" />
            <xs:enumeration value="RealTime" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="FeatureHvacSpecificUsageEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="OperationMode" />
            <xs:enumeration value="Overrun" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="FeatureMeasurementSpecificUsageEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="Contact" />
            <xs:enumeration value="Electrical" />
            <xs:enumeration value="Heat" />
            <xs:enumeration value="Level" />
            <xs:enumeration value="Pressure" />
            <xs:enumeration value="Temperature" />
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="FeatureSetpointSpecificUsageEnumType">
        <xs:restriction base="ns_p:FeatureMeasurementSpecificUsageEnumType" />
    </xs:simpleType>
    <xs:simpleType name="FeatureSmartEnergyManagementPsSpecificUsageEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="FixedForecast" />
            <xs:enumeration value="FlexibleChosenForecast" />
            <xs:enumeration value="FlexibleOptionalForecast" />
            <xs:enumeration value="OptionalSequenceBasedImmediateControl" />
        </xs:restriction>
    </xs:simpleType>
</xs:schema>

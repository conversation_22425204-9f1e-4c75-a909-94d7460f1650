<?xml version="1.0" encoding="UTF-8" ?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:simpleType name="IntervalTypeEnum">
        <xs:restriction base="xs:string">
            <xs:enumeration value="yearly" />
            <xs:enumeration value="monthly" />
            <xs:enumeration value="weekly" />
            <xs:enumeration value="daily" />
            <xs:enumeration value="hourly" />
            <xs:enumeration value="everyMinute" />
            <xs:enumeration value="everySecond" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="EnumExtendType">
        <xs:restriction base="xs:string" />
    </xs:simpleType>

    <xs:simpleType name="IntervalType">
        <xs:union memberTypes="ns_p:IntervalTypeEnum ns_p:EnumExtendType" />
    </xs:simpleType>

    <xs:element name="value" type="IntervalType" />
</xs:schema>

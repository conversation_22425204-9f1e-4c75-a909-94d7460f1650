<?xml version="1.0" encoding="UTF-8" ?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:complexType name="ComplexDataType">
        <xs:sequence>
            <xs:element name="itemDouble" type="xs:double" minOccurs="0" />
            <xs:element name="itemString" type="xs:string" minOccurs="0" />
        </xs:sequence>
    </xs:complexType>

    <xs:element name="complexData" type="ComplexDataType" />

    <xs:complexType name="ComplexListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="complexData" />
        </xs:sequence>
    </xs:complexType>

    <xs:element name="complexListData" type="ComplexListDataType" />

    <xs:element name="example">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="complexListData" />
                <xs:element name="itemEmpty">
                    <xs:complexType />
                </xs:element>
                <xs:element
                    name="optionalItem"
                    type="xs:boolean"
                    minOccurs="0"
                />
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>

use std::{env, path::PathBuf};

macro_rules! generate {
    ($name:expr, $out_dir:expr) => {
        eebus_typegen::generate_from_str(
            include_str!(concat!("xsd/", $name, ".xsd")),
            $out_dir.join(concat!($name, ".rs")),
        );
    };
}

fn main() {
    let out_dir = PathBuf::from(env::var("OUT_DIR").unwrap());

    generate!("enum", out_dir);
    generate!("example1", out_dir);
    generate!("example2", out_dir);
    generate!("example3", out_dir);
    generate!("example5", out_dir);
    generate!("example6", out_dir);
    generate!("example10", out_dir);
    generate!("example11", out_dir);
    generate!("union", out_dir);

    eebus_typegen::generate_spine_xsds(out_dir.join("eebus_spine.rs"));
    eebus_typegen::generate_ship_xsds(out_dir.join("eebus_ship.rs"));
}

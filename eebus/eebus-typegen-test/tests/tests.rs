#![feature(if_let_guard, let_chains)]
#![allow(dead_code)]
#![allow(unused_assignments)]
#![allow(unused_imports)]
#![allow(unused_mut)]
#![allow(unused_variables)]

mod test_example1 {
    use crate::spine::types::{invalid_variant, missing_field, unexpected_field};
    include!(concat!(env!("OUT_DIR"), "/example1.rs"));

    /// EEBus_SHIP_TS_Specification_v1.0.1.pdf
    /// Section 11.4.7
    /// Page 34
    #[test]
    fn test_example1() {
        let orig_data = TopLevelHeight { height: 1.73 };

        let json = nanoserde::SerJson::serialize_json(&orig_data);
        assert_eq!(json, r#"{"height":1.73}"#);

        let parsed_data = nanoserde::DeJson::deserialize_json(&json).unwrap();
        assert!(matches!(parsed_data, TopLevelHeight { height: 1.73 }));
    }
}

mod test_example2 {
    use crate::spine::types::{invalid_variant, missing_field, unexpected_field};
    include!(concat!(env!("OUT_DIR"), "/example2.rs"));

    /// EEBus_SHIP_TS_Specification_v1.0.1.pdf
    /// Section 11.4.7
    /// Page 34
    #[test]
    fn test_example2() {
        let orig_data = TopLevelChecked { checked: true };

        let json = nanoserde::SerJson::serialize_json(&orig_data);
        assert_eq!(json, r#"{"checked":true}"#);

        let parsed_data = nanoserde::DeJson::deserialize_json(&json).unwrap();
        assert!(matches!(parsed_data, TopLevelChecked { checked: true }));
    }
}

mod test_example3 {
    use crate::spine::types::{invalid_variant, missing_field, unexpected_field};
    include!(concat!(env!("OUT_DIR"), "/example3.rs"));

    /// EEBus_SHIP_TS_Specification_v1.0.1.pdf
    /// Section 11.4.7
    /// Page 34
    #[test]
    fn test_example3() {
        let orig_data = TopLevelEmpty {
            empty: TopLevelEmptyEmpty {},
        };

        let json = nanoserde::SerJson::serialize_json(&orig_data);
        assert_eq!(json, r#"{"empty":[]}"#);

        let parsed_data = nanoserde::DeJson::deserialize_json(&json).unwrap();
        assert!(matches!(
            parsed_data,
            TopLevelEmpty {
                empty: TopLevelEmptyEmpty {}
            }
        ));
    }
}

mod test_example5 {
    use crate::spine::types::{invalid_variant, missing_field, unexpected_field};
    include!(concat!(env!("OUT_DIR"), "/example5.rs"));

    /// EEBus_SHIP_TS_Specification_v1.0.1.pdf
    /// Section 11.4.7
    /// Page 34
    #[test]
    fn test_example5() {
        let orig_data = TopLevelItems {
            items: TopLevelItemsItems {
                item: vec![1, 2, 3],
            },
        };

        let json = nanoserde::SerJson::serialize_json(&orig_data);
        assert_eq!(json, r#"{"items":[{"item":[1,2,3]}]}"#);

        let parsed_data = nanoserde::DeJson::deserialize_json(&json).unwrap();
        assert!(matches!(
            parsed_data,
            TopLevelItems {
                items: TopLevelItemsItems { item },
            } if item == &[1, 2, 3]
        ));
    }
}

mod test_example6 {
    use crate::spine::types::{invalid_variant, missing_field, unexpected_field};
    include!(concat!(env!("OUT_DIR"), "/example6.rs"));

    /// EEBus_SHIP_TS_Specification_v1.0.1.pdf
    /// Section 11.4.7
    /// Page 35
    #[test]
    fn test_example6() {
        let orig_data = TopLevelItems {
            items: TopLevelItemsItems {
                item1: 1,
                item2: 2,
                item3: 3,
            },
        };

        let json = nanoserde::SerJson::serialize_json(&orig_data);
        assert_eq!(json, r#"{"items":[{"item1":1},{"item2":2},{"item3":3}]}"#);

        let parsed_data = nanoserde::DeJson::deserialize_json(&json).unwrap();
        assert!(matches!(
            parsed_data,
            TopLevelItems {
                items: TopLevelItemsItems {
                    item1: 1,
                    item2: 2,
                    item3: 3,
                },
            }
        ));
    }
}

mod test_example10 {
    use crate::spine::types::{invalid_variant, missing_field, unexpected_field};
    include!(concat!(env!("OUT_DIR"), "/example10.rs"));

    /// EEBus_SHIP_TS_Specification_v1.0.1.pdf
    /// Section 11.4.7
    /// Page 36
    #[test]
    fn test_example10() {
        let orig_data = TopLevelExample {
            example: TopLevelExampleExample {
                complex_list_data: ComplexListData {
                    complex_data: vec![
                        ComplexData {
                            item_double: Some(1.6),
                            item_string: Some(String::from("abc")),
                        },
                        ComplexData {
                            item_double: Some(2.4),
                            item_string: Some(String::from("def")),
                        },
                    ],
                },
                item_empty: TopLevelExampleExampleItemEmpty {},
                optional_item: None,
            },
        };

        let json = nanoserde::SerJson::serialize_json(&orig_data);
        assert_eq!(
            json,
            r#"{"example":[{"complexListData":[{"complexData":[[{"itemDouble":1.6},{"itemString":"abc"}],[{"itemDouble":2.4},{"itemString":"def"}]]}]},{"itemEmpty":[]}]}"#
        );

        let parsed_data = nanoserde::DeJson::deserialize_json(&json).unwrap();
        assert!(matches!(
            parsed_data,
            TopLevelExample {
                example: TopLevelExampleExample {
                    complex_list_data: ComplexListData {
                        complex_data,
                    },
                    item_empty: TopLevelExampleExampleItemEmpty {},
                    optional_item: None,
                },
            } if complex_data.len() == 2
            && matches!(
                &complex_data[0],
                ComplexData {
                    item_double: Some(1.6),
                    item_string: Some(s),
                } if s == "abc"
            ) && matches!(
                &complex_data[1],
                ComplexData {
                    item_double: Some(2.4),
                    item_string: Some(s),
                } if s == "def"
            )
        ));
    }
}

mod test_example11 {
    use crate::spine::types::{invalid_variant, missing_field, unexpected_field};
    include!(concat!(env!("OUT_DIR"), "/example11.rs"));

    /// EEBus_SHIP_TS_Specification_v1.0.1.pdf
    /// Section 11.5.3
    /// Page 38
    #[test]
    fn test_example11() {
        const VALID_JSON_1: &str = r#"{"items":[{"item":[]}]}"#;
        const VALID_JSON_2: &str = r#"{"items":[]}"#;

        let orig_data = TopLevelItems {
            items: TopLevelItemsItems { item: vec![] },
        };

        let json = nanoserde::SerJson::serialize_json(&orig_data);
        assert!(json == VALID_JSON_1 || json == VALID_JSON_2);

        let parsed_data_1 = nanoserde::DeJson::deserialize_json(VALID_JSON_1).unwrap();
        assert!(matches!(
            parsed_data_1,
            TopLevelItems {
                items: TopLevelItemsItems { item },
            } if item.is_empty()
        ));

        let parsed_data_2 = nanoserde::DeJson::deserialize_json(VALID_JSON_2).unwrap();
        assert!(matches!(
            parsed_data_2,
            TopLevelItems {
                items: TopLevelItemsItems { item },
            } if item.is_empty()
        ));
    }
}

mod test_enum {
    use crate::spine::types::{invalid_variant, missing_field, unexpected_field};
    include!(concat!(env!("OUT_DIR"), "/enum.rs"));

    #[test]
    fn test_enum() {
        let orig_data = TopLevelValue {
            value: Interval::Weekly,
        };

        let json = nanoserde::SerJson::serialize_json(&orig_data);
        assert_eq!(json, r#"{"value":"weekly"}"#);

        let parsed_data = nanoserde::DeJson::deserialize_json(&json).unwrap();
        assert!(matches!(
            parsed_data,
            TopLevelValue {
                value: Interval::Weekly
            }
        ));
    }

    #[test]
    fn test_enum_extend() {
        let orig_data = TopLevelValue {
            value: Interval::VendorExtension("_i:12345_biweekly".to_owned()),
        };

        let json = nanoserde::SerJson::serialize_json(&orig_data);
        assert_eq!(json, r#"{"value":"_i:12345_biweekly"}"#);

        let parsed_data = nanoserde::DeJson::deserialize_json(&json).unwrap();
        assert!(matches!(parsed_data, TopLevelValue {
            value: Interval::VendorExtension(ve)
        } if ve == "_i:12345_biweekly"));
    }
}

mod test_union {
    use crate::spine::types::{invalid_variant, missing_field, unexpected_field};
    include!(concat!(env!("OUT_DIR"), "/union.rs"));

    #[test]
    fn test_union() {
        let orig_data = TopLevelValue {
            value: FeatureSpecificUsage::FeatureHvacSpecificUsage(
                FeatureHvacSpecificUsage::OperationMode,
            ),
        };

        let json = nanoserde::SerJson::serialize_json(&orig_data);
        assert_eq!(json, r#"{"value":"OperationMode"}"#);

        let parsed_data = nanoserde::DeJson::deserialize_json(&json).unwrap();
        assert!(matches!(
            parsed_data,
            TopLevelValue {
                value: FeatureSpecificUsage::FeatureHvacSpecificUsage(
                    FeatureHvacSpecificUsage::OperationMode,
                ),
            }
        ));
    }
}

mod test_eebus {
    use super::{ship::types as ship, spine::types::*};

    /// EEBus_SPINE_TS_TechnologyMappings.pdf, Section 2.2, Page 7
    /// EEBus_SPINE_TS_ProtocolSpecification.pdf, Section *******, Page 50-51
    #[test]
    fn test_ship_spine() {
        let orig_data = ship::MessageValue::Data(ship::MsgTypeData::Data(Box::new(ship::Data {
            header: ship::Header {
                protocol_id: "ee1.0".into(),
            },
            payload: TopLevelDatagram {
                datagram: Datagram {
                    header: Header {
                        cmd_classifier: Some(CmdClassifier::Read),
                        specification_version: None,
                        address_source: None,
                        address_destination: None,
                        address_originator: None,
                        msg_counter: None,
                        msg_counter_reference: None,
                        ack_request: None,
                        timestamp: None,
                    },
                    payload: Payload {
                        cmd: vec![Cmd {
                            function: Some(Function::MeasurementListData),
                            filter: vec![Filter {
                                filter_id: None,
                                cmd_control: Some(CmdControl::Partial),
                                data_selectors: vec![DataSelectors::MeasurementListDataSelectors(
                                    MeasurementListDataSelectors {
                                        measurement_id: Some(5),
                                        value_type: Some(MeasurementValue::MinValue),
                                        timestamp_interval: None,
                                    },
                                )],
                                data_element: None,
                            }],
                            data: Some(Data::MeasurementListData(MeasurementListData {
                                measurement_data: vec![],
                            })),
                            last_update_at: None,
                            manufacturer_specific_extension: None,
                        }],
                    },
                },
            },
            extension: None,
        })));

        let json = nanoserde::SerJson::serialize_json(&orig_data);
        assert_eq!(
            json,
            r#"{"data":[{"header":[{"protocolId":"ee1.0"}]},{"payload":{"datagram":[{"header":[{"cmdClassifier":"read"}]},{"payload":[{"cmd":[[{"function":"measurementListData"},{"filter":[[{"cmdControl":[{"partial":[]}]},{"measurementListDataSelectors":[{"measurementId":5},{"valueType":"minValue"}]}]]},{"measurementListData":[]}]]}]}]}}]}"#
        );

        let parsed_data: ship::MsgTypeData = nanoserde::DeJson::deserialize_json(&json).unwrap();

        let payload = match parsed_data {
            ship::MsgTypeData::Data(ref boxed_data)
                if let ship::Data {
                    header: ship::Header { protocol_id },
                    extension: None,
                    payload,
                } = &**boxed_data
                    && protocol_id == "ee1.0" =>
            {
                payload
            }
            _ => panic!("invalid message value: {parsed_data:?}"),
        };

        assert!(matches!(
            payload,
            TopLevelDatagram {
                datagram: Datagram {
                    header: Header {
                        cmd_classifier: Some(CmdClassifier::Read),
                        specification_version: None,
                        address_source: None,
                        address_destination: None,
                        address_originator: None,
                        msg_counter: None,
                        msg_counter_reference: None,
                        ack_request: None,
                        timestamp: None,
                    },
                    payload: Payload { cmd: _ }
                },
            }
        ));

        assert_eq!(payload.datagram.payload.cmd.len(), 1);
        let cmd = &payload.datagram.payload.cmd[0];

        assert!(matches!(cmd, Cmd {
            function: Some(Function::MeasurementListData),
            filter: _,
            data: Some(Data::MeasurementListData(MeasurementListData {
                measurement_data,
            })),
            last_update_at: None,
            manufacturer_specific_extension: None,
        } if measurement_data.is_empty()));

        assert_eq!(cmd.filter.len(), 1);
        let filter = &cmd.filter[0];

        assert!(matches!(
            filter,
            Filter {
                filter_id: None,
                cmd_control: Some(CmdControl::Partial),
                data_selectors: _,
                data_element: None,
            }
        ));

        assert_eq!(filter.data_selectors.len(), 1);
        let data_selector = &filter.data_selectors[0];

        assert!(matches!(
            data_selector,
            DataSelectors::MeasurementListDataSelectors(MeasurementListDataSelectors {
                measurement_id: Some(5),
                value_type: Some(MeasurementValue::MinValue),
                timestamp_interval: None,
            })
        ));
    }

    #[test]
    fn test_ship_hello() {
        use ship::*;

        let orig_data = MessageValue::Control(MsgTypeControl::ConnectionHello(ConnectionHello {
            phase: ConnectionHelloPhase::Ready,
            prolongation_request: None,
            waiting: None,
        }));

        let json = nanoserde::SerJson::serialize_json(&orig_data);
        assert_eq!(json, r#"{"connectionHello":[{"phase":"ready"}]}"#);

        let parsed_data: MsgTypeControl = nanoserde::DeJson::deserialize_json(&json).unwrap();

        assert!(matches!(
            parsed_data,
            MsgTypeControl::ConnectionHello(ConnectionHello {
                phase: ConnectionHelloPhase::Ready,
                prolongation_request: None,
                waiting: None,
            })
        ));
    }

    #[test]
    fn test_abs_rel_time() {
        let orig_data =
            AbsoluteOrRelativeTime::XsDateTime(XsDateTime::new(2025, 03, 25, 11, 20, 48, 0));
        let json = nanoserde::SerJson::serialize_json(&orig_data);
        assert_eq!(json, "\"2025-03-25T11:20:48.000+00:00\"");
        let parsed_data: AbsoluteOrRelativeTime =
            nanoserde::DeJson::deserialize_json(&json).unwrap();
        assert!(matches!(
            parsed_data,
            AbsoluteOrRelativeTime::XsDateTime(dt) if dt == XsDateTime::new(2025, 03, 25, 11, 20, 48, 0)
        ));

        let orig_data = AbsoluteOrRelativeTime::XsDuration(XsDuration::new(1, 2, 3, 4, 5, 6, 7));
        let json = nanoserde::SerJson::serialize_json(&orig_data);
        assert_eq!(json, "\"P1Y2M3DT4H5M6.007S\"");
        let parsed_data: AbsoluteOrRelativeTime =
            nanoserde::DeJson::deserialize_json(&json).unwrap();
        assert!(matches!(
            parsed_data,
            AbsoluteOrRelativeTime::XsDuration(dt) if dt == XsDuration::new(1, 2, 3, 4, 5, 6, 7)
        ));

        <AbsoluteOrRelativeTime as nanoserde::DeJson>::deserialize_json("\"blub\"").unwrap_err();
    }
}

pub mod ship {
    pub mod types {
        pub use crate::spine::types::{invalid_variant, missing_field, unexpected_field};
        include!(concat!(env!("OUT_DIR"), "/eebus_ship.rs"));
        include!("../../src/ship/custom_msg.rs");
    }
}

pub mod spine {
    pub mod types {
        pub mod eebus_spine {
            include!(concat!(env!("OUT_DIR"), "/eebus_spine.rs"));
        }
        pub mod custom_types {
            include!("../../src/spine/types/custom_types.rs");
        }
        pub mod nanoserde_utils {
            include!("../../src/spine/types/nanoserde_utils.rs");
        }
        pub mod xsd_support {
            include!("../../src/spine/types/xsd_support.rs");
        }
        pub mod traits {
            include!("../../src/spine/types/traits.rs");
        }

        pub use custom_types::*;
        pub use eebus_spine::*;
        pub use nanoserde_utils::*;
        pub use traits::*;
        pub use xsd_support::*;
    }
}

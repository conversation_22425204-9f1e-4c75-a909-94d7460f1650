use libmdns::Responder;
use log::info;

pub fn serve_mdns(ski: &str) {
    // Create an mDNS responder
    let responder = Responder::new().expect("Failed to create mDNS responder");

    // Define the instance name and the service name as specified in SHIP
    let service_name = "_ship._tcp";

    // Define the port your SHIP WebSocket server is listening on
    let port = 1472;

    let id_txt = format!("id=WOLF-EEB-{ski}");
    let ski_txt = format!("ski={ski}");

    // Add mandatory TXT records according to SHIP TS Table 2
    let txt_records = vec![
        // Example SHIP-specific key/value pairs
        "txtvers=1",     // TXT record version
        &id_txt,         // SHIP node identifier (can be SKI or other unique ID)
        "register=true", // If in auto-accept mode
        "brand=WOLF",    // Manufacturer brand
        "type=HeatPump", // Device type
        "model=CHA",     // Device model
        "path=/ship/",   // Path to SHIP WebSocket server
        &ski_txt,
    ];

    // Register the service
    let _svc = responder.register(
        service_name.to_owned(),
        format!("sim-{ski}"),
        port,
        &txt_records,
    );

    info!("now advertising _ship._tcp on port {}", port);

    std::mem::forget(_svc);
    std::mem::forget(responder);
}

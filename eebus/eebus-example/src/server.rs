use eebus::{
    ship::{Config, Ship, ShipConnection},
    spine::{
        error::SpineError,
        measurement::{Measurement, MeasurementHandler, MeasurementType, Measurements},
        nodemgmt::{
            EEBusDevice, EEBusEntity, EEBusFeature, EEBusFunction, EEBusOperations, EEBusUseCase,
            NodeManagement,
        },
        types::*,
        Handler, Spine,
    },
};
use log::{error, info, warn};

use crate::trust::MyTrust;

pub async fn run<C: ShipConnection>(connection: C) {
    let ship = match Ship::init(
        connection,
        &MyTrust::new(),
        Config::default(),
        String::from("WOLF-EEB-00123123"),
        true,
    )
    .await
    {
        Ok(Ok(ship)) => ship,
        Ok(Err(_)) => unimplemented!("pin input"),
        Err(e) => {
            error!("error initializing ship: {:?}", e);
            return;
        }
    };
    let spine = Spine::new(ship);

    let mut handler = MyHandler {
        node_management: node_management(),
        measurements: measurements(),
    };

    loop {
        let request = match spine.recv_request().await {
            Ok(x) => x,
            Err(e) => {
                error!("error receiving request: {:?}", e);
                return;
            }
        };
        info!("received request: {:?}", request);

        handler.handle(request).await.unwrap();
    }
}

struct MyHandler {
    node_management: NodeManagement,
    measurements: Measurements,
}

impl Handler for MyHandler {
    async fn handle_detailed_discovery_read(
        &mut self,
        elements: &NodeManagementDetailedDiscoveryDataElements,
    ) -> Result<NodeManagementDetailedDiscoveryData, SpineError> {
        let mut resp = self.node_management.discovery_details_response();
        elements.filter_elements(&mut resp);
        Ok(resp)
    }

    async fn handle_detailed_discovery_read_partial(
        &mut self,
        _elements: &NodeManagementDetailedDiscoveryDataElements,
        _one_of: &[NodeManagementDetailedDiscoveryDataSelectors],
    ) -> Result<NodeManagementDetailedDiscoveryData, SpineError> {
        unimplemented!()
    }

    async fn handle_use_case_read(
        &mut self,
        elements: &NodeManagementUseCaseDataElements,
    ) -> Result<NodeManagementUseCaseData, SpineError> {
        let mut resp = self.node_management.use_case_response();
        elements.filter_elements(&mut resp);
        Ok(resp)
    }

    async fn handle_use_case_read_partial(
        &mut self,
        _elements: &NodeManagementUseCaseDataElements,
        _one_of: &[NodeManagementUseCaseDataSelectors],
    ) -> Result<NodeManagementUseCaseData, SpineError> {
        unimplemented!()
    }

    async fn handle_smart_energy_management_read(
        &mut self,
        _elements: &SmartEnergyManagementPsDataElements,
    ) -> Result<SmartEnergyManagementPsData, SpineError> {
        unimplemented!()
    }

    async fn handle_smart_energy_management_read_partial(
        &mut self,
        _elements: &SmartEnergyManagementPsDataElements,
        _one_of: &[SmartEnergyManagementPsDataSelectors],
    ) -> Result<SmartEnergyManagementPsData, SpineError> {
        unimplemented!()
    }

    async fn handle_smart_energy_management_upsert(
        &mut self,
        _request: &SmartEnergyManagementPsData,
    ) -> Result<(), SpineError> {
        unimplemented!()
    }

    async fn handle_smart_energy_management_upsert_partial(
        &mut self,
        _request: &SmartEnergyManagementPsData,
        _one_of: &[SmartEnergyManagementPsDataSelectors],
    ) -> Result<(), SpineError> {
        unimplemented!()
    }

    async fn handle_binding_request_call(
        &mut self,
        request: &NodeManagementBindingRequestCall,
    ) -> Result<(), SpineError> {
        self.node_management.device.add_binding(request)
    }

    async fn handle_subscription_request_call(
        &mut self,
        request: &NodeManagementSubscriptionRequestCall,
    ) -> Result<(), SpineError> {
        self.node_management.device.add_subscription(request)
    }

    async fn handle_device_config_description_read(
        &mut self,
        _elements: &DeviceConfigurationKeyValueDescriptionDataElements,
    ) -> Result<DeviceConfigurationKeyValueDescriptionListData, SpineError> {
        unimplemented!()
    }

    async fn handle_device_config_description_read_partial(
        &mut self,
        _elements: &DeviceConfigurationKeyValueDescriptionDataElements,
        _one_of: &[DeviceConfigurationKeyValueDescriptionListDataSelectors],
    ) -> Result<DeviceConfigurationKeyValueDescriptionListData, SpineError> {
        unimplemented!()
    }

    async fn handle_device_config_read(
        &mut self,
        _elements: &DeviceConfigurationKeyValueDataElements,
    ) -> Result<DeviceConfigurationKeyValueListData, SpineError> {
        unimplemented!()
    }

    async fn handle_device_config_read_partial(
        &mut self,
        _elements: &DeviceConfigurationKeyValueDataElements,
        _one_of: &[DeviceConfigurationKeyValueListDataSelectors],
    ) -> Result<DeviceConfigurationKeyValueListData, SpineError> {
        unimplemented!()
    }

    async fn handle_device_config_upsert(
        &mut self,
        _request: &DeviceConfigurationKeyValueListData,
    ) -> Result<(), SpineError> {
        unimplemented!()
    }

    async fn handle_device_config_upsert_partial(
        &mut self,
        _request: &DeviceConfigurationKeyValueListData,
        _one_of: &[DeviceConfigurationKeyValueListDataSelectors],
    ) -> Result<(), SpineError> {
        unimplemented!()
    }

    async fn handle_electrical_connection_description_read(
        &mut self,
        _elements: &ElectricalConnectionDescriptionDataElements,
    ) -> Result<ElectricalConnectionDescriptionListData, SpineError> {
        unimplemented!()
    }

    async fn handle_electrical_connection_description_read_partial(
        &mut self,
        _elements: &ElectricalConnectionDescriptionDataElements,
        _one_of: &[ElectricalConnectionDescriptionListDataSelectors],
    ) -> Result<ElectricalConnectionDescriptionListData, SpineError> {
        unimplemented!()
    }

    async fn handle_electrical_connection_parameter_description_read(
        &mut self,
        _elements: &ElectricalConnectionParameterDescriptionDataElements,
    ) -> Result<ElectricalConnectionParameterDescriptionListData, SpineError> {
        unimplemented!()
    }

    async fn handle_electrical_connection_parameter_description_read_partial(
        &mut self,
        _elements: &ElectricalConnectionParameterDescriptionDataElements,
        _one_of: &[ElectricalConnectionParameterDescriptionListDataSelectors],
    ) -> Result<ElectricalConnectionParameterDescriptionListData, SpineError> {
        unimplemented!()
    }

    async fn handle_load_control_limit_description_read(
        &mut self,
        _elements: &LoadControlLimitDescriptionDataElements,
    ) -> Result<LoadControlLimitDescriptionListData, SpineError> {
        unimplemented!()
    }

    async fn handle_load_control_limit_description_read_partial(
        &mut self,
        _elements: &LoadControlLimitDescriptionDataElements,
        _one_of: &[LoadControlLimitDescriptionListDataSelectors],
    ) -> Result<LoadControlLimitDescriptionListData, SpineError> {
        unimplemented!()
    }

    async fn handle_load_control_limit_read(
        &mut self,
        _elements: &LoadControlLimitDataElements,
    ) -> Result<LoadControlLimitListData, SpineError> {
        unimplemented!()
    }

    async fn handle_load_control_limit_read_partial(
        &mut self,
        _elements: &LoadControlLimitDataElements,
        _one_of: &[LoadControlLimitListDataSelectors],
    ) -> Result<LoadControlLimitListData, SpineError> {
        unimplemented!()
    }

    async fn handle_load_control_limit_upsert(
        &mut self,
        _request: &LoadControlLimitListData,
    ) -> Result<(), SpineError> {
        unimplemented!()
    }

    async fn handle_load_control_limit_upsert_partial(
        &mut self,
        _request: &LoadControlLimitListData,
        _one_of: &[LoadControlLimitListDataSelectors],
    ) -> Result<(), SpineError> {
        unimplemented!()
    }

    async fn handle_measurement_constraints_read(
        &mut self,
        _elements: &MeasurementConstraintsDataElements,
    ) -> Result<MeasurementConstraintsListData, SpineError> {
        unimplemented!()
    }

    async fn handle_measurement_constraints_read_partial(
        &mut self,
        _elements: &MeasurementConstraintsDataElements,
        _one_of: &[MeasurementConstraintsListDataSelectors],
    ) -> Result<MeasurementConstraintsListData, SpineError> {
        unimplemented!()
    }

    async fn handle_measurement_description_read(
        &mut self,
        _elements: &MeasurementDescriptionDataElements,
    ) -> Result<MeasurementDescriptionListData, SpineError> {
        unimplemented!()
    }

    async fn handle_measurement_description_read_partial(
        &mut self,
        _elements: &MeasurementDescriptionDataElements,
        _one_of: &[MeasurementDescriptionListDataSelectors],
    ) -> Result<MeasurementDescriptionListData, SpineError> {
        unimplemented!()
    }

    async fn handle_measurement_read(
        &mut self,
        elements: &MeasurementDataElements,
    ) -> Result<MeasurementListData, SpineError> {
        let (mut resp, err) = self
            .measurements
            .list_data_response(&MyMeasurementHandler, None)
            .await;
        if let Err(e) = err {
            warn!("measurement error: {e:?}");
        }
        for item in &mut resp.measurement_data {
            elements.filter_elements(item);
        }
        Ok(resp)
    }

    async fn handle_measurement_read_partial(
        &mut self,
        elements: &MeasurementDataElements,
        one_of: &[MeasurementListDataSelectors],
    ) -> Result<MeasurementListData, SpineError> {
        let (mut resp, err) = self
            .measurements
            .list_data_response(&MyMeasurementHandler, Some(one_of))
            .await;
        if let Err(e) = err {
            warn!("measurement error: {e:?}");
        }
        for item in &mut resp.measurement_data {
            elements.filter_elements(item);
        }
        Ok(resp)
    }

    async fn handle_heartbeat_read(
        &mut self,
        _elements: &DeviceDiagnosisHeartbeatDataElements,
        _new_request_destination: FeatureAddress,
    ) -> Result<DeviceDiagnosisHeartbeatData, SpineError> {
        unimplemented!()
    }

    async fn handle_heartbeat_reply(
        &mut self,
        _reply: &DeviceDiagnosisHeartbeatData,
    ) -> Result<(), SpineError> {
        unimplemented!()
    }

    async fn handle_heartbeat_notify(
        &mut self,
        _notification: &DeviceDiagnosisHeartbeatData,
    ) -> Result<(), SpineError> {
        unimplemented!()
    }
}

struct MyMeasurementHandler;

impl MeasurementHandler for MyMeasurementHandler {
    type Error = ();

    async fn measure(&self, _measurement_id: u32) -> Result<ScaledNumber, Self::Error> {
        Err(())
    }
}

const DEVICE_NAME: &str = "d:_i:46922_TheMostAmazingWolfHeatPump";
const NODE_MANAGEMENT_ID: &[u32] = &[0];
const HEAT_PUMP_APPLIANCE_ID: &[u32] = &[1];
const COMPRESSOR_ID: &[u32] = &[1, 3];

fn node_management() -> NodeManagement {
    let device_information = device_information_entity();
    let (heat_pump_appliance, compressor) = heat_pump_entity();

    let mut device = EEBusDevice::new(DEVICE_NAME.to_owned(), Device::HeatGenerationSystem);
    device.add_entity(device_information).unwrap();
    device.add_entity(heat_pump_appliance).unwrap();
    device.add_entity(compressor).unwrap();

    NodeManagement::new(device)
}

#[rustfmt::skip]
const fn device_information_entity() -> EEBusEntity {
    const FUNCTIONS: &[EEBusFunction; 4] = &[
        EEBusFunction::new(Function::NodeManagementDetailedDiscoveryData, EEBusOperations::READ),
        EEBusFunction::new(Function::NodeManagementUseCaseData, EEBusOperations::READ),
        EEBusFunction::new(Function::NodeManagementSubscriptionRequestCall, EEBusOperations::empty()), // TODO: what does call count as? read? write? none?
        EEBusFunction::new(Function::NodeManagementBindingRequestCall, EEBusOperations::empty()), // TODO: what does call count as? read? write? none?
    ];

    const FEATURES: &[EEBusFeature] = &[
        EEBusFeature::new(0, Feature::NodeManagement, Role::Special, FUNCTIONS),
    ];

    EEBusEntity::new(NODE_MANAGEMENT_ID, Entity::DeviceInformation, FEATURES, &[])
}

#[rustfmt::skip]
const fn heat_pump_entity() -> (EEBusEntity, EEBusEntity) {
    const SMART_ENERGY_MANAGEMENT_PS_FUNCTIONS: &[EEBusFunction] = &[
        EEBusFunction::new(Function::SmartEnergyManagementPsData, EEBusOperations::READ.union(EEBusOperations::WRITE_PARTIAL)),
    ];

    const LOAD_CONTROL_FUNCTIONS: &[EEBusFunction] = &[
        EEBusFunction::new(Function::LoadControlLimitDescriptionListData, EEBusOperations::READ),
        EEBusFunction::new(Function::LoadControlLimitListData, EEBusOperations::READ.union(EEBusOperations::WRITE_PARTIAL)),
    ];

    const DEVICE_CONFIGURATION_FUNCTIONS: &[EEBusFunction] = &[
        EEBusFunction::new(Function::DeviceConfigurationKeyValueDescriptionListData, EEBusOperations::READ),
        EEBusFunction::new(Function::DeviceConfigurationKeyValueListData, EEBusOperations::READ.union(EEBusOperations::WRITE_PARTIAL)),
    ];

    const DEVICE_DIAGNOSIS_FUNCTIONS: &[EEBusFunction] = &[
        EEBusFunction::new(Function::DeviceDiagnosisHeartbeatData, EEBusOperations::READ)
    ];

    const ELECTRICAL_CONNECTION_FUNCTIONS: &[EEBusFunction] = &[
        EEBusFunction::new(Function::ElectricalConnectionDescriptionListData, EEBusOperations::READ),
        EEBusFunction::new(Function::ElectricalConnectionParameterDescriptionListData, EEBusOperations::READ),
    ];

    const MEASUREMENT_FUNCTIONS: &[EEBusFunction] = &[
        EEBusFunction::new(Function::MeasurementDescriptionListData, EEBusOperations::READ),
        EEBusFunction::new(Function::MeasurementConstraintsListData, EEBusOperations::READ),
        EEBusFunction::new(Function::MeasurementListData, EEBusOperations::READ),
    ];

    const FEATURES: &[EEBusFeature] = &[
        EEBusFeature::new(1, Feature::SmartEnergyManagementPs, Role::Server, SMART_ENERGY_MANAGEMENT_PS_FUNCTIONS),
        EEBusFeature::new(2, Feature::LoadControl, Role::Server, LOAD_CONTROL_FUNCTIONS),
        EEBusFeature::new(3, Feature::DeviceConfiguration, Role::Server, DEVICE_CONFIGURATION_FUNCTIONS),
        EEBusFeature::new(4, Feature::DeviceDiagnosis, Role::Server, DEVICE_DIAGNOSIS_FUNCTIONS),
        EEBusFeature::new(5, Feature::ElectricalConnection, Role::Server, ELECTRICAL_CONNECTION_FUNCTIONS),
        EEBusFeature::new(6, Feature::Measurement, Role::Server, MEASUREMENT_FUNCTIONS),
    ];

    const USE_CASES: &[EEBusUseCase] = &[
        EEBusUseCase::OHPCF_SERVER,
        EEBusUseCase::LPC_SERVER,
        EEBusUseCase::MPC_SERVER,
    ];

    (
        EEBusEntity::new(HEAT_PUMP_APPLIANCE_ID, Entity::HeatPumpAppliance, &[], &[]),
        EEBusEntity::new(COMPRESSOR_ID, Entity::Compressor, FEATURES, USE_CASES),
    )
}

pub fn measurements() -> Measurements {
    pub const AC_POWER_TOTAL_MEASUREMENT_ID: u32 = 1;
    pub const AC_POWER_PHASE_MEASUREMENT_ID: u32 = 2;

    // these two measurements must be present in this exact configuration for MPC use case
    const MEASUREMENTS: &[Measurement] = &[
        Measurement::new(
            AC_POWER_TOTAL_MEASUREMENT_ID,
            MeasurementType::Power,
            Commodity::Electricity,
            Scope::AcPowerTotal,
            UnitOfMeasurement::Watt,
            None,
        ),
        Measurement::new(
            AC_POWER_PHASE_MEASUREMENT_ID,
            MeasurementType::Power,
            Commodity::Electricity,
            Scope::AcPower,
            UnitOfMeasurement::Watt,
            None,
        ),
    ];

    Measurements::new(MEASUREMENTS)
}

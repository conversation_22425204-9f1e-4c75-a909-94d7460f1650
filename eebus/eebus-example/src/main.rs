use std::{pin::Pin, sync::Mutex};

use async_tungstenite::tokio::TokioAdapter;
use cert::generate_cert;
use eebus::ship::tungstenite::Connection;
use embassy_sync::blocking_mutex::raw::RawMutex;
use log::{error, info, LevelFilter};
use mdns::serve_mdns;
use openssl::{
    hash::MessageDigest,
    ssl::{SslAcceptor, SslFiletype, SslMethod, SslVerifyMode, SslVersion},
    x509::X509,
};
use tokio::net::TcpListener;
use tokio_openssl::SslStream;
use tokio_stream::Stream;

mod cert;
mod client;
mod mdns;
mod server;
mod trust;

struct Raw(Mutex<()>);

unsafe impl RawMutex for Raw {
    const INIT: Self = Raw(Mutex::new(()));

    fn lock<R>(&self, f: impl FnOnce() -> R) -> R {
        let _guard = self.0.lock().unwrap();
        f()
    }
}

async fn load_certs() -> anyhow::Result<(SslAcceptor, String)> {
    // Check for cert/key, or generate
    if !tokio::fs::try_exists("secret/cert.pem").await? {
        info!("no certificate found, generating...");
        generate_cert().await?; // same as before but outputs .pem files
    }

    let mut builder = SslAcceptor::mozilla_intermediate(SslMethod::tls())?;
    builder.set_private_key_file("secret/key.pem", SslFiletype::PEM)?;
    builder.set_certificate_chain_file("secret/cert.pem")?;

    // Request client certificate but don't verify it
    builder.set_verify(SslVerifyMode::PEER | SslVerifyMode::FAIL_IF_NO_PEER_CERT);
    builder.set_verify_callback(
        SslVerifyMode::PEER | SslVerifyMode::FAIL_IF_NO_PEER_CERT,
        |_, _| true,
    );

    // Enable both TLS 1.2 and 1.3
    builder.set_min_proto_version(Some(SslVersion::TLS1_2))?;
    builder.set_max_proto_version(Some(SslVersion::TLS1_2))?;

    // Set cipher suites for TLS 1.2
    builder.set_cipher_list("TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256")?;

    let ski = X509::from_pem(&std::fs::read("secret/cert.pem")?)?.digest(MessageDigest::sha1())?;
    let ski = hex::encode(ski);
    info!("using certificate with ski: {ski}");

    Ok((builder.build(), ski))
}

async fn accept_connections(
    acceptor: SslAcceptor,
    addr: &str,
) -> impl Stream<Item = anyhow::Result<Connection<Raw>>> {
    let acceptor = std::sync::Arc::new(acceptor);

    let listener = TcpListener::bind(addr).await.unwrap();

    async_stream::try_stream! {
        loop {
            info!("waiting for client to connect");
            let (stream, peer_address) = listener.accept().await?;
            info!("accepted connection from {}", peer_address);
            let ssl = openssl::ssl::Ssl::new(acceptor.context())?;
            let mut stream = SslStream::new(ssl, stream)?;
            //Pin::new(&mut stream).read_early_data(&mut [0u8; 0]).await?;
            tokio_openssl::SslStream::accept(Pin::new(&mut stream)).await?;

            info!("accepted tls connection from {}", peer_address);

            let client_cert = stream
                .ssl()
                .peer_certificate();

            let ski = if let Some(ski) = client_cert.as_ref().and_then(|cert| cert.subject_key_id()) {
                hex::encode(ski.as_slice())
            } else {
                String::from("no client cert or missing ski extension")
            };
            info!("client cert ski: {}", ski);

            let (connection, runner) = Connection::new(TokioAdapter::new(stream), peer_address.ip(), peer_address.port(), ski.parse().unwrap(), |payload| {
                use base64::Engine;
                use base64::engine::general_purpose::STANDARD as base64_engine;
                use sha1::{Digest, Sha1};

                let mut hasher = Sha1::new();
                hasher.update(payload);
                base64_engine.encode(hasher.finalize())
            }).await?;

            tokio::spawn(runner);

            yield connection;
        }
    }
}

async fn app() -> anyhow::Result<()> {
    use tokio_stream::StreamExt;

    let (config, ski) = load_certs().await?;

    let server = tokio::spawn(async move {
        let acceptor = accept_connections(config, "0.0.0.0:1472").await;
        serve_mdns(&ski);
        tokio::pin!(acceptor);

        while let Some(connection) = acceptor.next().await {
            let connection = match connection {
                Ok(connection) => connection,
                Err(err) => {
                    error!("error accepting connection: {:?}", err);
                    continue;
                }
            };

            tokio::spawn(async move {
                server::run(connection).await;
            });
        }
    });

    server.await.unwrap();

    Ok(())
}

fn main() {
    use fern::colors::Color;

    let log_file_name = format!(
        "logs/eebus-log-{}.log",
        chrono::Local::now().format("%Y-%m-%d_%H-%M-%S")
    );

    let colors = fern::colors::ColoredLevelConfig::new()
        .error(Color::Red)
        .warn(Color::Yellow)
        .info(Color::Green)
        .debug(Color::Blue)
        .trace(Color::Magenta);

    fern::Dispatch::new()
        .format(move |out, message, record| {
            out.finish(format_args!(
                "[{}][{}][{}] {}",
                chrono::Local::now().format("%Y-%m-%d %H:%M:%S"),
                colors.color(record.level()),
                record.target(),
                message
            ))
        })
        .level(LevelFilter::Debug)
        .level_for("libmdns", LevelFilter::Warn)
        .chain(std::io::stdout())
        .chain(fern::log_file(log_file_name).unwrap())
        .apply()
        .unwrap();

    tokio::runtime::Builder::new_current_thread()
        .enable_io()
        .enable_time()
        .build()
        .unwrap()
        .block_on(async move {
            app().await.unwrap();
        })
}

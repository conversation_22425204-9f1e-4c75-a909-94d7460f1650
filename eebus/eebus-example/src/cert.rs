use anyhow::Result;
use openssl::{
    asn1::Asn1Time,
    ec::{Ec<PERSON>roup, EcKey},
    hash::MessageDigest,
    nid::Nid,
    pkey::PKey,
    x509::{
        extension::{BasicConstraints, SubjectAlternativeName, SubjectKeyIdentifier},
        X509NameBuilder, X509,
    },
};

pub async fn generate_cert() -> Result<()> {
    let hostname = "eebus-device.local"; // This is ignored by SHIP, but included in CN and SAN

    // Generate ECDSA keypair with prime256v1
    let group = EcGroup::from_curve_name(Nid::X9_62_PRIME256V1)?;
    let ec_key = EcKey::generate(&group)?;
    let pkey = PKey::from_ec_key(ec_key)?;

    // Subject name (CN = hostname)
    let mut name_builder = X509NameBuilder::new()?;
    name_builder.append_entry_by_nid(Nid::COMMONNAME, hostname)?;
    let name = name_builder.build();

    // Create X509 certificate
    let mut builder = X509::builder()?;
    builder.set_version(2)?; // X509v3
    builder.set_subject_name(&name)?;
    builder.set_issuer_name(&name)?; // self-signed
    builder.set_pubkey(&pkey)?;

    builder.set_not_before(Asn1Time::from_unix(1735689600)?.as_ref())?; // 2025-01-01
    builder.set_not_after(Asn1Time::from_unix(2366764800)?.as_ref())?; // 2045-01-01

    // Add extensions
    let basic_constraints = BasicConstraints::new().critical().ca().build()?;
    builder.append_extension(basic_constraints)?;

    let subject_alt_name = SubjectAlternativeName::new()
        .dns(hostname)
        .build(&builder.x509v3_context(None, None))?;
    builder.append_extension(subject_alt_name)?;

    let ski = SubjectKeyIdentifier::new().build(&builder.x509v3_context(None, None))?;
    builder.append_extension(ski)?;

    builder.sign(&pkey, MessageDigest::sha256())?;

    let cert = builder.build();

    tokio::fs::create_dir_all("secret").await?;
    tokio::fs::write("secret/cert.pem", cert.to_pem()?).await?;
    tokio::fs::write("secret/key.pem", pkey.private_key_to_pem_pkcs8()?).await?;

    Ok(())
}

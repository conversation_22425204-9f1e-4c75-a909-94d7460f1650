use std::{
    future::pending,
    time::{Duration, Instant},
};

use eebus::ship::{DeviceInfo, Trust, TrustLevel};
use tokio::time::sleep;

pub struct MyTrust {
    init: Instant,
}

impl MyTrust {
    pub fn new() -> Self {
        Self {
            init: Instant::now(),
        }
    }
}

const TEN_SECS: Duration = Duration::from_secs(10);

impl Trust for MyTrust {
    async fn wait_trust_update(&self, _info: &DeviceInfo, _from: TrustLevel) -> TrustLevel {
        let elapsed = self.init.elapsed();
        if elapsed >= TEN_SECS {
            pending().await
        } else {
            let wait_time = TEN_SECS - elapsed;
            sleep(wait_time).await;
            TrustLevel::Trust
        }
    }

    async fn trust_level(&self, _info: &DeviceInfo) -> TrustLevel {
        if self.init.elapsed() >= TEN_SECS {
            TrustLevel::Trust
        } else {
            TrustLevel::Pending
        }
    }
}

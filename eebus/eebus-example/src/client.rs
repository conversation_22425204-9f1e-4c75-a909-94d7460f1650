use std::time::Duration;

use eebus::{
    ship::{Config, Ship, ShipConnection},
    spine::{
        types::{
            Cmd, CmdClassifier, Data, Datagram, FeatureAddress, Header, MeasurementListData,
            Payload,
        },
        Spine,
    },
};
use log::info;

use crate::trust::MyTrust;

#[allow(dead_code)]
pub async fn run<C: ShipConnection>(connection: C) {
    let ship = Ship::init(
        connection,
        &MyTrust::new(),
        Config::default(),
        String::from("WOLF-CLI-00123123"),
        false,
    )
    .await
    .unwrap();

    let ship = match ship {
        Ok(ship) => ship,
        Err(_) => unimplemented!("pin input"),
    };

    let spine = Spine::new(ship);

    spine
        .send_request_raw(Datagram {
            header: Header {
                specification_version: Some("1.3.0".to_owned()),
                address_source: Some(FeatureAddress {
                    device: Some("1".to_owned()),
                    entity: vec![1],
                    feature: Some(1),
                }),
                address_destination: Some(FeatureAddress {
                    device: Some("1".to_owned()),
                    entity: vec![1],
                    feature: Some(1),
                }),
                address_originator: None,
                msg_counter: Some(42),
                msg_counter_reference: None,
                cmd_classifier: Some(CmdClassifier::Read),
                ack_request: None,
                timestamp: None,
            },
            payload: Payload {
                cmd: vec![Cmd {
                    function: None, //Some(Function::MeasurementListData),
                    filter: vec![],
                    manufacturer_specific_extension: None,
                    last_update_at: None,
                    data: Some(Data::MeasurementListData(MeasurementListData {
                        measurement_data: vec![],
                    })),
                }],
            },
        })
        .await
        .unwrap();
    let req = spine.recv_request().await.unwrap();
    info!("received response: {:?}", req);

    tokio::time::sleep(Duration::from_secs(1)).await;
}

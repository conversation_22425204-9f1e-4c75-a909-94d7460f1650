[package]
name = "eebus-example"
version = "0.1.0"
edition = "2024"
default-run = "eebus-example"

[dependencies]
anyhow = "1.0.97"
async-stream = "0.3.6"
async-tungstenite = { version = "0.29.1", features = [
    "tokio-runtime",
    "tokio-openssl",
] }
eebus = { path = "../" }
embassy-sync = { version = "0.6.2", default-features = false, features = [] }
env_logger = "0.11.7"
hex = "0.4.3"
hostname = "0.4.0"
libmdns = "0.9.1"
log = "0.4.27"

sha1 = "0.10.6"
tokio = { version = "1.44.1", features = ["fs", "macros", "rt", "io-util"] }
tokio-openssl = "0.6"
openssl = { version = "0.10", features = ["v111"] }
tokio-stream = "0.1.17"
tokio-util = { version = "0.7.14", features = ["compat"] }
base64 = "0.22.1"
fern = { version = "0.7.1", features = ["chrono", "colored"] }
chrono = "0.4.40"

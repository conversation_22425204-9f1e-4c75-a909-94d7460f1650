<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema"
    targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0" blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_OperatingConstraints.xsd"/>
    <xs:group name="group_operatingConstraints">
        <xs:sequence>
            <xs:group ref="ns_p:operatingConstraintsInterrupt"/>
            <xs:group ref="ns_p:operatingConstraintsDuration"/>
            <xs:group ref="ns_p:operatingConstraintsPowerDescription"/>
            <xs:group ref="ns_p:operatingConstraintsPowerRange"/>
            <xs:group ref="ns_p:operatingConstraintsPowerLevel"/>
            <xs:group ref="ns_p:operatingConstraintsResumeImplication"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="operatingConstraintsInterrupt">
        <xs:sequence>
            <xs:element ref="ns_p:operatingConstraintsInterruptListData"/>
            <xs:element ref="ns_p:operatingConstraintsInterruptListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="operatingConstraintsDuration">
        <xs:sequence>
            <xs:element ref="ns_p:operatingConstraintsDurationListData"/>
            <xs:element ref="ns_p:operatingConstraintsDurationListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="operatingConstraintsPowerDescription">
        <xs:sequence>
            <xs:element ref="ns_p:operatingConstraintsPowerDescriptionListData"/>
            <xs:element ref="ns_p:operatingConstraintsPowerDescriptionListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="operatingConstraintsPowerRange">
        <xs:sequence>
            <xs:element ref="ns_p:operatingConstraintsPowerRangeListData"/>
            <xs:element ref="ns_p:operatingConstraintsPowerRangeListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="operatingConstraintsPowerLevel">
        <xs:sequence>
            <xs:element ref="ns_p:operatingConstraintsPowerLevelListData"/>
            <xs:element ref="ns_p:operatingConstraintsPowerLevelListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="operatingConstraintsResumeImplication">
        <xs:sequence>
            <xs:element ref="ns_p:operatingConstraintsResumeImplicationListData"/>
            <xs:element ref="ns_p:operatingConstraintsResumeImplicationListDataSelectors"/>
        </xs:sequence>
    </xs:group>
</xs:schema>

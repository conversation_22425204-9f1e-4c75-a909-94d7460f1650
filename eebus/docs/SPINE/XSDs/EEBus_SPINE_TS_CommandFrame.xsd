<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0"
    blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_CommandCommonDefinitions.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_CommonDataTypes.xsd"/>
    <xs:simpleType name="MsgCounterType">
        <xs:restriction base="xs:unsignedLong"> </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CmdClassifierType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="read"/>
            <xs:enumeration value="reply"/>
            <xs:enumeration value="notify"/>
            <xs:enumeration value="write"/>
            <xs:enumeration value="call"/>
            <xs:enumeration value="result"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="cmdClassifier" type="ns_p:CmdClassifierType"/>
    <xs:element name="manufacturerSpecificExtension" type="xs:hexBinary"/>
    <xs:element name="lastUpdateAt" type="ns_p:AbsoluteOrRelativeTimeType"/>
    <xs:element name="function" type="ns_p:FunctionType"/>
    <xs:simpleType name="FilterIdType">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>
    <xs:complexType name="FilterType">
        <xs:sequence>
            <xs:element minOccurs="0" name="filterId" type="ns_p:FilterIdType"/>
            <xs:element minOccurs="0" ref="ns_p:cmdControl"/>
            <xs:group ref="ns_p:DataSelectorsChoiceGroup" minOccurs="0" maxOccurs="unbounded"/>
            <xs:group ref="ns_p:DataElementsChoiceGroup" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="filter" type="ns_p:FilterType"/>
    <xs:complexType name="CmdControlType">
        <xs:sequence>
            <xs:element name="delete" minOccurs="0" type="ns_p:ElementTagType"/>
            <xs:element name="partial" minOccurs="0" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="cmdControl" type="ns_p:CmdControlType"/>
    <xs:group name="CmdOptionGroup">
        <xs:sequence>
            <xs:element minOccurs="0" ref="ns_p:function"/>
            <xs:element minOccurs="0" ref="ns_p:filter" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="DataExtendGroup">
        <xs:sequence>
            <xs:element minOccurs="0" ref="ns_p:manufacturerSpecificExtension"/>
            <xs:element minOccurs="0" ref="ns_p:lastUpdateAt"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="PayloadContributionGroup">
        <xs:sequence>
            <xs:group ref="ns_p:CmdOptionGroup" minOccurs="0"/>
            <xs:group ref="ns_p:DataChoiceGroup" minOccurs="0"/>
            <xs:group minOccurs="0" ref="ns_p:DataExtendGroup"/>
        </xs:sequence>
    </xs:group>
    <xs:complexType name="CmdType">
        <xs:sequence>
            <xs:group ref="ns_p:PayloadContributionGroup"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>

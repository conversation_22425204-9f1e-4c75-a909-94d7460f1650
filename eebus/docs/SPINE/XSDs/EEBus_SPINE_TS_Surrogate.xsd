<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0"
    blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_CommonDataTypes.xsd"/>
    <xs:simpleType name="SurrogateIdType">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>
    <xs:simpleType name="SurrogateScopeEnumType">
        <xs:restriction base="xs:string">
            <xs:pattern value="regulation.*"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="SurrogateScopeType">
        <xs:union memberTypes="ns_p:SurrogateScopeEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="SurrogateOtherSourceType">
        <xs:restriction base="xs:string"/>
    </xs:simpleType>
    <xs:complexType name="SurrogateSourcesType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="spineSource" type="ns_p:EntityAddressType"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="otherSource" type="ns_p:SurrogateOtherSourceType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SurrogateDescriptionDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="surrogateId" type="ns_p:SurrogateIdType"/>
            <xs:element minOccurs="0" name="surrogateScope" type="ns_p:SurrogateScopeType"/>
            <xs:element minOccurs="0" name="surrogateSources" type="ns_p:SurrogateSourcesType"/>
            <xs:element minOccurs="0" name="label" type="ns_p:LabelType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:DescriptionType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="surrogateDescriptionData" type="ns_p:SurrogateDescriptionDataType"/>
    <xs:complexType name="SurrogateSourcesElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="spineSource" type="ns_p:EntityAddressElementsType"/>
            <xs:element minOccurs="0" name="otherSource" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SurrogateDescriptionDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="surrogateId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="surrogateScope" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="surrogateSources" type="ns_p:SurrogateSourcesElementsType"> </xs:element>
            <xs:element minOccurs="0" name="label" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="surrogateDescriptionDataElements" type="ns_p:SurrogateDescriptionDataElementsType"/>
    <xs:complexType name="SurrogateDescriptionListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:surrogateDescriptionData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SurrogateDescriptionListDataSelectorsSourcesType">
        <xs:sequence>
            <xs:element minOccurs="0" name="spineSource" type="ns_p:EntityAddressType"/>
            <xs:element minOccurs="0" name="otherSource" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="surrogateDescriptionListData" type="ns_p:SurrogateDescriptionListDataType"/>
    <xs:complexType name="SurrogateDescriptionListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="surrogateId" type="ns_p:SurrogateIdType"/>
            <xs:element minOccurs="0" name="surrogateScope" type="ns_p:SurrogateScopeType"/>
            <xs:element minOccurs="0" name="surrogateSources" type="ns_p:SurrogateDescriptionListDataSelectorsSourcesType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="surrogateDescriptionListDataSelectors" type="ns_p:SurrogateDescriptionListDataSelectorsType"/>
</xs:schema>

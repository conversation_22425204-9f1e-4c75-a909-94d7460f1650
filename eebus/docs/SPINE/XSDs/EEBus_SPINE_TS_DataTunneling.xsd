<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0"
    blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_CommonDataTypes.xsd"/>
    <xs:simpleType name="PurposeIdType">
        <xs:restriction base="xs:string"/>
    </xs:simpleType>
    <xs:simpleType name="ChannelIdType">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>
    <xs:complexType name="DataTunnelingHeaderType">
        <xs:sequence>
            <xs:element name="purposeId" type="ns_p:PurposeIdType" minOccurs="0"/>
            <xs:element name="channelId" type="ns_p:ChannelIdType" minOccurs="0"/>
            <xs:element name="sequenceId" type="xs:unsignedInt" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DataTunnelingHeaderElementsType">
        <xs:sequence>
            <xs:element name="purposeId" minOccurs="0" type="ns_p:ElementTagType"/>
            <xs:element name="channelId" minOccurs="0" type="ns_p:ElementTagType"/>
            <xs:element name="sequenceId" minOccurs="0" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DataTunnelingCallType">
        <xs:sequence>
            <xs:element name="header" minOccurs="0" type="ns_p:DataTunnelingHeaderType"/>
            <xs:element name="payload" type="xs:hexBinary" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="dataTunnelingCall" type="ns_p:DataTunnelingCallType"/>
    <xs:complexType name="DataTunnelingCallElementsType">
        <xs:sequence>
            <xs:element name="header" minOccurs="0" type="ns_p:DataTunnelingHeaderElementsType"/>
            <xs:element name="payload" minOccurs="0" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="dataTunnelingCallElements" type="ns_p:DataTunnelingCallElementsType"/>
</xs:schema>

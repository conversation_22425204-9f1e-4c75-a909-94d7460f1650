<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0"
    blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_CommonDataTypes.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_Identification.xsd"/>
    <xs:simpleType name="BillIdType">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>
    <xs:simpleType name="BillTypeType">
        <xs:union memberTypes="ns_p:BillTypeEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="BillTypeEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="chargingSummary"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="BillPositionIdType">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>
    <xs:simpleType name="BillPositionCountType">
        <xs:restriction base="ns_p:BillPositionIdType"/>
    </xs:simpleType>
    <xs:simpleType name="BillPositionTypeType">
        <xs:union memberTypes="ns_p:BillPositionTypeEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="BillPositionTypeEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="gridElectricEnergy"/>
            <xs:enumeration value="selfProducedElectricEnergy"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="BillValueIdType">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>
    <xs:simpleType name="BillCostIdType">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>
    <xs:simpleType name="BillCostTypeType">
        <xs:union memberTypes="ns_p:BillCostTypeEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="BillCostTypeEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="absolutePrice"/>
            <xs:enumeration value="relativePrice"/>
            <xs:enumeration value="co2Emission"/>
            <xs:enumeration value="renewableEnergy"/>
            <xs:enumeration value="radioactiveWaste"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="BillValueType">
        <xs:sequence>
            <xs:element minOccurs="0" name="valueId" type="ns_p:BillValueIdType"/>
            <xs:element minOccurs="0" name="unit" type="ns_p:UnitOfMeasurementType"/>
            <xs:element minOccurs="0" name="value" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="valuePercentage" type="ns_p:ScaledNumberType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="BillValueElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="valueId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="unit" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="value" type="ns_p:ScaledNumberElementsType"/>
            <xs:element minOccurs="0" name="valuePercentage" type="ns_p:ScaledNumberElementsType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="BillCostType">
        <xs:sequence>
            <xs:element minOccurs="0" name="costId" type="ns_p:BillCostIdType"/>
            <xs:element minOccurs="0" name="costType" type="ns_p:BillCostTypeType"/>
            <xs:element minOccurs="0" name="valueId" type="ns_p:BillValueIdType"/>
            <xs:element minOccurs="0" name="unit" type="ns_p:UnitOfMeasurementType"/>
            <xs:element minOccurs="0" name="currency" type="ns_p:CurrencyType"/>
            <xs:element minOccurs="0" name="cost" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="costPercentage" type="ns_p:ScaledNumberType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="BillCostElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="costId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="costType" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="valueId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="unit" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="currency" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="cost" type="ns_p:ScaledNumberElementsType"/>
            <xs:element minOccurs="0" name="costPercentage" type="ns_p:ScaledNumberElementsType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="BillPositionType">
        <xs:sequence>
            <xs:element minOccurs="0" name="positionId" type="ns_p:BillPositionIdType"/>
            <xs:element minOccurs="0" name="positionType" type="ns_p:BillPositionTypeType"/>
            <xs:element minOccurs="0" name="timePeriod" type="ns_p:TimePeriodType"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="value" type="ns_p:BillValueType"/>
            <xs:element minOccurs="0" name="cost" maxOccurs="unbounded" type="ns_p:BillCostType"/>
            <xs:element minOccurs="0" name="label" type="ns_p:LabelType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:DescriptionType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="BillPositionElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="positionId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="positionType" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="timePeriod" type="ns_p:TimePeriodElementsType"/>
            <xs:element minOccurs="0" name="value" type="ns_p:BillValueElementsType"/>
            <xs:element minOccurs="0" name="cost" type="ns_p:BillCostElementsType"/>
            <xs:element minOccurs="0" name="label" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="BillDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="billId" type="ns_p:BillIdType"/>
            <xs:element minOccurs="0" name="billType" type="ns_p:BillTypeType"/>
            <xs:element minOccurs="0" name="scopeType" type="ns_p:ScopeTypeType"/>
            <xs:element minOccurs="0" name="total">
                <xs:complexType>
                    <xs:complexContent>
                        <xs:restriction base="ns_p:BillPositionType">
                            <xs:sequence>
                                <xs:element minOccurs="0" name="timePeriod" type="ns_p:TimePeriodType"/>
                                <xs:element maxOccurs="unbounded" minOccurs="0" name="value">
                                    <xs:complexType>
                                        <xs:complexContent>
                                            <xs:restriction base="ns_p:BillValueType">
                                                <xs:sequence>
                                                    <xs:element minOccurs="0" name="valueId" type="ns_p:BillValueIdType"/>
                                                    <xs:element minOccurs="0" name="unit" type="ns_p:UnitOfMeasurementType"/>
                                                    <xs:element minOccurs="0" name="value" type="ns_p:ScaledNumberType"/>
                                                </xs:sequence>
                                            </xs:restriction>
                                        </xs:complexContent>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0" name="cost" maxOccurs="unbounded">
                                    <xs:complexType>
                                        <xs:complexContent>
                                            <xs:restriction base="ns_p:BillCostType">
                                                <xs:sequence>
                                                    <xs:element minOccurs="0" name="costId" type="ns_p:BillCostIdType"/>
                                                    <xs:element minOccurs="0" name="costType" type="ns_p:BillCostTypeType"/>
                                                    <xs:element minOccurs="0" name="valueId" type="ns_p:BillValueIdType"/>
                                                    <xs:element minOccurs="0" name="unit" type="ns_p:UnitOfMeasurementType"/>
                                                    <xs:element minOccurs="0" name="currency" type="ns_p:CurrencyType"/>
                                                    <xs:element minOccurs="0" name="cost" type="ns_p:ScaledNumberType"/>
                                                </xs:sequence>
                                            </xs:restriction>
                                        </xs:complexContent>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0" name="label" type="ns_p:LabelType"/>
                                <xs:element minOccurs="0" name="description" type="ns_p:DescriptionType"/>
                            </xs:sequence>
                        </xs:restriction>
                    </xs:complexContent>
                </xs:complexType>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="position">
                <xs:complexType>
                    <xs:complexContent>
                        <xs:restriction base="ns_p:BillPositionType">
                            <xs:sequence>
                                <xs:element minOccurs="0" name="positionId" type="ns_p:BillPositionIdType"/>
                                <xs:element minOccurs="0" name="positionType" type="ns_p:BillPositionTypeType"/>
                                <xs:element minOccurs="0" name="timePeriod" type="ns_p:TimePeriodType"/>
                                <xs:element maxOccurs="unbounded" minOccurs="0" name="value">
                                    <xs:complexType>
                                        <xs:complexContent>
                                            <xs:restriction base="ns_p:BillValueType">
                                                <xs:sequence>
                                                    <xs:element minOccurs="0" name="valueId" type="ns_p:BillValueIdType"/>
                                                    <xs:element minOccurs="0" name="value" type="ns_p:ScaledNumberType"/>
                                                    <xs:element minOccurs="0" name="valuePercentage" type="ns_p:ScaledNumberType"/>
                                                </xs:sequence>
                                            </xs:restriction>
                                        </xs:complexContent>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0" name="cost" maxOccurs="unbounded">
                                    <xs:complexType>
                                        <xs:complexContent>
                                            <xs:restriction base="ns_p:BillCostType">
                                                <xs:sequence>
                                                    <xs:element minOccurs="0" name="costId" type="ns_p:BillCostIdType"/>
                                                    <xs:element minOccurs="0" name="cost" type="ns_p:ScaledNumberType"/>
                                                    <xs:element minOccurs="0" name="costPercentage" type="ns_p:ScaledNumberType"/>
                                                </xs:sequence>
                                            </xs:restriction>
                                        </xs:complexContent>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0" name="label" type="ns_p:LabelType"/>
                                <xs:element minOccurs="0" name="description" type="ns_p:DescriptionType"/>
                            </xs:sequence>
                        </xs:restriction>
                    </xs:complexContent>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="billData" type="ns_p:BillDataType"/>
    <xs:complexType name="BillDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="billId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="billType" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="scopeType" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="total">
                <xs:complexType>
                    <xs:complexContent>
                        <xs:restriction base="ns_p:BillPositionElementsType">
                            <xs:sequence>
                                <xs:element minOccurs="0" name="timePeriod" type="ns_p:TimePeriodElementsType"/>
                                <xs:element minOccurs="0" name="value">
                                    <xs:complexType>
                                        <xs:complexContent>
                                            <xs:restriction base="ns_p:BillValueElementsType">
                                                <xs:sequence>
                                                    <xs:element minOccurs="0" name="valueId" type="ns_p:ElementTagType"/>
                                                    <xs:element minOccurs="0" name="unit" type="ns_p:ElementTagType"/>
                                                    <xs:element minOccurs="0" name="value" type="ns_p:ScaledNumberElementsType"/>
                                                </xs:sequence>
                                            </xs:restriction>
                                        </xs:complexContent>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0" name="cost">
                                    <xs:complexType>
                                        <xs:complexContent>
                                            <xs:restriction base="ns_p:BillCostElementsType">
                                                <xs:sequence>
                                                    <xs:element minOccurs="0" name="costId" type="ns_p:ElementTagType"/>
                                                    <xs:element minOccurs="0" name="costType" type="ns_p:ElementTagType"/>
                                                    <xs:element minOccurs="0" name="valueId" type="ns_p:ElementTagType"/>
                                                    <xs:element minOccurs="0" name="unit" type="ns_p:ElementTagType"/>
                                                    <xs:element minOccurs="0" name="currency" type="ns_p:ElementTagType"/>
                                                    <xs:element minOccurs="0" name="cost" type="ns_p:ScaledNumberElementsType"/>
                                                </xs:sequence>
                                            </xs:restriction>
                                        </xs:complexContent>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0" name="label" type="ns_p:ElementTagType"/>
                                <xs:element minOccurs="0" name="description" type="ns_p:ElementTagType"/>
                            </xs:sequence>
                        </xs:restriction>
                    </xs:complexContent>
                </xs:complexType>
            </xs:element>
            <xs:element minOccurs="0" name="position">
                <xs:complexType>
                    <xs:complexContent>
                        <xs:restriction base="ns_p:BillPositionElementsType">
                            <xs:sequence>
                                <xs:element minOccurs="0" name="positionId" type="ns_p:ElementTagType"/>
                                <xs:element minOccurs="0" name="positionType" type="ns_p:ElementTagType"/>
                                <xs:element minOccurs="0" name="timePeriod" type="ns_p:TimePeriodElementsType"/>
                                <xs:element minOccurs="0" name="value">
                                    <xs:complexType>
                                        <xs:complexContent>
                                            <xs:restriction base="ns_p:BillValueElementsType">
                                                <xs:sequence>
                                                    <xs:element minOccurs="0" name="valueId" type="ns_p:ElementTagType"/>
                                                    <xs:element minOccurs="0" name="value" type="ns_p:ScaledNumberElementsType"/>
                                                    <xs:element minOccurs="0" name="valuePercentage" type="ns_p:ScaledNumberElementsType"/>
                                                </xs:sequence>
                                            </xs:restriction>
                                        </xs:complexContent>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0" name="cost">
                                    <xs:complexType>
                                        <xs:complexContent>
                                            <xs:restriction base="ns_p:BillCostElementsType">
                                                <xs:sequence>
                                                    <xs:element minOccurs="0" name="costId" type="ns_p:ElementTagType"/>
                                                    <xs:element minOccurs="0" name="cost" type="ns_p:ScaledNumberElementsType"/>
                                                    <xs:element minOccurs="0" name="costPercentage" type="ns_p:ScaledNumberElementsType"/>
                                                </xs:sequence>
                                            </xs:restriction>
                                        </xs:complexContent>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0" name="label" type="ns_p:ElementTagType"/>
                                <xs:element minOccurs="0" name="description" type="ns_p:ElementTagType"/>
                            </xs:sequence>
                        </xs:restriction>
                    </xs:complexContent>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="billDataElements" type="ns_p:BillDataElementsType"/>
    <xs:complexType name="BillListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:billData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="billListData" type="ns_p:BillListDataType"/>
    <xs:complexType name="BillListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="billId" type="ns_p:BillIdType"/>
            <xs:element minOccurs="0" name="scopeType" type="ns_p:ScopeTypeType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="billListDataSelectors" type="ns_p:BillListDataSelectorsType"/>
    <xs:complexType name="BillConstraintsDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="billId" type="ns_p:BillIdType"/>
            <xs:element minOccurs="0" name="positionCountMin" type="ns_p:BillPositionCountType"/>
            <xs:element minOccurs="0" name="positionCountMax" type="ns_p:BillPositionCountType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="billConstraintsData" type="ns_p:BillConstraintsDataType"/>
    <xs:complexType name="BillConstraintsDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="billId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="positionCountMin" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="positionCountMax" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="billConstraintsDataElements" type="ns_p:BillConstraintsDataElementsType"/>
    <xs:complexType name="BillConstraintsListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:billConstraintsData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="billConstraintsListData" type="ns_p:BillConstraintsListDataType"/>
    <xs:complexType name="BillConstraintsListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="billId" type="ns_p:BillIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="billConstraintsListDataSelectors" type="ns_p:BillConstraintsListDataSelectorsType"/>
    <xs:complexType name="BillDescriptionDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="billId" type="ns_p:BillIdType"/>
            <xs:element minOccurs="0" name="billWriteable" type="xs:boolean"/>
            <xs:element minOccurs="0" name="updateRequired" type="xs:boolean"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="supportedBillType" type="ns_p:BillTypeType"/>
            <xs:element minOccurs="0" name="sessionId" type="ns_p:SessionIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="billDescriptionData" type="ns_p:BillDescriptionDataType"/>
    <xs:complexType name="BillDescriptionDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="billId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="billWriteable" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="updateRequired" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="supportedBillType" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="sessionId" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="billDescriptionDataElements" type="ns_p:BillDescriptionDataElementsType"/>
    <xs:complexType name="BillDescriptionListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:billDescriptionData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="billDescriptionListData" type="ns_p:BillDescriptionListDataType"/>
    <xs:complexType name="BillDescriptionListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="billId" type="ns_p:BillIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="billDescriptionListDataSelectors" type="ns_p:BillDescriptionListDataSelectorsType"/>
</xs:schema>

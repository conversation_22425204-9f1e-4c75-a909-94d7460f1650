<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0"
    blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_CommonDataTypes.xsd"/>
    <xs:simpleType name="stateInformationIdType">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>
    <xs:simpleType name="StateInformationType">
        <xs:union memberTypes="ns_p:StateInformationFunctionalityEnumType ns_p:StateInformationFailureEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="StateInformationFunctionalityEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="externalOverrideFromGrid"/>
            <xs:enumeration value="autonomousGridSupport"/>
            <xs:enumeration value="islandingMode"/>
            <xs:enumeration value="balancing"/>
            <xs:enumeration value="trickleCharging"/>
            <xs:enumeration value="calibration"/>
            <xs:enumeration value="commissioningMissing"/>
            <xs:enumeration value="sleeping"/>
            <xs:enumeration value="starting"/>
            <xs:enumeration value="mppt"/>
            <xs:enumeration value="throttled"/>
            <xs:enumeration value="shuttingDown"/>
            <xs:enumeration value="manualShutdown"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="StateInformationFailureEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="inverterDefective"/>
            <xs:enumeration value="batteryOvercurrentProtection"/>
            <xs:enumeration value="pvStringOvercurrentProtection"/>
            <xs:enumeration value="gridFault"/>
            <xs:enumeration value="groundFault"/>
            <xs:enumeration value="acDisconnected"/>
            <xs:enumeration value="dcDisconnected"/>
            <xs:enumeration value="cabinetOpen"/>
            <xs:enumeration value="overTemperature"/>
            <xs:enumeration value="underTemperature"/>
            <xs:enumeration value="frequencyAboveLimit"/>
            <xs:enumeration value="frequencyBelowLimit"/>
            <xs:enumeration value="acVoltageAboveLimit"/>
            <xs:enumeration value="acVoltageBelowLimit"/>
            <xs:enumeration value="dcVoltageAboveLimit"/>
            <xs:enumeration value="dcVoltageBelowLimit"/>
            <xs:enumeration value="hardwareTestFailure"/>
            <xs:enumeration value="genericInternalError"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="StateInformationCategoryType">
        <xs:union memberTypes="ns_p:StateInformationCategoryEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="StateInformationCategoryEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="functionality"/>
            <xs:enumeration value="failure"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="StateInformationDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="stateInformationId" type="ns_p:stateInformationIdType"/>
            <xs:element minOccurs="0" name="stateInformation" type="ns_p:StateInformationType"/>
            <xs:element minOccurs="0" name="isActive" type="xs:boolean"/>
            <xs:element minOccurs="0" name="category" type="ns_p:StateInformationCategoryType"/>
            <xs:element minOccurs="0" name="timeOfLastChange" type="ns_p:AbsoluteOrRelativeTimeType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="stateInformationData" type="ns_p:StateInformationDataType"/>
    <xs:complexType name="StateInformationDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="stateInformationId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="stateInformation" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="isActive" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="category" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="timeOfLastChange" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="stateInformationDataElements" type="ns_p:StateInformationDataElementsType"/>
    <xs:complexType name="StateInformationListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:stateInformationData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="stateInformationListData" type="ns_p:StateInformationListDataType"/>
    <xs:complexType name="StateInformationListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="stateInformationId" type="ns_p:stateInformationIdType"/>
            <xs:element minOccurs="0" name="stateInformation" type="ns_p:StateInformationType"/>
            <xs:element minOccurs="0" name="isActive" type="xs:boolean"/>
            <xs:element minOccurs="0" name="category" type="ns_p:StateInformationCategoryType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="stateInformationListDataSelectors" type="ns_p:StateInformationListDataSelectorsType"/>
</xs:schema>

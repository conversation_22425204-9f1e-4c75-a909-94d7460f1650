<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0"
    blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_CommonDataTypes.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_Measurement.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_TimeTable.xsd"/>
    <xs:simpleType name="SetpointIdType">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>
    <xs:simpleType name="SetpointTypeType">
        <xs:union memberTypes="ns_p:SetpointTypeEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="SetpointTypeEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="valueAbsolute"/>
            <xs:enumeration value="valueRelative"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="SetpointValuesType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="value" type="ns_p:ScaledNumberType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SetpointValuesElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="value" type="ns_p:ScaledNumberElementsType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SetpointDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="setpointId" type="ns_p:SetpointIdType"/>
            <xs:element minOccurs="0" name="value" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="valueMin" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="valueMax" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="valueToleranceAbsolute" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="valueTolerancePercentage" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="isSetpointChangeable" type="xs:boolean"/>
            <xs:element minOccurs="0" name="isSetpointActive" type="xs:boolean"/>
            <xs:element minOccurs="0" name="timePeriod" type="ns_p:TimePeriodType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="setpointData" type="ns_p:SetpointDataType"/>
    <xs:complexType name="SetpointDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="setpointId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="value" type="ns_p:ScaledNumberElementsType"/>
            <xs:element minOccurs="0" name="valueMin" type="ns_p:ScaledNumberElementsType"/>
            <xs:element minOccurs="0" name="valueMax" type="ns_p:ScaledNumberElementsType"/>
            <xs:element minOccurs="0" name="valueToleranceAbsolute" type="ns_p:ScaledNumberElementsType"/>
            <xs:element minOccurs="0" name="valueTolerancePercentage" type="ns_p:ScaledNumberElementsType"/>
            <xs:element minOccurs="0" name="isSetpointChangeable" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="isSetpointActive" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="timePeriod" type="ns_p:TimePeriodElementsType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="setpointDataElements" type="ns_p:SetpointDataElementsType"/>
    <xs:complexType name="SetpointListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:setpointData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="setpointListData" type="ns_p:SetpointListDataType"/>
    <xs:complexType name="SetpointListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="setpointId" type="ns_p:SetpointIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="setpointListDataSelectors" type="ns_p:SetpointListDataSelectorsType"/>
   <xs:complexType name="SetpointConstraintsDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="setpointId" type="ns_p:SetpointIdType"/>
            <xs:element minOccurs="0" name="setpointRangeMin" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="setpointRangeMax" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="setpointStepSize" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="setpointValues" type="ns_p:SetpointValuesType"> </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="setpointConstraintsData" type="ns_p:SetpointConstraintsDataType"/>
    <xs:complexType name="SetpointConstraintsDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="setpointId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="setpointRangeMin" type="ns_p:ScaledNumberElementsType"/>
            <xs:element minOccurs="0" name="setpointRangeMax" type="ns_p:ScaledNumberElementsType"/>
            <xs:element minOccurs="0" name="setpointStepSize" type="ns_p:ScaledNumberElementsType"/>
            <xs:element minOccurs="0" name="setpointValues" type="ns_p:SetpointValuesElementsType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="setpointConstraintsDataElements" type="ns_p:SetpointConstraintsDataElementsType"/>
    <xs:complexType name="SetpointConstraintsListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:setpointConstraintsData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="setpointConstraintsListData" type="ns_p:SetpointConstraintsListDataType"/>
    <xs:complexType name="SetpointConstraintsListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="setpointId" type="ns_p:SetpointIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="setpointConstraintsListDataSelectors" type="ns_p:SetpointConstraintsListDataSelectorsType"/>
    <xs:complexType name="SetpointDescriptionDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="setpointId" type="ns_p:SetpointIdType"/>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:MeasurementIdType"/>
            <xs:element minOccurs="0" name="timeTableId" type="ns_p:TimeTableIdType"/>
            <xs:element minOccurs="0" name="setpointType" type="ns_p:SetpointTypeType"/>
            <xs:element minOccurs="0" name="unit" type="ns_p:UnitOfMeasurementType"/>
            <xs:element minOccurs="0" name="scopeType" type="ns_p:ScopeTypeType"/>
            <xs:element minOccurs="0" name="label" type="ns_p:LabelType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:DescriptionType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="setpointDescriptionData" type="ns_p:SetpointDescriptionDataType"/>
    <xs:complexType name="SetpointDescriptionDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="setpointId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="timeTableId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="setpointType" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="unit" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="scopeType" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="label" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="setpointDescriptionDataElements" type="ns_p:SetpointDescriptionDataElementsType"/>
    <xs:complexType name="SetpointDescriptionListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:setpointDescriptionData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="setpointDescriptionListData" type="ns_p:SetpointDescriptionListDataType"/>
    <xs:complexType name="SetpointDescriptionListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="setpointId" type="ns_p:SetpointIdType"/>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:MeasurementIdType"/>
            <xs:element minOccurs="0" name="timeTableId" type="ns_p:TimeTableIdType"/>
            <xs:element minOccurs="0" name="setpointType" type="ns_p:SetpointTypeType"/>
            <xs:element minOccurs="0" name="scopeType" type="ns_p:ScopeTypeType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="setpointDescriptionListDataSelectors" type="ns_p:SetpointDescriptionListDataSelectorsType"/>
</xs:schema>

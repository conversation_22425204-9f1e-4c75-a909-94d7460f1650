<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0"
    blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_CommonDataTypes.xsd"/>
    <xs:simpleType name="TimeTableIdType">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>
    <xs:simpleType name="TimeSlotIdType">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>
    <xs:simpleType name="TimeSlotCountType">
        <xs:restriction base="ns_p:TimeSlotIdType"/>
    </xs:simpleType>
    <xs:simpleType name="TimeSlotTimeModeType">
        <xs:union memberTypes="ns_p:TimeSlotTimeModeEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="TimeSlotTimeModeEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="absolute"/>
            <xs:enumeration value="recurring"/>
            <xs:enumeration value="both"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="TimeTableDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeTableId" type="ns_p:TimeTableIdType"/>
            <xs:element minOccurs="0" name="timeSlotId" type="ns_p:TimeSlotIdType"/>
            <xs:element name="recurrenceInformation" minOccurs="0" type="ns_p:RecurrenceInformationType"/>
            <xs:element minOccurs="0" name="startTime" type="ns_p:AbsoluteOrRecurringTimeType"/>
            <xs:element minOccurs="0" name="endTime" type="ns_p:AbsoluteOrRecurringTimeType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeTableData" type="ns_p:TimeTableDataType"/>
    <xs:complexType name="TimeTableDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeTableId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="timeSlotId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="recurrenceInformation" type="ns_p:RecurrenceInformationElementsType"/>
            <xs:element minOccurs="0" name="startTime" type="ns_p:AbsoluteOrRecurringTimeElementsType"/>
            <xs:element minOccurs="0" name="endTime" type="ns_p:AbsoluteOrRecurringTimeElementsType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeTableDataElements" type="ns_p:TimeTableDataElementsType"/>
    <xs:complexType name="TimeTableListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:timeTableData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeTableListData" type="ns_p:TimeTableListDataType"/>
    <xs:complexType name="TimeTableListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeTableId" type="ns_p:TimeTableIdType"/>
            <xs:element minOccurs="0" name="timeSlotId" type="ns_p:TimeSlotIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeTableListDataSelectors" type="ns_p:TimeTableListDataSelectorsType"/>
    <xs:complexType name="TimeTableConstraintsDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeTableId" type="xs:unsignedInt"/>
            <xs:element minOccurs="0" name="slotCountMin" type="ns_p:TimeSlotCountType"/>
            <xs:element minOccurs="0" name="slotCountMax" type="ns_p:TimeSlotCountType"/>
            <xs:element minOccurs="0" name="slotDurationMin" type="xs:duration"/>
            <xs:element minOccurs="0" name="slotDurationMax" type="xs:duration"/>
            <xs:element minOccurs="0" name="slotDurationStepSize" type="xs:duration"/>
            <xs:element minOccurs="0" name="slotShiftStepSize" type="xs:duration"/>
            <xs:element minOccurs="0" name="firstSlotBeginsAt" type="xs:time"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeTableConstraintsData" type="ns_p:TimeTableConstraintsDataType"/>
    <xs:complexType name="TimeTableConstraintsDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeTableId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="slotCountMin" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="slotCountMax" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="slotDurationMin" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="slotDurationMax" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="slotDurationStepSize" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="slotShiftStepSize" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="firstSlotBeginsAt" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeTableConstraintsDataElements" type="ns_p:TimeTableConstraintsDataElementsType"/>
    <xs:complexType name="TimeTableConstraintsListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:timeTableConstraintsData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeTableConstraintsListData" type="ns_p:TimeTableConstraintsListDataType"/>
    <xs:complexType name="TimeTableConstraintsListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeTableId" type="ns_p:TimeTableIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeTableConstraintsListDataSelectors" type="ns_p:TimeTableConstraintsListDataSelectorsType"/>
    <xs:complexType name="TimeTableDescriptionDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeTableId" type="xs:unsignedInt"/>
            <xs:element minOccurs="0" name="timeSlotCountChangeable" type="xs:boolean"/>
            <xs:element minOccurs="0" name="timeSlotTimesChangeable" type="xs:boolean"/>
            <xs:element minOccurs="0" name="timeSlotTimeMode" type="ns_p:TimeSlotTimeModeType"/>
            <xs:element minOccurs="0" name="label" type="ns_p:LabelType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:DescriptionType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeTableDescriptionData" type="ns_p:TimeTableDescriptionDataType"/>
    <xs:complexType name="TimeTableDescriptionDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeTableId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="timeSlotCountChangeable" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="timeSlotTimesChangeable" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="timeSlotTimeMode" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="label" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeTableDescriptionDataElements" type="ns_p:TimeTableDescriptionDataElementsType"/>
    <xs:complexType name="TimeTableDescriptionListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:timeTableDescriptionData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeTableDescriptionListData" type="ns_p:TimeTableDescriptionListDataType"/>
    <xs:complexType name="TimeTableDescriptionListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeTableId" type="ns_p:TimeTableIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeTableDescriptionListDataSelectors" type="ns_p:TimeTableDescriptionListDataSelectorsType"/>
</xs:schema>

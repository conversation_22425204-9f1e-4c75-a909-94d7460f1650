<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0"
    blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_ElectricalConnection.xsd"/>
    <xs:group name="group_electricalConnection">
        <xs:sequence>
            <xs:group ref="ns_p:electricalConnectionParameterDescription"/>
            <xs:group ref="ns_p:electricalConnectionPermittedValueSet"/>
            <xs:group ref="ns_p:electricalConnectionCharacteristic"/>
            <xs:group ref="ns_p:electricalConnectionState"/>
            <xs:group ref="ns_p:electricalConnectionDescription"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="electricalConnectionParameterDescription">
        <xs:sequence>
            <xs:element ref="ns_p:electricalConnectionParameterDescriptionListData"/>
            <xs:element ref="ns_p:electricalConnectionParameterDescriptionListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="electricalConnectionPermittedValueSet">
        <xs:sequence>
            <xs:element ref="ns_p:electricalConnectionPermittedValueSetListData"/>
            <xs:element ref="ns_p:electricalConnectionPermittedValueSetListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="electricalConnectionCharacteristic">
        <xs:sequence>
            <xs:element ref="ns_p:electricalConnectionCharacteristicListData"/>
            <xs:element ref="ns_p:electricalConnectionCharacteristicListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="electricalConnectionState">
        <xs:sequence>
            <xs:element ref="ns_p:electricalConnectionStateListData"/>
            <xs:element ref="ns_p:electricalConnectionStateListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="electricalConnectionDescription">
        <xs:sequence>
            <xs:element ref="ns_p:electricalConnectionDescriptionListData"/>
            <xs:element ref="ns_p:electricalConnectionDescriptionListDataSelectors"/>
        </xs:sequence>
    </xs:group>
</xs:schema>

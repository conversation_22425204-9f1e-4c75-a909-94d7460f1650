<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0"
    blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_CommonDataTypes.xsd"/>
    <xs:simpleType name="ActuatorLevelFctType">
        <xs:union memberTypes="ns_p:ActuatorLevelFctEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="ActuatorLevelFctEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="start"/>
            <xs:enumeration value="up"/>
            <xs:enumeration value="down"/>
            <xs:enumeration value="stop"/>
            <xs:enumeration value="percentageAbsolute"/>
            <xs:enumeration value="percentageRelative"/>
            <xs:enumeration value="absolute"/>
            <xs:enumeration value="relative"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ActuatorLevelDataType">
        <xs:sequence>
            <xs:element name="function" type="ns_p:ActuatorLevelFctType" minOccurs="0"/>
            <xs:element name="value" type="ns_p:ScaledNumberType" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="actuatorLevelData" type="ns_p:ActuatorLevelDataType"/>
    <xs:complexType name="ActuatorLevelDataElementsType">
        <xs:sequence>
            <xs:element name="function" minOccurs="0" type="ns_p:ElementTagType"/>
            <xs:element name="value" type="ns_p:ScaledNumberElementsType" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="actuatorLevelDataElements" type="ns_p:ActuatorLevelDataElementsType"/>
    <xs:complexType name="ActuatorLevelDescriptionDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="label" type="ns_p:LabelType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:DescriptionType"/>
            <xs:element minOccurs="0" name="levelDefaultUnit" type="ns_p:UnitOfMeasurementType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="actuatorLevelDescriptionData" type="ns_p:ActuatorLevelDescriptionDataType"/>
    <xs:complexType name="ActuatorLevelDescriptionDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="label" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="levelDefaultUnit" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="actuatorLevelDescriptionDataElements" type="ns_p:ActuatorLevelDescriptionDataElementsType"/>
</xs:schema>

<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema"
    targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0" blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_Measurement.xsd"/>
    <xs:group name="group_measurement">
        <xs:sequence>
            <xs:group ref="ns_p:measurement"/>
            <xs:group ref="ns_p:measurementSeries"/>
            <xs:group ref="ns_p:measurementConstraints"/>
            <xs:group ref="ns_p:measurementDescription"/>
            <xs:group ref="ns_p:measurementThresholdRelation"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="measurement">
        <xs:sequence>
            <xs:element ref="ns_p:measurementListData"/>
            <xs:element ref="ns_p:measurementListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="measurementSeries">
        <xs:sequence>
            <xs:element ref="ns_p:measurementSeriesListData"/>
            <xs:element ref="ns_p:measurementSeriesListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="measurementConstraints">
        <xs:sequence>
            <xs:element ref="ns_p:measurementConstraintsListData"/>
            <xs:element ref="ns_p:measurementConstraintsListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="measurementDescription">
        <xs:sequence>
            <xs:element ref="ns_p:measurementDescriptionListData"/>
            <xs:element ref="ns_p:measurementDescriptionListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="measurementThresholdRelation">
        <xs:sequence>
            <xs:element ref="ns_p:measurementThresholdRelationListData"/>
            <xs:element ref="ns_p:measurementThresholdRelationListDataSelectors"/>
        </xs:sequence>
    </xs:group>
</xs:schema>

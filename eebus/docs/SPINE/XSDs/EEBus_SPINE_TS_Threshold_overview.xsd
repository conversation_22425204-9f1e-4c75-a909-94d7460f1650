<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema"
    targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0" blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_Threshold.xsd"/>
    <xs:group name="group_threshold">
        <xs:sequence>
            <xs:group ref="ns_p:threshold"/>
            <xs:group ref="ns_p:thresholdConstraints"/>
            <xs:group ref="ns_p:thresholdDescription"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="threshold">
        <xs:sequence>
            <xs:element ref="ns_p:thresholdListData"/>
            <xs:element ref="ns_p:thresholdListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="thresholdConstraints">
        <xs:sequence>
            <xs:element ref="ns_p:thresholdConstraintsListData"/>
            <xs:element ref="ns_p:thresholdConstraintsListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="thresholdDescription">
        <xs:sequence>
            <xs:element ref="ns_p:thresholdDescriptionListData"/>
            <xs:element ref="ns_p:thresholdDescriptionListDataSelectors"/>
        </xs:sequence>
    </xs:group>
</xs:schema>

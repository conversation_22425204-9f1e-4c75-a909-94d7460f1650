<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0"
    blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_CommonDataTypes.xsd"/>
    <xs:simpleType name="MessagingNumberType">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>
    <xs:simpleType name="MessagingTypeType">
        <xs:union memberTypes="ns_p:MessagingTypeEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="MessagingDataTextType">
        <xs:restriction base="xs:string"/>
    </xs:simpleType>
    <xs:simpleType name="MessagingTypeEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="logging"/>
            <xs:enumeration value="information"/>
            <xs:enumeration value="warning"/>
            <xs:enumeration value="alarm"/>
            <xs:enumeration value="emergency"/>
            <xs:enumeration value="obsolete"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="MessagingDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timestamp" type="ns_p:AbsoluteOrRelativeTimeType"/>
            <xs:element minOccurs="0" name="messagingNumber" type="ns_p:MessagingNumberType"/>
            <xs:element minOccurs="0" name="type" type="ns_p:MessagingTypeType"/>
            <xs:element minOccurs="0" name="text" type="ns_p:MessagingDataTextType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="messagingData" type="ns_p:MessagingDataType"/>
    <xs:complexType name="MessagingDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timestamp" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="messagingNumber" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="type" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="text" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="messagingDataElements" type="ns_p:MessagingDataElementsType"/>
    <xs:complexType name="MessagingListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:messagingData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="messagingListData" type="ns_p:MessagingListDataType"/>
    <xs:complexType name="MessagingListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timestampInterval" type="ns_p:TimestampIntervalType"/>
            <xs:element minOccurs="0" name="messagingNumber" type="ns_p:MessagingNumberType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="messagingListDataSelectors" type="ns_p:MessagingListDataSelectorsType"/>
</xs:schema>

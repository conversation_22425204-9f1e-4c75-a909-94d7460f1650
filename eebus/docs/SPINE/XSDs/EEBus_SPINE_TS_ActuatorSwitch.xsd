<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0"
    blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_CommonDataTypes.xsd"/>
    <xs:simpleType name="ActuatorSwitchFctType">
        <xs:union memberTypes="ns_p:ActuatorSwitchFctEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="ActuatorSwitchFctEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="on"/>
            <xs:enumeration value="off"/>
            <xs:enumeration value="toggle"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ActuatorSwitchDataType">
        <xs:sequence>
            <xs:element name="function" type="ns_p:ActuatorSwitchFctType" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="actuatorSwitchData" type="ns_p:ActuatorSwitchDataType"/>
    <xs:complexType name="ActuatorSwitchDataElementsType">
        <xs:sequence>
            <xs:element name="function" minOccurs="0" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="actuatorSwitchDataElements" type="ns_p:ActuatorSwitchDataElementsType"/>
    <xs:complexType name="ActuatorSwitchDescriptionDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="label" type="ns_p:LabelType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:DescriptionType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="actuatorSwitchDescriptionData" type="ns_p:ActuatorSwitchDescriptionDataType"/>
    <xs:complexType name="ActuatorSwitchDescriptionDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="label" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="actuatorSwitchDescriptionDataElements" type="ns_p:ActuatorSwitchDescriptionDataElementsType"/>
</xs:schema>

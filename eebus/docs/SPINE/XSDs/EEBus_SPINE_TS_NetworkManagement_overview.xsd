<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema"
    targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0" blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_NetworkManagement.xsd"/>
    <xs:group name="group_networkManagementCall">
        <xs:sequence>
            <xs:group ref="ns_p:networkManagementAddNodeCall"/>
            <xs:group ref="ns_p:networkManagementRemoveNodeCall"/>
            <xs:group ref="ns_p:networkManagementModifyNodeCall"/>
            <xs:group ref="ns_p:networkManagementScanNetworkCall"/>
            <xs:group ref="ns_p:networkManagementDiscoverCall"/>
            <xs:group ref="ns_p:networkManagementAbortCall"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="group_networkManagement">
        <xs:sequence>
            <xs:group ref="ns_p:networkManagementProcessState"/>
            <xs:group ref="ns_p:networkManagementJoiningMode"/>
            <xs:group ref="ns_p:networkManagementReportCandidate"/>
            <xs:group ref="ns_p:networkManagementDeviceDescription"/>
            <xs:group ref="ns_p:networkManagementEntityDescription"/>
            <xs:group ref="ns_p:networkManagementFeatureDescription"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="networkManagementAddNodeCall">
        <xs:sequence>
            <xs:element ref="ns_p:networkManagementAddNodeCall"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="networkManagementRemoveNodeCall">
        <xs:sequence>
            <xs:element ref="ns_p:networkManagementRemoveNodeCall"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="networkManagementModifyNodeCall">
        <xs:sequence>
            <xs:element ref="ns_p:networkManagementModifyNodeCall"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="networkManagementScanNetworkCall">
        <xs:sequence>
            <xs:element ref="ns_p:networkManagementScanNetworkCall"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="networkManagementAbortCall">
        <xs:sequence>
            <xs:element ref="ns_p:networkManagementAbortCall"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="networkManagementDiscoverCall">
        <xs:sequence>
            <xs:element ref="ns_p:networkManagementDiscoverCall"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="networkManagementProcessState">
        <xs:sequence>
            <xs:element ref="ns_p:networkManagementProcessStateData"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="networkManagementJoiningMode">
        <xs:sequence>
            <xs:element ref="ns_p:networkManagementJoiningModeData"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="networkManagementReportCandidate">
        <xs:sequence>
            <xs:element ref="ns_p:networkManagementReportCandidateData"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="networkManagementDeviceDescription">
        <xs:sequence>
            <xs:element ref="ns_p:networkManagementDeviceDescriptionListData"/>
            <xs:element ref="ns_p:networkManagementDeviceDescriptionListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="networkManagementEntityDescription">
        <xs:sequence>
            <xs:element ref="ns_p:networkManagementEntityDescriptionListData"/>
            <xs:element ref="ns_p:networkManagementEntityDescriptionListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="networkManagementFeatureDescription">
        <xs:sequence>
            <xs:element ref="ns_p:networkManagementFeatureDescriptionListData"/>
            <xs:element ref="ns_p:networkManagementFeatureDescriptionListDataSelectors"/>
        </xs:sequence>
    </xs:group>
</xs:schema>

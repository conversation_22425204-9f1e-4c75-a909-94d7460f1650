<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0"
    blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_SubscriptionManagement.xsd"/>
    <xs:group name="group_subscriptionManagement">
        <xs:sequence>
            <xs:group ref="ns_p:subscriptionManagementEntry"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="group_subscriptionManagementCall">
        <xs:sequence>
            <xs:group ref="ns_p:subscriptionManagementRequestCall"/>
            <xs:group ref="ns_p:subscriptionManagementDeleteCall"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="subscriptionManagementEntry">
        <xs:sequence>
            <xs:element ref="ns_p:subscriptionManagementEntryListData"/>
            <xs:element ref="ns_p:subscriptionManagementEntryListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="subscriptionManagementRequestCall">
        <xs:sequence>
            <xs:element ref="ns_p:subscriptionManagementRequestCall"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="subscriptionManagementDeleteCall">
        <xs:sequence>
            <xs:element ref="ns_p:subscriptionManagementDeleteCall"/>
        </xs:sequence>
    </xs:group>
</xs:schema>

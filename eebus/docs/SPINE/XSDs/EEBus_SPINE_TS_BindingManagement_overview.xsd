<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema"
    targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0" blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_BindingManagement.xsd"/>
    <xs:group name="group_bindingManagement">
        <xs:sequence>
            <xs:group ref="ns_p:bindingManagementEntry"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="group_bindingManagementCall">
        <xs:sequence>
            <xs:group ref="ns_p:bindingManagementRequestCall"/>
            <xs:group ref="ns_p:bindingManagementDeleteCall"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="bindingManagementEntry">
        <xs:sequence>
            <xs:element ref="ns_p:bindingManagementEntryListData"/>
            <xs:element ref="ns_p:bindingManagementEntryListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="bindingManagementRequestCall">
        <xs:sequence>
            <xs:element ref="ns_p:bindingManagementRequestCall"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="bindingManagementDeleteCall">
        <xs:sequence>
            <xs:element ref="ns_p:bindingManagementDeleteCall"/>
        </xs:sequence>
    </xs:group>
</xs:schema>

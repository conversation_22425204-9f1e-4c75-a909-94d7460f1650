<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0"
    blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_ActuatorLevel.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_ActuatorSwitch.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_Alarm.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_Bill.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_BindingManagement.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_DataTunneling.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_DeviceClassification.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_DeviceConfiguration.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_DeviceDiagnosis.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_DirectControl.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_ElectricalConnection.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_HVAC.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_Identification.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_IncentiveTable.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_LoadControl.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_Measurement.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_Messaging.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_NetworkManagement.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_NodeManagement.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_OperatingConstraints.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_PowerSequences.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_Result.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_Sensing.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_Setpoint.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_SmartEnergyManagementPs.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_StateInformation.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_SubscriptionManagement.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_SupplyCondition.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_Surrogate.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_TariffInformation.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_TaskManagement.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_Threshold.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_TimeInformation.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_TimeSeries.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_TimeTable.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_UseCaseInformation.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_Version.xsd"/>
    <xs:group name="DataChoiceGroup">
        <xs:choice>
            <xs:element ref="ns_p:actuatorLevelData"/>
            <xs:element ref="ns_p:actuatorLevelDescriptionData"/>
            <xs:element ref="ns_p:actuatorSwitchData"/>
            <xs:element ref="ns_p:actuatorSwitchDescriptionData"/>
            <xs:element ref="ns_p:alarmListData"/>
            <xs:element ref="ns_p:billConstraintsListData"/>
            <xs:element ref="ns_p:billDescriptionListData"/>
            <xs:element ref="ns_p:billListData"/>
            <xs:element ref="ns_p:bindingManagementDeleteCall"/>
            <xs:element ref="ns_p:bindingManagementEntryListData"/>
            <xs:element ref="ns_p:bindingManagementRequestCall"/>
            <xs:element ref="ns_p:commodityListData"/>
            <xs:element ref="ns_p:dataTunnelingCall"/>
            <xs:element ref="ns_p:deviceClassificationManufacturerData"/>
            <xs:element ref="ns_p:deviceClassificationUserData"/>
            <xs:element ref="ns_p:deviceConfigurationKeyValueConstraintsListData"/>
            <xs:element ref="ns_p:deviceConfigurationKeyValueDescriptionListData"/>
            <xs:element ref="ns_p:deviceConfigurationKeyValueListData"/>
            <xs:element ref="ns_p:deviceDiagnosisHeartbeatData"/>
            <xs:element ref="ns_p:deviceDiagnosisServiceData"/>
            <xs:element ref="ns_p:deviceDiagnosisStateData"/>
            <xs:element ref="ns_p:directControlActivityListData"/>
            <xs:element ref="ns_p:directControlDescriptionData"/>
            <xs:element ref="ns_p:electricalConnectionCharacteristicListData"/>
            <xs:element ref="ns_p:electricalConnectionDescriptionListData"/>
            <xs:element ref="ns_p:electricalConnectionParameterDescriptionListData"/>
            <xs:element ref="ns_p:electricalConnectionPermittedValueSetListData"/>
            <xs:element ref="ns_p:electricalConnectionStateListData"/>
            <xs:element ref="ns_p:hvacOperationModeDescriptionListData"/>
            <xs:element ref="ns_p:hvacOverrunDescriptionListData"/>
            <xs:element ref="ns_p:hvacOverrunListData"/>
            <xs:element ref="ns_p:hvacSystemFunctionDescriptionListData"/>
            <xs:element ref="ns_p:hvacSystemFunctionListData"/>
            <xs:element ref="ns_p:hvacSystemFunctionOperationModeRelationListData"/>
            <xs:element ref="ns_p:hvacSystemFunctionPowerSequenceRelationListData"/>
            <xs:element ref="ns_p:hvacSystemFunctionSetpointRelationListData"/>
            <xs:element ref="ns_p:identificationListData"/>
            <xs:element ref="ns_p:incentiveDescriptionListData"/>
            <xs:element ref="ns_p:incentiveListData"/>
            <xs:element ref="ns_p:incentiveTableConstraintsData"/>
            <xs:element ref="ns_p:incentiveTableData"/>
            <xs:element ref="ns_p:incentiveTableDescriptionData"/>
            <xs:element ref="ns_p:loadControlEventListData"/>
            <xs:element ref="ns_p:loadControlLimitConstraintsListData"/>
            <xs:element ref="ns_p:loadControlLimitDescriptionListData"/>
            <xs:element ref="ns_p:loadControlLimitListData"/>
            <xs:element ref="ns_p:loadControlNodeData"/>
            <xs:element ref="ns_p:loadControlStateListData"/>
            <xs:element ref="ns_p:measurementConstraintsListData"/>
            <xs:element ref="ns_p:measurementDescriptionListData"/>
            <xs:element ref="ns_p:measurementListData"/>
            <xs:element ref="ns_p:measurementSeriesListData"/>
            <xs:element ref="ns_p:measurementThresholdRelationListData"/>
            <xs:element ref="ns_p:messagingListData"/>
            <xs:element ref="ns_p:networkManagementAbortCall"/>
            <xs:element ref="ns_p:networkManagementAddNodeCall"/>
            <xs:element ref="ns_p:networkManagementDeviceDescriptionListData"/>
            <xs:element ref="ns_p:networkManagementDiscoverCall"/>
            <xs:element ref="ns_p:networkManagementEntityDescriptionListData"/>
            <xs:element ref="ns_p:networkManagementFeatureDescriptionListData"/>
            <xs:element ref="ns_p:networkManagementJoiningModeData"/>
            <xs:element ref="ns_p:networkManagementModifyNodeCall"/>
            <xs:element ref="ns_p:networkManagementProcessStateData"/>
            <xs:element ref="ns_p:networkManagementRemoveNodeCall"/>
            <xs:element ref="ns_p:networkManagementReportCandidateData"/>
            <xs:element ref="ns_p:networkManagementScanNetworkCall"/>
            <xs:element ref="ns_p:nodeManagementBindingData"/>
            <xs:element ref="ns_p:nodeManagementBindingDeleteCall"/>
            <xs:element ref="ns_p:nodeManagementBindingRequestCall"/>
            <xs:element ref="ns_p:nodeManagementDestinationListData"/>
            <xs:element ref="ns_p:nodeManagementDetailedDiscoveryData"/>
            <xs:element ref="ns_p:nodeManagementSubscriptionData"/>
            <xs:element ref="ns_p:nodeManagementSubscriptionDeleteCall"/>
            <xs:element ref="ns_p:nodeManagementSubscriptionRequestCall"/>
            <xs:element ref="ns_p:nodeManagementUseCaseData"/>
            <xs:element ref="ns_p:operatingConstraintsDurationListData"/>
            <xs:element ref="ns_p:operatingConstraintsInterruptListData"/>
            <xs:element ref="ns_p:operatingConstraintsPowerDescriptionListData"/>
            <xs:element ref="ns_p:operatingConstraintsPowerLevelListData"/>
            <xs:element ref="ns_p:operatingConstraintsPowerRangeListData"/>
            <xs:element ref="ns_p:operatingConstraintsResumeImplicationListData"/>
            <xs:element ref="ns_p:powerSequenceAlternativesRelationListData"/>
            <xs:element ref="ns_p:powerSequenceDescriptionListData"/>
            <xs:element ref="ns_p:powerSequenceNodeScheduleInformationData"/>
            <xs:element ref="ns_p:powerSequencePriceCalculationRequestCall"/>
            <xs:element ref="ns_p:powerSequencePriceListData"/>
            <xs:element ref="ns_p:powerSequenceScheduleConfigurationRequestCall"/>
            <xs:element ref="ns_p:powerSequenceScheduleConstraintsListData"/>
            <xs:element ref="ns_p:powerSequenceScheduleListData"/>
            <xs:element ref="ns_p:powerSequenceSchedulePreferenceListData"/>
            <xs:element ref="ns_p:powerSequenceStateListData"/>
            <xs:element ref="ns_p:powerTimeSlotScheduleConstraintsListData"/>
            <xs:element ref="ns_p:powerTimeSlotScheduleListData"/>
            <xs:element ref="ns_p:powerTimeSlotValueListData"/>
            <xs:element ref="ns_p:resultData"/>
            <xs:element ref="ns_p:sensingDescriptionData"/>
            <xs:element ref="ns_p:sensingListData"/>
            <xs:element ref="ns_p:sessionIdentificationListData"/>
            <xs:element ref="ns_p:sessionMeasurementRelationListData"/>
            <xs:element ref="ns_p:setpointConstraintsListData"/>
            <xs:element ref="ns_p:setpointDescriptionListData"/>
            <xs:element ref="ns_p:setpointListData"/>
            <xs:element ref="ns_p:smartEnergyManagementPsConfigurationRequestCall"/>
            <xs:element ref="ns_p:smartEnergyManagementPsData"/>
            <xs:element ref="ns_p:smartEnergyManagementPsPriceCalculationRequestCall"/>
            <xs:element ref="ns_p:smartEnergyManagementPsPriceData"/>
            <xs:element ref="ns_p:specificationVersionListData"/>
            <xs:element ref="ns_p:stateInformationListData"/>
            <xs:element ref="ns_p:subscriptionManagementDeleteCall"/>
            <xs:element ref="ns_p:subscriptionManagementEntryListData"/>
            <xs:element ref="ns_p:subscriptionManagementRequestCall"/>
            <xs:element ref="ns_p:supplyConditionDescriptionListData"/>
            <xs:element ref="ns_p:supplyConditionListData"/>
            <xs:element ref="ns_p:supplyConditionThresholdRelationListData"/>
            <xs:element ref="ns_p:surrogateDescriptionListData"/>
            <xs:element ref="ns_p:tariffBoundaryRelationListData"/>
            <xs:element ref="ns_p:tariffDescriptionListData"/>
            <xs:element ref="ns_p:tariffListData"/>
            <xs:element ref="ns_p:tariffOverallConstraintsData"/>
            <xs:element ref="ns_p:tariffTierRelationListData"/>
            <xs:element ref="ns_p:taskManagementJobDescriptionListData"/>
            <xs:element ref="ns_p:taskManagementJobListData"/>
            <xs:element ref="ns_p:taskManagementJobRelationListData"/>
            <xs:element ref="ns_p:taskManagementOverviewData"/>
            <xs:element ref="ns_p:thresholdConstraintsListData"/>
            <xs:element ref="ns_p:thresholdDescriptionListData"/>
            <xs:element ref="ns_p:thresholdListData"/>
            <xs:element ref="ns_p:tierBoundaryDescriptionListData"/>
            <xs:element ref="ns_p:tierBoundaryListData"/>
            <xs:element ref="ns_p:tierDescriptionListData"/>
            <xs:element ref="ns_p:tierIncentiveRelationListData"/>
            <xs:element ref="ns_p:tierListData"/>
            <xs:element ref="ns_p:timeDistributorData"/>
            <xs:element ref="ns_p:timeDistributorEnquiryCall"/>
            <xs:element ref="ns_p:timeInformationData"/>
            <xs:element ref="ns_p:timePrecisionData"/>
            <xs:element ref="ns_p:timeSeriesConstraintsListData"/>
            <xs:element ref="ns_p:timeSeriesDescriptionListData"/>
            <xs:element ref="ns_p:timeSeriesListData"/>
            <xs:element ref="ns_p:timeTableConstraintsListData"/>
            <xs:element ref="ns_p:timeTableDescriptionListData"/>
            <xs:element ref="ns_p:timeTableListData"/>
            <xs:element ref="ns_p:useCaseInformationListData"/>
        </xs:choice>
    </xs:group>
    <xs:group name="DataElementsChoiceGroup">
        <xs:choice>
            <xs:element ref="ns_p:actuatorLevelDataElements"/>
            <xs:element ref="ns_p:actuatorLevelDescriptionDataElements"/>
            <xs:element ref="ns_p:actuatorSwitchDataElements"/>
            <xs:element ref="ns_p:actuatorSwitchDescriptionDataElements"/>
            <xs:element ref="ns_p:alarmDataElements"/>
            <xs:element ref="ns_p:billConstraintsDataElements"/>
            <xs:element ref="ns_p:billDataElements"/>
            <xs:element ref="ns_p:billDescriptionDataElements"/>
            <xs:element ref="ns_p:bindingManagementDeleteCallElements"/>
            <xs:element ref="ns_p:bindingManagementEntryDataElements"/>
            <xs:element ref="ns_p:bindingManagementRequestCallElements"/>
            <xs:element ref="ns_p:commodityDataElements"/>
            <xs:element ref="ns_p:dataTunnelingCallElements"/>
            <xs:element ref="ns_p:deviceClassificationManufacturerDataElements"/>
            <xs:element ref="ns_p:deviceClassificationUserDataElements"/>
            <xs:element ref="ns_p:deviceConfigurationKeyValueConstraintsDataElements"/>
            <xs:element ref="ns_p:deviceConfigurationKeyValueDataElements"/>
            <xs:element ref="ns_p:deviceConfigurationKeyValueDescriptionDataElements"/>
            <xs:element ref="ns_p:deviceDiagnosisHeartbeatDataElements"/>
            <xs:element ref="ns_p:deviceDiagnosisServiceDataElements"/>
            <xs:element ref="ns_p:deviceDiagnosisStateDataElements"/>
            <xs:element ref="ns_p:directControlActivityDataElements"/>
            <xs:element ref="ns_p:directControlDescriptionDataElements"/>
            <xs:element ref="ns_p:electricalConnectionCharacteristicDataElements"/>
            <xs:element ref="ns_p:electricalConnectionDescriptionDataElements"/>
            <xs:element ref="ns_p:electricalConnectionParameterDescriptionDataElements"/>
            <xs:element ref="ns_p:electricalConnectionPermittedValueSetDataElements"/>
            <xs:element ref="ns_p:electricalConnectionStateDataElements"/>
            <xs:element ref="ns_p:hvacOperationModeDescriptionDataElements"/>
            <xs:element ref="ns_p:hvacOverrunDataElements"/>
            <xs:element ref="ns_p:hvacOverrunDescriptionDataElements"/>
            <xs:element ref="ns_p:hvacSystemFunctionDataElements"/>
            <xs:element ref="ns_p:hvacSystemFunctionDescriptionDataElements"/>
            <xs:element ref="ns_p:hvacSystemFunctionOperationModeRelationDataElements"/>
            <xs:element ref="ns_p:hvacSystemFunctionPowerSequenceRelationDataElements"/>
            <xs:element ref="ns_p:hvacSystemFunctionSetpointRelationDataElements"/>
            <xs:element ref="ns_p:identificationDataElements"/>
            <xs:element ref="ns_p:incentiveDataElements"/>
            <xs:element ref="ns_p:incentiveDescriptionDataElements"/>
            <xs:element ref="ns_p:incentiveTableConstraintsDataElements"/>
            <xs:element ref="ns_p:incentiveTableDataElements"/>
            <xs:element ref="ns_p:incentiveTableDescriptionDataElements"/>
            <xs:element ref="ns_p:loadControlEventDataElements"/>
            <xs:element ref="ns_p:loadControlLimitConstraintsDataElements"/>
            <xs:element ref="ns_p:loadControlLimitDataElements"/>
            <xs:element ref="ns_p:loadControlLimitDescriptionDataElements"/>
            <xs:element ref="ns_p:loadControlNodeDataElements"/>
            <xs:element ref="ns_p:loadControlStateDataElements"/>
            <xs:element ref="ns_p:measurementConstraintsDataElements"/>
            <xs:element ref="ns_p:measurementDataElements"/>
            <xs:element ref="ns_p:measurementDescriptionDataElements"/>
            <xs:element ref="ns_p:measurementSeriesDataElements"/>
            <xs:element ref="ns_p:measurementThresholdRelationDataElements"/>
            <xs:element ref="ns_p:messagingDataElements"/>
            <xs:element ref="ns_p:networkManagementAbortCallElements"/>
            <xs:element ref="ns_p:networkManagementAddNodeCallElements"/>
            <xs:element ref="ns_p:networkManagementDeviceDescriptionDataElements"/>
            <xs:element ref="ns_p:networkManagementDiscoverCallElements"/>
            <xs:element ref="ns_p:networkManagementEntityDescriptionDataElements"/>
            <xs:element ref="ns_p:networkManagementFeatureDescriptionDataElements"/>
            <xs:element ref="ns_p:networkManagementJoiningModeDataElements"/>
            <xs:element ref="ns_p:networkManagementModifyNodeCallElements"/>
            <xs:element ref="ns_p:networkManagementProcessStateDataElements"/>
            <xs:element ref="ns_p:networkManagementRemoveNodeCallElements"/>
            <xs:element ref="ns_p:networkManagementReportCandidateDataElements"/>
            <xs:element ref="ns_p:networkManagementScanNetworkCallElements"/>
            <xs:element ref="ns_p:nodeManagementBindingDataElements"/>
            <xs:element ref="ns_p:nodeManagementBindingDeleteCallElements"/>
            <xs:element ref="ns_p:nodeManagementBindingRequestCallElements"/>
            <xs:element ref="ns_p:nodeManagementDestinationDataElements"/>
            <xs:element ref="ns_p:nodeManagementDetailedDiscoveryDataElements"/>
            <xs:element ref="ns_p:nodeManagementSubscriptionDataElements"/>
            <xs:element ref="ns_p:nodeManagementSubscriptionDeleteCallElements"/>
            <xs:element ref="ns_p:nodeManagementSubscriptionRequestCallElements"/>
            <xs:element ref="ns_p:nodeManagementUseCaseDataElements"/>
            <xs:element ref="ns_p:operatingConstraintsDurationDataElements"/>
            <xs:element ref="ns_p:operatingConstraintsInterruptDataElements"/>
            <xs:element ref="ns_p:operatingConstraintsPowerDescriptionDataElements"/>
            <xs:element ref="ns_p:operatingConstraintsPowerLevelDataElements"/>
            <xs:element ref="ns_p:operatingConstraintsPowerRangeDataElements"/>
            <xs:element ref="ns_p:operatingConstraintsResumeImplicationDataElements"/>
            <xs:element ref="ns_p:powerSequenceAlternativesRelationDataElements"/>
            <xs:element ref="ns_p:powerSequenceDescriptionDataElements"/>
            <xs:element ref="ns_p:powerSequenceNodeScheduleInformationDataElements"/>
            <xs:element ref="ns_p:powerSequencePriceCalculationRequestCallElements"/>
            <xs:element ref="ns_p:powerSequencePriceDataElements"/>
            <xs:element ref="ns_p:powerSequenceScheduleConfigurationRequestCallElements"/>
            <xs:element ref="ns_p:powerSequenceScheduleConstraintsDataElements"/>
            <xs:element ref="ns_p:powerSequenceScheduleDataElements"/>
            <xs:element ref="ns_p:powerSequenceSchedulePreferenceDataElements"/>
            <xs:element ref="ns_p:powerSequenceStateDataElements"/>
            <xs:element ref="ns_p:powerTimeSlotScheduleConstraintsDataElements"/>
            <xs:element ref="ns_p:powerTimeSlotScheduleDataElements"/>
            <xs:element ref="ns_p:powerTimeSlotValueDataElements"/>
            <xs:element ref="ns_p:sensingDataElements"/>
            <xs:element ref="ns_p:sensingDescriptionDataElements"/>
            <xs:element ref="ns_p:sessionIdentificationDataElements"/>
            <xs:element ref="ns_p:sessionMeasurementRelationDataElements"/>
            <xs:element ref="ns_p:setpointConstraintsDataElements"/>
            <xs:element ref="ns_p:setpointDataElements"/>
            <xs:element ref="ns_p:setpointDescriptionDataElements"/>
            <xs:element ref="ns_p:smartEnergyManagementPsConfigurationRequestCallElements"/>
            <xs:element ref="ns_p:smartEnergyManagementPsDataElements"/>
            <xs:element ref="ns_p:smartEnergyManagementPsPriceCalculationRequestCallElements"/>
            <xs:element ref="ns_p:smartEnergyManagementPsPriceDataElements"/>
            <xs:element ref="ns_p:specificationVersionDataElements"/>
            <xs:element ref="ns_p:stateInformationDataElements"/>
            <xs:element ref="ns_p:subscriptionManagementDeleteCallElements"/>
            <xs:element ref="ns_p:subscriptionManagementEntryDataElements"/>
            <xs:element ref="ns_p:subscriptionManagementRequestCallElements"/>
            <xs:element ref="ns_p:supplyConditionDataElements"/>
            <xs:element ref="ns_p:supplyConditionDescriptionDataElements"/>
            <xs:element ref="ns_p:supplyConditionThresholdRelationDataElements"/>
            <xs:element ref="ns_p:surrogateDescriptionDataElements"/>
            <xs:element ref="ns_p:tariffBoundaryRelationDataElements"/>
            <xs:element ref="ns_p:tariffDataElements"/>
            <xs:element ref="ns_p:tariffDescriptionDataElements"/>
            <xs:element ref="ns_p:tariffOverallConstraintsDataElements"/>
            <xs:element ref="ns_p:tariffTierRelationDataElements"/>
            <xs:element ref="ns_p:taskManagementJobDataElements"/>
            <xs:element ref="ns_p:taskManagementJobDescriptionDataElements"/>
            <xs:element ref="ns_p:taskManagementJobRelationDataElements"/>
            <xs:element ref="ns_p:taskManagementOverviewDataElements"/>
            <xs:element ref="ns_p:thresholdConstraintsDataElements"/>
            <xs:element ref="ns_p:thresholdDataElements"/>
            <xs:element ref="ns_p:thresholdDescriptionDataElements"/>
            <xs:element ref="ns_p:tierBoundaryDataElements"/>
            <xs:element ref="ns_p:tierBoundaryDescriptionDataElements"/>
            <xs:element ref="ns_p:tierDataElements"/>
            <xs:element ref="ns_p:tierDescriptionDataElements"/>
            <xs:element ref="ns_p:tierIncentiveRelationDataElements"/>
            <xs:element ref="ns_p:timeDistributorDataElements"/>
            <xs:element ref="ns_p:timeDistributorEnquiryCallElements"/>
            <xs:element ref="ns_p:timeInformationDataElements"/>
            <xs:element ref="ns_p:timePrecisionDataElements"/>
            <xs:element ref="ns_p:timeSeriesConstraintsDataElements"/>
            <xs:element ref="ns_p:timeSeriesDataElements"/>
            <xs:element ref="ns_p:timeSeriesDescriptionDataElements"/>
            <xs:element ref="ns_p:timeTableConstraintsDataElements"/>
            <xs:element ref="ns_p:timeTableDataElements"/>
            <xs:element ref="ns_p:timeTableDescriptionDataElements"/>
            <xs:element ref="ns_p:useCaseInformationDataElements"/>
        </xs:choice>
    </xs:group>
    <xs:group name="DataSelectorsChoiceGroup">
        <xs:choice>
            <xs:element ref="ns_p:alarmListDataSelectors"/>
            <xs:element ref="ns_p:billConstraintsListDataSelectors"/>
            <xs:element ref="ns_p:billDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:billListDataSelectors"/>
            <xs:element ref="ns_p:bindingManagementEntryListDataSelectors"/>
            <xs:element ref="ns_p:commodityListDataSelectors"/>
            <xs:element ref="ns_p:deviceConfigurationKeyValueConstraintsListDataSelectors"/>
            <xs:element ref="ns_p:deviceConfigurationKeyValueDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:deviceConfigurationKeyValueListDataSelectors"/>
            <xs:element ref="ns_p:directControlActivityListDataSelectors"/>
            <xs:element ref="ns_p:electricalConnectionCharacteristicListDataSelectors"/>
            <xs:element ref="ns_p:electricalConnectionDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:electricalConnectionParameterDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:electricalConnectionPermittedValueSetListDataSelectors"/>
            <xs:element ref="ns_p:electricalConnectionStateListDataSelectors"/>
            <xs:element ref="ns_p:hvacOperationModeDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:hvacOverrunDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:hvacOverrunListDataSelectors"/>
            <xs:element ref="ns_p:hvacSystemFunctionDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:hvacSystemFunctionListDataSelectors"/>
            <xs:element ref="ns_p:hvacSystemFunctionOperationModeRelationListDataSelectors"/>
            <xs:element ref="ns_p:hvacSystemFunctionPowerSequenceRelationListDataSelectors"/>
            <xs:element ref="ns_p:hvacSystemFunctionSetpointRelationListDataSelectors"/>
            <xs:element ref="ns_p:identificationListDataSelectors"/>
            <xs:element ref="ns_p:incentiveDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:incentiveListDataSelectors"/>
            <xs:element ref="ns_p:incentiveTableConstraintsDataSelectors"/>
            <xs:element ref="ns_p:incentiveTableDataSelectors"/>
            <xs:element ref="ns_p:incentiveTableDescriptionDataSelectors"/>
            <xs:element ref="ns_p:loadControlEventListDataSelectors"/>
            <xs:element ref="ns_p:loadControlLimitConstraintsListDataSelectors"/>
            <xs:element ref="ns_p:loadControlLimitDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:loadControlLimitListDataSelectors"/>
            <xs:element ref="ns_p:loadControlStateListDataSelectors"/>
            <xs:element ref="ns_p:measurementConstraintsListDataSelectors"/>
            <xs:element ref="ns_p:measurementDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:measurementListDataSelectors"/>
            <xs:element ref="ns_p:measurementSeriesListDataSelectors"/>
            <xs:element ref="ns_p:measurementThresholdRelationListDataSelectors"/>
            <xs:element ref="ns_p:messagingListDataSelectors"/>
            <xs:element ref="ns_p:networkManagementDeviceDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:networkManagementEntityDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:networkManagementFeatureDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:nodeManagementBindingDataSelectors"/>
            <xs:element ref="ns_p:nodeManagementDestinationListDataSelectors"/>
            <xs:element ref="ns_p:nodeManagementDetailedDiscoveryDataSelectors"/>
            <xs:element ref="ns_p:nodeManagementSubscriptionDataSelectors"/>
            <xs:element ref="ns_p:nodeManagementUseCaseDataSelectors"/>
            <xs:element ref="ns_p:operatingConstraintsDurationListDataSelectors"/>
            <xs:element ref="ns_p:operatingConstraintsInterruptListDataSelectors"/>
            <xs:element ref="ns_p:operatingConstraintsPowerDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:operatingConstraintsPowerLevelListDataSelectors"/>
            <xs:element ref="ns_p:operatingConstraintsPowerRangeListDataSelectors"/>
            <xs:element ref="ns_p:operatingConstraintsResumeImplicationListDataSelectors"/>
            <xs:element ref="ns_p:powerSequenceAlternativesRelationListDataSelectors"/>
            <xs:element ref="ns_p:powerSequenceDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:powerSequencePriceListDataSelectors"/>
            <xs:element ref="ns_p:powerSequenceScheduleConstraintsListDataSelectors"/>
            <xs:element ref="ns_p:powerSequenceScheduleListDataSelectors"/>
            <xs:element ref="ns_p:powerSequenceSchedulePreferenceListDataSelectors"/>
            <xs:element ref="ns_p:powerSequenceStateListDataSelectors"/>
            <xs:element ref="ns_p:powerTimeSlotScheduleConstraintsListDataSelectors"/>
            <xs:element ref="ns_p:powerTimeSlotScheduleListDataSelectors"/>
            <xs:element ref="ns_p:powerTimeSlotValueListDataSelectors"/>
            <xs:element ref="ns_p:sensingListDataSelectors"/>
            <xs:element ref="ns_p:sessionIdentificationListDataSelectors"/>
            <xs:element ref="ns_p:sessionMeasurementRelationListDataSelectors"/>
            <xs:element ref="ns_p:setpointConstraintsListDataSelectors"/>
            <xs:element ref="ns_p:setpointDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:setpointListDataSelectors"/>
            <xs:element ref="ns_p:smartEnergyManagementPsDataSelectors"/>
            <xs:element ref="ns_p:smartEnergyManagementPsPriceDataSelectors"/>
            <xs:element ref="ns_p:specificationVersionListDataSelectors"/>
            <xs:element ref="ns_p:stateInformationListDataSelectors"/>
            <xs:element ref="ns_p:subscriptionManagementEntryListDataSelectors"/>
            <xs:element ref="ns_p:supplyConditionDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:supplyConditionListDataSelectors"/>
            <xs:element ref="ns_p:supplyConditionThresholdRelationListDataSelectors"/>
            <xs:element ref="ns_p:surrogateDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:tariffBoundaryRelationListDataSelectors"/>
            <xs:element ref="ns_p:tariffDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:tariffListDataSelectors"/>
            <xs:element ref="ns_p:tariffTierRelationListDataSelectors"/>
            <xs:element ref="ns_p:taskManagementJobDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:taskManagementJobListDataSelectors"/>
            <xs:element ref="ns_p:taskManagementJobRelationListDataSelectors"/>
            <xs:element ref="ns_p:thresholdConstraintsListDataSelectors"/>
            <xs:element ref="ns_p:thresholdDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:thresholdListDataSelectors"/>
            <xs:element ref="ns_p:tierBoundaryDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:tierBoundaryListDataSelectors"/>
            <xs:element ref="ns_p:tierDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:tierIncentiveRelationListDataSelectors"/>
            <xs:element ref="ns_p:tierListDataSelectors"/>
            <xs:element ref="ns_p:timeSeriesConstraintsListDataSelectors"/>
            <xs:element ref="ns_p:timeSeriesDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:timeSeriesListDataSelectors"/>
            <xs:element ref="ns_p:timeTableConstraintsListDataSelectors"/>
            <xs:element ref="ns_p:timeTableDescriptionListDataSelectors"/>
            <xs:element ref="ns_p:timeTableListDataSelectors"/>
            <xs:element ref="ns_p:useCaseInformationListDataSelectors"/>
        </xs:choice>
    </xs:group>
</xs:schema>

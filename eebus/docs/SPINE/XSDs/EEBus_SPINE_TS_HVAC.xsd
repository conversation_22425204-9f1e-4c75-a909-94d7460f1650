<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0"
    blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_CommonDataTypes.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_PowerSequences.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_Setpoint.xsd"/>
    <xs:simpleType name="HvacSystemFunctionIdType">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>
    <xs:simpleType name="HvacSystemFunctionTypeType">
        <xs:union memberTypes="ns_p:HvacSystemFunctionTypeEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="HvacSystemFunctionTypeEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="heating"/>
            <xs:enumeration value="cooling"/>
            <xs:enumeration value="ventilation"/>
            <xs:enumeration value="dhw"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="HvacOperationModeIdType">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>
    <xs:simpleType name="HvacOperationModeTypeType">
        <xs:union memberTypes="ns_p:HvacOperationModeTypeEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="HvacOperationModeTypeEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="auto"/>
            <xs:enumeration value="on"/>
            <xs:enumeration value="off"/>
            <xs:enumeration value="eco"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="HvacOverrunIdType">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>
    <xs:simpleType name="HvacOverrunTypeType">
        <xs:union memberTypes="ns_p:HvacOverrunTypeEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="HvacOverrunTypeEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="oneTimeDhw"/>
            <xs:enumeration value="party"/>
            <xs:enumeration value="sgReadyCondition1"/>
            <xs:enumeration value="sgReadyCondition3"/>
            <xs:enumeration value="sgReadyCondition4"/>
            <xs:enumeration value="oneDayAway"/>
            <xs:enumeration value="oneDayAtHome"/>
            <xs:enumeration value="oneTimeVentilation"/>
            <xs:enumeration value="hvacSystemOff"/>
            <xs:enumeration value="valveKick"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="HvacOverrunStatusType">
        <xs:union memberTypes="ns_p:HvacOverrunStatusEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="HvacOverrunStatusEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="active"/>
            <xs:enumeration value="running"/>
            <xs:enumeration value="finished"/>
            <xs:enumeration value="inactive"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="HvacSystemFunctionDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="systemFunctionId" type="ns_p:HvacSystemFunctionIdType"/>
            <xs:element minOccurs="0" name="currentOperationModeId" type="ns_p:HvacOperationModeIdType"/>
            <xs:element minOccurs="0" name="isOperationModeIdChangeable" type="xs:boolean"/>
            <xs:element minOccurs="0" name="currentSetpointId" type="ns_p:SetpointIdType"/>
            <xs:element minOccurs="0" name="isSetpointIdChangeable" type="xs:boolean"/>
            <xs:element minOccurs="0" name="isOverrunActive" type="xs:boolean"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionData" type="ns_p:HvacSystemFunctionDataType"/>
    <xs:complexType name="HvacSystemFunctionDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="systemFunctionId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="currentOperationModeId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="isOperationModeIdChangeable" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="currentSetpointId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="isSetpointIdChangeable" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="isOverrunActive" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionDataElements" type="ns_p:HvacSystemFunctionDataElementsType"/>
    <xs:complexType name="HvacSystemFunctionListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:hvacSystemFunctionData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionListData" type="ns_p:HvacSystemFunctionListDataType"/>
    <xs:complexType name="HvacSystemFunctionListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="systemFunctionId" type="ns_p:HvacSystemFunctionIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionListDataSelectors" type="ns_p:HvacSystemFunctionListDataSelectorsType"/>
    <xs:complexType name="HvacSystemFunctionOperationModeRelationDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="systemFunctionId" type="ns_p:HvacSystemFunctionIdType"/>
            <xs:element minOccurs="0" name="operationModeId" type="ns_p:HvacOperationModeIdType" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionOperationModeRelationData" type="ns_p:HvacSystemFunctionOperationModeRelationDataType"/>
    <xs:complexType name="HvacSystemFunctionOperationModeRelationDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="systemFunctionId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="operationModeId" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionOperationModeRelationDataElements" type="ns_p:HvacSystemFunctionOperationModeRelationDataElementsType"/>
    <xs:complexType name="HvacSystemFunctionOperationModeRelationListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:hvacSystemFunctionOperationModeRelationData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionOperationModeRelationListData" type="ns_p:HvacSystemFunctionOperationModeRelationListDataType"/>
    <xs:complexType name="HvacSystemFunctionOperationModeRelationListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="systemFunctionId" type="ns_p:HvacSystemFunctionIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionOperationModeRelationListDataSelectors" type="ns_p:HvacSystemFunctionOperationModeRelationListDataSelectorsType"/>
    <xs:complexType name="HvacSystemFunctionSetpointRelationDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="systemFunctionId" type="ns_p:HvacSystemFunctionIdType"/>
            <xs:element minOccurs="0" name="operationModeId" type="ns_p:HvacOperationModeIdType"/>
            <xs:element minOccurs="0" name="setpointId" type="ns_p:SetpointIdType" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionSetpointRelationData" type="ns_p:HvacSystemFunctionSetpointRelationDataType"/>
    <xs:complexType name="HvacSystemFunctionSetpointRelationDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="systemFunctionId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="operationModeId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="setpointId" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionSetpointRelationDataElements" type="ns_p:HvacSystemFunctionSetpointRelationDataElementsType"/>
    <xs:complexType name="HvacSystemFunctionSetpointRelationListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:hvacSystemFunctionSetpointRelationData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionSetpointRelationListData" type="ns_p:HvacSystemFunctionSetpointRelationListDataType"/>
    <xs:complexType name="HvacSystemFunctionSetpointRelationListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="systemFunctionId" type="ns_p:HvacSystemFunctionIdType"/>
            <xs:element minOccurs="0" name="operationModeId" type="ns_p:HvacOperationModeIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionSetpointRelationListDataSelectors" type="ns_p:HvacSystemFunctionSetpointRelationListDataSelectorsType"/>
    <xs:complexType name="HvacSystemFunctionPowerSequenceRelationDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="systemFunctionId" type="ns_p:HvacSystemFunctionIdType"/>
            <xs:element minOccurs="0" name="sequenceId" type="ns_p:PowerSequenceIdType" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionPowerSequenceRelationData" type="ns_p:HvacSystemFunctionPowerSequenceRelationDataType"/>
    <xs:complexType name="HvacSystemFunctionPowerSequenceRelationDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="systemFunctionId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="sequenceId" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionPowerSequenceRelationDataElements" type="ns_p:HvacSystemFunctionPowerSequenceRelationDataElementsType"/>
    <xs:complexType name="HvacSystemFunctionPowerSequenceRelationListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:hvacSystemFunctionPowerSequenceRelationData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionPowerSequenceRelationListData" type="ns_p:HvacSystemFunctionPowerSequenceRelationListDataType"/>
    <xs:complexType name="HvacSystemFunctionPowerSequenceRelationListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="systemFunctionId" type="ns_p:HvacSystemFunctionIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionPowerSequenceRelationListDataSelectors" type="ns_p:HvacSystemFunctionPowerSequenceRelationListDataSelectorsType"/>
    <xs:complexType name="HvacSystemFunctionDescriptionDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="systemFunctionId" type="ns_p:HvacSystemFunctionIdType"/>
            <xs:element minOccurs="0" name="systemFunctionType" type="ns_p:HvacSystemFunctionTypeType"/>
            <xs:element minOccurs="0" name="label" type="ns_p:LabelType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:DescriptionType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionDescriptionData" type="ns_p:HvacSystemFunctionDescriptionDataType"/>
    <xs:complexType name="HvacSystemFunctionDescriptionDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="systemFunctionId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="systemFunctionType" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="label" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionDescriptionDataElements" type="ns_p:HvacSystemFunctionDescriptionDataElementsType"/>
    <xs:complexType name="HvacSystemFunctionDescriptionListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:hvacSystemFunctionDescriptionData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionDescriptionListData" type="ns_p:HvacSystemFunctionDescriptionListDataType"/>
    <xs:complexType name="HvacSystemFunctionDescriptionListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="systemFunctionId" type="ns_p:HvacSystemFunctionIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacSystemFunctionDescriptionListDataSelectors" type="ns_p:HvacSystemFunctionDescriptionListDataSelectorsType"/>
    <xs:complexType name="HvacOperationModeDescriptionDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="operationModeId" type="ns_p:HvacOperationModeIdType"/>
            <xs:element minOccurs="0" name="operationModeType" type="ns_p:HvacOperationModeTypeType"/>
            <xs:element minOccurs="0" name="label" type="ns_p:LabelType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:DescriptionType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacOperationModeDescriptionData" type="ns_p:HvacOperationModeDescriptionDataType"/>
    <xs:complexType name="HvacOperationModeDescriptionDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="operationModeId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="operationModeType" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="label" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacOperationModeDescriptionDataElements" type="ns_p:HvacOperationModeDescriptionDataElementsType"/>
    <xs:complexType name="HvacOperationModeDescriptionListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:hvacOperationModeDescriptionData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacOperationModeDescriptionListData" type="ns_p:HvacOperationModeDescriptionListDataType"/>
    <xs:complexType name="HvacOperationModeDescriptionListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="operationModeId" type="ns_p:HvacOperationModeIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacOperationModeDescriptionListDataSelectors" type="ns_p:HvacOperationModeDescriptionListDataSelectorsType"/>
    <xs:complexType name="HvacOverrunDataType">
        <xs:sequence>
            <xs:element name="overrunId" minOccurs="0" type="ns_p:HvacOverrunIdType"/>
            <xs:element name="overrunStatus" minOccurs="0" type="ns_p:HvacOverrunStatusType"/>
            <xs:element minOccurs="0" name="timeTableId" type="ns_p:TimeTableIdType"/>
            <xs:element minOccurs="0" name="isOverrunStatusChangeable" type="xs:boolean"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacOverrunData" type="ns_p:HvacOverrunDataType"/>
    <xs:complexType name="HvacOverrunDataElementsType">
        <xs:sequence>
            <xs:element name="overrunId" minOccurs="0" type="ns_p:ElementTagType"/>
            <xs:element name="overrunStatus" minOccurs="0" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="timeTableId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="isOverrunStatusChangeable" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacOverrunDataElements" type="ns_p:HvacOverrunDataElementsType"/>
    <xs:complexType name="HvacOverrunListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:hvacOverrunData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacOverrunListData" type="ns_p:HvacOverrunListDataType"/>
    <xs:complexType name="HvacOverrunListDataSelectorsType">
        <xs:sequence>
            <xs:element name="overrunId" minOccurs="0" type="ns_p:HvacOverrunIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacOverrunListDataSelectors" type="ns_p:HvacOverrunListDataSelectorsType"/>
    <xs:complexType name="HvacOverrunDescriptionDataType">
        <xs:sequence>
            <xs:element name="overrunId" minOccurs="0" type="ns_p:HvacOverrunIdType"/>
            <xs:element minOccurs="0" name="overrunType" type="ns_p:HvacOverrunTypeType"/>
            <xs:element minOccurs="0" name="affectedSystemFunctionId" type="ns_p:HvacSystemFunctionIdType" maxOccurs="unbounded"/>
            <xs:element minOccurs="0" name="label" type="ns_p:LabelType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:DescriptionType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacOverrunDescriptionData" type="ns_p:HvacOverrunDescriptionDataType"/>
    <xs:complexType name="HvacOverrunDescriptionDataElementsType">
        <xs:sequence>
            <xs:element name="overrunId" minOccurs="0" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="overrunType" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="affectedSystemFunctionId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="label" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacOverrunDescriptionDataElements" type="ns_p:HvacOverrunDescriptionDataElementsType"/>
    <xs:complexType name="HvacOverrunDescriptionListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:hvacOverrunDescriptionData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacOverrunDescriptionListData" type="ns_p:HvacOverrunDescriptionListDataType"/>
    <xs:complexType name="HvacOverrunDescriptionListDataSelectorsType">
        <xs:sequence>
            <xs:element name="overrunId" minOccurs="0" type="ns_p:HvacOverrunIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="hvacOverrunDescriptionListDataSelectors" type="ns_p:HvacOverrunDescriptionListDataSelectorsType"/>
</xs:schema>

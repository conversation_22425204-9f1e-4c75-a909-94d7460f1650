<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema"
    targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0" blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_PowerSequences.xsd"/>
    <xs:group name="group_powerTimeSlot">
        <xs:sequence>
            <xs:group ref="ns_p:powerTimeSlotSchedule"/>
            <xs:group ref="ns_p:powerTimeSlotValue"/>
            <xs:group ref="ns_p:powerTimeSlotScheduleConstraints"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="group_powerSequence">
        <xs:sequence>
            <xs:group ref="ns_p:powerSequenceAlternativesRelation"/>
            <xs:group ref="ns_p:powerSequenceDescription"/>
            <xs:group ref="ns_p:powerSequenceState"/>
            <xs:group ref="ns_p:powerSequenceSchedule"/>
            <xs:group ref="ns_p:powerSequenceScheduleConstraints"/>
            <xs:group ref="ns_p:powerSequencePrice"/>
            <xs:group ref="ns_p:powerSequenceSchedulePreference"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="group_powerSequenceNode">
        <xs:sequence>
            <xs:group ref="ns_p:powerSequenceNodeScheduleInformation"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="group_powerSequenceCall">
        <xs:sequence>
            <xs:group ref="ns_p:powerSequenceScheduleConfigurationRequestCall"/>
            <xs:group ref="ns_p:powerSequencePriceCalculationRequestCall"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="powerTimeSlotSchedule">
        <xs:sequence>
            <xs:element ref="ns_p:powerTimeSlotScheduleListData"/>
            <xs:element ref="ns_p:powerTimeSlotScheduleListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="powerTimeSlotValue">
        <xs:sequence>
            <xs:element ref="ns_p:powerTimeSlotValueListData"/>
            <xs:element ref="ns_p:powerTimeSlotValueListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="powerTimeSlotScheduleConstraints">
        <xs:sequence>
            <xs:element ref="ns_p:powerTimeSlotScheduleConstraintsListData"/>
            <xs:element ref="ns_p:powerTimeSlotScheduleConstraintsListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="powerSequenceAlternativesRelation">
        <xs:sequence>
            <xs:element ref="ns_p:powerSequenceAlternativesRelationListData"/>
            <xs:element ref="ns_p:powerSequenceAlternativesRelationListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="powerSequenceDescription">
        <xs:sequence>
            <xs:element ref="ns_p:powerSequenceDescriptionListData"/>
            <xs:element ref="ns_p:powerSequenceDescriptionListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="powerSequenceState">
        <xs:sequence>
            <xs:element ref="ns_p:powerSequenceStateListData"/>
            <xs:element ref="ns_p:powerSequenceStateListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="powerSequenceSchedule">
        <xs:sequence>
            <xs:element ref="ns_p:powerSequenceScheduleListData"/>
            <xs:element ref="ns_p:powerSequenceScheduleListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="powerSequenceScheduleConstraints">
        <xs:sequence>
            <xs:element ref="ns_p:powerSequenceScheduleConstraintsListData"/>
            <xs:element ref="ns_p:powerSequenceScheduleConstraintsListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="powerSequencePrice">
        <xs:sequence>
            <xs:element ref="ns_p:powerSequencePriceListData"/>
            <xs:element ref="ns_p:powerSequencePriceListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="powerSequenceSchedulePreference">
        <xs:sequence>
            <xs:element ref="ns_p:powerSequenceSchedulePreferenceListData"/>
            <xs:element ref="ns_p:powerSequenceSchedulePreferenceListDataSelectors"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="powerSequenceNodeScheduleInformation">
        <xs:sequence>
            <xs:element ref="ns_p:powerSequenceNodeScheduleInformationData"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="powerSequenceScheduleConfigurationRequestCall">
        <xs:sequence>
            <xs:element ref="ns_p:powerSequenceScheduleConfigurationRequestCall"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="powerSequencePriceCalculationRequestCall">
        <xs:sequence>
            <xs:element ref="ns_p:powerSequencePriceCalculationRequestCall"/>
        </xs:sequence>
    </xs:group>
</xs:schema>

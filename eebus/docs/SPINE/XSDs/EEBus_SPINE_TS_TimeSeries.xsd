<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0"
    blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_CommonDataTypes.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_Measurement.xsd"/>
    <xs:simpleType name="TimeSeriesIdType">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>
    <xs:simpleType name="TimeSeriesSlotIdType">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>
    <xs:simpleType name="TimeSeriesSlotCountType">
        <xs:restriction base="ns_p:TimeSeriesSlotIdType"/>
    </xs:simpleType>
    <xs:simpleType name="TimeSeriesTypeType">
        <xs:union memberTypes="ns_p:TimeSeriesTypeEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="TimeSeriesTypeEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="plan"/>
            <xs:enumeration value="singleDemand"/>
            <xs:enumeration value="constraints"/>
            <xs:enumeration value="energyRequest"/>
            <xs:enumeration value="dischargingEnergyRequest"/>
            <xs:enumeration value="consumptionLimitCurve"/>
            <xs:enumeration value="productionLimitCurve"/>
            <xs:enumeration value="fallbackConsumptionLimitCurve"/>
            <xs:enumeration value="fallbackProductionLimitCurve"/>
            <xs:enumeration value="powerRequest"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="TimeSeriesStateType">
        <xs:union memberTypes="ns_p:TimeSeriesStateEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="TimeSeriesStateEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="request"/>
            <xs:enumeration value="requestAccepted"/>
            <xs:enumeration value="requestRejected"/>
            <xs:enumeration value="cancelRequest"/>
            <xs:enumeration value="cancelRequestAccepted"/>
            <xs:enumeration value="cancelRequestRejected"/>
            <xs:enumeration value="requestCancelled"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="TimeSeriesSlotType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeSeriesSlotId" type="ns_p:TimeSeriesSlotIdType"/>
            <xs:element minOccurs="0" name="timePeriod" type="ns_p:TimePeriodType"/>
            <xs:element minOccurs="0" name="duration" type="xs:duration"/>
            <xs:element minOccurs="0" name="recurrenceInformation" type="ns_p:AbsoluteOrRecurringTimeType"/>
            <xs:element minOccurs="0" name="value" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="minValue" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="maxValue" type="ns_p:ScaledNumberType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TimeSeriesSlotElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeSeriesSlotId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="timePeriod" type="ns_p:TimePeriodElementsType"/>
            <xs:element minOccurs="0" name="duration" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="recurrenceInformation" type="ns_p:AbsoluteOrRecurringTimeElementsType"/>
            <xs:element minOccurs="0" name="value" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="minValue" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="maxValue" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TimeSeriesDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeSeriesId" type="ns_p:TimeSeriesIdType"/>
            <xs:element minOccurs="0" name="timePeriod" type="ns_p:TimePeriodType"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="timeSeriesSlot" type="ns_p:TimeSeriesSlotType"/>
            <xs:element minOccurs="0" name="timeSeriesState" type="ns_p:TimeSeriesStateType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeSeriesData" type="ns_p:TimeSeriesDataType"/>
    <xs:complexType name="TimeSeriesDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeSeriesId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="timePeriod" type="ns_p:TimePeriodElementsType"/>
            <xs:element minOccurs="0" name="timeSeriesSlot" type="ns_p:TimeSeriesSlotElementsType"/>
            <xs:element minOccurs="0" name="timeSeriesState" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeSeriesDataElements" type="ns_p:TimeSeriesDataElementsType"/>
    <xs:complexType name="TimeSeriesListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:timeSeriesData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeSeriesListData" type="ns_p:TimeSeriesListDataType"/>
    <xs:complexType name="TimeSeriesListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeSeriesId" type="ns_p:TimeSeriesIdType"/>
            <xs:element minOccurs="0" name="timeSeriesSlotId" type="ns_p:TimeSeriesSlotIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeSeriesListDataSelectors" type="ns_p:TimeSeriesListDataSelectorsType"/>
    <xs:complexType name="TimeSeriesDescriptionDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeSeriesId" type="ns_p:TimeSeriesIdType"/>
            <xs:element minOccurs="0" name="timeSeriesType" type="ns_p:TimeSeriesTypeType"/>
            <xs:element minOccurs="0" name="timeSeriesWriteable" type="xs:boolean"/>
            <xs:element minOccurs="0" name="updateRequired" type="xs:boolean"/>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:MeasurementIdType"/>
            <xs:element minOccurs="0" name="currency" type="ns_p:CurrencyType"/>
            <xs:element minOccurs="0" name="unit" type="ns_p:UnitOfMeasurementType"/>
            <xs:element minOccurs="0" name="label" type="ns_p:LabelType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:DescriptionType"/>
            <xs:element minOccurs="0" name="scopeType" type="ns_p:ScopeTypeType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeSeriesDescriptionData" type="ns_p:TimeSeriesDescriptionDataType"/>
    <xs:complexType name="TimeSeriesDescriptionDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeSeriesId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="timeSeriesType" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="timeSeriesWriteable" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="updateRequired" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="currency" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="unit" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="label" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="scopeType" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeSeriesDescriptionDataElements" type="ns_p:TimeSeriesDescriptionDataElementsType"/>
    <xs:complexType name="TimeSeriesDescriptionListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:timeSeriesDescriptionData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeSeriesDescriptionListData" type="ns_p:TimeSeriesDescriptionListDataType"/>
    <xs:complexType name="TimeSeriesDescriptionListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeSeriesId" type="ns_p:TimeSeriesIdType"/>
            <xs:element minOccurs="0" name="timeSeriesType" type="ns_p:TimeSeriesTypeType"/>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:MeasurementIdType"/>
            <xs:element minOccurs="0" name="scopeType" type="ns_p:ScopeTypeType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeSeriesDescriptionListDataSelectors" type="ns_p:TimeSeriesDescriptionListDataSelectorsType"/>
    <xs:complexType name="TimeSeriesConstraintsDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeSeriesId" type="ns_p:TimeSeriesIdType"/>
            <xs:element minOccurs="0" name="slotCountMin" type="ns_p:TimeSeriesSlotCountType"/>
            <xs:element minOccurs="0" name="slotCountMax" type="ns_p:TimeSeriesSlotCountType"/>
            <xs:element minOccurs="0" name="slotDurationMin" type="xs:duration"/>
            <xs:element minOccurs="0" name="slotDurationMax" type="xs:duration"/>
            <xs:element minOccurs="0" name="slotDurationStepSize" type="xs:duration"/>
            <xs:element minOccurs="0" name="earliestTimeSeriesStartTime" type="ns_p:AbsoluteOrRelativeTimeType"/>
            <xs:element minOccurs="0" name="latestTimeSeriesEndTime" type="ns_p:AbsoluteOrRelativeTimeType"/>
            <xs:element minOccurs="0" name="slotValueMin" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="slotValueMax" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="slotValueStepSize" type="ns_p:ScaledNumberType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeSeriesConstraintsData" type="ns_p:TimeSeriesConstraintsDataType"/>
    <xs:complexType name="TimeSeriesConstraintsDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeSeriesId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="slotCountMin" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="slotCountMax" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="slotDurationMin" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="slotDurationMax" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="slotDurationStepSize" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="earliestTimeSeriesStartTime" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="latestTimeSeriesEndTime" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="slotValueMin" type="ns_p:ScaledNumberElementsType"/>
            <xs:element minOccurs="0" name="slotValueMax" type="ns_p:ScaledNumberElementsType"/>
            <xs:element minOccurs="0" name="slotValueStepSize" type="ns_p:ScaledNumberElementsType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeSeriesConstraintsDataElements" type="ns_p:TimeSeriesConstraintsDataElementsType"/>
    <xs:complexType name="TimeSeriesConstraintsListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:timeSeriesConstraintsData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeSeriesConstraintsListData" type="ns_p:TimeSeriesConstraintsListDataType"/>
    <xs:complexType name="TimeSeriesConstraintsListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="timeSeriesId" type="ns_p:TimeSeriesIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="timeSeriesConstraintsListDataSelectors" type="ns_p:TimeSeriesConstraintsListDataSelectorsType"/>
</xs:schema>

<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.4.0 beta2
    2025-01-27
    Copyright (c) 2025 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/1.4.0/beta2" xmlns:xs="http://www.w3.org/2001/XMLSchema"
    targetNamespace="http://docs.eebus.org/spine/xsd/1.4.0/beta2" version="1.4.0" blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2025 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_Alarm.xsd"/>
    <xs:group name="group_alarm">
        <xs:sequence>
            <xs:group ref="ns_p:alarm"/>
        </xs:sequence>
    </xs:group>
    <xs:group name="alarm">
        <xs:sequence>
            <xs:element ref="ns_p:alarmListData"/>
            <xs:element ref="ns_p:alarmListDataSelectors"/>
        </xs:sequence>
    </xs:group>
</xs:schema>

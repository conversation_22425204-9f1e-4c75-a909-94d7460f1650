<?xml version="1.0" encoding="UTF-8"?>
<datagram xmlns="http://docs.eebus.org/spine/xsd/1.4.0/beta2"
 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
 xsi:schemaLocation="http://docs.eebus.org/spine/xsd/1.4.0/beta2 file:../../XSDs/EEBus_SPINE_TS_Datagram.xsd">
    <header>
        <specificationVersion>1.4.0</specificationVersion>
        <addressSource>
            <device>d:_i:46925_TestDevice-C</device>
            <entity>1</entity>
            <feature>1</feature>
        </addressSource>
        <addressDestination>
            <device>d:_i:46925_TestDevice-S</device>
            <entity>1</entity>
            <feature>1</feature>
        </addressDestination>
        <msgCounter>13</msgCounter>
        <cmdClassifier>write</cmdClassifier>
        <ackRequest>true</ackRequest>
        <timestamp>2025-01-27T09:58:00.0Z</timestamp>
    </header>
    <payload>
        <cmd>
            <function>deviceClassificationUserData</function>
			<filter>
				<cmdControl>
					<delete/>
				</cmdControl>
                <deviceClassificationUserDataElements>
                    <userLabel/> <!-- Element to be deleted; was present before! -->
                </deviceClassificationUserDataElements>
            </filter>
            <deviceClassificationUserData/>
        </cmd>
    </payload>
</datagram>

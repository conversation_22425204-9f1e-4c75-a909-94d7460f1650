
import os
import json
import re

def extract_json_from_log(log_content):
    # Regex pattern to find <PERSON><PERSON><PERSON> in the log messages
    pattern = r'(Send|Received) message .*?: ({.*})'
    matches = re.finditer(pattern, log_content)
    
    for match in matches:
        try:
            direction = match.group(1).lower()
            json_str = match.group(2)

            #print(f"Found JSON: {json_str}")

            json_data = json.loads(json_str)

            # example line:
            # [36m[keo_spine_transport] 2018-03-12 08:47:10 INFO Send message to 'ship_SEMPSHIPGW_0xb50005d8': {"datagram":[{"header":[{"specificationVersion":"1.1.0"},{"addressSource":[{"device":"d:_i:46922_MySpineDeviceAddress"},{"entity":[0]},{"feature":0}]},{"addressDestination":[{"device":"d:_i:36013_1901706113"},{"entity":[0]},{"feature":0}]},{"msgCounter":26},{"cmdClassifier":"notify"}]},{"payload":[{"cmd":[[{"function":"nodeManagementDestinationListData"},{"filter":[[{"cmdControl":[{"partial":[]}]}]]},{"nodeManagementDestinationListData":[{"nodeManagementDestinationData":[[{"deviceDescription":[{"deviceAddress":[{"device":"d:_i:46922_MySpineDeviceAddressOTS"}]},{"networkFeatureSet":"gateway"},{"lastStateChange":"added"}]}]]}]}]]}]}]}[0m
            
            datagram = json_data.get('datagram', [{}])

            # Extract required fields
            header = datagram[0].get('header', [{}])
            cmd_classifier = next((item.get('cmdClassifier') for item in header if 'cmdClassifier' in item), 'unknown')

            # Extract function name from payload if exists
            payload = datagram[1].get('payload', [{}])
            cmd = payload[0]['cmd'][0]
            #print(cmd)
            # Each command field is a separate object in an array
            # Find the first object that has a key that's not in the excluded list
            excluded_keys = ["filter", "lastUpdateAt", "function", "manufacturerSpecificExtension"]
            function_name = 'unknown'
            for obj in cmd:
                key = next((k for k in obj.keys() if k not in excluded_keys), None)
                if key:
                    function_name = key
                    break
            #print(f"function_name: {function_name}")

            # Get message counter
            msg_counter = next((item.get('msgCounter') for item in header if 'msgCounter' in item), 0)
            msg_counter_reference = next((item.get('msgCounterReference') for item in header if 'msgCounterReference' in item), 0)
            
            return {
                'direction': direction,
                'classifier': cmd_classifier,
                'function': function_name,
                'counter': msg_counter,
                'reply': msg_counter_reference,
                'json_data': json_data
            }
        except json.JSONDecodeError:
            print(f"Error processing JSON: {e}")
            continue
        except Exception as e:
            print(f"Error processing JSON: {e}")
    return None

def main():
    # Process all .log files in current directory
    for filename in os.listdir('.'):
        if not filename.endswith('.log'):
            continue
            
        base_name = os.path.splitext(filename)[0]
        
        # Create directory for extracted JSONs
        os.makedirs(base_name, exist_ok=True)
        
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                result = extract_json_from_log(line)
                if result:
                    #print(result)
                    # Create filename for the JSON
                    reply_to = f"_reply_{result['reply']}" if result['reply'] != 0 else ''
                    json_filename = f"{result['direction']}_{result['classifier']}_{result['function']}_{result['counter']}{reply_to}.json"
                    json_path = os.path.join(base_name, json_filename)
                    
                    # Write JSON to file with pretty printing
                    with open(json_path, 'w', encoding='utf-8') as f:
                        json.dump(result['json_data'], f, indent=2)

if __name__ == "__main__":
    main()

{"datagram": [{"header": [{"specificationVersion": "1.1.0"}, {"addressSource": [{"device": "d:_i:46922_MySpineDeviceAddressHGS"}, {"entity": [1, 3]}, {"feature": 1}]}, {"addressDestination": [{"device": "d:_i:36013_1901706113"}, {"entity": [1]}, {"feature": 1}]}, {"msgCounter": 62}, {"cmdClassifier": "notify"}]}, {"payload": [{"cmd": [[{"function": "smartEnergyManagementPsData"}, {"filter": [[{"cmdControl": [{"partial": []}]}]]}, {"smartEnergyManagementPsData": [{"alternatives": [[{"powerSequence": [[{"description": [{"sequenceId": 2}]}, {"state": [{"state": "scheduled"}, {"sequenceRemoteControllable": true}]}, {"schedule": [{"startTime": "PT0S"}]}, {"scheduleConstraints": [{"earliestStartTime": "PT0S"}, {"latestEndTime": "PT23H59M9S"}]}]]}]]}]}]]}]}]}
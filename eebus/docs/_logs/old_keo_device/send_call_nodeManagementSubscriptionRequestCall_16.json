{"datagram": [{"header": [{"specificationVersion": "1.1.0"}, {"addressSource": [{"device": "d:_i:46922_MySpineDeviceAddress"}, {"entity": [0]}, {"feature": 0}]}, {"addressDestination": [{"device": "d:_i:36013_1901706113_ElectricitySupplySystem"}, {"entity": [0]}, {"feature": 0}]}, {"msgCounter": 16}, {"cmdClassifier": "call"}, {"ackRequest": true}]}, {"payload": [{"cmd": [[{"nodeManagementSubscriptionRequestCall": [{"subscriptionRequest": [{"clientAddress": [{"device": "d:_i:46922_MySpineDeviceAddress"}, {"entity": [0]}, {"feature": 0}]}, {"serverAddress": [{"device": "d:_i:36013_1901706113_ElectricitySupplySystem"}, {"entity": [0]}, {"feature": 0}]}, {"serverFeatureType": "NodeManagement"}]}]}]]}]}]}
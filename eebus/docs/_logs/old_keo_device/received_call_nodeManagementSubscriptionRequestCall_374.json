{"datagram": [{"header": [{"specificationVersion": "1.1.0"}, {"addressSource": [{"device": "d:_i:36013_1901706113"}, {"entity": [1]}, {"feature": 1}]}, {"addressDestination": [{"device": "d:_i:46922_MySpineDeviceAddressHGS"}, {"entity": [0]}, {"feature": 0}]}, {"msgCounter": 374}, {"cmdClassifier": "call"}, {"ackRequest": true}]}, {"payload": [{"cmd": [[{"nodeManagementSubscriptionRequestCall": [{"subscriptionRequest": [{"clientAddress": [{"device": "d:_i:36013_1901706113"}, {"entity": [1]}, {"feature": 1}]}, {"serverAddress": [{"device": "d:_i:46922_MySpineDeviceAddressHGS"}, {"entity": [1, 3]}, {"feature": 2}]}, {"serverFeatureType": "Measurement"}]}]}]]}]}]}
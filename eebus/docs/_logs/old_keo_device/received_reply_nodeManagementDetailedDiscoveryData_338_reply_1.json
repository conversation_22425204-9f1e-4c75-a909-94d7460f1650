{"datagram": [{"header": [{"specificationVersion": "1.1.0"}, {"addressSource": [{"device": "d:_i:36013_1901706113"}, {"entity": [0]}, {"feature": 0}]}, {"addressDestination": [{"device": "d:_i:46922_MySpineDeviceAddress"}, {"entity": [0]}, {"feature": 0}]}, {"msgCounter": 338}, {"msgCounterReference": 1}, {"cmdClassifier": "reply"}]}, {"payload": [{"cmd": [[{"nodeManagementDetailedDiscoveryData": [{"specificationVersionList": [{"specificationVersion": ["1.1.0"]}]}, {"deviceInformation": [{"description": [{"deviceAddress": [{"device": "d:_i:36013_1901706113"}]}, {"networkFeatureSet": "smart"}, {"lastStateChange": "added"}]}]}, {"entityInformation": [[{"description": [{"entityAddress": [{"entity": [0]}]}, {"entityType": "DeviceInformation"}]}], [{"description": [{"entityAddress": [{"entity": [1]}]}, {"entityType": "Generic"}, {"description": "SmartEnergyManagementPs client entity"}]}]]}, {"featureInformation": [[{"description": [{"featureAddress": [{"entity": [0]}, {"feature": 0}]}, {"featureType": "NodeManagement"}, {"role": "special"}, {"supportedFunction": [[{"function": "nodeManagementDetailedDiscoveryData"}, {"possibleOperations": [{"read": []}]}], [{"function": "nodeManagementSubscriptionRequestCall"}, {"possibleOperations": []}], [{"function": "nodeManagementSubscriptionDeleteCall"}, {"possibleOperations": []}], [{"function": "nodeManagementBindingRequestCall"}, {"possibleOperations": []}], [{"function": "nodeManagementBindingDeleteCall"}, {"possibleOperations": []}], [{"function": "nodeManagementSubscriptionData"}, {"possibleOperations": [{"read": []}]}], [{"function": "nodeManagementBindingData"}, {"possibleOperations": [{"read": []}]}], [{"function": "nodeManagementDestinationListData"}, {"possibleOperations": [{"read": []}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [0]}, {"feature": 1}]}, {"featureType": "DeviceClassification"}, {"role": "server"}, {"supportedFunction": [[{"function": "deviceClassificationManufacturerData"}, {"possibleOperations": [{"read": []}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [1]}, {"feature": 1}]}, {"featureType": "Generic"}, {"role": "client"}, {"description": "SmartEnergyManagementPs client feature"}]}]]}]}]]}]}]}
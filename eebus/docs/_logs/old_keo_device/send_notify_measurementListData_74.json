{"datagram": [{"header": [{"specificationVersion": "1.1.0"}, {"addressSource": [{"device": "d:_i:46922_MySpineDeviceAddressHGS"}, {"entity": [1, 3]}, {"feature": 2}]}, {"addressDestination": [{"device": "d:_i:36013_1901706113"}, {"entity": [1]}, {"feature": 1}]}, {"msgCounter": 74}, {"cmdClassifier": "notify"}]}, {"payload": [{"cmd": [[{"measurementListData": [{"measurementData": [[{"measurementId": 1}, {"valueType": "value"}, {"timestamp": "2018-03-12T07:54:11.982Z"}, {"value": [{"number": 0}, {"scale": 0}]}, {"valueSource": "empiricalValue"}, {"valueState": "normal"}]]}]}]]}]}]}
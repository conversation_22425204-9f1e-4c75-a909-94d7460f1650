{"datagram": [{"header": [{"specificationVersion": "1.1.0"}, {"addressSource": [{"device": "d:_i:46922_MySpineDeviceAddressOTS"}, {"entity": [0]}, {"feature": 0}]}, {"addressDestination": [{"device": "d:_i:36013_1901706113"}, {"entity": [0]}, {"feature": 0}]}, {"msgCounter": 37}, {"msgCounterReference": 363}, {"cmdClassifier": "reply"}]}, {"payload": [{"cmd": [[{"nodeManagementDetailedDiscoveryData": [{"specificationVersionList": [{"specificationVersion": ["1.1.0"]}]}, {"deviceInformation": [{"description": [{"deviceAddress": [{"device": "d:_i:46922_MySpineDeviceAddressOTS"}]}, {"deviceType": "EnvironmentSensor"}, {"networkFeatureSet": "gateway"}, {"lastStateChange": "added"}]}]}, {"entityInformation": [[{"description": [{"entityAddress": [{"entity": [0]}]}, {"entityType": "DeviceInformation"}]}], [{"description": [{"entityAddress": [{"entity": [1]}]}, {"entityType": "TemperatureSensor"}]}]]}, {"featureInformation": [[{"description": [{"featureAddress": [{"entity": [0]}, {"feature": 0}]}, {"featureType": "NodeManagement"}, {"role": "special"}, {"supportedFunction": [[{"function": "nodeManagementDetailedDiscoveryData"}, {"possibleOperations": [{"read": []}]}], [{"function": "nodeManagementSubscriptionRequestCall"}, {"possibleOperations": []}], [{"function": "nodeManagementBindingRequestCall"}, {"possibleOperations": []}], [{"function": "nodeManagementSubscriptionDeleteCall"}, {"possibleOperations": []}], [{"function": "nodeManagementBindingDeleteCall"}, {"possibleOperations": []}], [{"function": "nodeManagementSubscriptionData"}, {"possibleOperations": [{"read": []}]}], [{"function": "nodeManagementBindingData"}, {"possibleOperations": [{"read": []}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [0]}, {"feature": 1}]}, {"featureType": "DeviceClassification"}, {"role": "server"}, {"supportedFunction": [[{"function": "deviceClassificationManufacturerData"}, {"possibleOperations": [{"read": []}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [1]}, {"feature": 1}]}, {"featureType": "Measurement"}, {"role": "server"}, {"supportedFunction": [[{"function": "measurementDescriptionListData"}, {"possibleOperations": [{"read": []}]}], [{"function": "measurementListData"}, {"possibleOperations": [{"read": []}]}], [{"function": "measurementConstraintsListData"}, {"possibleOperations": [{"read": []}]}]]}]}]]}]}]]}]}]}
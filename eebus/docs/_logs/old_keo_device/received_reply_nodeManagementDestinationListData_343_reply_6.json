{"datagram": [{"header": [{"specificationVersion": "1.1.0"}, {"addressSource": [{"device": "d:_i:36013_1901706113"}, {"entity": [0]}, {"feature": 0}]}, {"addressDestination": [{"device": "d:_i:46922_MySpineDeviceAddress"}, {"entity": [0]}, {"feature": 0}]}, {"msgCounter": 343}, {"msgCounterReference": 6}, {"cmdClassifier": "reply"}]}, {"payload": [{"cmd": [[{"nodeManagementDestinationListData": [{"nodeManagementDestinationData": [[{"deviceDescription": [{"deviceAddress": [{"device": "d:_i:36013_1901706113"}]}, {"networkFeatureSet": "smart"}, {"lastStateChange": "added"}]}], [{"deviceDescription": [{"deviceAddress": [{"device": "d:_i:36013_1901706113_ElectricitySupplySystem"}]}, {"networkFeatureSet": "smart"}, {"lastStateChange": "added"}, {"label": ""}]}]]}]}]]}]}]}
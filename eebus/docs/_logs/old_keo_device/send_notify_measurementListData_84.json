{"datagram": [{"header": [{"specificationVersion": "1.1.0"}, {"addressSource": [{"device": "d:_i:46922_MySpineDeviceAddressHGS"}, {"entity": [1, 3]}, {"feature": 2}]}, {"addressDestination": [{"device": "d:_i:36013_1901706113"}, {"entity": [1]}, {"feature": 1}]}, {"msgCounter": 84}, {"cmdClassifier": "notify"}]}, {"payload": [{"cmd": [[{"measurementListData": [{"measurementData": [[{"measurementId": 1}, {"valueType": "value"}, {"timestamp": "2018-03-12T08:49:58.972Z"}, {"value": [{"number": 2900}, {"scale": 0}]}, {"valueSource": "empiricalValue"}, {"valueState": "normal"}]]}]}]]}]}]}
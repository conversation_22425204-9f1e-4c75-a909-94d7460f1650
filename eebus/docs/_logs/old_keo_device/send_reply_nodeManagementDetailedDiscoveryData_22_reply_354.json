{"datagram": [{"header": [{"specificationVersion": "1.1.0"}, {"addressSource": [{"device": "d:_i:46922_MySpineDeviceAddressHGS"}, {"entity": [0]}, {"feature": 0}]}, {"addressDestination": [{"device": "d:_i:36013_1901706113"}, {"entity": [0]}, {"feature": 0}]}, {"msgCounter": 22}, {"msgCounterReference": 354}, {"cmdClassifier": "reply"}]}, {"payload": [{"cmd": [[{"nodeManagementDetailedDiscoveryData": [{"specificationVersionList": [{"specificationVersion": ["1.1.0"]}]}, {"deviceInformation": [{"description": [{"deviceAddress": [{"device": "d:_i:46922_MySpineDeviceAddressHGS"}]}, {"deviceType": "HeatGenerationSystem"}, {"networkFeatureSet": "gateway"}, {"lastStateChange": "added"}]}]}, {"entityInformation": [[{"description": [{"entityAddress": [{"entity": [0]}]}, {"entityType": "DeviceInformation"}]}], [{"description": [{"entityAddress": [{"entity": [1]}]}, {"entityType": "HeatPumpAppliance"}]}], [{"description": [{"entityAddress": [{"entity": [1, 3]}]}, {"entityType": "Compressor"}]}]]}, {"featureInformation": [[{"description": [{"featureAddress": [{"entity": [0]}, {"feature": 0}]}, {"featureType": "NodeManagement"}, {"role": "special"}, {"supportedFunction": [[{"function": "nodeManagementDetailedDiscoveryData"}, {"possibleOperations": [{"read": []}]}], [{"function": "nodeManagementSubscriptionRequestCall"}, {"possibleOperations": []}], [{"function": "nodeManagementBindingRequestCall"}, {"possibleOperations": []}], [{"function": "nodeManagementSubscriptionDeleteCall"}, {"possibleOperations": []}], [{"function": "nodeManagementBindingDeleteCall"}, {"possibleOperations": []}], [{"function": "nodeManagementSubscriptionData"}, {"possibleOperations": [{"read": []}]}], [{"function": "nodeManagementBindingData"}, {"possibleOperations": [{"read": []}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [0]}, {"feature": 1}]}, {"featureType": "DeviceClassification"}, {"role": "server"}, {"supportedFunction": [[{"function": "deviceClassificationManufacturerData"}, {"possibleOperations": [{"read": []}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [1]}, {"feature": 1}]}, {"featureType": "DeviceClassification"}, {"role": "server"}, {"supportedFunction": [[{"function": "deviceClassificationManufacturerData"}, {"possibleOperations": [{"read": []}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [1]}, {"feature": 2}]}, {"featureType": "HVAC"}, {"role": "server"}, {"supportedFunction": [[{"function": "hvacOverrunDescriptionListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "hvacOverrunListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "hvacSystemFunctionListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "hvacSystemFunctionDescriptionListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [1, 3]}, {"feature": 1}]}, {"featureType": "SmartEnergyManagementPs"}, {"role": "server"}, {"supportedFunction": [[{"function": "smartEnergyManagementPsData"}, {"possibleOperations": [{"read": []}]}]]}, {"maxResponseDelay": "PT3M"}]}], [{"description": [{"featureAddress": [{"entity": [1, 3]}, {"feature": 2}]}, {"featureType": "Measurement"}, {"role": "server"}, {"supportedFunction": [[{"function": "measurementDescriptionListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "measurementListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "measurementConstraintsListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [1, 3]}, {"feature": 3}]}, {"featureType": "ElectricalConnection"}, {"role": "server"}, {"supportedFunction": [[{"function": "electricalConnectionDescriptionListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "electricalConnectionParameterDescriptionListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}]]}]}]]}]}]]}]}]}
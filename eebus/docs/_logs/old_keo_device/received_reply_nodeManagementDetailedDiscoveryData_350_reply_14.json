{"datagram": [{"header": [{"specificationVersion": "1.1.0"}, {"addressSource": [{"device": "d:_i:36013_1901706113_ElectricitySupplySystem"}, {"entity": [0]}, {"feature": 0}]}, {"addressDestination": [{"device": "d:_i:46922_MySpineDeviceAddress"}, {"entity": [0]}, {"feature": 0}]}, {"msgCounter": 350}, {"msgCounterReference": 14}, {"cmdClassifier": "reply"}]}, {"payload": [{"cmd": [[{"nodeManagementDetailedDiscoveryData": [{"specificationVersionList": [{"specificationVersion": ["1.1.0"]}]}, {"deviceInformation": [{"description": [{"deviceAddress": [{"device": "d:_i:36013_1901706113_ElectricitySupplySystem"}]}, {"deviceType": "ElectricitySupplySystem"}, {"networkFeatureSet": "smart"}, {"lastStateChange": "added"}]}]}, {"entityInformation": [[{"description": [{"entityAddress": [{"entity": [0]}]}, {"entityType": "DeviceInformation"}]}], [{"description": [{"entityAddress": [{"entity": [1]}]}, {"entityType": "GridConnectionPointOfPremises"}]}], [{"description": [{"entityAddress": [{"entity": [2]}]}, {"entityType": "ElectricityStorageSystem"}]}], [{"description": [{"entityAddress": [{"entity": [2, 1]}]}, {"entityType": "BatterySystem"}]}], [{"description": [{"entityAddress": [{"entity": [3]}]}, {"entityType": "ElectricityGenerationSystem"}]}], [{"description": [{"entityAddress": [{"entity": [3, 1]}]}, {"entityType": "PVSystem"}]}]]}, {"featureInformation": [[{"description": [{"featureAddress": [{"entity": [0]}, {"feature": 0}]}, {"featureType": "NodeManagement"}, {"role": "special"}, {"supportedFunction": [[{"function": "nodeManagementDetailedDiscoveryData"}, {"possibleOperations": [{"read": []}]}], [{"function": "nodeManagementSubscriptionRequestCall"}, {"possibleOperations": []}], [{"function": "nodeManagementSubscriptionDeleteCall"}, {"possibleOperations": []}], [{"function": "nodeManagementBindingRequestCall"}, {"possibleOperations": []}], [{"function": "nodeManagementBindingDeleteCall"}, {"possibleOperations": []}], [{"function": "nodeManagementSubscriptionData"}, {"possibleOperations": [{"read": []}]}], [{"function": "nodeManagementBindingData"}, {"possibleOperations": [{"read": []}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [0]}, {"feature": 1}]}, {"featureType": "DeviceClassification"}, {"role": "server"}, {"supportedFunction": [[{"function": "deviceClassificationManufacturerData"}, {"possibleOperations": [{"read": []}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [1]}, {"feature": 1}]}, {"featureType": "DeviceConfiguration"}, {"role": "server"}, {"supportedFunction": [[{"function": "deviceConfigurationKeyValueDescriptionListData"}, {"possibleOperations": [{"read": []}]}], [{"function": "deviceConfigurationKeyValueListData"}, {"possibleOperations": [{"read": []}]}]]}, {"description": "Energy Cockpit - Grid Connection Point - Device Configuration"}]}], [{"description": [{"featureAddress": [{"entity": [1]}, {"feature": 2}]}, {"featureType": "Measurement"}, {"specificUsage": ["Electrical"]}, {"role": "server"}, {"supportedFunction": [[{"function": "measurementDescriptionListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "measurementListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}]]}, {"description": "Energy Cockpit - Grid Connection Point"}]}], [{"description": [{"featureAddress": [{"entity": [1]}, {"feature": 3}]}, {"featureType": "ElectricalConnection"}, {"role": "server"}, {"supportedFunction": [[{"function": "electricalConnectionDescriptionListData"}, {"possibleOperations": [{"read": []}]}], [{"function": "electricalConnectionParameterDescriptionListData"}, {"possibleOperations": [{"read": []}]}]]}, {"description": "Energy Cockpit - Grid Connection Point - Electrical Connection"}]}], [{"description": [{"featureAddress": [{"entity": [2, 1]}, {"feature": 1}]}, {"featureType": "Measurement"}, {"specificUsage": ["Electrical"]}, {"role": "server"}, {"supportedFunction": [[{"function": "measurementDescriptionListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "measurementListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}]]}, {"description": "Energy Cockpit - Battery"}]}], [{"description": [{"featureAddress": [{"entity": [2, 1]}, {"feature": 2}]}, {"featureType": "ElectricalConnection"}, {"role": "server"}, {"supportedFunction": [[{"function": "electricalConnectionDescriptionListData"}, {"possibleOperations": [{"read": []}]}], [{"function": "electricalConnectionParameterDescriptionListData"}, {"possibleOperations": [{"read": []}]}]]}, {"description": "Energy Cockpit - Battery System - Electrical Connection"}]}], [{"description": [{"featureAddress": [{"entity": [3, 1]}, {"feature": 1}]}, {"featureType": "DeviceConfiguration"}, {"role": "server"}, {"supportedFunction": [[{"function": "deviceConfigurationKeyValueDescriptionListData"}, {"possibleOperations": [{"read": []}]}], [{"function": "deviceConfigurationKeyValueListData"}, {"possibleOperations": [{"read": []}]}]]}, {"description": "Energy Cockpit - PV - Device Configuration"}]}], [{"description": [{"featureAddress": [{"entity": [3, 1]}, {"feature": 2}]}, {"featureType": "Measurement"}, {"specificUsage": ["Electrical"]}, {"role": "server"}, {"supportedFunction": [[{"function": "measurementDescriptionListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "measurementListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}]]}, {"description": "Energy Cockpit - PV"}]}], [{"description": [{"featureAddress": [{"entity": [3, 1]}, {"feature": 3}]}, {"featureType": "ElectricalConnection"}, {"role": "server"}, {"supportedFunction": [[{"function": "electricalConnectionDescriptionListData"}, {"possibleOperations": [{"read": []}]}], [{"function": "electricalConnectionParameterDescriptionListData"}, {"possibleOperations": [{"read": []}]}]]}, {"description": "Energy Cockpit - PV - Electrical Connection"}]}]]}]}]]}]}]}
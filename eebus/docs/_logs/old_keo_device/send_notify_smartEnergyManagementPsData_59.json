{"datagram": [{"header": [{"specificationVersion": "1.1.0"}, {"addressSource": [{"device": "d:_i:46922_MySpineDeviceAddressHGS"}, {"entity": [1, 3]}, {"feature": 1}]}, {"addressDestination": [{"device": "d:_i:36013_1901706113"}, {"entity": [1]}, {"feature": 1}]}, {"msgCounter": 59}, {"cmdClassifier": "notify"}, {"ackRequest": true}, {"timestamp": "2018-03-12T07:47:53.622Z"}]}, {"payload": [{"cmd": [[{"smartEnergyManagementPsData": [{"nodeScheduleInformation": [{"nodeRemoteControllable": true}, {"supportsSingleSlotSchedulingOnly": true}, {"alternativesCount": 1}, {"totalSequencesCountMax": 1}, {"supportsReselection": false}]}, {"alternatives": [[{"relation": [{"alternativesId": 1}]}, {"powerSequence": [[{"description": [{"sequenceId": 2}, {"powerUnit": "W"}, {"valueSource": "empiricalValue"}]}, {"state": [{"state": "inactive"}, {"sequenceRemoteControllable": true}]}, {"scheduleConstraints": [{"earliestStartTime": "PT0S"}, {"latestEndTime": "P1D"}]}, {"operatingConstraintsInterrupt": [{"isPausable": false}, {"isStoppable": true}]}, {"operatingConstraintsDuration": [{"activeDurationMin": "PT3M"}]}, {"powerTimeSlot": [[{"schedule": [{"slotNumber": 1}, {"description": "Slot Description"}]}, {"valueList": [{"value": [[{"valueType": "powerMax"}, {"value": [{"number": 125}, {"scale": 2}]}]]}]}]]}]]}]]}]}]]}]}]}
{"datagram": [{"header": [{"specificationVersion": "1.1.0"}, {"addressSource": [{"device": "d:_i:36013_1901706113"}, {"entity": [1]}, {"feature": 1}]}, {"addressDestination": [{"device": "d:_i:46922_MySpineDeviceAddressHGS"}, {"entity": [1, 3]}, {"feature": 1}]}, {"msgCounter": 387}, {"cmdClassifier": "write"}]}, {"payload": [{"cmd": [[{"function": "smartEnergyManagementPsData"}, {"filter": [[{"cmdControl": [{"partial": []}]}]]}, {"smartEnergyManagementPsData": [{"alternatives": [[{"powerSequence": [[{"description": [{"sequenceId": 2}]}, {"schedule": [{"startTime": "PT0S"}]}]]}]]}]}]]}]}]}
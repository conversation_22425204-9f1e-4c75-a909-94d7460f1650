{"datagram": [{"header": [{"specificationVersion": "1.1.0"}, {"addressSource": [{"device": "d:_i:46922_MySpineDeviceAddressHSS"}, {"entity": [0]}, {"feature": 0}]}, {"addressDestination": [{"device": "d:_i:36013_1901706113"}, {"entity": [0]}, {"feature": 0}]}, {"msgCounter": 31}, {"msgCounterReference": 360}, {"cmdClassifier": "reply"}]}, {"payload": [{"cmd": [[{"nodeManagementDetailedDiscoveryData": [{"specificationVersionList": [{"specificationVersion": ["1.1.0"]}]}, {"deviceInformation": [{"description": [{"deviceAddress": [{"device": "d:_i:46922_MySpineDeviceAddressHSS"}]}, {"deviceType": "HeatSinkSystem"}, {"networkFeatureSet": "gateway"}, {"lastStateChange": "added"}]}]}, {"entityInformation": [[{"description": [{"entityAddress": [{"entity": [0]}]}, {"entityType": "DeviceInformation"}]}], [{"description": [{"entityAddress": [{"entity": [1]}]}, {"entityType": "DHWCircuit"}]}], [{"description": [{"entityAddress": [{"entity": [2]}]}, {"entityType": "HeatingCircuit"}]}], [{"description": [{"entityAddress": [{"entity": [2, 1]}]}, {"entityType": "HeatingZone"}]}], [{"description": [{"entityAddress": [{"entity": [2, 1, 2]}]}, {"entityType": "HVACRoom"}]}]]}, {"featureInformation": [[{"description": [{"featureAddress": [{"entity": [0]}, {"feature": 0}]}, {"featureType": "NodeManagement"}, {"role": "special"}, {"supportedFunction": [[{"function": "nodeManagementDetailedDiscoveryData"}, {"possibleOperations": [{"read": []}]}], [{"function": "nodeManagementSubscriptionRequestCall"}, {"possibleOperations": []}], [{"function": "nodeManagementBindingRequestCall"}, {"possibleOperations": []}], [{"function": "nodeManagementSubscriptionDeleteCall"}, {"possibleOperations": []}], [{"function": "nodeManagementBindingDeleteCall"}, {"possibleOperations": []}], [{"function": "nodeManagementSubscriptionData"}, {"possibleOperations": [{"read": []}]}], [{"function": "nodeManagementBindingData"}, {"possibleOperations": [{"read": []}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [0]}, {"feature": 1}]}, {"featureType": "DeviceClassification"}, {"role": "server"}, {"supportedFunction": [[{"function": "deviceClassificationManufacturerData"}, {"possibleOperations": [{"read": []}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [1]}, {"feature": 1}]}, {"featureType": "DeviceClassification"}, {"role": "server"}, {"supportedFunction": [[{"function": "deviceClassificationManufacturerData"}, {"possibleOperations": [{"read": []}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [1]}, {"feature": 2}]}, {"featureType": "Measurement"}, {"role": "server"}, {"supportedFunction": [[{"function": "measurementDescriptionListData"}, {"possibleOperations": [{"read": []}]}], [{"function": "measurementListData"}, {"possibleOperations": [{"read": []}]}], [{"function": "measurementConstraintsListData"}, {"possibleOperations": [{"read": []}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [1]}, {"feature": 3}]}, {"featureType": "Setpoint"}, {"role": "server"}, {"supportedFunction": [[{"function": "setpointDescriptionListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "setpointListData"}, {"possibleOperations": [{"read": [{"partial": []}]}, {"write": [{"partial": []}]}]}], [{"function": "setpointConstraintsListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [1]}, {"feature": 4}]}, {"featureType": "HVAC"}, {"role": "server"}, {"supportedFunction": [[{"function": "hvacSystemFunctionDescriptionListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "hvacSystemFunctionOperationModeRelationListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "hvacSystemFunctionSetpointRelationListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "hvacSystemFunctionListData"}, {"possibleOperations": [{"read": [{"partial": []}]}, {"write": [{"partial": []}]}]}], [{"function": "hvacOperationModeDescriptionListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "hvacOverrunDescriptionListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "hvacOverrunListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [2, 1, 2]}, {"feature": 1}]}, {"featureType": "DeviceClassification"}, {"role": "server"}, {"supportedFunction": [[{"function": "deviceClassificationManufacturerData"}, {"possibleOperations": [{"read": []}]}], [{"function": "deviceClassificationUserData"}, {"possibleOperations": [{"read": []}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [2, 1, 2]}, {"feature": 3}]}, {"featureType": "Setpoint"}, {"role": "server"}, {"supportedFunction": [[{"function": "setpointDescriptionListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "setpointListData"}, {"possibleOperations": [{"read": [{"partial": []}]}, {"write": [{"partial": []}]}]}], [{"function": "setpointConstraintsListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [2, 1, 2]}, {"feature": 4}]}, {"featureType": "HVAC"}, {"role": "server"}, {"supportedFunction": [[{"function": "hvacSystemFunctionDescriptionListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "hvacSystemFunctionOperationModeRelationListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "hvacSystemFunctionSetpointRelationListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}], [{"function": "hvacSystemFunctionListData"}, {"possibleOperations": [{"read": [{"partial": []}]}, {"write": [{"partial": []}]}]}], [{"function": "hvacOperationModeDescriptionListData"}, {"possibleOperations": [{"read": [{"partial": []}]}]}]]}]}], [{"description": [{"featureAddress": [{"entity": [2, 1, 2]}, {"feature": 5}]}, {"featureType": "Measurement"}, {"role": "server"}, {"supportedFunction": [[{"function": "measurementDescriptionListData"}, {"possibleOperations": [{"read": []}]}], [{"function": "measurementListData"}, {"possibleOperations": [{"read": []}]}], [{"function": "measurementConstraintsListData"}, {"possibleOperations": [{"read": []}]}]]}]}]]}]}]]}]}]}
use std::{
    future::pending,
    net::{IpAddr, Ipv4Addr},
    time::Duration,
};

use async_channel::{Receiver, Sender};
use async_io::Timer;
use eebus::ship::{
    init_ship_connection, Config, DeviceInfo, MessageValue, ShipConnection, ShipError, Ski, Trust,
    TrustLevel,
};
use embassy_futures::join::join;
use log::debug;
#[derive(<PERSON>lone, Debug)]
struct Duplex {
    pub tx: Sender<MessageValue>,
    pub rx: Receiver<MessageValue>,
}

impl ShipConnection for Duplex {
    async fn read_message(&self) -> Result<MessageValue, ShipError> {
        Ok(self.rx.recv().await.unwrap())
    }

    async fn write_message(&self, msg: MessageValue) -> Result<(), ShipError> {
        debug!("Sending message: {:?}", msg);
        self.tx.send(msg).await.unwrap();
        Ok(())
    }

    async fn close(&self) -> Result<(), ShipError> {
        Ok(())
    }

    fn device_info(&self) -> DeviceInfo {
        DeviceInfo {
            ip: IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1)),
            port: 1234,
            ski: Ski::from([0u8; 20]),
        }
    }
}

#[derive(Debug, Default)]
struct DummyTrust;

impl Trust for DummyTrust {
    async fn wait_trust_update(&self, _info: &DeviceInfo, from: TrustLevel) -> TrustLevel {
        if from == TrustLevel::Trust {
            return pending().await;
        }

        Timer::after(Duration::from_secs(1)).await;

        TrustLevel::Trust
    }

    async fn trust_level(&self, _info: &DeviceInfo) -> TrustLevel {
        TrustLevel::Pending
    }
}

#[test]
fn test_basic() {
    use embassy_time::Duration;

    unsafe { std::env::set_var("RUST_LOG", "debug") };
    env_logger::init();

    let (tx1, rx1) = async_channel::unbounded();
    let (tx2, rx2) = async_channel::unbounded();

    let c1 = Duplex { tx: tx1, rx: rx2 };
    let c2 = Duplex { tx: tx2, rx: rx1 };

    async_io::block_on(async {
        let trust = DummyTrust::default();
        let server = init_ship_connection(
            &c1,
            &trust,
            Config {
                cmi_timeout: Duration::from_secs(30),
            },
            true,
        );
        let client = init_ship_connection(
            &c2,
            &trust,
            Config {
                cmi_timeout: Duration::from_secs(30),
            },
            false,
        );

        let (r1, r2) = join(server, client).await;
        r1.unwrap();
        r2.unwrap();
    });
}

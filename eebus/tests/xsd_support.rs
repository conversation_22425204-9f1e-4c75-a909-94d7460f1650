use eebus::spine::types::{XsDate, XsDateTime, XsDuration, XsTime};
use nanoserde::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>};

#[test]
fn xs_date_to_string() {
    let v = XsDate::new(2025, 3, 25);
    assert_eq!(v.to_string(), "2025-03-25+00:00");
}

#[test]
fn xs_date_parse() {
    let v: XsDate = "2025-03-25+00:00".parse().unwrap();
    assert_eq!(v, XsDate::new(2025, 3, 25));
}

#[test]
fn xs_date_serialize() {
    let v = XsDate::new(2025, 3, 25);
    let s = SerJson::serialize_json(&v);
    assert_eq!(s, "\"2025-03-25+00:00\"");
}

#[test]
fn xs_date_deserialize() {
    let v: XsDate = DeJson::deserialize_json("\"2025-03-25+00:00\"").unwrap();
    assert_eq!(v, XsDate::new(2025, 3, 25));
}

#[test]
fn xs_time_to_string() {
    let mut v = XsTime::new(09, 58, 35, 740);
    v.tz_hours = 5;
    v.tz_mins = 30;
    assert_eq!(v.to_string(), "09:58:35.740+05:30");
}

#[test]
fn xs_time_parse() {
    let v: XsTime = "09:58:35.740+05:30".parse().unwrap();
    let mut e = XsTime::new(09, 58, 35, 740);
    e.tz_hours = 5;
    e.tz_mins = 30;
    assert_eq!(v, e);
}

#[test]
fn xs_time_serialize() {
    let mut v = XsTime::new(09, 58, 35, 740);
    v.tz_hours = 5;
    v.tz_mins = 30;
    let s = SerJson::serialize_json(&v);
    assert_eq!(s, "\"09:58:35.740+05:30\"");
}

#[test]
fn xs_time_deserialize() {
    let v: XsTime = DeJson::deserialize_json("\"09:58:35.740+05:30\"").unwrap();
    let mut e = XsTime::new(09, 58, 35, 740);
    e.tz_hours = 5;
    e.tz_mins = 30;
    assert_eq!(v, e);
}

#[test]
fn xs_datetime_to_string() {
    let mut v = XsDateTime::new(2025, 3, 25, 09, 58, 35, 740);
    v.tz_hours = -5;
    v.tz_mins = 30;
    assert_eq!(v.to_string(), "2025-03-25T09:58:35.740-05:30");
}

#[test]
fn xs_datetime_parse() {
    let v: XsDateTime = "2025-03-25T09:58:35.740-05:30".parse().unwrap();
    let mut e = XsDateTime::new(2025, 3, 25, 09, 58, 35, 740);
    e.tz_hours = -5;
    e.tz_mins = 30;
    assert_eq!(v, e);
}

#[test]
fn xs_datetime_serialize() {
    let mut v = XsDateTime::new(2025, 3, 25, 09, 58, 35, 740);
    v.tz_hours = -5;
    v.tz_mins = 30;
    let s = SerJson::serialize_json(&v);
    assert_eq!(s, "\"2025-03-25T09:58:35.740-05:30\"");
}

#[test]
fn xs_datetime_deserialize() {
    let v: XsDateTime = DeJson::deserialize_json("\"2025-03-25T09:58:35.740-05:30\"").unwrap();
    let mut e = XsDateTime::new(2025, 3, 25, 09, 58, 35, 740);
    e.tz_hours = -5;
    e.tz_mins = 30;
    assert_eq!(v, e);
}

#[test]
fn xs_duration_to_string() {
    let mut v = XsDuration::new(0, 0, 63, 55, 91, 4, 485);
    v.sign = -1;
    assert_eq!(v.to_string(), "-P0Y0M63DT55H91M4.485S");
}

#[test]
fn xs_duration_parse() {
    let mut v: XsDuration = "-P0Y0M63DT55H91M4.485S".parse().unwrap();
    assert_eq!(v.sign, -1);
    v.sign = 1;
    assert_eq!(v, XsDuration::new(0, 0, 63, 55, 91, 4, 485));
    let v: XsDuration = "P2Y0M63DT55H91M4S".parse().unwrap();
    assert_eq!(v, XsDuration::new(2, 0, 63, 55, 91, 4, 0));
    let v: XsDuration = "P5MT3M".parse().unwrap();
    assert_eq!(v, XsDuration::new(0, 5, 0, 0, 3, 0, 0));
    let v: XsDuration = "PT0S".parse().unwrap();
    assert_eq!(v, XsDuration::new(0, 0, 0, 0, 0, 0, 0));
    let v: XsDuration = "P0D".parse().unwrap();
    assert_eq!(v, XsDuration::new(0, 0, 0, 0, 0, 0, 0));
}

#[test]
fn xs_duration_serialize() {
    let mut v = XsDuration::new(0, 0, 63, 55, 91, 4, 485);
    v.sign = -1;
    let s = SerJson::serialize_json(&v);
    assert_eq!(s, "\"-P0Y0M63DT55H91M4.485S\"");
}

#[test]
fn xs_duration_deserialize() {
    let mut v: XsDuration = DeJson::deserialize_json("\"-P0Y0M63DT55H91M4.485S\"").unwrap();
    assert_eq!(v.sign, -1);
    v.sign = 1;
    assert_eq!(v, XsDuration::new(0, 0, 63, 55, 91, 4, 485));
    let v: XsDuration = DeJson::deserialize_json("\"P2Y0M63DT55H91M4S\"").unwrap();
    assert_eq!(v, XsDuration::new(2, 0, 63, 55, 91, 4, 0));
    let v: XsDuration = DeJson::deserialize_json("\"P5MT3M\"").unwrap();
    assert_eq!(v, XsDuration::new(0, 5, 0, 0, 3, 0, 0));
    let v: XsDuration = DeJson::deserialize_json("\"PT0S\"").unwrap();
    assert_eq!(v, XsDuration::new(0, 0, 0, 0, 0, 0, 0));
    let v: XsDuration = DeJson::deserialize_json("\"P0D\"").unwrap();
    assert_eq!(v, XsDuration::new(0, 0, 0, 0, 0, 0, 0));
}

use std::{
    net::{IpAddr, Ipv4Addr},
    sync::Arc,
    time::Duration,
};

use async_channel;
use async_executor::Executor;
use async_io::Timer;
use async_lock::Mutex;
use eebus::ship::*;

#[derive(Clone)]
struct MockTrust {
    level: Arc<Mutex<TrustLevel>>,
    update_tx: async_channel::Sender<TrustLevel>,
    update_rx: Arc<Mutex<async_channel::Receiver<TrustLevel>>>,
}

impl MockTrust {
    fn new(initial_level: TrustLevel) -> Self {
        let (tx, rx) = async_channel::bounded(1);
        Self {
            level: Arc::new(Mutex::new(initial_level)),
            update_tx: tx,
            update_rx: Arc::new(Mutex::new(rx)),
        }
    }

    async fn set_trust_level(&self, new_level: TrustLevel) {
        *self.level.lock().await = new_level;
        self.update_tx.send(new_level).await.unwrap();
    }
}

impl Trust for MockTrust {
    async fn wait_trust_update(&self, info: &DeviceInfo, from: TrustLevel) -> TrustLevel {
        if from != self.trust_level(info).await {
            return self.trust_level(info).await;
        }
        self.update_rx.lock().await.recv().await.unwrap()
    }

    async fn trust_level(&self, _info: &DeviceInfo) -> TrustLevel {
        self.level.lock().await.clone()
    }
}

#[derive(Clone)]
struct MockConnection {
    tx: async_channel::Sender<MessageValue>,
    rx: Arc<Mutex<async_channel::Receiver<MessageValue>>>,
}

impl MockConnection {
    fn new() -> (Self, Self) {
        let (tx1, rx1) = async_channel::bounded(10);
        let (tx2, rx2) = async_channel::bounded(10);
        let conn1 = Self {
            tx: tx1,
            rx: Arc::new(Mutex::new(rx2)),
        };
        let conn2 = Self {
            tx: tx2,
            rx: Arc::new(Mutex::new(rx1)),
        };
        (conn1, conn2)
    }
}

impl ShipConnection for MockConnection {
    async fn read_message(&self) -> Result<MessageValue, ShipError> {
        Ok(self.rx.lock().await.recv().await.unwrap())
    }

    async fn write_message(&self, msg: MessageValue) -> Result<(), ShipError> {
        self.tx.send(msg).await.unwrap();
        Ok(())
    }

    async fn close(&self) -> Result<(), ShipError> {
        Ok(())
    }

    fn device_info(&self) -> DeviceInfo {
        DeviceInfo {
            ip: IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1)),
            port: 1234,
            ski: Ski::from([0u8; 20]),
        }
    }
}

#[test]
fn test_handle_hello_successful() {
    let ex = Executor::new();

    let (conn1, conn2) = MockConnection::new();
    let trust1 = MockTrust::new(TrustLevel::Pending);
    let trust2 = MockTrust::new(TrustLevel::Pending);

    // Start both connections
    let handle1 = {
        let conn1 = conn1.clone();
        let trust1 = trust1.clone();
        ex.spawn(async move { handle_hello(&conn1, &trust1).await })
    };
    let handle2 = {
        let conn2 = conn2.clone();
        let trust2 = trust2.clone();
        ex.spawn(async move { handle_hello(&conn2, &trust2).await })
    };
    let task = ex.spawn(async move {
        // Wait a bit for initial messages to be exchanged
        Timer::after(Duration::from_millis(100)).await;

        // Set both to trust
        trust1.set_trust_level(TrustLevel::Trust).await;
        trust2.set_trust_level(TrustLevel::Trust).await;

        // Both should complete successfully
        assert!(handle1.await.is_ok());
        assert!(handle2.await.is_ok());
    });

    async_io::block_on(ex.run(task));
}

#[test]
fn test_handle_hello_abort() {
    let ex = Executor::new();

    let (conn1, conn2) = MockConnection::new();
    let trust1 = MockTrust::new(TrustLevel::Pending);
    let trust2 = MockTrust::new(TrustLevel::Pending);

    // Start both connections
    let handle1 = {
        let conn1 = conn1.clone();
        let trust1 = trust1.clone();
        ex.spawn(async move { handle_hello(&conn1, &trust1).await })
    };
    let handle2 = {
        let conn2 = conn2.clone();
        let trust2 = trust2.clone();
        ex.spawn(async move { handle_hello(&conn2, &trust2).await })
    };
    let task = ex.spawn(async move {
        // Wait a bit for initial messages
        Timer::after(Duration::from_millis(100)).await;

        // Set one to distrust
        trust1.set_trust_level(TrustLevel::Distrust).await;

        // Both should fail
        assert!(matches!(handle1.await, Err(ShipError::UnexpectedMessage)));
        assert!(matches!(handle2.await, Err(ShipError::UnexpectedMessage)));
    });

    async_io::block_on(ex.run(task));
}

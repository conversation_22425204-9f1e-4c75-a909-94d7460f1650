<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.3.0 (final)
    2023-08-31
    Copyright (c) 2023 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/v1" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://docs.eebus.org/spine/xsd/v1" version="1.3.0"
    blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2023 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_CommonDataTypes.xsd"/>
    <xs:simpleType name="VendorStateCodeType">
        <xs:restriction base="xs:string"/>
    </xs:simpleType>
    <xs:simpleType name="LastErrorCodeType">
        <xs:restriction base="xs:string"/>
    </xs:simpleType>
    <xs:simpleType name="DeviceDiagnosisOperatingStateType">
        <xs:union memberTypes="ns_p:DeviceDiagnosisOperatingStateEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="DeviceDiagnosisOperatingStateEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="normalOperation"/>
            <xs:enumeration value="standby"/>
            <xs:enumeration value="failure"/>
            <xs:enumeration value="serviceNeeded"/>
            <xs:enumeration value="overrideDetected"/>
            <xs:enumeration value="inAlarm"/>
            <xs:enumeration value="notReachable"/>
            <xs:enumeration value="finished"/>
            <xs:enumeration value="temporarilyNotReady"/>
            <xs:enumeration value="off"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="PowerSupplyConditionType">
        <xs:union memberTypes="ns_p:PowerSupplyConditionEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="PowerSupplyConditionEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="good"/>
            <xs:enumeration value="low"/>
            <xs:enumeration value="critical"/>
            <xs:enumeration value="unknown"/>
            <xs:enumeration value="error"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="DeviceDiagnosisStateDataType">
        <xs:sequence>
            <xs:element name="timestamp" type="ns_p:AbsoluteOrRelativeTimeType" minOccurs="0"/>
            <xs:element name="operatingState" type="ns_p:DeviceDiagnosisOperatingStateType" minOccurs="0"/>
            <xs:element name="vendorStateCode" type="ns_p:VendorStateCodeType" minOccurs="0"/>
            <xs:element name="lastErrorCode" type="ns_p:LastErrorCodeType" minOccurs="0"/>
            <xs:element name="upTime" type="xs:duration" minOccurs="0"/>
            <xs:element name="totalUpTime" type="xs:duration" minOccurs="0"/>
            <xs:element name="powerSupplyCondition" minOccurs="0" type="ns_p:PowerSupplyConditionType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="deviceDiagnosisStateData" type="ns_p:DeviceDiagnosisStateDataType"/>
    <xs:complexType name="DeviceDiagnosisStateDataElementsType">
        <xs:sequence>
            <xs:element name="timestamp" minOccurs="0" type="ns_p:ElementTagType"/>
            <xs:element name="operatingState" minOccurs="0" type="ns_p:ElementTagType"/>
            <xs:element name="vendorStateCode" minOccurs="0" type="ns_p:ElementTagType"/>
            <xs:element name="lastErrorCode" minOccurs="0" type="ns_p:ElementTagType"/>
            <xs:element name="upTime" minOccurs="0" type="ns_p:ElementTagType"/>
            <xs:element name="totalUpTime" minOccurs="0" type="ns_p:ElementTagType"/>
            <xs:element name="powerSupplyCondition" minOccurs="0" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="deviceDiagnosisStateDataElements" type="ns_p:DeviceDiagnosisStateDataElementsType"/>
    <xs:complexType name="DeviceDiagnosisHeartbeatDataType">
        <xs:sequence>
            <xs:element name="timestamp" type="ns_p:AbsoluteOrRelativeTimeType" minOccurs="0"/>
            <xs:element name="heartbeatCounter" type="xs:unsignedLong" minOccurs="0"/>
            <xs:element name="heartbeatTimeout" type="xs:duration" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="deviceDiagnosisHeartbeatData" type="ns_p:DeviceDiagnosisHeartbeatDataType"/>
    <xs:complexType name="DeviceDiagnosisHeartbeatDataElementsType">
        <xs:sequence>
            <xs:element name="timestamp" minOccurs="0" type="ns_p:ElementTagType"/>
            <xs:element name="heartbeatCounter" minOccurs="0" type="ns_p:ElementTagType"/>
            <xs:element name="heartbeatTimeout" minOccurs="0" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="deviceDiagnosisHeartbeatDataElements" type="ns_p:DeviceDiagnosisHeartbeatDataElementsType"/>
    <xs:complexType name="DeviceDiagnosisServiceDataType">
        <xs:sequence>
            <xs:element name="timestamp" type="ns_p:AbsoluteOrRelativeTimeType" minOccurs="0"/>
            <xs:element name="installationTime" type="ns_p:AbsoluteOrRelativeTimeType" minOccurs="0"/>
            <xs:element name="bootCounter" type="xs:unsignedLong" minOccurs="0"/>
            <xs:element name="nextService" type="ns_p:AbsoluteOrRelativeTimeType" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="deviceDiagnosisServiceData" type="ns_p:DeviceDiagnosisServiceDataType"/>
    <xs:complexType name="DeviceDiagnosisServiceDataElementsType">
        <xs:sequence>
            <xs:element name="timestamp" minOccurs="0" type="ns_p:ElementTagType"/>
            <xs:element name="installationTime" minOccurs="0" type="ns_p:ElementTagType"/>
            <xs:element name="bootCounter" minOccurs="0" type="ns_p:ElementTagType"/>
            <xs:element name="nextService" minOccurs="0" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="deviceDiagnosisServiceDataElements" type="ns_p:DeviceDiagnosisServiceDataElementsType"/>
</xs:schema>

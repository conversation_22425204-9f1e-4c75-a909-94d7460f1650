<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.3.0 (final)
    2023-08-31
    Copyright (c) 2023 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/v1" xmlns:xs="http://www.w3.org/2001/XMLSchema"
    targetNamespace="http://docs.eebus.org/spine/xsd/v1" version="1.3.0" blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2023 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_CommonDataTypes.xsd"/>
    <xs:complexType name="SpecificationVersionDataType">
        <xs:simpleContent>
            <xs:extension base="ns_p:SpecificationVersionType"/>
        </xs:simpleContent>
    </xs:complexType>
    <xs:element name="specificationVersionData" type="ns_p:SpecificationVersionDataType"/>
    <xs:complexType name="SpecificationVersionDataElementsType"/>
    <xs:element name="specificationVersionDataElements" type="ns_p:SpecificationVersionDataElementsType"/>
    <xs:complexType name="SpecificationVersionListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:specificationVersionData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="specificationVersionListData" type="ns_p:SpecificationVersionListDataType"/>
    <xs:complexType name="SpecificationVersionListDataSelectorsType"/>
    <xs:element name="specificationVersionListDataSelectors" type="ns_p:SpecificationVersionListDataSelectorsType"/>
</xs:schema>

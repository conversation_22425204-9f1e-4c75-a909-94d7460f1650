<?xml version="1.0" encoding="UTF-8" ?>
<xs:schema
    xmlns:ns_p="http://docs.eebus.org/spine/xsd/v1"
    xmlns:xs="http://www.w3.org/2001/XMLSchema"
    targetNamespace="http://docs.eebus.org/spine/xsd/v1"
    version="1.3.0"
    blockDefault="#all"
    elementFormDefault="qualified"
>
    <!-- EEBus_SPINE_TS_CommandFrame.xsd - - - - - - - - - - - - - - - - - - -->

    <xs:simpleType name="MsgCounterType">
        <xs:restriction base="xs:unsignedLong"> </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CmdClassifierType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="read" />
            <xs:enumeration value="reply" />
            <xs:enumeration value="notify" />
            <xs:enumeration value="write" />
            <xs:enumeration value="call" />
            <xs:enumeration value="result" />
        </xs:restriction>
    </xs:simpleType>
    <xs:element name="cmdClassifier" type="ns_p:CmdClassifierType" />
    <xs:element name="manufacturerSpecificExtension" type="xs:hexBinary" />
    <xs:element name="lastUpdateAt" type="ns_p:AbsoluteOrRelativeTimeType" />
    <xs:element name="function" type="ns_p:FunctionType" />
    <xs:simpleType name="FilterIdType">
        <xs:restriction base="xs:unsignedInt" />
    </xs:simpleType>
    <xs:element name="filter" type="ns_p:FilterType" />
    <xs:element name="cmdControl" type="ns_p:CmdControlType" />
</xs:schema>

<?xml version="1.0" encoding="UTF-8"?>
<!--
    Smart Premises Interoperable Neutral-Message Exchange (SPINE)
    Version 1.3.0 (final)
    2023-08-31
    Copyright (c) 2023 EEBus Initiative e.V.  All Rights Reserved.
    Source: https://www.eebus.org/en/specifications/
-->
<xs:schema xmlns:ns_p="http://docs.eebus.org/spine/xsd/v1" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://docs.eebus.org/spine/xsd/v1" version="1.3.0"
    blockDefault="#all" elementFormDefault="qualified">
    <xs:annotation>
        <xs:documentation>EEBus SPINE Specification schema. Copyright 2023 EEBus Initiative e.V.  All rights reserved.</xs:documentation>
    </xs:annotation>
    <xs:include schemaLocation="EEBus_SPINE_TS_CommonDataTypes.xsd"/>
    <xs:include schemaLocation="EEBus_SPINE_TS_Threshold.xsd"/>
    <xs:simpleType name="MeasurementIdType">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>
    <xs:simpleType name="MeasurementTypeType">
        <xs:union memberTypes="ns_p:MeasurementTypeEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="MeasurementTypeEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="acceleration"/>
            <xs:enumeration value="angle"/>
            <xs:enumeration value="angularVelocity"/>
            <xs:enumeration value="area"/>
            <xs:enumeration value="atmosphericPressure"/>
            <xs:enumeration value="capacity"/>
            <xs:enumeration value="concentration"/>
            <xs:enumeration value="count"/>
            <xs:enumeration value="current"/>
            <xs:enumeration value="density"/>
            <xs:enumeration value="distance"/>
            <xs:enumeration value="electricField"/>
            <xs:enumeration value="energy"/>
            <xs:enumeration value="force"/>
            <xs:enumeration value="frequency"/>
            <xs:enumeration value="harmonicDistortion"/>
            <xs:enumeration value="heat"/>
            <xs:enumeration value="heatFlux"/>
            <xs:enumeration value="illuminance"/>
            <xs:enumeration value="impulse"/>
            <xs:enumeration value="level"/>
            <xs:enumeration value="magneticField"/>
            <xs:enumeration value="mass"/>
            <xs:enumeration value="massFlow"/>
            <xs:enumeration value="particles"/>
            <xs:enumeration value="percentage"/>
            <xs:enumeration value="power"/>
            <xs:enumeration value="powerFactor"/>
            <xs:enumeration value="pressure"/>
            <xs:enumeration value="radonActivity"/>
            <xs:enumeration value="relativeHumidity"/>
            <xs:enumeration value="resistance"/>
            <xs:enumeration value="solarRadiation"/>
            <xs:enumeration value="speed"/>
            <xs:enumeration value="temperature"/>
            <xs:enumeration value="time"/>
            <xs:enumeration value="torque"/>
            <xs:enumeration value="unknown"/>
            <xs:enumeration value="velocity"/>
            <xs:enumeration value="voltage"/>
            <xs:enumeration value="volume"/>
            <xs:enumeration value="volumetricFlow"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="MeasurementValueTypeType">
        <xs:union memberTypes="ns_p:MeasurementValueTypeEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="MeasurementValueTypeEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="value"/>
            <xs:enumeration value="averageValue"/>
            <xs:enumeration value="minValue"/>
            <xs:enumeration value="maxValue"/>
            <xs:enumeration value="standardDeviation"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="MeasurementValueSourceType">
        <xs:union memberTypes="ns_p:MeasurementValueSourceEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="MeasurementValueSourceEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="measuredValue"/>
            <xs:enumeration value="calculatedValue"/>
            <xs:enumeration value="empiricalValue"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="MeasurementValueTendencyType">
        <xs:union memberTypes="ns_p:MeasurementValueTendencyEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="MeasurementValueTendencyEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="rising"/>
            <xs:enumeration value="stable"/>
            <xs:enumeration value="falling"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="MeasurementValueStateType">
        <xs:union memberTypes="ns_p:MeasurementValueStateEnumType ns_p:EnumExtendType"/>
    </xs:simpleType>
    <xs:simpleType name="MeasurementValueStateEnumType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="normal"/>
            <xs:enumeration value="outOfRange"/>
            <xs:enumeration value="error"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="MeasurementDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:MeasurementIdType"/>
            <xs:element minOccurs="0" name="valueType" type="ns_p:MeasurementValueTypeType"/>
            <xs:element minOccurs="0" name="timestamp" type="ns_p:AbsoluteOrRelativeTimeType"/>
            <xs:element minOccurs="0" name="value" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="evaluationPeriod" type="ns_p:TimePeriodType"/>
            <xs:element minOccurs="0" name="valueSource" type="ns_p:MeasurementValueSourceType"/>
            <xs:element minOccurs="0" name="valueTendency" type="ns_p:MeasurementValueTendencyType"/>
            <xs:element minOccurs="0" name="valueState" type="ns_p:MeasurementValueStateType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementData" type="ns_p:MeasurementDataType"/>
    <xs:complexType name="MeasurementDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="valueType" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="timestamp" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="value" type="ns_p:ScaledNumberElementsType"/>
            <xs:element minOccurs="0" name="evaluationPeriod" type="ns_p:TimePeriodElementsType"/>
            <xs:element minOccurs="0" name="valueSource" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="valueTendency" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="valueState" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementDataElements" type="ns_p:MeasurementDataElementsType"/>
    <xs:complexType name="MeasurementListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:measurementData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementListData" type="ns_p:MeasurementListDataType"/>
    <xs:complexType name="MeasurementListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:MeasurementIdType"/>
            <xs:element minOccurs="0" name="valueType" type="ns_p:MeasurementValueTypeType"/>
            <xs:element minOccurs="0" name="timestampInterval" type="ns_p:TimestampIntervalType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementListDataSelectors" type="ns_p:MeasurementListDataSelectorsType"/>
    <xs:complexType name="MeasurementSeriesDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:MeasurementIdType"/>
            <xs:element minOccurs="0" name="valueType" type="ns_p:MeasurementValueTypeType"/>
            <xs:element minOccurs="0" name="timestamp" type="ns_p:AbsoluteOrRelativeTimeType"/>
            <xs:element minOccurs="0" name="value" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="evaluationPeriod" type="ns_p:TimePeriodType"/>
            <xs:element minOccurs="0" name="valueSource" type="ns_p:MeasurementValueSourceType"/>
            <xs:element minOccurs="0" name="valueTendency" type="ns_p:MeasurementValueTendencyType"/>
            <xs:element minOccurs="0" name="valueState" type="ns_p:MeasurementValueStateType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementSeriesData" type="ns_p:MeasurementSeriesDataType"/>
    <xs:complexType name="MeasurementSeriesDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="valueType" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="timestamp" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="value" type="ns_p:ScaledNumberElementsType"/>
            <xs:element minOccurs="0" name="evaluationPeriod" type="ns_p:TimePeriodElementsType"/>
            <xs:element minOccurs="0" name="valueSource" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="valueTendency" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="valueState" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementSeriesDataElements" type="ns_p:MeasurementSeriesDataElementsType"/>
    <xs:complexType name="MeasurementSeriesListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:measurementSeriesData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementSeriesListData" type="ns_p:MeasurementSeriesListDataType"/>
    <xs:complexType name="MeasurementSeriesListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:MeasurementIdType"/>
            <xs:element minOccurs="0" name="valueType" type="ns_p:MeasurementValueTypeType"/>
            <xs:element minOccurs="0" name="timestampInterval" type="ns_p:TimestampIntervalType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementSeriesListDataSelectors" type="ns_p:MeasurementSeriesListDataSelectorsType"/>
    <xs:complexType name="MeasurementConstraintsDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:MeasurementIdType"/>
            <xs:element minOccurs="0" name="valueRangeMin" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="valueRangeMax" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="valueStepSize" type="ns_p:ScaledNumberType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementConstraintsData" type="ns_p:MeasurementConstraintsDataType"/>
    <xs:complexType name="MeasurementConstraintsDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="valueRangeMin" type="ns_p:ScaledNumberElementsType"/>
            <xs:element minOccurs="0" name="valueRangeMax" type="ns_p:ScaledNumberElementsType"/>
            <xs:element minOccurs="0" name="valueStepSize" type="ns_p:ScaledNumberElementsType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementConstraintsDataElements" type="ns_p:MeasurementConstraintsDataElementsType"/>
    <xs:complexType name="MeasurementConstraintsListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:measurementConstraintsData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementConstraintsListData" type="ns_p:MeasurementConstraintsListDataType"/>
    <xs:complexType name="MeasurementConstraintsListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:MeasurementIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementConstraintsListDataSelectors" type="ns_p:MeasurementConstraintsListDataSelectorsType"/>
    <xs:complexType name="MeasurementDescriptionDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:MeasurementIdType"/>
            <xs:element minOccurs="0" name="measurementType" type="ns_p:MeasurementTypeType"/>
            <xs:element minOccurs="0" name="commodityType" type="ns_p:CommodityTypeType"/>
            <xs:element minOccurs="0" name="unit" type="ns_p:UnitOfMeasurementType"/>
            <xs:element minOccurs="0" name="calibrationValue" type="ns_p:ScaledNumberType"/>
            <xs:element minOccurs="0" name="scopeType" type="ns_p:ScopeTypeType"/>
            <xs:element minOccurs="0" name="label" type="ns_p:LabelType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:DescriptionType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementDescriptionData" type="ns_p:MeasurementDescriptionDataType"/>
    <xs:complexType name="MeasurementDescriptionDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="measurementType" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="commodityType" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="unit" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="calibrationValue" type="ns_p:ScaledNumberElementsType"/>
            <xs:element minOccurs="0" name="scopeType" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="label" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="description" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementDescriptionDataElements" type="ns_p:MeasurementDescriptionDataElementsType"/>
    <xs:complexType name="MeasurementDescriptionListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:measurementDescriptionData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementDescriptionListData" type="ns_p:MeasurementDescriptionListDataType"/>
    <xs:complexType name="MeasurementDescriptionListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:MeasurementIdType"/>
            <xs:element minOccurs="0" name="measurementType" type="ns_p:MeasurementTypeType"/>
            <xs:element minOccurs="0" name="commodityType" type="ns_p:CommodityTypeType"/>
            <xs:element minOccurs="0" name="scopeType" type="ns_p:ScopeTypeType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementDescriptionListDataSelectors" type="ns_p:MeasurementDescriptionListDataSelectorsType"/>
    <xs:complexType name="MeasurementThresholdRelationDataType">
        <xs:sequence>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:MeasurementIdType"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="thresholdId" type="ns_p:ThresholdIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementThresholdRelationData" type="ns_p:MeasurementThresholdRelationDataType"/>
    <xs:complexType name="MeasurementThresholdRelationDataElementsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:ElementTagType"/>
            <xs:element minOccurs="0" name="thresholdId" type="ns_p:ElementTagType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementThresholdRelationDataElements" type="ns_p:MeasurementThresholdRelationDataElementsType"/>
    <xs:complexType name="MeasurementThresholdRelationListDataType">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" ref="ns_p:measurementThresholdRelationData"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementThresholdRelationListData" type="ns_p:MeasurementThresholdRelationListDataType"/>
    <xs:complexType name="MeasurementThresholdRelationListDataSelectorsType">
        <xs:sequence>
            <xs:element minOccurs="0" name="measurementId" type="ns_p:MeasurementIdType"/>
            <xs:element minOccurs="0" name="thresholdId" type="ns_p:ThresholdIdType"/>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="measurementThresholdRelationListDataSelectors" type="ns_p:MeasurementThresholdRelationListDataSelectorsType"/>
</xs:schema>

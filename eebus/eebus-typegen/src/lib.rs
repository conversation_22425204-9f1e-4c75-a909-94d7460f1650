#![allow(stable_features)]
#![feature(let_chains, os_str_display)]

use std::{
    fs::File,
    io::BufWriter,
    path::{Path, PathBuf},
};

use crate::{codegen::Generator, parse::Parser};

mod codegen;
mod parse;

// TODO: check correctness of struct generation for restrictions on sequences
//       (e.g. NetworkManagementEntityDescriptionDataTypeEntityAddress)

pub fn generate_spine_xsds(output_file: impl AsRef<Path>) {
    let manifest_dir = PathBuf::from(env!("CARGO_MANIFEST_DIR"));
    let xsd_path = manifest_dir.join("xsd/spine");
    let input_files = xsd_path.read_dir().unwrap().map(|e| e.unwrap().path());
    generate_from_files(
        input_files,
        output_file,
        &["UnitOfMeasurementEnumType"],
        &["Type", "Enum", "Type"],
        Some(&["datagram"]),
    );
}

pub fn generate_ship_xsds(output_file: impl AsRef<Path>) {
    let manifest_dir = PathBuf::from(env!("CARGO_MANIFEST_DIR"));
    let xsd_path = manifest_dir.join("xsd/ship");
    let input_files = xsd_path.read_dir().unwrap().map(|e| e.unwrap().path());
    generate_from_files(
        input_files,
        output_file,
        &[],
        &["Type", "Enum", "Type"],
        Some(&[]),
    );
}

pub fn generate_from_files(
    input_files: impl IntoIterator<Item = impl AsRef<Path>>,
    output_file: impl AsRef<Path>,
    omissions: &[&str],
    trim_end: &[&str],
    top_level: Option<&[&str]>,
) {
    let parser = Parser::parse_files(input_files, trim_end);
    let output_file = File::create(output_file).expect("failed to open output file");
    let mut output_file = BufWriter::new(output_file);
    Generator::generate(&parser, &mut output_file, omissions, trim_end, top_level);
}

pub fn generate_from_str(xsd: &str, output_file: impl AsRef<Path>) {
    let parser = Parser::parse_str(xsd, &[]);
    let output_file = File::create(output_file).expect("failed to open output file");
    let mut output_file = BufWriter::new(output_file);
    Generator::generate(
        &parser,
        &mut output_file,
        &[],
        &["Type", "Enum", "Type"],
        None,
    );
}

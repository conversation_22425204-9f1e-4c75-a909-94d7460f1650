use std::{
    error::<PERSON>rror,
    fs::File,
    io::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>},
    path::Path,
};

use xml::{attribute::OwnedAttribute, reader::XmlEvent, EventReader};

pub struct Parser<'a, R: Read> {
    current_file: Option<String>,
    parser: Option<EventReader<R>>,
    xml_path: Vec<String>,
    trim_end: &'a [&'a str],
    pub simple_types: Vec<SimpleType>,
    pub complex_types: Vec<ComplexType>,
    pub global_elements: Vec<Element>,
}

impl<'a> Parser<'a, BufReader<File>> {
    pub fn parse_files(
        input_files: impl IntoIterator<Item = impl AsRef<Path>>,
        trim_end: &'a [&'a str],
    ) -> Self {
        let mut this = Self {
            current_file: None,
            parser: None,
            xml_path: Vec::new(),
            trim_end,
            simple_types: Vec::new(),
            complex_types: Vec::new(),
            global_elements: Vec::new(),
        };

        for file_path in input_files {
            let file = File::open(&file_path).expect("failed to open file");
            let file = BufReader::new(file);
            this.parser = Some(EventReader::new(file));
            let file_name = file_path.as_ref().file_name().unwrap();
            this.current_file = Some(file_name.display().to_string());
            this.parse_xsd();
        }

        this.clean_up_enum_extends();

        this
    }
}

impl<'a, 's> Parser<'a, Cursor<&'s str>> {
    pub fn parse_str(input_str: &'s str, trim_end: &'a [&'a str]) -> Self {
        let mut this = Self {
            current_file: Some("input_str".into()),
            parser: Some(EventReader::new(Cursor::new(input_str))),
            xml_path: Vec::new(),
            trim_end,
            simple_types: Vec::new(),
            complex_types: Vec::new(),
            global_elements: Vec::new(),
        };
        this.parse_xsd();
        this.clean_up_enum_extends();
        this
    }
}

impl<R: Read> Parser<'_, R> {
    fn parse_xsd(&mut self) {
        self.consume_xsd_start();
        loop {
            match self.next() {
                XmlEvent::StartElement {
                    name, attributes, ..
                } => self.handle_element(&name.local_name, &attributes),
                XmlEvent::EndElement { name } if name.local_name == "schema" => break,
                other => self.panic_wrong_xml("StartElement or EndElement", &other),
            }
        }
        self.consume_xsd_end();
    }

    fn handle_element(&mut self, name: &str, attributes: &[OwnedAttribute]) {
        match name {
            "annotation" | "include" => self.ignore_element(),
            "complexType" => {
                let entry = self.complex_type(attributes);
                self.complex_types.push(entry)
            }
            "simpleType" => {
                let entry = self.simple_type(attributes);
                self.simple_types.push(entry)
            }
            "element" => {
                let entry = self.element("", attributes);
                self.global_elements.push(entry);
            }
            _ => self.panic(format!("unexpected {name} at top level")),
        }
    }

    fn ignore_element(&mut self) {
        let mut depth = 0;
        loop {
            match self.next() {
                XmlEvent::StartElement { .. } => depth += 1,
                XmlEvent::EndElement { .. } if depth == 0 => break,
                XmlEvent::EndElement { .. } => depth -= 1,
                XmlEvent::Characters(_) => continue,
                other => self.panic(format!("unexpected event {other:?} while ignoring element")),
            }
        }
    }

    fn complex_type(&mut self, attributes: &[OwnedAttribute]) -> ComplexType {
        let Some(type_name) = get_attr(attributes, "name") else {
            self.panic("missing name attribute on complex type");
        };
        self.nested_complex_type(type_name)
    }

    fn nested_complex_type(&mut self, type_name: &str) -> ComplexType {
        let data = match self.next() {
            XmlEvent::StartElement { name, .. } => {
                let data = match name.local_name.as_str() {
                    "sequence" => ComplexTypeData::Sequence(self.sequence(type_name)),
                    "complexContent" => ComplexTypeData::ComplexContent(self.complex_content()),
                    "simpleContent" => ComplexTypeData::SimpleContent(self.simple_content()),
                    other => self.panic(format!("unexpected complex type kind {other}")),
                };
                self.expect_end_element("complexType");
                data
            }
            XmlEvent::EndElement { .. } => ComplexTypeData::Empty,
            other => self.panic_wrong_xml("StartElement or EndElement (complex type)", &other),
        };

        ComplexType {
            type_name: type_name.to_owned(),
            data,
            file_name: self.current_file.as_ref().unwrap().clone(),
        }
    }

    fn sequence(&mut self, type_name: &str) -> Vec<Element> {
        let mut elements = Vec::new();

        loop {
            match self.next() {
                XmlEvent::StartElement {
                    name, attributes, ..
                } => match name.local_name.as_str() {
                    "element" => elements.push(self.element(type_name, &attributes)),
                    "sequence" => {
                        let sub_elements = self.sequence(type_name);
                        elements.extend_from_slice(&sub_elements);
                    }
                    other => self.panic(format!("expected element or sequence, got {other}")),
                },
                XmlEvent::EndElement { .. } => break,
                other => self.panic_wrong_xml("StartElement(element) or EndElement", &other),
            }
        }

        elements
    }

    fn element(&mut self, type_name: &str, attributes: &[OwnedAttribute]) -> Element {
        let data = match get_attr(attributes, "name") {
            Some(elem_name) => {
                let type_name = match type_name {
                    "" => format!("TopLevel_{elem_name}"),
                    other => other.to_owned(),
                };

                let type_name = match self.next() {
                    XmlEvent::StartElement { name, .. } => {
                        let mut nested_type_name = type_name.as_str();
                        for trim_end in self.trim_end {
                            nested_type_name = nested_type_name.trim_end_matches(trim_end);
                        }
                        let mut nested_type_name = format!("{nested_type_name}_{elem_name}");
                        if type_name.ends_with("ElementsType") {
                            nested_type_name.push_str("ElementsType");
                        }
                        match name.local_name.as_str() {
                            "complexType" => {
                                let ty = self.nested_complex_type(&nested_type_name);
                                self.complex_types.push(ty);
                            }
                            "simpleType" => {
                                let ty = self.nested_simple_type(&nested_type_name);
                                self.simple_types.push(ty);
                            }
                            other => self.panic(format!("unsupported nested type {other}")),
                        }
                        self.expect_end_element("element");
                        nested_type_name
                    }
                    XmlEvent::EndElement { .. } => {
                        let Some(type_name) = get_attr(attributes, "type") else {
                            self.panic("missing type attribute on named element");
                        };
                        type_name.to_owned()
                    }
                    other => self.panic_wrong_xml("nested type or end element", &other),
                };

                ElementData::Def(ElementDef {
                    name: elem_name.to_owned(),
                    type_name,
                })
            }
            None => {
                let Some(ref_name) = get_attr(attributes, "ref") else {
                    self.panic("missing name or ref attribute on element");
                };
                self.expect_end_element("element");
                ElementData::Ref(ref_name.to_owned())
            }
        };

        let min_occurs = match get_attr(attributes, "minOccurs") {
            Some(s) => match s.parse::<usize>() {
                Ok(n) => n,
                Err(e) => self.panic_err("invalid min occurs attribute on element", e),
            },
            None => 1,
        };

        let max_occurs = match get_attr(attributes, "maxOccurs") {
            Some("unbounded") => usize::MAX,
            Some(s) => match s.parse::<usize>() {
                Ok(n) => n,
                Err(e) => self.panic_err("invalid max occurs attribute on element", e),
            },
            None => 1,
        };

        Element {
            min_occurs,
            max_occurs,
            data,
        }
    }

    fn complex_content(&mut self) -> ComplexContent {
        let ret = match self.next() {
            XmlEvent::StartElement {
                name, attributes, ..
            } => match name.local_name.as_str() {
                "extension" => ComplexContent::Extension(self.complex_content_data(&attributes)),
                "restriction" => {
                    ComplexContent::Restriction(self.complex_content_data(&attributes))
                }
                other => self.panic(format!("unexpected complex content kind {other:?}")),
            },
            other => self.panic_wrong_xml("StartElement(restriction/extension)", &other),
        };

        self.expect_end_element("complex content");

        ret
    }

    fn complex_content_data(&mut self, attributes: &[OwnedAttribute]) -> ComplexContentData {
        let Some(base_type) = get_attr(attributes, "base") else {
            self.panic("missing base attribute on complex content extension/restriction");
        };

        let elements = match self.next() {
            XmlEvent::StartElement { name, .. } => {
                if name.local_name != "sequence" {
                    self.panic(
                        "complex content extension/restriction expected to only contain sequence",
                    );
                }
                let elements = self.sequence(base_type);
                self.expect_end_element("complex content extension/restriction");
                elements
            }
            XmlEvent::EndElement { .. } => Vec::new(),
            other => {
                self.panic_wrong_xml("StartElement or EndElement in complex content data", &other)
            }
        };

        ComplexContentData {
            base_type: base_type.to_owned(),
            elements,
        }
    }

    fn simple_content(&mut self) -> SimpleContent {
        let ret = match self.next() {
            XmlEvent::StartElement {
                name, attributes, ..
            } => match name.local_name.as_str() {
                "extension" => SimpleContent::Extension(self.simple_content_data(&attributes)),
                other => self.panic(format!("unexpected simple content kind {other:?}")),
            },
            other => self.panic_wrong_xml("StartElement(restriction/extension)", &other),
        };

        self.expect_end_element("simple content");

        ret
    }

    fn simple_content_data(&mut self, attributes: &[OwnedAttribute]) -> SimpleContentData {
        let Some(base_type) = get_attr(attributes, "base") else {
            self.panic("missing base attribute on simple content extension/restriction");
        };

        self.expect_end_element("extension or restriction, since contents not implemented yet");

        SimpleContentData {
            base_type: base_type.to_owned(),
        }
    }

    fn simple_type(&mut self, attributes: &[OwnedAttribute]) -> SimpleType {
        let Some(type_name) = get_attr(attributes, "name") else {
            self.panic("missing name attribute on simple type");
        };
        self.nested_simple_type(type_name)
    }

    fn nested_simple_type(&mut self, type_name: &str) -> SimpleType {
        let data = match self.next() {
            XmlEvent::StartElement {
                name, attributes, ..
            } => match name.local_name.as_str() {
                "restriction" => SimpleTypeData::Restriction(self.simple_restriction(&attributes)),
                "union" => SimpleTypeData::Union(self.union(&attributes)),
                other => self.panic(format!("unexpected simple type kind {other}")),
            },
            other => self.panic_wrong_xml("StartElement", &other),
        };

        self.expect_end_element("simpleType");

        SimpleType {
            type_name: type_name.to_owned(),
            data,
            file_name: self.current_file.as_ref().unwrap().clone(),
        }
    }

    fn simple_restriction(&mut self, attributes: &[OwnedAttribute]) -> RestrictionType {
        let Some(base_type) = get_attr(attributes, "base") else {
            self.panic("missing base attribute on simple type");
        };

        let mut restrictions = Vec::new();

        loop {
            match self.next() {
                XmlEvent::StartElement {
                    name, attributes, ..
                } => restrictions.push(self.restriction_entry(&name.local_name, &attributes)),
                XmlEvent::EndElement { .. } => break,
                other => self.panic_wrong_xml("StartElement(element) or EndElement", &other),
            }
        }

        RestrictionType {
            base_type: base_type.to_owned(),
            extended_enum: false,
            restrictions,
        }
    }

    fn restriction_entry(&mut self, key: &str, attributes: &[OwnedAttribute]) -> Restriction {
        let Some(value) = get_attr(attributes, "value") else {
            self.panic("missing value attribute on restriction entry");
        };

        self.expect_end_element("restriction entry");

        Restriction {
            key: key.to_owned(),
            value: value.to_owned(),
        }
    }

    fn union(&mut self, attributes: &[OwnedAttribute]) -> Union {
        let Some(member_types) = get_attr(attributes, "memberTypes") else {
            self.panic("missing memberTypes attribute on restriction entry");
        };

        self.expect_end_element("union");

        let member_types = member_types.split_whitespace().map(str::to_owned).collect();

        Union {
            member_types,
            extended_enum: false,
        }
    }

    fn consume_xsd_start(&mut self) {
        match self.next() {
            XmlEvent::StartDocument { .. } => (),
            other => self.panic_wrong_xml("StartDocument", &other),
        }

        match self.next() {
            XmlEvent::StartElement { name, .. } if name.local_name == "schema" => (),
            other => self.panic_wrong_xml("StartElement(xs:schema)", &other),
        }
    }

    fn consume_xsd_end(&mut self) {
        match self.next() {
            XmlEvent::EndDocument => (),
            other => self.panic_wrong_xml("EndDocument", &other),
        }
    }

    fn next(&mut self) -> XmlEvent {
        loop {
            let event = match self.parser.as_mut().unwrap().next() {
                Ok(event) => event,
                Err(err) => self.panic_err("xml parsing failed", err),
            };

            match &event {
                XmlEvent::StartElement {
                    name, attributes, ..
                } => {
                    let tag_name = name.local_name.as_str();
                    match get_attr(attributes, "name") {
                        Some(name_attr) => self.xml_path.push(format!("{tag_name}[{name_attr}]")),
                        None => self.xml_path.push(tag_name.to_owned()),
                    }
                    break event;
                }
                XmlEvent::EndElement { .. } => {
                    _ = {
                        self.xml_path.pop();
                        break event;
                    }
                }
                XmlEvent::Whitespace(_) | XmlEvent::Comment(_) => continue,
                _ => break event,
            }
        }
    }

    fn expect_end_element(&mut self, kind: impl AsRef<str>) {
        match self.next() {
            XmlEvent::EndElement { .. } => (),
            other => self.panic_wrong_xml(format!("EndElement({})", kind.as_ref()), &other),
        }
    }

    fn clean_up_enum_extends(&mut self) {
        let mut cleaned_up_simple_types = Vec::new();
        let mut extended_enums = Vec::new();

        for simple_type in &self.simple_types {
            match &simple_type.data {
                SimpleTypeData::Union(u) => {
                    match u
                        .member_types
                        .iter()
                        .position(|ty| *ty == "ns_p:EnumExtendType")
                    {
                        Some(extend_enum_idx) => {
                            if u.member_types.len() != 2 {
                                panic!(
                                    "EnumExtendType on >2 memberType union {}",
                                    simple_type.type_name
                                );
                            }

                            let enum_idx = if extend_enum_idx == 0 { 1 } else { 0 };
                            let enum_name = u.member_types[enum_idx]
                                .as_str()
                                .trim_start_matches("ns_p:");
                            extended_enums.push(enum_name);
                        }
                        None => cleaned_up_simple_types.push(simple_type.clone()),
                    }
                }
                SimpleTypeData::Restriction(_) => cleaned_up_simple_types.push(simple_type.clone()),
            }
        }

        for simple_type in &mut cleaned_up_simple_types {
            if extended_enums
                .iter()
                .any(|name| name == &simple_type.type_name)
            {
                match &mut simple_type.data {
                    SimpleTypeData::Union(u) => u.extended_enum = true,
                    SimpleTypeData::Restriction(u) => u.extended_enum = true,
                }
            }
        }

        self.simple_types = cleaned_up_simple_types;
    }

    fn panic(&self, msg: impl AsRef<str>) -> ! {
        let path = self.xml_path.join("/");
        panic!(
            "{} (xml path: {path}), (file: {})",
            msg.as_ref(),
            self.current_file.as_ref().unwrap()
        );
    }

    fn panic_err(&self, msg: impl AsRef<str>, err: impl Error) -> ! {
        let path = self.xml_path.join("/");
        panic!(
            "{} (caused by: {err:?}) (xml path: {path}) (file: {})",
            msg.as_ref(),
            self.current_file.as_ref().unwrap()
        );
    }

    fn panic_wrong_xml(&self, expected: impl AsRef<str>, got: &XmlEvent) -> ! {
        let path = self.xml_path.join("/");
        panic!(
            "expected {}, got {got:?} (xml path: {path}) (file: {})",
            expected.as_ref(),
            self.current_file.as_ref().unwrap()
        );
    }
}

fn get_attr<'a>(attrs: &'a [OwnedAttribute], name: &str) -> Option<&'a str> {
    attrs
        .iter()
        .find(|attr| attr.name.local_name == name)
        .map(|attr| attr.value.as_str())
}

#[derive(Clone, Debug)]
pub struct SimpleType {
    pub type_name: String,
    pub data: SimpleTypeData,
    pub file_name: String,
}

#[derive(Clone, Debug)]
pub enum SimpleTypeData {
    Restriction(RestrictionType),
    Union(Union),
}

#[derive(Clone, Debug)]
pub struct RestrictionType {
    pub base_type: String,
    pub restrictions: Vec<Restriction>,
    pub extended_enum: bool,
}

#[derive(Clone, Debug)]
pub struct Restriction {
    pub key: String,
    pub value: String,
}

#[derive(Clone, Debug)]
pub struct Union {
    pub member_types: Vec<String>,
    pub extended_enum: bool,
}

#[derive(Clone, Debug)]
pub struct Element {
    pub min_occurs: usize,
    pub max_occurs: usize,
    pub data: ElementData,
}

#[derive(Clone, Debug)]
pub enum ElementData {
    Def(ElementDef),
    Ref(String),
}

#[derive(Clone, Debug)]
pub struct ElementDef {
    pub name: String,
    pub type_name: String,
}

#[derive(Clone, Debug)]
pub struct ComplexType {
    pub type_name: String,
    pub data: ComplexTypeData,
    pub file_name: String,
}

#[derive(Clone, Debug)]
pub enum ComplexTypeData {
    Empty,
    Sequence(Vec<Element>),
    ComplexContent(ComplexContent),
    SimpleContent(SimpleContent),
}

#[derive(Clone, Debug)]
pub enum ComplexContent {
    Restriction(ComplexContentData),
    Extension(ComplexContentData),
}

#[derive(Clone, Debug)]
pub struct ComplexContentData {
    pub base_type: String,
    pub elements: Vec<Element>,
}

#[derive(Clone, Debug)]
pub enum SimpleContent {
    Extension(SimpleContentData),
}

#[derive(Clone, Debug)]
pub struct SimpleContentData {
    pub base_type: String,
}

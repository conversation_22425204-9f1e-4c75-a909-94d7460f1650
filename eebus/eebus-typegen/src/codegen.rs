use std::{
    cell::RefCell,
    io::{Read, Write},
};

use crate::parse::*;

macro_rules! w {
    ($self:ident) => {
        writeln!($self.writer.borrow_mut()).unwrap()
    };
    ($self:ident, $($args:tt)*) => {
        writeln!($self.writer.borrow_mut(), $($args)*).unwrap()
    };
}

pub struct Generator<'a, R: Read, W: Write> {
    parser: &'a Parser<'a, R>,
    writer: Ref<PERSON>ell<W>,
    omissions: &'a [&'a str],
    trim_end: &'a [&'a str],
    top_level: Option<&'a [&'a str]>,
}

impl<'a, R: Read, W: Write> Generator<'a, R, W> {
    pub fn generate(
        parser: &'a Parser<R>,
        writer: W,
        omissions: &'a [&'a str],
        trim_end: &'a [&'a str],
        top_level: Option<&'a [&'a str]>,
    ) {
        let writer = RefCell::new(writer);

        let this = Self {
            parser,
            writer,
            omissions,
            trim_end,
            top_level,
        };

        this.prelude();

        for complex_type in &this.parser.complex_types {
            this.complex_type(complex_type);
        }
        for simple_type in &this.parser.simple_types {
            this.simple_type(simple_type);
        }
        for global_element in &this.parser.global_elements {
            this.global_element(global_element);
        }
    }

    #[rustfmt::skip]
    fn prelude(&self) {
        w!(self, "use std::str::FromStr;");
        w!(self, "use nanoserde::{{DeJson, DeJsonErr, DeJsonState, DeJsonTok, SerJson, SerJsonState}};");
        w!(self, "use super::*;");
        w!(self);
    }

    fn complex_type(&self, ty: &ComplexType) {
        if self.is_omitted(&ty.type_name) {
            return;
        }

        match &ty.data {
            ComplexTypeData::SimpleContent(SimpleContent::Extension(sc)) => {
                let mut st = self.get_simple_type_by_name(&sc.base_type).clone();
                st.type_name = ty.type_name.clone();
                st.file_name = ty.file_name.clone();
                self.simple_type(&st);
            }
            _ => {
                let elements = self.get_complex_type_fields(ty);
                self.sequence(&ty.type_name, &elements, &ty.file_name);
            }
        }
    }

    fn sequence(&self, type_name: &str, elements: &[Element], file_name: &str) {
        let rust_type_name = self.rust_type_name(type_name);
        w!(self);
        w!(self, "/// Defined in `{file_name}`");
        if elements.is_empty() {
            w!(self, "#[derive(Copy)]");
        }
        if rust_type_name != "Data" && rust_type_name.ends_with("Data") {
            w!(self, "#[derive(Default)]");
        }
        if rust_type_name.ends_with("Elements") {
            w!(self, "#[derive(Clone, PartialEq)]");
        } else {
            w!(self, "#[derive(Clone, Debug, PartialEq)]");
        }
        w!(self, "pub struct {rust_type_name} {{");
        let mut fields = Vec::new();
        for element in elements {
            fields.push(self.element(element));
        }
        w!(self, "}}");
        self.impl_serialize_sequence(&rust_type_name, &fields);
        self.impl_deserialize_sequence(&rust_type_name, &fields);
        if rust_type_name.ends_with("Elements") {
            self.impl_elements(&rust_type_name, &fields);
        }
    }

    // returns (xml_field_name, rust_field_name, rust_type_name, min_occurs, max_occurs)
    fn element<'b>(&'b self, element: &'b Element) -> (&'b str, String, String, usize, usize) {
        let (name, type_name) = self.get_element_name_and_type(element);
        let rust_name = to_snake_case(name);
        let rust_type_name = self
            .xs_type_to_rust_type(type_name)
            .map(str::to_owned)
            .unwrap_or_else(|| self.rust_type_name(type_name));
        let format = self.xs_type_format_comment(type_name);

        let (prefix, postfix) = match (element.min_occurs, element.max_occurs) {
            (0, 0) => panic!("max occurs == min occurs == 0 not supported"),
            (0, 1) => ("Option<", ">"),
            (1, 1) => ("", ""),
            (0, usize::MAX) => ("Vec<", ">"),
            (min, usize::MAX) => {
                w!(self, "    /// minOccurs: {min}, maxOccurs: unbounded");
                ("Vec<", ">")
            }
            (min, max) => {
                w!(self, "    /// minOccurs: {min}, maxOccurs: {max}");
                ("Vec<", ">")
            }
        };

        if let Some(format) = format {
            w!(self, "    /// {type_name} format: `{format}`");
        }

        let rust_type_name = format!("{prefix}{rust_type_name}{postfix}");
        w!(self, "    pub {rust_name}: {rust_type_name},");

        (
            name,
            rust_name,
            rust_type_name,
            element.min_occurs,
            element.max_occurs,
        )
    }

    fn simple_type(&self, ty: &SimpleType) {
        if self.is_omitted(&ty.type_name) {
            return;
        }

        match &ty.data {
            SimpleTypeData::Union(u) => self.union(&ty.type_name, u, &ty.file_name),
            SimpleTypeData::Restriction(r) => self.restriction(&ty.type_name, r, &ty.file_name),
        }
    }

    fn union(&self, type_name: &str, uni: &Union, file_name: &str) {
        let rust_type_name = self.rust_type_name(type_name);
        w!(self);
        w!(self, "/// Defined in `{file_name}`");
        w!(self, "#[derive(Clone, Debug, PartialEq)]");
        w!(self, "pub enum {rust_type_name} {{");
        for member_type in &uni.member_types {
            let variant = self.rust_type_name(member_type);
            let member_type = self.xs_type_to_rust_type(member_type).unwrap_or(&variant);
            if let Some(format) = self.xs_type_format_comment(member_type) {
                w!(self, "    /// {member_type} format: `{format}`");
            }
            w!(self, "    {variant}({member_type}),");
        }
        if uni.extended_enum {
            w!(
                self,
                "    /// This variant might get masked by other variants in this enumeration which can take arbitrary data."
            );
            w!(self, "    VendorExtension(EnumExtend),");
        }
        w!(self, "}}");
        self.impl_serialize_union(&rust_type_name, uni);
        self.impl_deserialize_union(&rust_type_name, uni);
    }

    fn restriction(&self, type_name: &str, restriction: &RestrictionType, file_name: &str) {
        let is_enum = restriction
            .restrictions
            .iter()
            .any(|r| r.key == "enumeration");

        match is_enum {
            true => self.enumeration(type_name, restriction, file_name),
            false => self.restricted_type(type_name, restriction, file_name),
        }
    }

    fn enumeration(&self, type_name: &str, restriction: &RestrictionType, file_name: &str) {
        if restriction.base_type != "xs:string" {
            panic!(
                "invalid base type {} for enumeration {type_name}",
                restriction.base_type
            );
        }

        if let Some(r) = restriction
            .restrictions
            .iter()
            .find(|r| r.key != "enumeration")
        {
            panic!("invalid restriction {} for enumeration {type_name}", r.key);
        }

        let rust_type_name = self.rust_type_name(type_name);

        w!(self);
        w!(self, "/// Defined in `{file_name}`");
        if !restriction.extended_enum {
            w!(self, "#[derive(Copy)]");
        }
        w!(self, "#[derive(Clone, Debug, Eq, PartialEq)]");
        w!(self, "pub enum {rust_type_name} {{");
        for restriction in &restriction.restrictions {
            let variant = to_pascal_case(&restriction.value);
            w!(self, "    {variant},");
        }
        if restriction.extended_enum {
            w!(self, "    VendorExtension(EnumExtend),");
        }
        w!(self, "}}");
        self.impl_serialize_enum(&rust_type_name, restriction);
        self.impl_deserialize_enum(&rust_type_name, restriction);
    }

    fn restricted_type(&self, type_name: &str, restriction: &RestrictionType, file_name: &str) {
        let rust_type_name = self.rust_type_name(type_name);
        let rust_base_type = match self.xs_type_to_rust_type(&restriction.base_type) {
            Some(rust_base_type) => rust_base_type.to_owned(),
            None => self.rust_type_name(&restriction.base_type),
        };
        let format = self.xs_type_format_comment(&restriction.base_type);

        w!(self);
        w!(self, "/// Defined in `{file_name}`");
        if let Some(format) = format {
            w!(self, "///");
            w!(self, "/// {} format: `{format}`", restriction.base_type);
        }
        if !restriction.restrictions.is_empty() {
            w!(self, "///");
            w!(self, "/// Restrictions:");
        }
        for restriction in &restriction.restrictions {
            w!(self, "///  - {}: `{}`", restriction.key, restriction.value);
        }
        w!(self, "pub type {rust_type_name} = {rust_base_type};");
    }

    fn global_element(&self, element: &Element) {
        let (name, _) = self.get_element_name_and_type(element);
        if let Some(top_level) = &self.top_level {
            if !top_level.iter().any(|t| t == &name) {
                return;
            }
        }
        let rust_name = self.rust_type_name(name);

        w!(self);
        w!(self, "#[derive(Clone, Debug, DeJson, SerJson, PartialEq)]");
        w!(self, "pub struct TopLevel{rust_name} {{");
        w!(self, "    #[nserde(rename = \"{name}\")]");
        self.element(element);
        w!(self, "}}");
    }

    #[rustfmt::skip]
    fn impl_serialize_sequence(
        &self,
        rust_type_name: &str,
        // (xml_field_name, rust_field_name, rust_type_name, min_occurs, max_occurs)
        fields: &[(impl AsRef<str>, impl AsRef<str>, impl AsRef<str>, usize, usize)]
    ) {
        w!(self);
        w!(self, "impl SerJson for {rust_type_name} {{");
        w!(self, "    fn ser_json(&self, d: usize, s: &mut SerJsonState) {{");
        w!(self, "        s.out.push('[');");
        w!(self, "        let mut first_element = true;");
        for (xml_field_name, rust_field_name, field_type, _, _) in fields {
            let field_type = field_type.as_ref();
            let rust_field_name = rust_field_name.as_ref();
            let xml_field_name = xml_field_name.as_ref();
            if field_type.starts_with("Option<") {
                w!(self, "        if self.{rust_field_name}.is_some() {{");
            } else if field_type.starts_with("Vec<") {
                w!(self, "        if !self.{rust_field_name}.is_empty() {{");
            }
            w!(self, "            match first_element {{");
            w!(self, "                false => s.out.push(','),");
            w!(self, "                true => first_element = false,");
            w!(self, "            }}");
            w!(self, "            s.out.push_str(\"{{\\\"{xml_field_name}\\\":\");");
            w!(self, "            self.{rust_field_name}.ser_json(d + 1, s);");
            w!(self, "            s.out.push('}}');");
            if field_type.starts_with("Option<") || field_type.starts_with("Vec<") {
                w!(self, "        }}");
            }
        }
        w!(self, "        s.out.push(']');");
        w!(self, "    }}");
        w!(self, "}}");
    }

    #[rustfmt::skip]
    fn impl_deserialize_sequence(
        &self,
        rust_type_name: &str,
        // (xml_field_name, rust_field_name, rust_type_name, min_occurs, max_occurs)
        fields: &[(impl AsRef<str>, impl AsRef<str>, impl AsRef<str>, usize, usize)]
    ) {
        if fields.is_empty() {
            self.impl_empty_deserialize_sequence(rust_type_name);
            return;
        }

        w!(self);
        w!(self, "impl DeJson for {rust_type_name} {{");
        w!(self, "    fn de_json(s: &mut DeJsonState, i: &mut std::str::Chars) -> Result<Self, DeJsonErr> {{");
        for (_, rust_field_name, _, _, _) in fields {
            let rust_field_name = rust_field_name.as_ref();
            w!(self, "                let mut {rust_field_name} = None;");
        }
        w!(self, "        s.block_open(i)?;");
        w!(self, "        while s.tok != DeJsonTok::BlockClose {{");
        w!(self, "            s.curly_open(i)?;");
        w!(self, "            let key = String::de_json(s, i)?;");
        w!(self, "            s.colon(i)?;");
        w!(self, "            match key.as_str() {{");
        for (xml_field_name, rust_field_name, _, _, _) in fields {
            let xml_field_name = xml_field_name.as_ref();
            let rust_field_name = rust_field_name.as_ref();
            w!(self, "                \"{xml_field_name}\" => {{");
            w!(self, "                    if {rust_field_name}.is_some() {{");
            w!(self, "                        return Err(unexpected_field(s, \"{xml_field_name}\"));");
            w!(self, "                    }}");
            w!(self, "                    {rust_field_name} = Some(DeJson::de_json(s, i)?);");
            w!(self, "                }}");
        }
        w!(self, "                _ => Err(unexpected_field(s, &key))?,");
        w!(self, "            }}");
        w!(self, "            s.curly_close(i)?;");
        w!(self, "            s.eat_comma_block(i)?;");
        w!(self, "        }}");
        w!(self, "        s.block_close(i)?;");
        for (xml_field_name, rust_field_name, rust_type_name, _, _) in fields {
            let xml_field_name = xml_field_name.as_ref();
            let rust_field_name = rust_field_name.as_ref();
            let rust_type_name = rust_type_name.as_ref();
            if rust_type_name.starts_with("Vec<") {
                w!(self, "                let {rust_field_name} = {rust_field_name}.unwrap_or(Vec::new());");
            } else if !rust_type_name.starts_with("Option<") {
                w!(self, "                let {rust_field_name} = {rust_field_name}.ok_or_else(|| missing_field(s, \"{xml_field_name}\"))?;");
            }
        }
        w!(self, "        Ok(Self {{");
        for (_, rust_field_name, _, _, _) in fields {
            let rust_field_name = rust_field_name.as_ref();
            w!(self, "            {rust_field_name},");
        }
        w!(self, "        }})");
        w!(self, "    }}");
        w!(self, "}}");
    }

    #[rustfmt::skip]
    fn impl_empty_deserialize_sequence(&self, rust_type_name: &str) {
        w!(self);
        w!(self, "impl DeJson for {rust_type_name} {{");
        w!(self, "    fn de_json(s: &mut DeJsonState, i: &mut std::str::Chars) -> Result<Self, DeJsonErr> {{");
        w!(self, "        s.block_open(i)?;");
        w!(self, "        s.block_close(i)?;");
        w!(self, "        Ok(Self {{}})");
        w!(self, "    }}");
        w!(self, "}}");
    }

    #[rustfmt::skip]
    fn impl_serialize_union(&self, rust_type_name: &str, uni: &Union) {
        w!(self);
        w!(self, "impl SerJson for {rust_type_name} {{");
        w!(self, "    fn ser_json(&self, d: usize, s: &mut SerJsonState) {{");
        w!(self, "        match self {{");
        for variant in &uni.member_types {
            let variant = self.rust_type_name(variant);
            w!(self, "            Self::{variant}(x) => x.ser_json(d, s),");
        }
        if uni.extended_enum {
            w!(self, "            Self::VendorExtension(x) => x.ser_json(d, s),");
        }
        w!(self, "        }}");
        w!(self, "    }}");
        w!(self, "}}");
    }

    #[rustfmt::skip]
    fn impl_deserialize_union(&self, rust_type_name: &str, uni: &Union) {
        w!(self);
        w!(self, "impl DeJson for {rust_type_name} {{");
        w!(self, "    fn de_json(s: &mut DeJsonState, i: &mut std::str::Chars) -> Result<Self, DeJsonErr> {{");
        w!(self, "        let str = String::de_json(s, i)?;");
        for variant in &uni.member_types {
            let variant = self.rust_type_name(variant);
            w!(self, "        if let Ok(x) = str.parse() {{");
            w!(self, "            return Ok(Self::{variant}(x));");
            w!(self, "        }}");
        }
        if uni.extended_enum {
            w!(self, "        Ok(Self::VendorExtension(str))");
        } else {
            w!(self, "        Err(invalid_variant(s, \"{rust_type_name}\"))");
        }
        w!(self, "    }}");
        w!(self, "}}");
    }

    #[rustfmt::skip]
    fn impl_serialize_enum(&self, rust_type_name: &str, restriction: &RestrictionType) {
        w!(self);
        w!(self, "impl SerJson for {rust_type_name} {{");
        w!(self, "    fn ser_json(&self, d: usize, s: &mut SerJsonState) {{");
        w!(self, "        match self {{");
        for variant in &restriction.restrictions {
            let variant = &variant.value;
            let rust_variant = to_pascal_case(variant);
            w!(self, "            Self::{rust_variant} => s.out.push_str(\"\\\"{variant}\\\"\"),");
        }
        if restriction.extended_enum {
            w!(self, "            Self::VendorExtension(x) => x.ser_json(d, s),");
        }
        w!(self, "        }}");
        w!(self, "    }}");
        w!(self, "}}");
    }

    #[rustfmt::skip]
    fn impl_deserialize_enum(&self, rust_type_name: &str, restriction: &RestrictionType) {
        w!(self);
        w!(self, "impl FromStr for {rust_type_name} {{");
        w!(self, "    type Err = ();");
        w!(self, "    fn from_str(s: &str) -> Result<Self, Self::Err> {{");
        w!(self, "        match s {{");
        for variant in &restriction.restrictions {
            let variant = &variant.value;
            let rust_variant = to_pascal_case(variant);
            w!(self, "            \"{variant}\" => Ok(Self::{rust_variant}),");
        }
        if restriction.extended_enum {
            w!(self, "            _ => Ok(Self::VendorExtension(s.to_owned())),");
        } else {
            w!(self, "            v => Err(()),");
        }
        w!(self, "        }}");
        w!(self, "    }}");
        w!(self, "}}");
        w!(self);
        w!(self, "impl DeJson for {rust_type_name} {{");
        w!(self, "    fn de_json(s: &mut DeJsonState, i: &mut std::str::Chars) -> Result<Self, DeJsonErr> {{");
        w!(self, "        let v = String::de_json(s, i)?;");
        w!(self, "        v.parse().map_err(|_| invalid_variant(s, \"{rust_type_name}\"))");
        w!(self, "    }}");
        w!(self, "}}");
    }

    #[rustfmt::skip]
    fn impl_elements(
        &self,
        rust_type_name: &str,
        // (xml_field_name, rust_field_name, rust_type_name, min_occurs, max_occurs)
        fields: &[(impl AsRef<str>, impl AsRef<str>, impl AsRef<str>, usize, usize)],
    ) {
        w!(self);
        w!(self, "impl AllElements for {rust_type_name} {{");
        w!(self, "    fn all_elements() -> Self {{");
        w!(self, "        Self {{");
        for (_, rust_field_name, _, _, _) in fields {
            let rust_field_name = rust_field_name.as_ref();
            w!(self, "            {rust_field_name}: Some(AllElements::all_elements()),");
        }
        w!(self, "        }}");
        w!(self, "    }}");
        w!(self, "}}");
        w!(self);
        w!(self, "impl std::fmt::Debug for {rust_type_name} {{");
        w!(self, "    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {{");
        w!(self, "        if *self == AllElements::all_elements() {{");
        w!(self, "            return f.write_str(\"*\");");
        w!(self, "        }}");
        w!(self, "        f.debug_struct(\"{rust_type_name}\")");
        for (xml_field_name, rust_field_name, _, _, _) in fields {
            let xml_field_name = xml_field_name.as_ref();
            let rust_field_name = rust_field_name.as_ref();
            w!(self, "            .field(\"{xml_field_name}\", &self.{rust_field_name})");
        }
        w!(self, "            .finish()");
        w!(self, "    }}");
        w!(self, "}}");
        w!(self);
        let data_type_name = rust_type_name.replace("Elements", "");
        w!(self, "impl FilterElements<{data_type_name}> for {rust_type_name} {{");
        w!(self, "    fn filter_elements(&self, value: &mut {data_type_name}) {{");
        for (_, rust_field_name, _, _, _) in fields {
            let rust_field_name = rust_field_name.as_ref();
            w!(self, "        FilterElements::filter_elements(&self.{rust_field_name}, &mut value.{rust_field_name});");
        }
        w!(self, "    }}");
        w!(self, "}}");
    }

    fn get_simple_type_by_name(&self, type_name: &str) -> &SimpleType {
        let type_name = type_name
            .trim_start_matches("ns_p:")
            .trim_start_matches("ship:");
        self.parser
            .simple_types
            .iter()
            .find(|ty| ty.type_name == type_name)
            .unwrap_or_else(|| panic!("could not find simple type {type_name}"))
    }

    fn get_complex_type_by_name(&self, type_name: &str) -> &ComplexType {
        let type_name = type_name
            .trim_start_matches("ns_p:")
            .trim_start_matches("ship:");
        self.parser
            .complex_types
            .iter()
            .find(|ty| ty.type_name == type_name)
            .unwrap_or_else(|| panic!("could not find complex type {type_name}"))
    }

    fn get_complex_type_fields(&self, complex_type: &ComplexType) -> Vec<Element> {
        match &complex_type.data {
            ComplexTypeData::Empty => Vec::new(),
            ComplexTypeData::Sequence(elements) => elements.clone(),
            ComplexTypeData::ComplexContent(ComplexContent::Extension(e)) => {
                let complex_type = self.get_complex_type_by_name(&e.base_type);
                let mut elements = self.get_complex_type_fields(complex_type);
                elements.extend_from_slice(&e.elements);
                elements
            }
            ComplexTypeData::ComplexContent(ComplexContent::Restriction(r)) => {
                //let complex_type = self.get_complex_type_by_name(&r.base_type);
                //let mut elements = self.get_complex_type_fields(complex_type);
                //'outer: for element in &mut elements {
                //    let (element_name, _) = self.get_element_name_and_type(element);
                //    for restriction in &r.elements {
                //        let (restriction_name, _) = self.get_element_name_and_type(restriction);
                //        if element_name == restriction_name {
                //            *element = restriction.clone();
                //            continue 'outer;
                //        }
                //    }
                //}
                r.elements.clone()
            }
            ComplexTypeData::SimpleContent(_) => panic!("cannot get fields of simplecontent"),
        }
    }

    fn get_element_name_and_type<'b>(&'b self, element: &'b Element) -> (&'b str, &'b str) {
        match &element.data {
            ElementData::Def(def) => (&def.name, &def.type_name),
            ElementData::Ref(ref_name) => {
                let ref_name = ref_name
                    .trim_start_matches("ns_p:")
                    .trim_start_matches("ship:");
                let global_elems = &self.parser.global_elements;
                let elem = global_elems
                    .iter()
                    .find_map(|elem| {
                        let ElementData::Def(elem) = &elem.data else {
                            panic!("global ref element not allowed");
                        };
                        (elem.name == ref_name).then_some(elem)
                    })
                    .unwrap_or_else(|| panic!("no global element {ref_name} found"));
                (&elem.name, &elem.type_name)
            }
        }
    }

    fn is_omitted(&self, type_name: &str) -> bool {
        self.omissions.contains(&type_name)
    }

    fn rust_type_name(&self, mut type_name: &str) -> String {
        type_name = type_name
            .trim_start_matches("ns_p:")
            .trim_start_matches("ship:");
        for trim in self.trim_end {
            type_name = type_name.trim_end_matches(trim);
        }
        to_pascal_case(type_name)
    }

    fn xs_type_to_rust_type(&self, ty: &str) -> Option<&str> {
        if !ty.starts_with("xs:") {
            return None;
        }

        Some(match ty {
            "xs:string" => "String",
            "xs:date" => "XsDate",
            "xs:time" => "XsTime",
            "xs:dateTime" => "XsDateTime",
            "xs:duration" => "XsDuration",
            "xs:hexBinary" => "String",
            "xs:byte" => "i8",
            "xs:short" => "i16",
            "xs:int" => "i32",
            "xs:long" => "i64",
            "xs:unsignedByte" => "u8",
            "xs:unsignedShort" => "u16",
            "xs:unsignedInt" => "u32",
            "xs:unsignedLong" => "u64",
            "xs:decimal" => "f32",
            "xs:float" => "f32",
            "xs:double" => "f32",
            "xs:boolean" => "bool",
            // HACK: anyType is only used once, and in this case we want it to be this.
            "xs:anyType" => "crate::spine::types::TopLevelDatagram",
            ty => panic!("unsupported xml schema type {ty}"),
        })
    }

    fn xs_type_format_comment(&self, ty: &str) -> Option<&str> {
        Some(match ty {
            "xs:date" => "<yyyy>-<mm>-<dd>Z",
            "xs:time" => "<hh>:<mm>:<ss>.<n*>Z",
            "xs:dateTime" => "<yyyy>-<mm>-<dd>T<hh>:<mm>:<ss>.<n*>Z",
            "xs:duration" => "[-]P<n>Y<n>M<n>DT<n>H<n>M<n>S",
            "xs:hexBinary" => "[0-9a-fA-F]* (binary data as hex string)",
            _ => return None,
        })
    }
}

fn to_pascal_case(type_name: impl AsRef<str>) -> String {
    let mut out = String::new();
    let mut uppercase_next = true;
    for c in type_name.as_ref().chars() {
        if uppercase_next && c.is_ascii_lowercase() {
            out.push(c.to_ascii_uppercase());
            uppercase_next = false;
        } else if c.is_ascii_alphanumeric() {
            out.push(c);
            uppercase_next = false;
        } else {
            uppercase_next = true;
        }
    }
    out
}

fn to_snake_case(type_name: impl AsRef<str>) -> String {
    let mut out = String::new();
    let mut first = true;
    for c in type_name.as_ref().chars() {
        if c == '-' {
            out.push('_');
        } else if c.is_ascii_uppercase() {
            if !first {
                out.push('_');
            }
            out.push(c.to_ascii_lowercase());
        } else if c.is_ascii_alphanumeric() {
            out.push(c);
        }
        first = false;
    }
    out
}

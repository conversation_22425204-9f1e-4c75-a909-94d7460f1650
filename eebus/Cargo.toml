[package]
name = "eebus"
version = "0.1.0"
edition = "2024"

[features]
default = ["log", "tungstenite"]
tungstenite = ["dep:async-tungstenite"]

[dependencies]
bitflags = { version = "2.4.1", default-features = false, features = ["std"] }
defmt = { version = "1.0.1", default-features = false, optional = true, features = [
    "alloc",
] }
embassy-futures = { version = "0.1.1", default-features = false }
embassy-sync = { version = "0.6.2", default-features = false, features = [] }
embassy-time = "0.4.0"
futures-lite = { version = "2.6", default-features = false, features = ["std"] }
heapless = "0.8"
hex = "0.4.3"
log = { version = "0.4.26", default-features = false, optional = true }
nanoserde = { version = "0.2.1", default-features = false, features = [
    "json",
    "std",
] }
paste = "1.0.15"

async-tungstenite = { version = "0.29.1", default-features = false, optional = true }

[dev-dependencies]
env_logger = "0.11.7"
async-io = "2.4"
async-executor = { version = "1.13", features = [] }
async-lock = "3.4"
async-channel = "2.3"

[build-dependencies]
eebus-typegen = { path = "./eebus-typegen" }

use std::sync::{atomic::AtomicU32, Arc};

use nodemgmt::Relationship;
use types::{
    CmdClassifier, Datagram, FeatureAddress, Header, NodeManagementSubscriptionData,
    NodeManagementSubscriptionDataSubscriptionEntry, Payload,
};

use self::error::SpineError;
use crate::ship::{Ship, ShipConnection, Trust};

pub mod devdiag;
pub mod error;
pub mod loadctl;
pub mod measurement;
pub mod nodemgmt;
pub mod semps;
pub mod types;

mod functions;
mod request;
mod transform;

pub const VERSION: &str = "1.3.0";

pub use self::{functions::Function, request::*};

pub type Result<T, E = SpineError> = std::result::Result<T, E>;

#[derive(Clone)]
pub struct Spine<C, T> {
    pub ship: Arc<Ship<C, T>>,
    msg_counter: Arc<AtomicU32>,
}

impl<C, T> Spine<C, T>
where
    C: ShipConnection + 'static,
    T: Trust + 'static,
{
    pub fn new(ship: Ship<C, T>) -> Self {
        Self {
            ship: Arc::new(ship),
            msg_counter: Arc::new(AtomicU32::new(0)),
        }
    }

    pub async fn recv_request(&self) -> Result<Request, SpineError> {
        let msg = self.ship.recv().await?;

        if msg.header.protocol_id != "ee1.0" {
            return Err(SpineError::InvalidProtocolId);
        }

        let msg_counter = self
            .msg_counter
            .fetch_add(1, std::sync::atomic::Ordering::Relaxed);

        transform::transform_cmd(self.ship.clone(), msg.payload.datagram, msg_counter as u64).await
    }

    pub async fn send_request_raw(&self, datagram: Datagram) -> Result<(), SpineError> {
        self.ship
            .send_data(types::TopLevelDatagram { datagram })
            .await?;

        Ok(())
    }

    pub async fn send_request(
        &self,
        source: FeatureAddress,
        destination: FeatureAddress,
        cmd_classifier: CmdClassifier,
        ack_request: Option<bool>,
        cmd: Box<[types::Cmd; 1]>,
    ) -> Result<(), SpineError> {
        self.send_request_raw(Datagram {
            header: Header {
                specification_version: Some(VERSION.to_owned()),
                address_source: Some(source),
                address_destination: Some(destination),
                address_originator: None,
                msg_counter: Some(
                    self.msg_counter
                        .fetch_add(1, std::sync::atomic::Ordering::Relaxed)
                        as u64,
                ),
                msg_counter_reference: None,
                cmd_classifier: Some(cmd_classifier),
                ack_request,
                timestamp: None,
            },
            payload: Payload {
                cmd: (cmd as Box<[_]>).into_vec(),
            },
        })
        .await
    }

    pub async fn notify(&self, sub: &Relationship) -> Result<(), SpineError> {
        self.send_request(
            sub.server_address.clone(),
            sub.client_address.clone(),
            CmdClassifier::Notify,
            None,
            Box::new([types::Cmd {
                function: None,
                filter: vec![],
                manufacturer_specific_extension: None,
                last_update_at: None,
                data: Some(types::Data::NodeManagementSubscriptionData(
                    NodeManagementSubscriptionData {
                        subscription_entry: vec![NodeManagementSubscriptionDataSubscriptionEntry {
                            subscription_id: None,
                            client_address: Some(sub.client_address.clone()),
                            server_address: Some(sub.server_address.clone()),
                            label: None,
                            description: None,
                        }],
                    },
                )),
            }]),
        )
        .await
    }
}

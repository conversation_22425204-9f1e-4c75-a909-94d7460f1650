use std::marker::PhantomData;

use embassy_time::{Duration, Timer};
use nanoserde::<PERSON><PERSON><PERSON>;

use crate::{log::*, spine::types::TopLevelDatagram};

#[cfg(feature = "tungstenite")]
pub mod tungstenite;

mod connection;
mod error;
mod http;
mod msg;
mod parser;

pub use self::{connection::*, error::*, http::websocket_handshake, msg::*, parser::*};

pub const VERSION: &str = "1.0.1";

pub struct Ship<C, T> {
    connection: C,
    device_id: String,
    marker: PhantomData<fn(T)>,
}

impl<C, T> Ship<C, T>
where
    C: ShipConnection,
    T: Trust,
{
    /// Try to establish a SHIP connection.
    ///
    /// Possible return values:
    ///  - `Ok(Ok(Ship))`: No PIN submisison required, connection fully established.
    ///  - `Ok(Err(PinExchange))`: PIN submission required to establish connection.
    ///  - `Err(ShipError)`: Other error.
    pub async fn init(
        connection: C,
        trust: &T,
        config: Config,
        device_id: String,
        is_server: bool,
    ) -> Result<Result<Self, PinExchange<C, T>>, ShipError> {
        let pin_required = init_ship_connection(&connection, trust, config, is_server).await?;

        let ship = Self {
            connection,
            device_id,
            marker: PhantomData,
        };

        match pin_required {
            true => Ok(Err(PinExchange(ship))),
            false => Ok(Ok(ship)),
        }
    }

    pub async fn send_data(&self, data: TopLevelDatagram) -> Result<(), ShipError> {
        trace!("sending data: {:#?}", data.datagram);
        self.connection
            .write_message(MessageValue::Data(MsgTypeData::Data(Box::new(Data {
                header: Header {
                    protocol_id: "spine".to_owned(),
                },
                payload: data,
                extension: None,
            }))))
            .await
    }

    pub async fn recv(&self) -> Result<Box<Data>, ShipError> {
        loop {
            let msg = self.connection.read_message().await;
            match msg {
                Ok(MessageValue::Data(MsgTypeData::Data(data))) => {
                    let header = data.payload.datagram.header.serialize_json();
                    let cmd = data.payload.datagram.payload.cmd.first();
                    debug!(
                        "ship data received: \n  header: {}\n  cmd: {:#?}",
                        header.as_str(),
                        cmd
                    );

                    break Ok(data);
                }
                Ok(MessageValue::Control(MsgTypeControl::ConnectionHello(_))) => {
                    debug!("received additional ConnectionHello");
                }
                Ok(MessageValue::Control(MsgTypeControl::AccessMethodsRequest(_))) => {
                    self.connection
                        .write_message(MessageValue::Control(MsgTypeControl::AccessMethods(
                            AccessMethods {
                                id: self.device_id.clone(),
                                dns_sdm_dns: Some(AccessMethodsDnsSdMDns {}),
                                dns: None,
                            },
                        )))
                        .await?;
                }
                Ok(MessageValue::End(MsgTypeEnd::ConnectionClose(close))) => {
                    info!("received ConnectionClose");

                    if close.phase == ConnectionClosePhase::Confirm {
                        warn!("wrong close phase");
                        break Err(ShipError::UnexpectedMessage);
                    } else {
                        self.connection
                            .write_message(MessageValue::End(MsgTypeEnd::ConnectionClose(
                                ConnectionClose {
                                    phase: ConnectionClosePhase::Confirm,
                                    max_time: None,
                                    reason: None,
                                },
                            )))
                            .await?;
                        self.connection.close().await?;
                        break Err(ShipError::ConnectionClosed);
                    }
                }
                Ok(_) => {
                    warn!("received unexpected message");
                }
                Err(e) => {
                    #[cfg(feature = "log")]
                    error!("error reading message: {:?}", e);
                    #[cfg(feature = "defmt")]
                    error!("error reading message: {:?}", defmt::Debug2Format(&e));
                    break Err(e);
                }
            }
        }
    }

    pub async fn close(&self) -> Result<(), ShipError> {
        let max_time = 5000;
        let _ = self
            .connection
            .write_message(MessageValue::End(MsgTypeEnd::ConnectionClose(
                ConnectionClose {
                    reason: Some(ConnectionCloseReason::Unspecific),
                    phase: ConnectionClosePhase::Announce,
                    max_time: Some(max_time),
                },
            )))
            .await;
        Timer::after(Duration::from_millis(max_time as u64)).await;
        // TODO: we could wait for a close confirm here, but that would interfere with the data loop...
        self.connection.close().await
    }
}

pub struct PinExchange<C, T>(Ship<C, T>);

impl<C, T> PinExchange<C, T>
where
    C: ShipConnection,
    T: Trust,
{
    /// Submit a PIN to continue the SHIP connection attempt.
    ///
    /// Possible return values:
    ///  - `Ok(Ok(Ship))`: PIN submission sucessful, connection fully established.
    ///  - `Ok(Err(PinExchange))`: PIN submission failed (invalid PIN), retry required.
    ///  - `Err(ShipError)`: Other error.
    pub async fn submit_pin(self, pin: String) -> Result<Result<Ship<C, T>, Self>, ShipError> {
        send_pin_input(&self.0.connection, pin).await?;
        let pin_required = handle_pin_exchange(&self.0.connection, Duration::from_secs(30)).await?; // 30s..120s allowed
        match pin_required {
            true => Ok(Err(self)),
            false => Ok(Ok(self.0)),
        }
    }
}

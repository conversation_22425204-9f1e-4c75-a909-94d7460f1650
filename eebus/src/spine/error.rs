use std::fmt;

use crate::ship::ShipError;

#[derive(Debug)]
#[cfg_attr(feature = "defmt", derive(defmt::Format))]
pub enum SpineError {
    Unsupported,
    UnexpectedVariant,
    MissingField,
    InvalidProtocolId,
    /// The application dropped the reply token
    ReplyInternalError,
    DuplicateId,
    Overflow,
    Ship(ShipError),
    Custom(String),
}

impl fmt::Display for SpineError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::Unsupported => write!(f, "not supported"),
            Self::UnexpectedVariant => write!(f, "unexpected variant"),
            Self::MissingField => write!(f, "missing field"),
            Self::InvalidProtocolId => write!(f, "invalid protocol id"),
            Self::ReplyInternalError => write!(f, "application dropped the reply token"),
            Self::DuplicateId => write!(f, "duplicate id"),
            Self::Overflow => write!(f, "overflow"),
            Self::Ship(e) => write!(f, "ship error: {}", e),
            Self::Custom(e) => write!(f, "custom: {}", e.as_str()),
        }
    }
}

impl std::error::Error for SpineError {}

impl From<ShipError> for SpineError {
    fn from(err: ShipError) -> Self {
        SpineError::Ship(err)
    }
}

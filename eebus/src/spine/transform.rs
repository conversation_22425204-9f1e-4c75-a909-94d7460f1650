use std::{iter, sync::Arc};

use paste::paste;

use super::types;
use crate::{
    log::*,
    ship::{Ship, ShipConnection, Trust},
    spine::{error::SpineError, request::*, types::*, Function, VERSION},
};

type Result<T, E = SpineError> = std::result::Result<T, E>;

struct MyReplySender<C, T, I> {
    ship: Arc<Ship<C, T>>,
    header: Header,
    cmd_classifier: CmdClassifier,
    ack_request: bool,
    was_partial: bool,
    _marker: std::marker::PhantomData<fn(I)>,
}

impl<C, T, I> ReplySender for MyReplySender<C, T, I>
where
    C: Send + Sync + ShipConnection,
    T: Send + Sync + Trust,
    I: Function + Send + 'static,
{
    type Item = I;

    fn send_reply(
        self: Box<Self>,
        data: std::result::Result<Self::Item, SpineError>,
    ) -> std::pin::Pin<Box<dyn Future<Output = std::result::Result<(), SpineError>> + Send + 'static>>
    {
        Box::pin(async move {
            send_result(
                self.ship,
                self.header,
                self.cmd_classifier,
                self.ack_request,
                self.was_partial,
                data,
            )
            .await
        })
    }
}

pub async fn transform_cmd(
    ship: Arc<Ship<impl ShipConnection, impl Trust>>,
    mut msg: Datagram,
    msg_counter: MsgCounter,
) -> Result<Request, SpineError> {
    let cmd_classifier = msg.header.cmd_classifier.ok_or(SpineError::MissingField)?;
    let cmd = msg
        .payload
        .cmd
        .drain(..)
        .next()
        .ok_or(SpineError::MissingField)?;
    let function = cmd.data.clone().ok_or(SpineError::MissingField)?;

    let header = Header {
        specification_version: Some(VERSION.to_owned()),
        address_source: msg.header.address_destination,
        address_destination: msg.header.address_source,
        address_originator: None,
        msg_counter: Some(msg_counter),
        msg_counter_reference: msg.header.msg_counter,
        cmd_classifier: None,
        ack_request: None,
        timestamp: None,
    };

    let ack_request = msg.header.ack_request.unwrap_or(false);
    let was_partial = cmd
        .filter
        .iter()
        .any(|f| matches!(&f.cmd_control, Some(CmdControl::Partial)));

    macro_rules! transform {
        ($name:ident, $function_typed:ident) => {
            {
                let action = Action::from_filters(cmd_classifier.clone(), cmd.filter, $name::extract_elements, $name::extract_selectors)?;

                let destination = header.address_destination.clone().unwrap_or(FeatureAddress {
                    device: None,
                    entity: Vec::new(),
                    feature: None,
                });
                let sender = Box::new(MyReplySender {
                    ship,
                    header,
                    cmd_classifier,
                    ack_request,
                    was_partial,
                    _marker: std::marker::PhantomData,
                });
                // TODO: ReplyToken should only be present for actions which require replies,
                //       but not for actions which do not (i.e. Action::Notify or Action::Reply)
                let reply = ReplyToken {
                    sender: Some(sender),
                    destination,
                };
                let function = $function_typed; // from match arm

                let request_data = RequestData {
                    action,
                    reply,
                    function,
                };

                Ok(paste!(Request::[< $name >])(request_data))
            }
        };
        ($($name:ident,)*) => {
            match function {
                $(
                    Data::$name(function_typed) => transform!($name, function_typed),
                )*
                unknown_function => crate::log::todo!("function not implemented: {:?}", unknown_function),
            }
        }
    }

    transform!(
        DeviceConfigurationKeyValueDescriptionListData,
        DeviceConfigurationKeyValueListData,
        DeviceDiagnosisHeartbeatData,
        ElectricalConnectionDescriptionListData,
        ElectricalConnectionParameterDescriptionListData,
        LoadControlLimitDescriptionListData,
        LoadControlLimitListData,
        MeasurementConstraintsListData,
        MeasurementDescriptionListData,
        MeasurementListData,
        NodeManagementBindingRequestCall,
        NodeManagementDetailedDiscoveryData,
        NodeManagementUseCaseData,
        NodeManagementSubscriptionRequestCall,
        SmartEnergyManagementPsData,
    )
}

impl<E, S> Action<E, S>
where
    E: AllElements,
{
    fn from_filter(
        cmd_classifier: CmdClassifier,
        filter: Filter,
        element_extractor: impl Fn(DataElement) -> Result<E> + Copy + 'static,
        selector_extractor: impl Fn(DataSelectors) -> Result<S> + Copy + 'static,
    ) -> Result<Self, SpineError> {
        let cmd_control = filter.cmd_control.clone();
        let (has_elements, has_selector) = (
            filter.data_element.is_some(),
            !filter.data_selectors.is_empty(),
        );

        let elements = filter
            .data_element
            .clone()
            .and_then(|e| element_extractor(e).ok())
            .unwrap_or_else(|| E::all_elements());
        let one_of = filter
            .data_selectors
            .iter()
            .cloned()
            .map(selector_extractor)
            .collect::<Result<_>>()?;

        let a = match (&cmd_classifier, &cmd_control, has_elements, has_selector) {
            // (classifier, control, has_elements, has_selector)
            (CmdClassifier::Read, None, _, false) => Action::FullRead { elements },
            (CmdClassifier::Read, Some(CmdControl::Partial), _, true) => {
                // TODO: somewhere, we need to make sure that the reply always includes the ID (see spine proto 1428)
                Action::PartialRead { elements, one_of }
            }
            (CmdClassifier::Write, None, false, false) => Action::FullWrite,
            (CmdClassifier::Write, Some(CmdControl::Partial), false, false) => Action::Upsert,
            (CmdClassifier::Write, Some(CmdControl::Partial), false, true) => {
                Action::UpsertPartial { one_of }
            }
            (CmdClassifier::Write, Some(CmdControl::Delete), _, false) => {
                Action::DeleteElements { elements }
            }
            (CmdClassifier::Write, Some(CmdControl::Delete), _, true) => {
                Action::DeleteElementsIn { elements, one_of }
            }
            (CmdClassifier::Call, None, false, false) => Action::Call,
            _ => {
                #[cfg(feature = "log")]
                error!(
                    "unsupported cmdControl: {:?}, {:?}, {:?}",
                    cmd_control, cmd_classifier, filter
                );
                #[cfg(feature = "defmt")]
                error!(
                    "unsupported cmdControl: {:?}, {:?}, {:?}",
                    defmt::Debug2Format(&cmd_control),
                    defmt::Debug2Format(&cmd_classifier),
                    defmt::Debug2Format(&filter)
                );
                return Err(SpineError::Unsupported);
            }
        };

        Ok(a)
    }

    fn from_filters(
        cmd_classifier: CmdClassifier,
        filter: Vec<Filter>,
        element_extractor: impl Fn(DataElement) -> Result<E> + Copy + 'static,
        selector_extractor: impl Fn(DataSelectors) -> Result<S> + Copy + 'static,
    ) -> Result<Self, SpineError> {
        match &filter[..] {
            [] => Action::from_filter(
                cmd_classifier,
                Filter {
                    filter_id: None,
                    cmd_control: None,
                    data_selectors: vec![],
                    data_element: None,
                },
                element_extractor,
                selector_extractor,
            ),
            [filter] => Action::from_filter(
                cmd_classifier,
                filter.clone(),
                element_extractor,
                selector_extractor,
            ),
            _ => filter
                .into_iter()
                .map(|f| {
                    Action::from_filter(cmd_classifier, f, element_extractor, selector_extractor)
                })
                .collect::<Result<Vec<_>, _>>()
                .map(Action::Sequence),
        }
    }
}

async fn send_result<T: Function>(
    ship: Arc<Ship<impl ShipConnection, impl Trust>>,
    header: Header,
    recv_cmd_classifier: CmdClassifier,
    ack_request: bool,
    was_partial: bool,
    data: Result<T>,
) -> Result<(), SpineError> {
    send_result_common(
        ship,
        header,
        recv_cmd_classifier,
        T::get_filter_function(),
        ack_request,
        was_partial,
        data.map(Function::into_generic).unwrap_or_else(|e| {
            Data::ResultData(ResultData {
                error_number: Some(1),
                description: Some(format!("{e:?}")),
            })
        }),
    )
    .await
}

// extracted into function for monomorphization
async fn send_result_common(
    ship: Arc<Ship<impl ShipConnection, impl Trust>>,
    header: Header,
    recv_cmd_classifier: CmdClassifier,
    filter_function: types::Function,
    mut ack_request: bool,
    was_partial: bool,
    data: Data,
) -> Result<(), SpineError> {
    if matches!(recv_cmd_classifier, CmdClassifier::Read) {
        ack_request = true;
    }

    if !ack_request {
        return Ok(()); // no need to send a result
    }

    let (data, cmd_classifier, function, filter) = match recv_cmd_classifier {
        CmdClassifier::Read => (
            data,
            CmdClassifier::Reply,
            Some(filter_function).filter(|_| was_partial),
            iter::once(Filter {
                cmd_control: Some(CmdControl::Partial),
                ..Default::default()
            })
            .filter(|_| was_partial)
            .collect(),
        ),
        CmdClassifier::Reply => crate::log::unimplemented!("we don't read so we don't get replies"),
        CmdClassifier::Notify => {
            crate::log::unimplemented!("we don't subscribe so we don't get notifies")
        }
        CmdClassifier::Write | CmdClassifier::Call => (
            match data {
                Data::ResultData(_) => data,
                _ => Data::ResultData(ResultData {
                    error_number: Some(0),
                    description: None,
                }),
            },
            CmdClassifier::Result,
            None,
            vec![],
        ),
        CmdClassifier::Result => crate::log::todo!("not sure if we need this"),
    };

    ship.send_data(TopLevelDatagram {
        datagram: Datagram {
            header: Header {
                cmd_classifier: Some(cmd_classifier),
                ..header
            },
            payload: Payload {
                cmd: vec![Cmd {
                    function,
                    filter,
                    manufacturer_specific_extension: None,
                    last_update_at: None, // TODO
                    data: Some(data),
                }],
            },
        },
    })
    .await?;

    Ok(())
}

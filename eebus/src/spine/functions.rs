use std::fmt::Debug;

use paste::paste;

use super::{
    types::{self, *},
    Action, SpineError,
};
use crate::log::*;

pub trait Function {
    /// What elements is applied to; this is Self for "singletons" and MyData for a list called MyListData
    type Data: Debug;
    type Elements: AllElements + Debug;
    type Selectors: Debug;

    type Action = Action<Self::Elements, Self::Selectors>;

    fn into_generic(self) -> Data;
    fn get_filter_function() -> types::Function;
    fn extract_elements(selectors: DataElement) -> Result<Self::Elements, SpineError>;
    fn extract_selectors(selectors: DataSelectors) -> Result<Self::Selectors, SpineError>;
}

macro_rules! impl_function {
    ($ty:ident, $data:ident) => {
        impl Function for $ty {
            type Data = $data;
            type Elements = paste!( [< $data  Elements >] );
            type Selectors = paste!( [< $ty  Selectors >] );

            fn into_generic(self) -> Data {
                Data::$ty(self)
            }

            fn get_filter_function() -> types::Function {
                types::Function::$ty
            }

            fn extract_elements(selectors: DataElement) -> Result<Self::Elements, SpineError> {
                match selectors {
                    paste!(DataElement:: [< $data  Elements >] (elements)) => Ok(elements),
                    _ => Err(SpineError::UnexpectedVariant),
                }
            }

            fn extract_selectors(selectors: DataSelectors) -> Result<Self::Selectors, SpineError> {
                match selectors {
                    paste!(DataSelectors:: [< $ty  Selectors >] (selectors)) => Ok(selectors),
                    _ => Err(SpineError::UnexpectedVariant),
                }
            }
        }
    };
    ($ty:ident) => {
        impl_function!($ty, $ty);
    };
}

macro_rules! impl_function_noselect {
    ($ty:ident, $data:ident) => {
        impl Function for $ty {
            type Data = $data;
            type Elements = paste!( [< $data  Elements >] );
            type Selectors = ();

            fn into_generic(self) -> Data {
                Data::$ty(self)
            }

            fn get_filter_function() -> types::Function {
                types::Function::$ty
            }

            fn extract_elements(selectors: DataElement) -> Result<Self::Elements, SpineError> {
                match selectors {
                    paste!(DataElement:: [< $data  Elements >] (elements)) => Ok(elements),
                    _ => Err(SpineError::UnexpectedVariant),
                }
            }

            fn extract_selectors(selectors: DataSelectors) -> Result<Self::Selectors, SpineError> {
                warn!("tried to extract selectors from a function that doesn't have any");

                match selectors {
                    _ => Err(SpineError::UnexpectedVariant),
                }
            }
        }
    };
    ($ty:ident) => {
        impl_function_noselect!($ty, $ty);
    };
}

impl_function!(
    DeviceConfigurationKeyValueDescriptionListData,
    DeviceConfigurationKeyValueDescriptionData
);
impl_function!(
    DeviceConfigurationKeyValueListData,
    DeviceConfigurationKeyValueData
);
//impl_function!(DeviceDiagnosisHeartbeatData);
impl_function!(
    ElectricalConnectionDescriptionListData,
    ElectricalConnectionDescriptionData
);
impl_function!(
    ElectricalConnectionParameterDescriptionListData,
    ElectricalConnectionParameterDescriptionData
);
impl_function!(
    LoadControlLimitDescriptionListData,
    LoadControlLimitDescriptionData
);
impl_function!(LoadControlLimitListData, LoadControlLimitData);
impl_function!(MeasurementConstraintsListData, MeasurementConstraintsData);
impl_function!(MeasurementDescriptionListData, MeasurementDescriptionData);
impl_function!(MeasurementListData, MeasurementData);
impl_function!(NodeManagementDetailedDiscoveryData);
impl_function!(NodeManagementUseCaseData);
impl_function!(SmartEnergyManagementPsData);
impl_function_noselect!(NodeManagementBindingRequestCall);
impl_function_noselect!(NodeManagementSubscriptionRequestCall);
impl_function_noselect!(DeviceDiagnosisHeartbeatData);

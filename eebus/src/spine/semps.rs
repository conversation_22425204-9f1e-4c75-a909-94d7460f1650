//! SmartEnergyManagementPs

use std::fmt::Debug;

use super::{error::SpineError, types::*};
use crate::log::*;

#[derive(Default)]
pub struct SmartEnergyManagement {
    slots: Vec<SmartEnergyManagementPsPowerTimeSlot>,
}

impl SmartEnergyManagement {
    pub fn new() -> Self {
        Self::default()
    }

    pub async fn read<O: OhpcfHandler>(
        &self,
        is_offering: bool,
        is_running: bool,
        ohpcf: &O,
    ) -> Result<SmartEnergyManagementPsData, O::Error> {
        let mut alternatives = vec![];
        if is_offering {
            alternatives.push(SmartEnergyManagementPsAlternatives {
                relation: Some(SmartEnergyManagementPsAlternativesRelation {
                    alternatives_id: Some(1),
                }),
                power_sequence: vec![SmartEnergyManagementPsPowerSequence {
                    description: Some(SmartEnergyManagementPsPowerSequenceDescription {
                        sequence_id: Some(1),
                        description: Some("alternative sequence".to_owned()),
                        power_unit: Some(UnitOfMeasurement::Watt),
                        energy_unit: None,
                        value_source: Some(MeasurementValueSource::MeasuredValue),
                        task_identifier: None,
                        repetitions_total: None,
                    }),
                    state: Some(SmartEnergyManagementPsPowerSequenceState {
                        state: Some(if is_running {
                            PowerSequenceState::Running
                        } else {
                            PowerSequenceState::Inactive
                        }),
                        active_slot_number: None,
                        elapsed_slot_time: None,
                        remaining_slot_time: None,
                        sequence_remote_controllable: Some(true),
                        active_repetition_number: None,
                        remaining_pause_time: None,
                    }),
                    schedule: None,
                    schedule_constraints: Some(
                        // we can start within 24h
                        SmartEnergyManagementPsPowerSequenceScheduleConstraints {
                            earliest_start_time: Some(XsDuration::zero()),
                            latest_end_time: Some(XsDuration::hms(23, 59, 59)),
                        },
                    ),
                    schedule_preference: None,
                    operating_constraints_interrupt: Some(
                        SmartEnergyManagementPsPowerSequenceOperatingConstraintsInterrupt {
                            is_pausable: Some(false),
                            is_stoppable: Some(true),
                            max_cycles_per_day: None,
                        },
                    ),
                    operating_constraints_duration: Some(
                        SmartEnergyManagementPsPowerSequenceOperatingConstraintsDuration {
                            active_duration_min: Some(
                                ohpcf
                                    .read_min_operation_time_compressor_minutes()
                                    .await
                                    .map(|x| XsDuration::minutes(x as i64))?,
                            ),
                            active_duration_max: None,
                            pause_duration_min: None,
                            pause_duration_max: None,
                            active_duration_sum_min: None,
                            active_duration_sum_max: None,
                        },
                    ),
                    operating_constraints_resume_implication: None,
                    power_time_slot: self.slots.clone(),
                }],
            });
        }

        Ok(SmartEnergyManagementPsData {
            node_schedule_information: Some(SmartEnergyManagementPsDataNodeScheduleInformation {
                node_remote_controllable: Some(true),
                supports_single_slot_scheduling_only: Some(false),
                alternatives_count: Some(1),
                total_sequences_count_max: Some(1),
                supports_reselection: Some(false),
            }),
            alternatives,
        })
    }

    pub async fn upsert<O: OhpcfHandler>(
        &mut self,
        request: &SmartEnergyManagementPsData,
        selectors: &[SmartEnergyManagementPsDataSelectors],
        is_offering: bool,
        ohpcf: &O,
    ) -> Result<(), SpineError> {
        if !is_offering {
            return Err(SpineError::Custom(
                "not currently offering power sequence".to_owned(),
            ));
        }

        let SmartEnergyManagementPsDataSelectors {
            alternatives_relation:
                Some(SmartEnergyManagementPsDataSelectorsAlternativesRelation {
                    alternatives_id: Some(1),
                }),
            power_sequence_description:
                Some(SmartEnergyManagementPsDataSelectorsPowerSequenceDescription {
                    sequence_id: Some(1),
                }),
            power_time_slot_schedule,
            power_time_slot_value,
        } = selectors.first().ok_or(SpineError::Unsupported)?
        else {
            warn!("received ohpcf command with bad alt/pseq addressing");
            return Err(SpineError::Unsupported);
        };

        if power_time_slot_value.is_some() {
            info!("received ohpcf command with power_time_slot_value selector, ignoring");
            return Ok(());
        }

        let selector_slot_number = power_time_slot_schedule
            .as_ref()
            .and_then(|x| x.slot_number);

        let ps = request
            .alternatives
            .first()
            .ok_or(SpineError::Unsupported)?
            .power_sequence
            .first()
            .ok_or(SpineError::Unsupported)?;
        for slot in &ps.power_time_slot {
            let Some(write_schedule) = &slot.schedule else {
                error!("received power time slot without schedule");
                return Err(SpineError::Unsupported);
            };
            let slot_number = match (selector_slot_number, write_schedule.slot_number) {
                (None, None) => {
                    error!("missing slot number");
                    return Err(SpineError::Unsupported);
                }
                (Some(x), None) => x,
                (None, Some(x)) => x,
                (Some(x), Some(y)) if x == y => x,
                (Some(x), Some(y)) => {
                    error!("slot number mismatch: {} != {}", x, y);
                    return Err(SpineError::Unsupported);
                }
            };

            let slot_index = self.slots.iter().position(|slot| {
                slot.schedule
                    .as_ref()
                    .and_then(|schedule| schedule.slot_number)
                    == Some(slot_number)
            });

            let local = match slot_index {
                Some(index) => &mut self.slots[index],
                None => {
                    self.slots.push(SmartEnergyManagementPsPowerTimeSlot {
                        schedule: None,
                        value_list: None,
                        schedule_constraints: None,
                    });
                    self.slots.last_mut().unwrap()
                }
            };

            match &mut local.schedule {
                Some(s) => {
                    s.default_duration = s
                        .default_duration
                        .clone()
                        .or_else(|| write_schedule.default_duration.clone());
                    s.description = s
                        .description
                        .clone()
                        .or_else(|| write_schedule.description.clone());
                    s.duration_uncertainty = s
                        .duration_uncertainty
                        .clone()
                        .or_else(|| write_schedule.duration_uncertainty.clone());
                    s.time_period = s
                        .time_period
                        .clone()
                        .or_else(|| write_schedule.time_period.clone());
                }
                None => {
                    local.schedule = Some(write_schedule.clone());
                }
            }
            // TODO: we don't care about those, do we need to maintain them?
            //local.value_list = slot.value_list;
            //local.schedule_constraints = slot.schedule_constraints;

            if local
                .schedule
                .as_ref()
                .unwrap()
                .time_period
                .as_ref()
                .unwrap()
                .start_time
                == Some(XsDuration::zero())
            {
                info!("sending elevation cmd");
                ohpcf.send_elevation_cmd(true).await;
            } else {
                warn!("we don't support scheduling the start");
            }
        }

        if let Some(state) = &ps.state {
            if matches!(
                state.state,
                Some(PowerSequenceState::Inactive | PowerSequenceState::Completed)
            ) {
                ohpcf.send_elevation_cmd(false).await;
            }
        }

        Ok(())
    }
}

pub trait OhpcfHandler {
    type Error: Debug;

    async fn send_elevation_cmd(&self, cmd: bool);
    async fn read_min_operation_time_compressor_minutes(&self) -> Result<u16, Self::Error>;
}

/*
pub fn data_response() -> SmartEnergyManagementPsData {
    SmartEnergyManagementPsData {
        node_schedule_information: Some(SmartEnergyManagementPsDataNodeScheduleInformation {
            // whether the ps server is configured for remote control (e.g. by energy management system)
            // must be `true` for ohpcf use case
            node_remote_controllable: Some(true),
            // true = ps server does not permit modification of more than one slot per config cmd received from client
            // TODO: do we want to support this?
            supports_single_slot_scheduling_only: Some(false),
            // number of alternatives given in "alternatives" field below
            alternatives_count: Some(1),
            // total number of power sequences within all alternatives of this message
            total_sequences_count_max: Some(1),
            // must be `false` for ohpcf use case
            supports_reselection: Some(false),
        }),
        // manadatory in case of:
        //  - scenario 1 && OHPCF-001 (announcement of an optional power consumption process)
        //  - scenario 1 && OHPCF-002 (report of the current state of the scheduled or active power consumption process)
        //  - scenario 1 && OHPCF-006/1 (explicit announcement of abortion of a process by compressor)
        //  - scenario 1 && OHPCF-006/3 (announcement of completion of a process by compressor)
        //  - scenario 2
        // can be absent in case of:
        //  - scenario1 && OHPCF-003 (absence of a power consumption process)
        //  - scenario1 && OHPCF-006/2 (announcement of absence of a power consumption process)
        // in the case of scenario 1: only a single optional or scheduled power consumption
        // process for a given time is permitted. the presence of alternative power consumption
        // processes is not permitted. [OHPCF-013]
        alternatives: vec![SmartEnergyManagementPsAlternatives {
            relation: Some(SmartEnergyManagementPsAlternativesRelation {
                // primary, endpoint-wide unique identifier of the alternative
                alternatives_id: Some(1),
            }),
            // in the case of scenario 1: only a single optional or scheduled power consumption
            // process for a given time is permitted. the presence of alternative power consumption
            // processes is not permitted. [OHPCF-013]
            power_sequence: vec![SmartEnergyManagementPsPowerSequence {
                description: Some(SmartEnergyManagementPsPowerSequenceDescription {
                    // primary, endpoint-wide unique identifier for the power sequence
                    sequence_id: Some(1),
                    description: Some(String::from("amazing power sequence")),
                    // only "W" is allowed for OHPCF
                    power_unit: Some(UnitOfMeasurement::Watt),
                    // TODO: unsure about this. this field is present in xsd, but is not mentioned in OHPCF use case.
                    energy_unit: None,
                    // optional
                    value_source: Some(MeasurementValueSource::MeasuredValue),
                    // TODO: unsure about this. this field is present in xsd, but is not mentioned in OHPCF use case.
                    task_identifier: None,
                    // if power sequence executes sequence of slots more than one time: total number of executions
                    // must be omitted, since it is marked as "F" in the ohpcf spec
                    repetitions_total: None,
                }),
                state: Some(SmartEnergyManagementPsPowerSequenceState {
                    // VALUE     | REQUIREMENT(S)
                    // ----------+-------------------------------------------------------------------------------------
                    // inactive  | OHPCF-011/1 (availability of optional consumption of power)
                    // scheduled | OHPCF-012/2/1 (power consumption process reported as scheduled but not started yet)
                    // running   | OHPCF-012/2/2 (power consumption process reported as currently consumes power)
                    //  "        | OHPCF-012/4 (compressor begins with power consumption autonomously at reported time)
                    //  "        | OHPCF-022/3 (resume paused power consumption process)
                    // paused    | OHPCF-012/2/3 (power consumption process reported as paused)
                    //  "        | OHPCF-022/2 (pause running power consumption process)
                    // completed | OHPCF-006/3 (announcement of completion of a process)
                    //  "        | OHPCF-012/2/5 (power consumption process reported as completed)
                    // invalid   | OHPCF-006/1 (explicit announcement of abortion of power consumption process)
                    //  "        | OHPCF-012/2/4 (power consumption process reported as stopped or aborted)
                    //  "        | OHPCF-022/1 (stop / abort a power consumption process)
                    state: Some(PowerSequenceState::Running),
                    // if state == "running":
                    //     must contain currently active slot number
                    //     [OHPCF-012 (report of current state of power consumption process)]
                    // else:
                    //     must be omitted
                    //     [OHPCF-011 (announcement of optional power consumption process)]
                    active_slot_number: Some(1),
                    // time that the slot has already been in running mode
                    // optional
                    elapsed_slot_time: Some(XsDuration::hms(0, 10, 0)),
                    // time that the slot still needs to be in "running" state
                    // must be omitted, since it is marked as "F" in the ohpcf spec
                    remaining_slot_time: None,
                    // whether the sequence can be configured by a client
                    // must be `true` for ohpcf use case
                    sequence_remote_controllable: Some(true),
                    // must be omitted, since it is marked as "F" in the ohpcf spec
                    active_repetition_number: None,
                    // TODO: unsure about this. this field is present in xsd, but is not mentioned in OHPCF use case.
                    remaining_pause_time: None,
                }),
                // scenario 1:
                //  - must be omitted for OHPCF-011/3 (announcement that optional power consumption will not be started
                //    automatically by compressor)
                //  - mandatory for OHPCF-012/1 (report of start time of consumption of power)
                // scenario 2:
                //  - mandatory for OHPCF-021/1 (cem schedules power consumption process of compressor)
                // if this element is absent, the sequence will not be started autonomously by the server
                schedule: Some(SmartEnergyManagementPsPowerSequenceSchedule {
                    // scenario 1: OHPCF-012/1 (report of start time of consumption of power)
                    // scenario 2: OHPCF-021/1 (cem schedules power consumption process of compressor)
                    start_time: Some(XsDuration::hms(0, 10, 0)),
                    // TODO: unsure about this. this field is present in xsd, but is not mentioned in OHPCF use case.
                    end_time: None,
                }),
                schedule_constraints: Some(
                    SmartEnergyManagementPsPowerSequenceScheduleConstraints {
                        earliest_start_time: Some(XsDuration::hms(0, 10, 0)),
                        latest_end_time: Some(XsDuration::hms(0, 30, 0)),
                    },
                ),
                operating_constraints_interrupt: Some(
                    SmartEnergyManagementPsPowerSequenceOperatingConstraintsInterrupt {
                        // OHPCF-011/6 (announcement whether optional power consumption process may be paused & resumed)
                        // OHPCF-012/3 (report current options of interrupting/resuming consumption to cem)
                        // if the sequence is pausable by the client, this must be set to `true`.
                        // if isPausable is not `true`, isStoppable must be `true` in order to satisfy OHPCF-011/7
                        // (compressor must offer at least one option to interrupt the power consumption).
                        is_pausable: Some(true),
                        // OHPCF-011/5 (announcement whether an optional power consumption may be stopped)
                        // OHPCF-012/3 (report current options of interrupting/resuming consumption to cem)
                        // if the sequence is stoppable by the client, this must be set to `true`.
                        // if isStoppable is not `true`, isPausable must be `true` in order to satisfy OHPCF-011/7
                        // (compressor must offer at least one option to interrupt the power consumption).
                        is_stoppable: Some(true),
                        // TODO: unsure about this. this field is present in xsd, but is not mentioned in OHPCF use case
                        max_cycles_per_day: None,
                    },
                ),
                operating_constraints_duration: Some(
                    SmartEnergyManagementPsPowerSequenceOperatingConstraintsDuration {
                        // OHPCF-008 (compressor should inform cem about minimal time the compressor must run until it
                        // can be stopped again)
                        active_duration_min: Some(XsDuration::hms(0, 10, 0)),
                        // TODO: unsure about this. this field is present in xsd, but is not mentioned in OHPCF use case
                        active_duration_max: None,
                        // OHPCF-009 (compressor should inform cem about minimal time the compressor needs after it was
                        // stopped to be restarted)
                        pause_duration_min: Some(XsDuration::hms(0, 20, 0)),
                        // TODO: unsure about this. this field is present in xsd, but is not mentioned in OHPCF use case
                        pause_duration_max: None,
                        // TODO: unsure about this. this field is present in xsd, but is not mentioned in OHPCF use case
                        active_duration_sum_min: None,
                        // TODO: unsure about this. this field is present in xsd, but is not mentioned in OHPCF use case
                        active_duration_sum_max: None,
                    },
                ),
                // TODO: unsure about this. this field is present in the xsd, but is not mentioned in the OHPCF use case
                operating_constraints_resume_implication: None,
                // only exactly one value is allowed.
                power_time_slot: vec![SmartEnergyManagementPsPowerTimeSlot {
                    schedule: Some(SmartEnergyManagementPsPowerTimeSlotSchedule {
                        // powerSequenceId-wide unique slot identifier
                        // "shall be set as sub identifier"
                        slot_number: Some(1),
                        // TODO: unsure about this. this field is present in xsd, but is not mentioned in OHPCF use case
                        time_period: None,
                        // must be omitted due to OHPCF-011/4 (announcement that duration of optional power consumption
                        // is unknown)
                        default_duration: None,
                        // uncertainty of defaultDuration field
                        // TODO: unsure about this. this field is present in xsd, but is not mentioned in OHPCF use case
                        duration_uncertainty: None,
                        // if the slot is optional (i.e. powerTimeSlot.scheduleContraints.optionalSlot == true), this
                        // element reflects the current status of the slot: true = slot will be executed, false = slot
                        // will not be executed.
                        // TODO: unsure about this. this field is present in xsd, but is not mentioned in OHPCF use case
                        slot_activated: None,
                        description: None,
                    }),
                    // TODO: unsure about this. this field is present in xsd, but is not mentioned in the OHPCF use case
                    schedule_constraints: None,
                    value_list: Some(SmartEnergyManagementPsPowerTimeSlotValueList {
                        // must be either one or two elements according to ohpcf use case. cannot be empty.
                        // if more than one value is present, each value must have a different valueType.
                        value: vec![SmartEnergyManagementPsPowerTimeSlotValueListValue {
                            // valid options:
                            //  - "power" [OHPCF-011/2/2] (announcement of "good approximation" of power value of
                            //    optional power consumption process)
                            //  - "powerMax" [OHPCF-011/2/3] (announcement of maximum power of optional power
                            //    consumption process)
                            value_type: Some(PowerTimeSlotValue::Power),
                            // OHPCF-011/2/1 (announcement of of power value of optional power consumption process)
                            // scaled number rules (`effective_value = number.unwrap() * 10.pow(scale.unwrap_or(0))`)
                            value: Some(ScaledNumber {
                                number: Some(42), // mandatory
                                scale: Some(3),   // default 0
                            }),
                        }],
                    }),
                }],
                // TODO: unsure about this. this field is present in the xsd, but is not mentioned in the OHPCF use case
                schedule_preference: None,
            }],
        }],
    }
}
*/

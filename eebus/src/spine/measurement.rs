use std::fmt::Debug;

use super::types::*;

pub type MeasurementType = super::types::Measurement;

const MAX_MEASUREMENT_COUNT: usize = 2;

pub struct Measurements {
    measurements: &'static [Measurement],
    recent_measurements: heapless::Vec<(u32, ScaledNumber), MAX_MEASUREMENT_COUNT>,
}

impl Measurements {
    pub fn new(measurements: &'static [Measurement]) -> Self {
        Self {
            measurements,
            recent_measurements: heapless::Vec::new(),
        }
    }

    pub fn description_list_data_response(&self) -> MeasurementDescriptionListData {
        MeasurementDescriptionListData {
            measurement_description_data: self
                .measurements
                .iter()
                .map(|m| m.description.clone())
                .collect(),
        }
    }

    pub fn constraints_list_data_response(&self) -> MeasurementConstraintsListData {
        MeasurementConstraintsListData {
            measurement_constraints_data: self
                .measurements
                .iter()
                .map(|m| m.constraints.clone())
                .collect(),
        }
    }

    pub async fn list_data_response<M: MeasurementHandler>(
        &mut self,
        measurement_handler: &M,
        partial_request: Option<&[MeasurementListDataSelectors]>,
    ) -> (MeasurementListData, Result<(), M::Error>) {
        fn selector_matches(sel: &MeasurementListDataSelectors, id: u32) -> bool {
            sel.measurement_id.is_some_and(|sel_id| sel_id == id)
        }

        let requested_ids = self.measurements.iter().map(|m| m.id()).filter(|id| {
            partial_request.is_none_or(|req| req.iter().any(|sel| selector_matches(sel, *id)))
        });

        let mut measurement_data = Vec::new();
        let mut error_result = Ok(());
        for id in requested_ids {
            let (value, state) = match measurement_handler.measure(id).await {
                Ok(value) => {
                    match self.recent_measurements.iter().position(|m| m.0 == id) {
                        Some(idx) => self.recent_measurements[idx].1 = value.clone(),
                        None => _ = self.recent_measurements.push((id, value.clone())),
                    }
                    (Some(value), MeasurementValueState::Normal)
                }
                Err(e) => {
                    if error_result.is_ok() {
                        error_result = Err(e);
                    }
                    let value = self
                        .recent_measurements
                        .iter()
                        .find(|m| m.0 == id)
                        .map(|m| m.1.clone());
                    (value, MeasurementValueState::Error)
                }
            };

            measurement_data.push(MeasurementData {
                measurement_id: Some(id),
                // specifies whether the actual value, an average, the minimum, etc. is given
                // must be "value" for MPC use case
                value_type: Some(MeasurementValue::Value),
                // time of creation of a measurement value element (i.e. time of measurement / calculation / ...),
                // or time of creation of measurementData instance.
                // in MPC use case, historical values are forbidden. only the newest measurement value shall be stated.
                // this field is optional, probably because it will be assumed to be "just now" if it is omitted,
                // but we fill it anyways with a duration of "zero", which definitely represents "just now".
                timestamp: Some(AbsoluteOrRelativeTime::XsDuration(XsDuration::zero())),
                // the measured value itself
                // scaled number rules (`effective_value = number.unwrap() * 10.pow(scale.unwrap_or(0))`)
                // MPC-001: energy consumption uses positive values, energy production uses negative values
                // MPC-011: total active power
                value,
                // some values require information about their evaluation period, e.g. when determining average values.
                // since we have "valueType" = "value", i.e. an actual value, this is likely not needed.
                // also, it is not mentioned in the MPC use case, further supporting the "not needed" theory.
                evaluation_period: None,
                // whether the value was measured, calculated or estimated
                value_source: Some(MeasurementValueSource::MeasuredValue),
                // whether the value tendency is rising, stable or falling
                // TODO: unsure about this. this field is present in the xsd, but is not mentioned in the MPC use case.
                value_tendency: None,
                // recommended
                // MPC-003: measurement values have state that defines whether value is correct or should be ignored by
                //          the monitoring appliacnce. options: normal (value correct), out of range (shall be ignored),
                //          error (shall be ignored)
                value_state: Some(state),
            });
        }

        (MeasurementListData { measurement_data }, error_result)
    }
}

pub struct Measurement {
    description: MeasurementDescriptionData,
    constraints: MeasurementConstraintsData,
}

impl Measurement {
    pub const fn new(
        id: u32,
        ty: MeasurementType,
        commodity: Commodity,
        scope: Scope,
        unit: UnitOfMeasurement,
        bounds: Option<MeasurementBounds>,
    ) -> Self {
        let (min, max, step) = match bounds {
            Some(b) => (Some(b.min), Some(b.max), Some(b.step)),
            None => (None, None, None),
        };

        Self {
            description: MeasurementDescriptionData {
                // enables the identification of different measurands on one spine feature
                // "shall be set as primary identifier"
                measurement_id: Some(id),
                measurement_type: Some(ty),
                commodity_type: Some(commodity),
                unit: Some(unit),
                scope_type: Some(scope),
                // TODO: unsure about this. this field is present in the xsd, but is not mentioned in the MPC use case.
                calibration_value: None,
                label: None,
                description: None,
            },
            constraints: MeasurementConstraintsData {
                // enables the identification of different sensors on one spine feature
                // "shall be set as primary identifier"
                measurement_id: Some(id),
                // minimum possible measurement value measurable by the feature
                // should be provided (but not mandatory)
                value_range_min: min,
                // maximum possible measurement value measurable by the feature
                // should be provided (but not mandatory)
                value_range_max: max,
                // minimum step size between two different measurement values
                // should be provided (but not mandatory)
                value_step_size: step,
            },
        }
    }

    pub fn id(&self) -> u32 {
        self.description.measurement_id.unwrap()
    }
}

// fields use scaled number rules (`effective_value = number.unwrap() * 10.pow(scale.unwrap_or(0))`)
pub struct MeasurementBounds {
    min: ScaledNumber,
    max: ScaledNumber,
    step: ScaledNumber,
}

impl MeasurementBounds {
    pub const fn new(min: ScaledNumber, max: ScaledNumber, step: ScaledNumber) -> Self {
        Self { min, max, step }
    }
}

pub trait MeasurementHandler {
    type Error: Debug;

    async fn measure(&self, measurement_id: u32) -> Result<ScaledNumber, Self::Error>;
}

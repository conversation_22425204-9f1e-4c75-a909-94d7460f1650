use std::fmt::Debug;

use super::{error::SpineError, types::*};
use crate::log::*;

const MAX_LIMIT_COUNT: usize = 1;

pub struct LoadControl {
    limits: heapless::Vec<Limit, MAX_LIMIT_COUNT>,
}

impl LoadControl {
    pub fn new(limits: heapless::Vec<Limit, MAX_LIMIT_COUNT>) -> Self {
        Self { limits }
    }

    pub fn lpc_default(limit_id: u32, ac_power_total_measurement_id: u32) -> Self {
        let mut limits = heapless::Vec::new();

        _ = limits.push(Limit::new(
            limit_id,
            // specifies whether the limit is a maximum or minimum.
            // must be "signDependentAbsValueLimit" for LPC.
            // the limit type shall be interpreted in the following way, given that passive sign conversion is being
            // used. if active sign conversion is applied, the logic shall apply vice versa:
            //  - a positive value only limits the consumption.
            //  - a negative value only limits the absolute value of the production.
            // since the value of the limit given by LoadControlLimitData::value is specified to only ever be positive
            // as per the LPC use case, only the consumption will be limited.
            LoadControlLimit::SignDependentAbsValueLimit,
            // specifies "how important" the limit is.
            // must be "obligation" for LPC.
            LoadControlCategory::Obligation,
            // specifies whether the consumption or production should be limited.
            // must be "consume" for LPC.
            EnergyDirection::Consume,
            // the measurand linked to the limit.
            // since we support MPC, this has to be the "acPowerTotal" measurement id.
            ac_power_total_measurement_id,
            // must be "W" for LPC
            UnitOfMeasurement::Watt,
            // must be "activePowerLimit" for LPC
            Scope::ActivePowerLimit,
        ));

        Self::new(limits)
    }

    pub fn read_descriptions(&self) -> LoadControlLimitDescriptionListData {
        LoadControlLimitDescriptionListData {
            load_control_limit_description_data: self
                .limits
                .iter()
                .map(|limit| limit.description.clone())
                .collect(),
        }
    }

    pub fn read_data(&self) -> LoadControlLimitListData {
        LoadControlLimitListData {
            load_control_limit_data: self.limits.iter().map(Limit::limit_data).collect(),
        }
    }

    pub async fn update<L: LpcHandler>(
        &mut self,
        request: &LoadControlLimitListData,
        lpc: &L,
    ) -> Result<(), SpineError> {
        for new_limit in &request.load_control_limit_data {
            let Some(new_limit_id) = new_limit.limit_id else {
                warn!("received write limit without id");
                return Err(SpineError::Unsupported);
            };

            let Some(our_limit) = self
                .limits
                .iter_mut()
                .find(|limit| limit.id() == new_limit_id)
            else {
                warn!("received write limit without id");
                return Err(SpineError::Unsupported);
            };

            our_limit.update(new_limit, lpc).await?;
        }

        Ok(())
    }
}

pub struct Limit {
    description: LoadControlLimitDescriptionData,
    active: Option<ActiveLimit>,
}

impl Limit {
    pub const fn new(
        id: u32,
        ty: LoadControlLimit,
        category: LoadControlCategory,
        direction: EnergyDirection,
        measurement_id: u32,
        unit: UnitOfMeasurement,
        scope_type: Scope,
    ) -> Self {
        Self {
            description: LoadControlLimitDescriptionData {
                limit_id: Some(id),
                limit_type: Some(ty),
                limit_category: Some(category),
                limit_direction: Some(direction),
                measurement_id: Some(measurement_id),
                unit: Some(unit),
                scope_type: Some(scope_type),
                label: None,
                description: None,
            },
            active: None,
        }
    }

    fn id(&self) -> u32 {
        self.description.limit_id.unwrap()
    }

    fn limit_data(&self) -> LoadControlLimitData {
        LoadControlLimitData {
            limit_id: self.description.limit_id,
            // states whether the limit may be changed by a client.
            // must be "true" for LPC.
            is_limit_changeable: Some(true),
            // states whether the limits is currently active.
            // LPC-007 (as soon as duration expires, the controllable system shall deactivate the limit)
            // LPC-008 (the energy guard may activate or deactivate the limit)
            // LPC-009 (the controllable system shall set the limit according to its state)
            // if set to false, the timePeriod and value elements shall be ignored.
            // if set to true, the timePeriod and value elements shall be applied.
            is_limit_active: Some(self.active.is_some()),
            // the period where the limit shall be active.
            // shall be set only if "isLimitActive" is true.
            time_period: self.active.as_ref().map(|active| TimePeriod {
                start_time: None,
                end_time: Some(AbsoluteOrRelativeTime::XsDuration(active.end_time.clone())),
            }),
            // the actual limit
            // scaled number rules (`effective_value = number.unwrap() * 10.pow(scale.unwrap_or(0))`)
            // ScaledNumber::number must be positive in this case
            value: self.active.as_ref().map(|active| active.value.clone()),
        }
    }

    async fn update<L: LpcHandler>(
        &mut self,
        new_limit: &LoadControlLimitData,
        lpc: &L,
    ) -> Result<(), SpineError> {
        // LPC mandates the use of changeable limits
        match new_limit.is_limit_changeable {
            Some(true) | None => (),
            Some(false) => {
                warn!("peer tried to deactivate changeability of limit");
                return Err(SpineError::Unsupported);
            }
        }

        let update_required = match (new_limit.is_limit_active, self.active.as_mut()) {
            // peer requests limit to be deactivated
            //   => deactivate limit
            (Some(false), _) => {
                if new_limit.time_period.is_some() || new_limit.value.is_some() {
                    warn!("peer tried to deactivate limit whilst keeping values");
                    return Err(SpineError::Unsupported);
                }
                self.active = None;
                true
            }
            // peer requests end time and/or limit value change of currently active limit
            //   => change end time and/or limit value
            (Some(true) | None, Some(active)) => {
                let mut update_required = false;
                if let Some(new_time_period) = &new_limit.time_period
                    && let Some(new_end_time) = &new_time_period.end_time
                {
                    match new_end_time {
                        AbsoluteOrRelativeTime::XsDateTime(_) => {
                            warn!("absolute time is not supported");
                            return Err(SpineError::Unsupported);
                        }
                        AbsoluteOrRelativeTime::XsDuration(d) => active.end_time = d.clone(),
                    }
                    update_required = true;
                }
                if let Some(new_value) = &new_limit.value {
                    active.value = new_value.clone();
                    update_required = true;
                }
                update_required
            }
            // peer requests activation of limit
            // (this requires that the client will send and appropriate end time and limit value)
            //   => activate limit
            (Some(true), None) => {
                let Some((end_time, value)) = new_limit
                    .time_period
                    .as_ref()
                    .and_then(|tp| tp.end_time.as_ref())
                    .zip(new_limit.value.as_ref())
                else {
                    warn!("peer tried to activate limit without values");
                    return Err(SpineError::Unsupported);
                };
                let end_time = match end_time {
                    AbsoluteOrRelativeTime::XsDateTime(_) => {
                        warn!("absolute time is not supported");
                        return Err(SpineError::Unsupported);
                    }
                    AbsoluteOrRelativeTime::XsDuration(d) => d.clone(),
                };
                let value = value.clone();
                self.active = Some(ActiveLimit { end_time, value });
                true
            }
            // peer requests no change in limit activation status
            //   => check that peer does not accidentally want to change values that do not exist
            (None, None) => {
                if new_limit.time_period.is_some() || new_limit.value.is_some() {
                    warn!("peer tried to update values of deactivated limit");
                    return Err(SpineError::Unsupported);
                }
                false
            }
        };

        if update_required {
            lpc.write_limit(&self.active)
                .await
                .map_err(|e| SpineError::Custom(format!("{e:?}")))?;
        }

        Ok(())
    }
}

pub struct ActiveLimit {
    pub end_time: XsDuration,
    pub value: ScaledNumber,
}

pub trait LpcHandler {
    type Error: Debug;

    async fn write_limit(&self, limit: &Option<ActiveLimit>) -> Result<(), Self::Error>;
}

use std::{fmt, future::AsyncDrop, pin::Pin};

use paste::paste;

use crate::{
    log::*,
    spine::{error::SpineError, types::*, Function},
};

#[derive(Debug)]
pub enum Action<E, S> {
    FullRead {
        elements: E,
    },
    PartialRead {
        elements: E,
        one_of: Vec<S>,
    },
    FullWrite,
    /// Add or modify list items, leave unmentioned items unchanged
    Upsert,
    UpsertPartial {
        one_of: Vec<S>,
    },
    DeleteElements {
        elements: E,
    },
    DeleteElementsIn {
        elements: E,
        one_of: Vec<S>,
    },
    Call,
    Notify,
    Reply,

    Sequence(Vec<Action<E, S>>),
}

#[allow(type_alias_bounds)]
pub type ActionFor<F: Function> = Action<F::Elements, F::Selectors>;

#[derive(Debug)]
pub struct RequestData<F>
where
    F: Function,
{
    pub action: F::Action,
    pub function: F,
    pub reply: ReplyToken<F>,
}

#[derive(Debug)]
#[allow(clippy::large_enum_variant)]
pub enum Request {
    /// rw
    MeasurementListData(RequestData<MeasurementListData>),
    /// ro
    NodeManagementDetailedDiscoveryData(RequestData<NodeManagementDetailedDiscoveryData>),
    /// ro
    NodeManagementUseCaseData(RequestData<NodeManagementUseCaseData>),
    /// rw
    SmartEnergyManagementPsData(RequestData<SmartEnergyManagementPsData>),
    /// call
    NodeManagementBindingRequestCall(RequestData<NodeManagementBindingRequestCall>),
    /// call
    NodeManagementSubscriptionRequestCall(RequestData<NodeManagementSubscriptionRequestCall>),
    /// ro
    DeviceConfigurationKeyValueDescriptionListData(
        RequestData<DeviceConfigurationKeyValueDescriptionListData>,
    ),
    /// rw
    DeviceConfigurationKeyValueListData(RequestData<DeviceConfigurationKeyValueListData>),
    /// ro
    ElectricalConnectionDescriptionListData(RequestData<ElectricalConnectionDescriptionListData>),
    /// ro
    ElectricalConnectionParameterDescriptionListData(
        RequestData<ElectricalConnectionParameterDescriptionListData>,
    ),
    /// ro
    LoadControlLimitDescriptionListData(RequestData<LoadControlLimitDescriptionListData>),
    /// rw
    LoadControlLimitListData(RequestData<LoadControlLimitListData>),
    /// ro
    MeasurementConstraintsListData(RequestData<MeasurementConstraintsListData>),
    /// ro
    MeasurementDescriptionListData(RequestData<MeasurementDescriptionListData>),
    /// ro
    DeviceDiagnosisHeartbeatData(RequestData<DeviceDiagnosisHeartbeatData>),
}

macro_rules! define_handler_read {
    ($func_name:ident, $ty:ty) => {
        paste! {
            async fn [<handle_ $func_name _read>](
                &mut self,
                elements: &<$ty as Function>::Elements,
            ) -> Result<$ty, SpineError>;
        }
    };

    ($func_name:ident, $ty:ty, partial) => {
        define_handler_read! { $func_name, $ty }

        paste! {
            async fn [<handle_ $func_name _read_partial>](
                &mut self,
                elements: &<$ty as Function>::Elements,
                one_of: &[<$ty as Function>::Selectors],
            ) -> Result<$ty, SpineError>;
        }
    };
}

macro_rules! define_handler_upsert {
    ($func_name:ident, $ty:ty) => {
        paste! {
            async fn [<handle_ $func_name _upsert>](
                &mut self,
                request: &$ty,
            ) -> Result<(), SpineError>;
        }
    };

    ($func_name:ident, $ty:ty, partial) => {
        define_handler_upsert! { $func_name, $ty }

        paste! {
            async fn [<handle_ $func_name _upsert_partial>](
                &mut self,
                request: &$ty,
                one_of: &[<$ty as Function>::Selectors],
            ) -> Result<(), SpineError>;
        }
    };
}

macro_rules! define_handler_call {
    ($func_name:ident, $ty:ty) => {
        paste! {
            async fn [<handle_ $func_name _call>](
                &mut self,
                request: &$ty,
            ) -> Result<(), SpineError>;
        }
    };
}

macro_rules! define_handler_notify {
    ($func_name:ident, $ty:ty) => {
        paste! {
            async fn [<handle_ $func_name _notify>](
                &mut self,
                notification: &$ty,
            ) -> Result<(), SpineError>;
        }
    };
}

macro_rules! define_handler_reply {
    ($func_name:ident, $ty:ty) => {
        paste! {
            async fn [<handle_ $func_name _reply>](
                &mut self,
                reply: &$ty,
            ) -> Result<(), SpineError>;
        }
    };
}

macro_rules! match_action_read {
    ($self:ident, $r:ident, $handler_func:ident) => {
        paste! {
            match &$r.action {
                Action::FullRead { elements } => {
                    $r.reply.reply($self.[<handle_ $handler_func _read>](elements).await).await
                },
                Action::PartialRead { elements, one_of } => {
                    $r.reply.reply($self.[<handle_ $handler_func _read_partial>](elements, one_of).await).await
                }
                _ => Err(SpineError::Unsupported),
            }
        }
    }
}

macro_rules! match_action_read_upsert {
    ($self:ident, $r:ident, $handler_func:ident) => {
        paste! {
            match &$r.action {
                Action::FullRead { elements } => {
                    $r.reply.reply($self.[<handle_ $handler_func _read>](elements).await).await
                },
                Action::PartialRead { elements, one_of } => {
                    $r.reply.reply($self.[<handle_ $handler_func _read_partial>](elements, one_of).await).await
                }
                Action::Upsert => {
                    $r.reply.reply($self.[<handle_ $handler_func _upsert>](&$r.function).await.map(|()| Default::default())).await
                }
                Action::UpsertPartial { one_of } => {
                    $r.reply.reply($self.[<handle_ $handler_func _upsert_partial>](&$r.function, one_of).await.map(|()| Default::default())).await
                }
                _ => Err(SpineError::Unsupported),
            }
        }
    }
}

macro_rules! match_action_call {
    ($self:ident, $r:ident, $handler_func:ident) => {
        paste! {
            match &$r.action {
                Action::Call => {
                    $r.reply.reply($self.[<handle_ $handler_func _call>](&$r.function).await.map(|()| Default::default())).await
                },
                _ => Err(SpineError::Unsupported),
            }
        }
    }
}

pub trait Handler {
    define_handler_read! { detailed_discovery, NodeManagementDetailedDiscoveryData, partial }
    define_handler_read! { use_case, NodeManagementUseCaseData, partial }
    define_handler_read! { smart_energy_management, SmartEnergyManagementPsData, partial }
    define_handler_upsert! { smart_energy_management, SmartEnergyManagementPsData, partial }
    define_handler_call! { binding_request, NodeManagementBindingRequestCall }
    define_handler_call! { subscription_request, NodeManagementSubscriptionRequestCall }
    define_handler_read! { device_config_description, DeviceConfigurationKeyValueDescriptionListData, partial }
    define_handler_read! { device_config, DeviceConfigurationKeyValueListData, partial }
    define_handler_upsert! { device_config, DeviceConfigurationKeyValueListData, partial }
    define_handler_read! { electrical_connection_description, ElectricalConnectionDescriptionListData, partial }
    define_handler_read! { electrical_connection_parameter_description, ElectricalConnectionParameterDescriptionListData, partial }
    define_handler_read! { load_control_limit_description, LoadControlLimitDescriptionListData, partial }
    define_handler_read! { load_control_limit, LoadControlLimitListData, partial }
    define_handler_upsert! { load_control_limit, LoadControlLimitListData, partial }
    define_handler_read! { measurement_description, MeasurementDescriptionListData, partial }
    define_handler_read! { measurement_constraints, MeasurementConstraintsListData, partial }
    define_handler_read! { measurement, MeasurementListData, partial }
    define_handler_reply! { heartbeat, DeviceDiagnosisHeartbeatData }
    define_handler_notify! { heartbeat, DeviceDiagnosisHeartbeatData }

    //define_handler_read! { heartbeat, DeviceDiagnosisHeartbeatData }
    async fn handle_heartbeat_read(
        &mut self,
        elements: &<DeviceDiagnosisHeartbeatData as Function>::Elements,
        new_request_destination: FeatureAddress,
    ) -> Result<DeviceDiagnosisHeartbeatData, SpineError>;

    #[rustfmt::skip]
    async fn handle(&mut self, request: Request) -> Result<(), SpineError> {
        match request {
            Request::NodeManagementDetailedDiscoveryData(r) => match_action_read!(self, r, detailed_discovery),
            Request::NodeManagementUseCaseData(r) => match_action_read!(self, r, use_case),
            Request::SmartEnergyManagementPsData(r) => match_action_read_upsert!(self, r, smart_energy_management),
            Request::NodeManagementBindingRequestCall(r) => match_action_call!(self, r, binding_request),
            Request::NodeManagementSubscriptionRequestCall(r) => match_action_call!(self, r, subscription_request),
            Request::DeviceConfigurationKeyValueDescriptionListData(r) => match_action_read!(self, r, device_config_description),
            Request::DeviceConfigurationKeyValueListData(r) => match_action_read_upsert!(self, r, device_config),
            Request::ElectricalConnectionDescriptionListData(r) => match_action_read!(self, r, electrical_connection_description),
            Request::ElectricalConnectionParameterDescriptionListData(r) => match_action_read!(self, r, electrical_connection_parameter_description),
            Request::LoadControlLimitDescriptionListData(r) => match_action_read!(self, r, load_control_limit_description),
            Request::LoadControlLimitListData(r) => match_action_read_upsert!(self, r, load_control_limit),
            Request::MeasurementDescriptionListData(r) => match_action_read!(self, r, measurement_description),
            Request::MeasurementConstraintsListData(r) => match_action_read!(self, r, measurement_constraints),
            Request::MeasurementListData(r) => match_action_read!(self, r, measurement),
            Request::DeviceDiagnosisHeartbeatData(r) => match &r.action {
                Action::FullRead { elements } => {
                    let destination = r.reply.destination.clone();
                    r.reply.reply(self.handle_heartbeat_read(elements, destination).await).await
                }
                Action::Notify => self.handle_heartbeat_notify(&r.function).await,
                Action::Reply => self.handle_heartbeat_reply(&r.function).await,
                _ => Err(SpineError::Unsupported),
            },
        }
    }
}

pub trait ReplySender: Send + Sync {
    type Item;

    fn send_reply(
        self: Box<Self>,
        data: Result<Self::Item, SpineError>,
    ) -> Pin<Box<dyn Future<Output = Result<(), SpineError>> + Send + 'static>>;
}

pub struct ReplyToken<T> {
    pub(crate) sender: Option<Box<dyn ReplySender<Item = T>>>,
    pub(crate) destination: FeatureAddress,
}

impl<T> ReplyToken<T> {
    pub async fn reply(mut self, data: Result<T, SpineError>) -> Result<(), SpineError> {
        self.sender.take().unwrap().send_reply(data).await
    }
}

impl<T> fmt::Debug for ReplyToken<T> {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("ReplyToken").finish()
    }
}

impl<T> AsyncDrop for ReplyToken<T> {
    type Dropper<'a>
        = impl Future<Output = ()>
    where
        Self: 'a;

    fn async_drop(mut self: Pin<&mut Self>) -> Self::Dropper<'_> {
        async move {
            if let Some(sender) = self.sender.take() {
                if let Err(e) = sender.send_reply(Err(SpineError::ReplyInternalError)).await {
                    error!("failed to send from async drop impl: {}", e);
                }
            }
        }
    }
}

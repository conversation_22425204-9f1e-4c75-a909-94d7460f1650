pub use self::{custom_types::*, eebus_spine::*, nanoserde_utils::*, traits::*, xsd_support::*};

mod eebus_spine {
    #![allow(dead_code)]
    #![allow(unused_assignments)]
    #![allow(unused_imports)]
    #![allow(unused_mut)]
    #![allow(unused_variables)]
    #![allow(clippy::all)]

    include!(concat!(env!("OUT_DIR"), "/eebus_spine.rs"));

    impl Default for NodeManagementBindingRequestCall {
        fn default() -> Self {
            Self {
                binding_request: None,
            }
        }
    }

    impl Default for NodeManagementSubscriptionRequestCall {
        fn default() -> Self {
            Self {
                subscription_request: None,
            }
        }
    }
}

mod custom_types;
mod nanoserde_utils;
mod traits;
mod xsd_support;

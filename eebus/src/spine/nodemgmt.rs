use bitflags::bitflags;

use super::{error::SpineError, types::*};

// TODO: (formatted as field_name: todo_text)
//  - feature_group: link to other features, e.g. LPC / MPC related stuff (?) (recommended but not required)
//  - network_feature_set: is "smart" correct?
//  - technology_address: address of device in its own "communication technology", i.e. ebus address of heat pump
//    (probably not necessary)
//  - native_setup: what is this for?
//  - communications_technology_information: what is this for?
//  - label: add free text labels
//  - description: add free text descriptions
//  - max_response_delay: what should we put here? default is 10 seconds.
//  - NodeManagementUseCaseDataTypeUseCaseInformation::address::feature: do we need to specify features for use cases?
//  - LPC_SERVER::scenarios: do we need scenario 4?
//  - MPC_SERVER::scenarios: do we need scenarios 2, 3, 4 or 5?

const MAX_ENTITY_COUNT: usize = 3;
const SPECIFICATION_VERSION: &str = super::VERSION;

#[derive(<PERSON><PERSON>, Debug)]
pub struct NodeManagement {
    pub device: EEBusDevice,
}

#[derive(Clone, Debug)]
pub struct EEBusDevice {
    pub id: String,
    pub device_type: Device,
    pub entities: heapless::Vec<EEBusEntity, MAX_ENTITY_COUNT>,
    pub subscriptions: Vec<Relationship>,
    pub bindings: Vec<Relationship>,
    pub state_change: Option<NetworkManagementStateChange>,
}

#[derive(Clone, Debug, PartialEq)]
pub struct Relationship {
    pub client_address: FeatureAddress,
    pub server_address: FeatureAddress,
    pub server_feature_type: Feature,
}

#[derive(Clone, Debug)]
pub struct EEBusEntity {
    pub id: &'static [u32],
    pub entity_type: Entity,
    pub features: &'static [EEBusFeature],
    pub use_cases: &'static [EEBusUseCase],
    pub state_change: Option<NetworkManagementStateChange>,
}

#[derive(Clone, Debug)]
pub struct EEBusFeature {
    pub id: u32,
    pub feature_type: Feature,
    pub role: Role,
    pub functions: &'static [EEBusFunction],
}

#[derive(Clone, Debug)]
pub struct EEBusFunction {
    pub function: Function,
    pub operations: EEBusOperations,
}

bitflags! {
    #[derive(Copy, Clone, Debug)]
    pub struct EEBusOperations: u8 {
        const READ = 0b0001;
        const READ_PARTIAL = 0b0011;
        const WRITE = 0b0100;
        const WRITE_PARTIAL = 0b1100;
    }
}

#[derive(Clone, Debug)]
pub struct EEBusUseCase {
    name: &'static str,
    version: &'static str,
    actor: &'static str,
    scenarios: &'static [u32],
}

impl NodeManagement {
    pub fn new(device: EEBusDevice) -> Self {
        Self { device }
    }

    /// Must be sent to client since state changes would not be reported properly otherwise
    #[must_use]
    pub fn discovery_details_response(&mut self) -> NodeManagementDetailedDiscoveryData {
        NodeManagementDetailedDiscoveryData {
            specification_version_list: Some(NodeManagementSpecificationVersionList {
                specification_version: vec![String::from(SPECIFICATION_VERSION)],
            }),
            device_information: self.device.discovery_device_information(false),
            entity_information: self.device.discovery_entity_information(false),
            feature_information: self.device.discovery_feature_information(),
        }
    }

    pub fn use_case_response(&self) -> NodeManagementUseCaseData {
        let mut use_case_information = Vec::new();

        for entity in &self.device.entities {
            for use_case in entity.use_cases {
                use_case_information.push(NodeManagementUseCaseDataUseCaseInformation {
                    address: Some(FeatureAddress {
                        device: Some(self.device.id.clone()),
                        entity: Vec::from(entity.id),
                        feature: None,
                    }),
                    actor: Some(String::from(use_case.actor)),
                    use_case_support: vec![UseCaseInformationDataUseCaseSupport {
                        use_case_name: Some(String::from(use_case.name)),
                        use_case_version: Some(String::from(use_case.version)),
                        // only relevant for client functionality of a use case.
                        // omitted or true = client functionality available.
                        // false = client functionality not available
                        use_case_available: Some(false),
                        scenario_support: Vec::from(use_case.scenarios),
                        // should always be "release" for fully released use cases
                        use_case_document_sub_revision: Some(String::from("release")),
                    }],
                });
            }
        }

        NodeManagementUseCaseData {
            use_case_information,
        }
    }

    /// Returns true when there are changes to the node management instance (i.e. added or removed devices),
    /// which would require a notify of the changes to the client.
    pub fn discovery_notify_required(&self) -> bool {
        let device_changed = self.device.state_change.is_some();
        let entities_changed = self
            .device
            .entities
            .iter()
            .any(|e| e.state_change.is_some());
        device_changed || entities_changed
    }

    /// Must be sent to client since state changes would not be reported properly otherwise
    #[must_use]
    pub fn get_discovery_notify_message(&mut self) -> NodeManagementDetailedDiscoveryData {
        NodeManagementDetailedDiscoveryData {
            specification_version_list: Some(NodeManagementSpecificationVersionList {
                specification_version: vec![String::from(SPECIFICATION_VERSION)],
            }),
            device_information: self.device.discovery_device_information(true),
            entity_information: self.device.discovery_entity_information(true),
            feature_information: self.device.discovery_feature_information(),
        }
    }
}

impl EEBusDevice {
    pub fn new(id: String, device_type: Device) -> Self {
        Self {
            id,
            device_type,
            entities: heapless::Vec::new(),
            state_change: Some(NetworkManagementStateChange::Added),
            subscriptions: vec![],
            bindings: vec![],
        }
    }

    pub fn add_entity(&mut self, entity: EEBusEntity) -> Result<(), SpineError> {
        let existing_entity = self
            .entities
            .iter()
            .enumerate()
            .find(|(_, e)| e.id == entity.id);

        match existing_entity {
            Some((index, existing_entity)) => match existing_entity.state_change {
                Some(NetworkManagementStateChange::Removed) => {
                    // TODO: Assume the following scenario:
                    //   1. Add entity (-> entity state: Some(Added))
                    //   2. Notify new entity to client (-> entity state: None, client now knows about entity)
                    //   3. Remove entity (-> entity state: Some(Removed)) - but do not notify client!!
                    //   4. Add entity again (-> entity state: Some(Added))
                    //   5. Notify "new" entity to client
                    // Now, the client will receive the same entity with the state "Added" again.
                    // We need to check that this doesn't cause any problems (which it shouldn't, because the entity ID
                    // will be the same, thus "overriding" the old entity, but who knows). Or, you know, just hope that
                    // it never happens and that device removals are immediately notified to the client :)
                    self.entities[index] = entity;
                }
                _ => return Err(SpineError::DuplicateId),
            },
            None => self
                .entities
                .push(entity)
                .map_err(|_| SpineError::Overflow)?,
        }

        if !matches!(self.state_change, Some(NetworkManagementStateChange::Added)) {
            self.state_change = Some(NetworkManagementStateChange::Modified);
        }

        Ok(())
    }

    pub fn remove_entity(&mut self, id: &[u32]) -> Option<()> {
        let entity = self.entities.iter_mut().find(|e| e.id == id)?;
        entity.state_change = Some(NetworkManagementStateChange::Removed);

        self.bindings.retain(|r| r.server_address.entity != id);
        self.subscriptions.retain(|r| r.server_address.entity != id);

        Some(())
    }

    pub fn add_binding(
        &mut self,
        binding: &NodeManagementBindingRequestCall,
    ) -> Result<(), SpineError> {
        let binding_request = binding
            .binding_request
            .as_ref()
            .ok_or(SpineError::MissingField)?;
        self.bindings.push(Relationship {
            client_address: binding_request
                .client_address
                .clone()
                .ok_or(SpineError::MissingField)?,
            server_address: binding_request
                .server_address
                .clone()
                .ok_or(SpineError::MissingField)?,
            server_feature_type: binding_request
                .server_feature_type
                .clone()
                .ok_or(SpineError::MissingField)?,
        });
        Ok(())
    }

    pub fn add_subscription(
        &mut self,
        subscription: &NodeManagementSubscriptionRequestCall,
    ) -> Result<(), SpineError> {
        // TODO: verify we have that entity

        let subscription_request = subscription
            .subscription_request
            .as_ref()
            .ok_or(SpineError::MissingField)?;
        self.subscriptions.push(Relationship {
            client_address: subscription_request
                .client_address
                .as_ref()
                .ok_or(SpineError::MissingField)?
                .clone(),
            server_address: subscription_request
                .server_address
                .as_ref()
                .ok_or(SpineError::MissingField)?
                .clone(),
            server_feature_type: subscription_request
                .server_feature_type
                .as_ref()
                .ok_or(SpineError::MissingField)?
                .clone(),
        });
        // TODO: reject duplicates

        Ok(())
    }

    pub fn subscriptions_for_feature(
        &self,
        server_feature_type: Feature,
    ) -> impl Iterator<Item = &Relationship> {
        self.subscriptions
            .iter()
            .filter(move |r| r.server_feature_type == server_feature_type)
    }

    pub fn get_entity(&self, id: &[u32]) -> Option<&EEBusEntity> {
        self.entities.iter().find(|e| e.id == id)
    }

    /// Must be sent to client since state changes would not be reported properly otherwise
    #[must_use]
    fn discovery_device_information(
        &mut self,
        changes_only: bool,
    ) -> Option<NodeManagementDetailedDiscoveryDeviceInformation> {
        let last_state_change = self.state_change.take();

        if changes_only && last_state_change.is_none() {
            return None;
        }

        Some(NodeManagementDetailedDiscoveryDeviceInformation {
            description: Some(
                NodeManagementDetailedDiscoveryDeviceInformationDescription {
                    device_address: Some(NetworkManagementDeviceDescriptionDataDeviceAddress {
                        device: Some(self.id.clone()),
                    }),
                    device_type: Some(self.device_type.clone()),
                    network_management_responsible_address: None, // reserved future use
                    native_setup: None,
                    technology_address: None,
                    communications_technology_information: None,
                    network_feature_set: Some(NetworkManagementFeatureSet::Smart),
                    last_state_change,
                    minimum_trust_level: None, // reserved for future use
                    label: None,
                    description: None,
                },
            ),
        })
    }

    /// Must be sent to client since state changes would not be reported properly otherwise
    #[must_use]
    fn discovery_entity_information(
        &mut self,
        changes_only: bool,
    ) -> Vec<NodeManagementDetailedDiscoveryEntityInformation> {
        self.entities
            .iter_mut()
            .filter(|e| !changes_only || e.state_change.is_some())
            .map(EEBusEntity::discovery_entity_information)
            .collect()
    }

    /// Must be sent to client since state changes would not be reported properly otherwise
    ///
    /// This also removes entities with a "removed" state
    #[must_use]
    fn discovery_feature_information(
        &mut self,
    ) -> Vec<NodeManagementDetailedDiscoveryFeatureInformation> {
        let mut out = Vec::new();
        let mut entity_idx = 0;
        while entity_idx < self.entities.len() {
            match self.entities[entity_idx].state_change {
                // entity marked as removed => remove entity from list and do not return features
                Some(NetworkManagementStateChange::Removed) => _ = self.entities.remove(entity_idx),
                // entity not marked as removed => return all features of entity as normal
                other => {
                    let entity = &self.entities[entity_idx];
                    for feature in entity.features {
                        out.push(feature.discovery_feature_information(entity.id, other));
                    }
                    entity_idx += 1;
                }
            }
        }
        out
    }
}

impl EEBusEntity {
    pub const fn new(
        id: &'static [u32],
        entity_type: Entity,
        features: &'static [EEBusFeature],
        use_cases: &'static [EEBusUseCase],
    ) -> Self {
        Self {
            id,
            entity_type,
            features,
            use_cases,
            state_change: Some(NetworkManagementStateChange::Added),
        }
    }

    pub fn get_feature(&self, id: u32) -> Option<&EEBusFeature> {
        self.features.iter().find(|f| f.id == id)
    }

    /// Must be sent to client since state changes would not be reported properly otherwise
    #[must_use]
    fn discovery_entity_information(&mut self) -> NodeManagementDetailedDiscoveryEntityInformation {
        let last_state_change = self.state_change.take();

        NodeManagementDetailedDiscoveryEntityInformation {
            description: Some(
                NodeManagementDetailedDiscoveryEntityInformationDescription {
                    entity_address: Some(NetworkManagementEntityDescriptionDataEntityAddress {
                        entity: Vec::from(self.id),
                    }),
                    entity_type: Some(self.entity_type.clone()),
                    last_state_change,
                    minimum_trust_level: None, // reserved for future use
                    label: None,
                    description: None,
                },
            ),
        }
    }
}

impl EEBusFeature {
    pub const fn new(
        id: u32,
        feature_type: Feature,
        role: Role,
        functions: &'static [EEBusFunction],
    ) -> Self {
        Self {
            id,
            feature_type,
            role,
            functions,
        }
    }

    /// Must be sent to client since state changes would not be reported properly otherwise
    #[must_use]
    fn discovery_feature_information(
        &self,
        device_id: &[u32],
        last_state_change: Option<NetworkManagementStateChange>,
    ) -> NodeManagementDetailedDiscoveryFeatureInformation {
        let supported_function = self
            .functions
            .iter()
            .map(|f| FunctionProperty {
                function: Some(f.function.clone()),
                possible_operations: Some(PossibleOperations {
                    read: f.operations.contains(EEBusOperations::READ).then(|| {
                        PossibleOperationsRead {
                            partial: f
                                .operations
                                .contains(EEBusOperations::READ_PARTIAL)
                                .then_some(ElementTag {}),
                        }
                    }),
                    write: f.operations.contains(EEBusOperations::WRITE).then(|| {
                        PossibleOperationsWrite {
                            partial: f
                                .operations
                                .contains(EEBusOperations::WRITE_PARTIAL)
                                .then_some(ElementTag {}),
                        }
                    }),
                }),
            })
            .collect();

        NodeManagementDetailedDiscoveryFeatureInformation {
            description: Some(
                NodeManagementDetailedDiscoveryFeatureInformationDescription {
                    feature_address: Some(NetworkManagementFeatureDescriptionDataFeatureAddress {
                        entity: Vec::from(device_id),
                        feature: Some(self.id),
                    }),
                    feature_type: Some(self.feature_type.clone()),
                    specific_usage: Vec::new(), // deprecated
                    feature_group: None,
                    role: Some(self.role),
                    supported_function,
                    last_state_change,
                    minimum_trust_level: None, // reserved for future use
                    label: None,
                    description: None,
                    max_response_delay: None, // None = fall back to "defaultMaxResponseDelay" (10 seconds)
                },
            ),
        }
    }
}

impl EEBusFunction {
    pub const fn new(function: Function, operations: EEBusOperations) -> Self {
        Self {
            function,
            operations,
        }
    }
}

impl EEBusUseCase {
    pub const fn new(
        name: &'static str,
        version: &'static str,
        actor: &'static str,
        scenarios: &'static [u32],
    ) -> Self {
        Self {
            name,
            version,
            actor,
            scenarios,
        }
    }

    pub const OHPCF_SERVER: EEBusUseCase = EEBusUseCase::new(
        "optimizationOfSelfConsumptionByHeatPumpCompressorFlexibility",
        "1.0.0",
        "Compressor",
        &[1, 2],
    );

    pub const LPC_SERVER: EEBusUseCase = EEBusUseCase::new(
        "limitationOfPowerConsumption",
        "1.0.0",
        "ControllableSystem",
        &[1, 2, 3],
    );

    pub const MPC_SERVER: EEBusUseCase = EEBusUseCase::new(
        "monitoringOfPowerConsumption",
        "1.0.0",
        "MonitoredUnit",
        &[1],
    );
}

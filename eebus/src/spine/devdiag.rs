use embassy_time::{Duration, Instant, Timer};

use super::types::*;

// heartbeat timeout that will be requested from the remote device. must be less than or equal to 60 seconds.
// [LPC-006 (heartbeat at least every 60 seconds)] the restriction of less than or equal to 60 seconds applies
// to LPC only. other use cases may have longer or shorter heartbeat timeouts.
const HEARTBEAT_RECEIVE_TIMEOUT: Duration = Duration::from_secs(50);

// margin that accounts for network delays or async weirdness.
// heartbeats to the remote will be sent this duration earlier than requested.
// heartbeats from the remote will be accepted this duration later than anticipated.
const MARGIN: Duration = Duration::from_secs(2);

/// Handles heartbeat timeouts and counters.
///
/// LPC heartbeat communication outline: (where client = energy guard, server = controllable system)
///  1. client sends read, server replies with information about the expected heartbeat interval
///  2. server sends read, client replies with information about the expected heartbeat interval
///  3. client and server each send notify messages within the intervals of their respective remote
///
/// The following pseudo code illustrates how to use this:
///
/// ```text
/// let mut hb = HeartbeatHandler::new();
/// select! {
///     request = recv_heartbeat_data_read().await => {
///         send(hb.read_data_response(request));
///         send(hb.get_data_read());
///         hb.process_reply(recv_heartbeat_data_reply());
///     }
///     notify = recv_heartbeat_data_notify().await => {
///         if let Err(e) = hb.process_notify(notify) {
///             panic!("got bad heartbeat counter from remote: {e}");
///         }
///     }
///     hb_event = hb.wait_notify_event().await => {
///         match hb_event {
///             NotifyRequired => send(hb.get_notify()),
///             RemoteTimedOut => panic!("remote timed out"),
///         }
///     }
/// }
/// ```
pub struct HeartbeatHandler {
    last_received_heartbeat: Option<Instant>,
    last_sent_heartbeat: Option<Instant>,
    send_timeout: Option<Duration>,
    expected_remote_counter: u64,
    own_counter: u64,
}

impl HeartbeatHandler {
    #[allow(clippy::new_without_default)]
    pub fn new() -> Self {
        Self {
            last_received_heartbeat: None,
            last_sent_heartbeat: None,
            send_timeout: None,
            expected_remote_counter: 0,
            own_counter: 0,
        }
    }

    /// Caller must use this value since the heartbeat timoeuts would not be accurate otherwise,
    /// or the remote device might think that the device is offline.
    #[must_use]
    pub fn read_data_response(&mut self) -> DeviceDiagnosisHeartbeatData {
        if self.last_sent_heartbeat.is_none() {
            self.last_sent_heartbeat = Some(Instant::now());
        }

        DeviceDiagnosisHeartbeatData {
            // time of creation of data.
            // a duration of 0s, as always with AbsoluteOrRelativeTime, means "just now".
            timestamp: Some(AbsoluteOrRelativeTime::XsDuration(XsDuration::hms(0, 0, 0))),
            // the value of this counter must be increased after every heartbeatTimeout, i.e. it should be increased
            // with every heartbeat notification message. however, the deviceDiagnosisHeartbeatData function can not
            // only be notified by the device due to a subscription, but can be requested with a read request by another
            // device, which will be replied to. in this case, the heartbeat counter must not be incremented, and the
            // heartbeat timeout has its fixed value (i.e. not the remaining time to the next (automatic) notification
            // by the device).
            heartbeat_counter: Some(self.own_counter),
            // the period in which the deviceDiagnosisHeartbeatData function is sent by the device.
            // this is not the remaining time until the next sending.
            heartbeat_timeout: Some(HEARTBEAT_RECEIVE_TIMEOUT.into()),
        }
    }

    /// Process read reply from remote.
    pub fn process_reply(&mut self, reply: &DeviceDiagnosisHeartbeatData) {
        self.expected_remote_counter = reply.heartbeat_counter.unwrap_or(0) + 1;
        self.last_received_heartbeat = Some(Instant::now());
        self.send_timeout = reply
            .heartbeat_timeout
            .as_ref()
            .map(XsDuration::abs_hms_duration)
    }

    /// Process notify message from remote and check its parameters.
    ///
    /// Returns `Ok(())` if the counter in the heartbeat matches the expected value,
    /// or `Err(HeartbeatError::CounterMismatch)` if it does not match.
    pub fn process_notify(
        &mut self,
        notify: &DeviceDiagnosisHeartbeatData,
    ) -> Result<(), HeartbeatError> {
        let received_counter = notify.heartbeat_counter.unwrap_or(0);
        self.process_reply(notify);
        match self.expected_remote_counter == received_counter {
            true => Ok(()),
            false => Err(HeartbeatError::CounterMismatch),
        }
    }

    /// Caller must use this value since the heartbeat timoeuts would not be accurate otherwise,
    /// or the remote device might think that the device is offline.
    #[must_use]
    pub fn get_data_notify(&mut self) -> DeviceDiagnosisHeartbeatData {
        self.own_counter += 1;
        self.get_data_read()
    }

    /// Caller must use this value since the heartbeat timoeuts would not be accurate otherwise,
    /// or the remote device might think that the device is offline.
    #[must_use]
    pub fn get_data_read(&mut self) -> DeviceDiagnosisHeartbeatData {
        self.last_sent_heartbeat = Some(Instant::now());

        DeviceDiagnosisHeartbeatData {
            timestamp: None,
            heartbeat_counter: None,
            heartbeat_timeout: None,
        }
    }

    /// Wait for "notify" events, i.e. wait until either a notify (from us to
    /// the remote) is required or the remote timed out.
    ///
    /// Margins to the timeouts are applied where necessary.
    ///
    /// If no heartbeat communication (i.e. reads or replies) has ever taken
    /// place before, this method waits indefinitely.
    pub async fn wait_notify_event(&self) -> HeartbeatEvent {
        let remote_timeout = self.last_received_heartbeat.map(|last_received| {
            duration_saturating_sub(HEARTBEAT_RECEIVE_TIMEOUT + MARGIN, last_received.elapsed())
        });

        let notify_required =
            self.last_sent_heartbeat
                .zip(self.send_timeout)
                .map(|(last_sent, total_timeout)| {
                    duration_saturating_sub(total_timeout + MARGIN, last_sent.elapsed())
                });

        let (timeout, event) = match (remote_timeout, notify_required) {
            (Some(remote_timeout), Some(notify_required)) => {
                if remote_timeout < notify_required {
                    (remote_timeout, HeartbeatEvent::RemoteTimedOut)
                } else {
                    (notify_required, HeartbeatEvent::NotifyRequired)
                }
            }
            (Some(remote_timeout), None) => (remote_timeout, HeartbeatEvent::RemoteTimedOut),
            (None, Some(notify_required)) => (notify_required, HeartbeatEvent::NotifyRequired),
            (None, None) => std::future::pending::<(Duration, HeartbeatEvent)>().await,
        };

        Timer::after(timeout).await;

        event
    }
}

fn duration_saturating_sub(a: Duration, b: Duration) -> Duration {
    if b > a {
        Duration::MIN
    } else {
        a - b
    }
}

#[derive(Clone, Copy, Debug)]
pub enum HeartbeatEvent {
    RemoteTimedOut,
    NotifyRequired,
}

#[derive(Debug)]
pub enum HeartbeatError {
    CounterMismatch,
}

use std::{
    error::<PERSON>rror,
    fmt::{self, Display, <PERSON>atter},
    str::{<PERSON><PERSON>, FromStr},
};

use embassy_time::Duration;
use nanoserde::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Err, DeJsonErrReason, DeJsonState, SerJson, SerJsonState};

/* xs:date ********************************************************************/

#[derive(Clone, Debug, Eq, PartialEq)]
pub struct XsDate {
    pub year: u16,
    pub month: u8,
    pub day: u8,
    pub tz_hours: i8,
    pub tz_mins: u8,
}

impl XsDate {
    pub fn new(year: u16, month: u8, day: u8) -> Self {
        Self {
            year,
            month,
            day,
            tz_hours: 0,
            tz_mins: 0,
        }
    }
}

impl Display for XsDate {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "{:02}-{:02}-{:02}{:+03}:{:02}",
            self.year, self.month, self.day, self.tz_hours, self.tz_mins
        )
    }
}

impl FromStr for XsDate {
    type Err = FormatError;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        let (timezone, (year, month, day)) = parse_date(s)?;
        let (tz_hours, tz_mins) = parse_timezone(timezone)?;

        Ok(Self {
            year,
            month,
            day,
            tz_hours,
            tz_mins,
        })
    }
}

impl SerJson for XsDate {
    fn ser_json(&self, _d: usize, s: &mut SerJsonState) {
        s.out.push('"');
        s.out.push_str(&self.to_string());
        s.out.push('"');
    }
}

impl DeJson for XsDate {
    fn de_json(s: &mut DeJsonState, i: &mut Chars) -> Result<Self, DeJsonErr> {
        let str = String::de_json(s, i)?;
        Self::from_str(&str).map_err(|_| DeJsonErr {
            msg: DeJsonErrReason::CannotParse("xs:date format".to_string()),
            line: s.line,
            col: s.col,
        })
    }
}

/* xs:time ********************************************************************/

#[derive(Clone, Debug, Eq, PartialEq)]
pub struct XsTime {
    pub hour: u8,
    pub min: u8,
    pub sec: u8,
    pub msec: u16,
    pub tz_hours: i8,
    pub tz_mins: u8,
}

impl XsTime {
    pub fn new(hour: u8, min: u8, sec: u8, msec: u16) -> Self {
        Self {
            hour,
            min,
            sec,
            msec,
            tz_hours: 0,
            tz_mins: 0,
        }
    }
}

impl Display for XsTime {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "{:02}:{:02}:{:02}.{:03}{:+03}:{:02}",
            self.hour, self.min, self.sec, self.msec, self.tz_hours, self.tz_mins
        )
    }
}

impl FromStr for XsTime {
    type Err = FormatError;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        let (timezone, (hour, min, sec, msec)) = parse_time(s)?;
        let (tz_hours, tz_mins) = parse_timezone(timezone)?;

        Ok(Self {
            hour,
            min,
            sec,
            msec,
            tz_hours,
            tz_mins,
        })
    }
}

impl SerJson for XsTime {
    fn ser_json(&self, _d: usize, s: &mut SerJsonState) {
        s.out.push('"');
        s.out.push_str(&self.to_string());
        s.out.push('"');
    }
}

impl DeJson for XsTime {
    fn de_json(s: &mut DeJsonState, i: &mut Chars) -> Result<Self, DeJsonErr> {
        let str = String::de_json(s, i)?;
        Self::from_str(&str).map_err(|_| DeJsonErr {
            msg: DeJsonErrReason::CannotParse("xs:time format".to_string()),
            line: s.line,
            col: s.col,
        })
    }
}

/* xs:datetime ****************************************************************/

#[derive(Clone, Debug, Eq, PartialEq)]
pub struct XsDateTime {
    pub year: u16,
    pub month: u8,
    pub date: u8,
    pub hour: u8,
    pub min: u8,
    pub sec: u8,
    pub msec: u16,
    pub tz_hours: i8,
    pub tz_mins: u8,
}

impl XsDateTime {
    pub fn new(year: u16, month: u8, date: u8, hour: u8, min: u8, sec: u8, msec: u16) -> Self {
        Self {
            year,
            month,
            date,
            hour,
            min,
            sec,
            msec,
            tz_hours: 0,
            tz_mins: 0,
        }
    }
}

impl Display for XsDateTime {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "{:02}-{:02}-{:02}T{:02}:{:02}:{:02}.{:03}{:+03}:{:02}",
            self.year,
            self.month,
            self.date,
            self.hour,
            self.min,
            self.sec,
            self.msec,
            self.tz_hours,
            self.tz_mins
        )
    }
}

impl FromStr for XsDateTime {
    type Err = FormatError;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        let (remainder, (year, month, date)) = parse_date(s)?;
        let remainder = expect_char(remainder, 'T')?;
        let (timezone, (hour, min, sec, msec)) = parse_time(remainder)?;
        let (tz_hours, tz_mins) = parse_timezone(timezone)?;

        Ok(Self {
            year,
            month,
            date,
            hour,
            min,
            sec,
            msec,
            tz_hours,
            tz_mins,
        })
    }
}

impl SerJson for XsDateTime {
    fn ser_json(&self, _d: usize, s: &mut SerJsonState) {
        s.out.push('"');
        s.out.push_str(&self.to_string());
        s.out.push('"');
    }
}

impl DeJson for XsDateTime {
    fn de_json(s: &mut DeJsonState, i: &mut Chars) -> Result<Self, DeJsonErr> {
        let str = String::de_json(s, i)?;
        Self::from_str(&str).map_err(|_| DeJsonErr {
            msg: DeJsonErrReason::CannotParse("xs:datetime format".to_string()),
            line: s.line,
            col: s.col,
        })
    }
}

/* xs:duration ****************************************************************/

#[derive(Clone, Debug, Eq, PartialEq)]
pub struct XsDuration {
    pub sign: i16,
    pub years: u16,
    pub months: u16,
    pub days: u16,
    pub hours: u16,
    pub mins: u16,
    pub secs: u16,
    pub msecs: u16,
}

impl XsDuration {
    pub const fn new(
        years: u16,
        months: u16,
        days: u16,
        hours: u16,
        mins: u16,
        secs: u16,
        msecs: u16,
    ) -> Self {
        Self {
            sign: 1,
            years,
            months,
            days,
            hours,
            mins,
            secs,
            msecs,
        }
    }

    pub const fn hms(hours: u16, mins: u16, secs: u16) -> Self {
        Self {
            sign: 1,
            years: 0,
            months: 0,
            days: 0,
            hours,
            mins,
            secs,
            msecs: 0,
        }
    }

    pub const fn minutes(minutes: i64) -> Self {
        Self::hms((minutes / 60) as u16, (minutes % 60) as u16, 0)
    }

    pub const fn zero() -> Self {
        Self::hms(0, 0, 0)
    }

    pub fn abs_hms_duration(&self) -> Duration {
        let mut secs: u64 = 0;
        secs += self.secs as u64;
        secs += self.mins as u64 * 60;
        secs += self.hours as u64 * 3600;
        let millis = secs * 1000 + self.msecs as u64;
        Duration::from_millis(millis)
    }
}

impl Display for XsDuration {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "{}P{}Y{}M{}DT{}H{}M{}.{:03}S",
            if self.sign >= 0 { "" } else { "-" },
            self.years,
            self.months,
            self.days,
            self.hours,
            self.mins,
            self.secs,
            self.msecs,
        )
    }
}

impl FromStr for XsDuration {
    type Err = FormatError;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        let (s, negative) = expect_char_opt(s, '-');
        let s = expect_char(s, 'P')?;

        let (s, years) = parse_duration_part(s, 'Y')?;
        let (s, months) = parse_duration_part(s, 'M')?;
        let (s, days) = parse_duration_part(s, 'D')?;

        let (s, has_day_part) = expect_char_opt(s, 'T');

        let (s, hours, mins, secs, msecs) = if has_day_part {
            let (s, hours) = parse_duration_part(s, 'H')?;
            let (s, mins) = parse_duration_part(s, 'M')?;
            let (s, secs, msecs) = if !s.is_empty() {
                let n_digits = get_non_digit_len(s);
                let c = *s.as_bytes().get(n_digits).ok_or(FormatError)?;
                let secs = s[0..n_digits].parse().unwrap();
                let s = &s[(n_digits + 1)..];
                if c == b'S' {
                    (s, secs, 0)
                } else if c == b'.' {
                    let (s, msecs) = parse_msecs(s)?;
                    let s = expect_char(s, 'S')?;
                    (s, secs, msecs)
                } else {
                    return Err(FormatError);
                }
            } else {
                (s, 0, 0)
            };
            (s, hours, mins, secs, msecs)
        } else {
            (s, 0, 0, 0, 0)
        };

        expect_empty(s)?;

        let sign = if negative { -1 } else { 1 };

        Ok(Self {
            sign,
            years,
            months,
            days,
            hours,
            mins,
            secs,
            msecs,
        })
    }
}

impl From<Duration> for XsDuration {
    fn from(value: Duration) -> Self {
        Self {
            sign: 1,
            years: 0,
            months: 0,
            days: 0,
            hours: 0,
            mins: 0,
            secs: value.as_secs() as u16,
            msecs: (value.as_millis() / 1000) as u16,
        }
    }
}

impl SerJson for XsDuration {
    fn ser_json(&self, _d: usize, s: &mut SerJsonState) {
        s.out.push('"');
        s.out.push_str(&self.to_string());
        s.out.push('"');
    }
}

impl DeJson for XsDuration {
    fn de_json(s: &mut DeJsonState, i: &mut Chars) -> Result<Self, DeJsonErr> {
        let str = String::de_json(s, i)?;
        Self::from_str(&str).map_err(|_| DeJsonErr {
            msg: DeJsonErrReason::CannotParse("xs:duration format".to_string()),
            line: s.line,
            col: s.col,
        })
    }
}

/* utilities ******************************************************************/

#[derive(Clone, Debug)]
pub struct FormatError;

impl Display for FormatError {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        f.write_str("format error")
    }
}

impl Error for FormatError {}

// returns Result<(s_remainder, (year, month, date))>
fn parse_date(s: &str) -> Result<(&str, (u16, u8, u8)), FormatError> {
    let (s, year) = parse_int(s, 4)?;
    let s = expect_char(s, '-')?;
    let (s, month) = parse_int(s, 2)?;
    let s = expect_char(s, '-')?;
    let (s, date) = parse_int(s, 2)?;
    Ok((s, (year, month, date)))
}

// returns Result<(s_remainder, (hours, mins, secs, msescs))>
#[allow(clippy::type_complexity)]
fn parse_time(s: &str) -> Result<(&str, (u8, u8, u8, u16)), FormatError> {
    let (s, hour) = parse_int(s, 2)?;
    let s = expect_char(s, ':')?;
    let (s, min) = parse_int(s, 2)?;
    let s = expect_char(s, ':')?;
    let (s, sec) = parse_int(s, 2)?;

    let (s, msec) = if let Some(msec) = s.strip_prefix('.') {
        parse_msecs(msec)?
    } else {
        (s, 0)
    };

    Ok((s, (hour, min, sec, msec)))
}

// returns Result<(tz_hours, tz_mins)>
fn parse_timezone(s: &str) -> Result<(i8, u8), FormatError> {
    if s.is_empty() || s == "Z" {
        return Ok((0, 0));
    }
    let b = s.as_bytes();
    if s.len() == 6
        && (b[0] == b'+' || b[0] == b'-')
        && b[1].is_ascii_digit()
        && b[2].is_ascii_digit()
        && b[3] == b':'
        && b[4].is_ascii_digit()
        && b[5].is_ascii_digit()
    {
        let tz_hours = s[0..=2].parse().unwrap();
        let tz_mins = s[4..=5].parse().unwrap();
        Ok((tz_hours, tz_mins))
    } else {
        Err(FormatError)
    }
}

// returns Result<(s_remainder, number)>
fn parse_int<T: FromStr>(s: &str, n_chars: usize) -> Result<(&str, T), FormatError> {
    if s.len() < n_chars || !s.as_bytes()[..n_chars].iter().all(u8::is_ascii_digit) {
        return Err(FormatError);
    }
    let number = s[0..n_chars].parse().map_err(|_| FormatError)?;
    let remainder = &s[n_chars..];
    Ok((remainder, number))
}

// returns Result<s_remainder>
fn expect_char(s: &str, expect: char) -> Result<&str, FormatError> {
    let mut chars = s.chars();
    match chars.next() {
        Some(c) if c == expect => Ok(chars.as_str()),
        _ => Err(FormatError),
    }
}

// returns (s_remainder, char_matched)
fn expect_char_opt(s: &str, expect: char) -> (&str, bool) {
    let mut chars = s.chars();
    match chars.next() {
        Some(c) if c == expect => (chars.as_str(), true),
        _ => (s, false),
    }
}

// returns Result<(s_remainder, number_or_0)>
fn parse_duration_part(s: &str, ident: char) -> Result<(&str, u16), FormatError> {
    let n_digits = get_non_digit_len(s);
    let c = *s.as_bytes().get(n_digits).ok_or(FormatError)?;
    if c == ident as u8 {
        Ok((&s[(n_digits + 1)..], s[0..n_digits].parse().unwrap()))
    } else {
        Ok((s, 0))
    }
}

// returns Result<(s_remainder, msecs)>
fn parse_msecs(s: &str) -> Result<(&str, u16), FormatError> {
    let n_digits = get_non_digit_len(s);
    if !(1..=12).contains(&n_digits) {
        return Err(FormatError);
    }
    let msec = &s[..n_digits];
    let msec = match n_digits {
        1 => ((msec.as_bytes()[0] as u8) - b'0') as u16 * 100,
        2 => msec.parse::<u16>().unwrap() * 10,
        _ => msec[0..3].parse().unwrap(),
    };
    Ok((&s[n_digits..], msec))
}

fn get_non_digit_len(s: &str) -> usize {
    s.as_bytes()
        .iter()
        .position(|c| !c.is_ascii_digit())
        .unwrap_or(s.len())
}

fn expect_empty(s: &str) -> Result<(), FormatError> {
    match s.is_empty() {
        true => Ok(()),
        false => Err(FormatError),
    }
}

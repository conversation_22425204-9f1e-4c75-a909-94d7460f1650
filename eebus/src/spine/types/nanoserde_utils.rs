use nanoserde::{<PERSON><PERSON><PERSON><PERSON><PERSON>, De<PERSON>sonErrReason, DeJsonState};

pub fn missing_field(state: &DeJsonState, name: &str) -> DeJsonErr {
    DeJsonErr {
        msg: DeJsonErrReason::Missing<PERSON><PERSON>(name.to_string()),
        line: state.line,
        col: state.col,
    }
}

pub fn unexpected_field(state: &DeJsonState, name: &str) -> DeJsonErr {
    DeJsonErr {
        msg: DeJsonErrReason::Unexpected<PERSON>ey(name.to_string()),
        line: state.line,
        col: state.col,
    }
}

pub fn invalid_variant(state: &DeJsonState, name: &str) -> DeJsonErr {
    DeJsonErr {
        msg: DeJsonErrReason::NoSuchEnum(name.to_string()),
        line: state.line,
        col: state.col,
    }
}

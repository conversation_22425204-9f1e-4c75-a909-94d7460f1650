use super::ElementTag;

pub trait AllElements: Sized {
    fn all_elements() -> Self;
}

impl AllElements for ElementTag {
    fn all_elements() -> Self {
        Self {}
    }
}

pub trait FilterElements<T> {
    /// Goes through the fields of `T` and sets their field based on the element filter.
    ///
    /// If the field in the element filter is Some(ElementTag{}), the field in the element will be kept as-is.
    /// If the field in the element filter is None, the field will be forcibly set to None.
    fn filter_elements(&self, value: &mut T);
}

// | current value | filter element   | result          |
// | ------------- | ---------------- | --------------- |
// | None          | None             | None            |
// | None          | Some(ElementTag) | None            |
// | Some(T)       | None             | None            |
// | Some(T)       | Some(ElementTag) | Some(filter(T)) |
impl<F, T> FilterElements<Option<T>> for Option<F>
where
    F: FilterElements<T>,
{
    fn filter_elements(&self, value: &mut Option<T>) {
        match (value.as_mut(), self) {
            (None, _) => (),
            (Some(_), None) => *value = None,
            (Some(value), Some(filter)) => filter.filter_elements(value),
        }
    }
}

// | current value | filter element   | result               |
// | ------------- | ---------------- | -------------------- |
// | vec![]        | None             | vec![]               |
// | vec![]        | Some(ElementTag) | vec![]               |
// | vec![Ts]      | None             | vec![]               |
// | vec![Ts]      | Some(ElementTag) | vec![Ts.map(filter)] |
impl<F, T> FilterElements<Vec<T>> for Option<F>
where
    F: FilterElements<T>,
{
    fn filter_elements(&self, value: &mut Vec<T>) {
        match self {
            Some(f) => value.iter_mut().for_each(|t| f.filter_elements(t)),
            None => *value = Vec::new(),
        }
    }
}

// termination case for "recursiveness" in element filtering
impl<T> FilterElements<T> for ElementTag {
    fn filter_elements(&self, value: &mut T) {
        _ = value;
    }
}

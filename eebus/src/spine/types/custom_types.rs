use std::str::Cha<PERSON>;

use nanoserde::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rr, DeJsonState, DeJsonTok, SerJson, SerJsonState};

use super::*;

/* EEBus_SPINE_TS_CommonDataTypes.xsd *************************************************************/

#[derive(Clone, Debug, DeJson, SerJson, PartialEq)]
pub enum UnitOfMeasurement {
    #[nserde(rename = "unknown")]
    Unknown,
    #[nserde(rename = "1")]
    Unit,
    #[nserde(rename = "m")]
    <PERSON><PERSON>,
    #[nserde(rename = "kg")]
    Kilogram,
    #[nserde(rename = "s")]
    Second,
    #[nserde(rename = "A")]
    Ampere,
    #[nserde(rename = "K")]
    <PERSON><PERSON>,
    #[nserde(rename = "mol")]
    Mol,
    #[nser<PERSON>(rename = "cd")]
    <PERSON><PERSON>,
    #[nserde(rename = "V")]
    Volt,
    #[nserde(rename = "W")]
    <PERSON>,
    #[nserde(rename = "Wh")]
    <PERSON>Hour,
    #[nserde(rename = "VA")]
    VoltAmpere,
    #[nserde(rename = "VAh")]
    VoltAmpereHour,
    #[nserde(rename = "var")]
    VoltAmpereReactive,
    #[nserde(rename = "varh")]
    VoltAmpereReactiveHour,
    #[nserde(rename = "degC")]
    DegreeCelsius,
    #[nserde(rename = "degF")]
    DegreeFahrenheit,
    #[nserde(rename = "Lm")]
    Lumen,
    #[nserde(rename = "lx")]
    Lux,
    #[nserde(rename = "Ohm")]
    Ohm,
    #[nserde(rename = "Hz")]
    Herz,
    #[nserde(rename = "dB")]
    Decibel,
    #[nserde(rename = "dBm")]
    Dbm,
    #[nserde(rename = "pct")]
    Percent,
    #[nserde(rename = "ppm")]
    PartsPerMillion,
    #[nserde(rename = "l")]
    Liter,
    #[nserde(rename = "l/s")]
    LiterPerSecond,
    #[nserde(rename = "l/h")]
    LiterPerHour,
    #[nserde(rename = "deg")]
    Degrees,
    #[nserde(rename = "rad")]
    Radians,
    #[nserde(rename = "rad/s")]
    RadiansPerSecond,
    #[nserde(rename = "sr")]
    Steradian,
    #[nserde(rename = "Gy")]
    Gray,
    #[nserde(rename = "Bq")]
    Becquerel,
    #[nserde(rename = "Bq/m^3")]
    BecquerelPerCubicMeter,
    #[nserde(rename = "Sv")]
    Sievert,
    #[nserde(rename = "Rd")]
    Roentgen,
    #[nserde(rename = "C")]
    Coulomb,
    #[nserde(rename = "F")]
    Farad,
    #[nserde(rename = "H")]
    Henry,
    #[nserde(rename = "J")]
    Joule,
    #[nserde(rename = "N")]
    Newton,
    #[nserde(rename = "N_m")]
    NewtonMeter,
    #[nserde(rename = "N_s")]
    NewtonSecond,
    #[nserde(rename = "Wb")]
    Weber,
    #[nserde(rename = "T")]
    Tesla,
    #[nserde(rename = "Pa")]
    Pascal,
    #[nserde(rename = "bar")]
    Bar,
    #[nserde(rename = "atm")]
    Atmosphere,
    #[nserde(rename = "psi")]
    PoundsPerSquareInch,
    #[nserde(rename = "mmHg")]
    MillemetersOfMercury,
    #[nserde(rename = "m^2")]
    SquareMeter,
    #[nserde(rename = "m^3")]
    CubicMeter,
    #[nserde(rename = "m^3/h")]
    CubicMeterPerHour,
    #[nserde(rename = "m/s")]
    MeterPerSecond,
    #[nserde(rename = "m/s^2")]
    MeterPerSecondSquared,
    #[nserde(rename = "m^3/s")]
    CubicMeterPerSecond,
    #[nserde(rename = "m/m^3")]
    MeterPerCubicMeter,
    #[nserde(rename = "kg/m^3")]
    KilogramPerCubicMeter,
    #[nserde(rename = "kg_m")]
    KilogramMeter,
    #[nserde(rename = "m^2/s")]
    SquareMeterPerSecond,
    #[nserde(rename = "W/m_K")]
    WattPerMeterKelvin,
    #[nserde(rename = "J/K")]
    JoulePerKelvin,
    #[nserde(rename = "1/s")]
    OneOverSecond,
    #[nserde(rename = "W/m^2")]
    WattPerSquareMeter,
    #[nserde(rename = "J/m^2")]
    JoulePerSquareMeter,
    #[nserde(rename = "S")]
    Siemens,
    #[nserde(rename = "S/m")]
    SiemensPerMeter,
    #[nserde(rename = "K/s")]
    KelvinPerSecond,
    #[nserde(rename = "Pa/s")]
    PascalPerSecond,
    #[nserde(rename = "J/kg_K")]
    JoulePerKilogramKelvin,
    #[nserde(rename = "Vs")]
    VoltSecond,
    #[nserde(rename = "V/m")]
    VoltPerMeter,
    #[nserde(rename = "V/Hz")]
    VoltHertz,
    #[nserde(rename = "As")]
    AmpereSecond,
    #[nserde(rename = "A/m")]
    AmperePerMeter,
    #[nserde(rename = "Hz/s")]
    HertzPerSecond,
    #[nserde(rename = "kg/s")]
    KilogramPerSecond,
    #[nserde(rename = "kg_m^2")]
    KilogramSquareMeter,
    #[nserde(rename = "J/Wh")]
    JoulePerWattHour,
    #[nserde(rename = "W/s")]
    WattPerSecond,
    #[nserde(rename = "ft^3")]
    CubicFoot,
    #[nserde(rename = "ft^3/h")]
    CubicFootPerHour,
    #[nserde(rename = "ccf")]
    HunderedCubicFeet,
    #[nserde(rename = "ccf/h")]
    HunderedCubicFeetPerHour,
    #[nserde(rename = "US.liq.gal")]
    UsLiquidGallon,
    #[nserde(rename = "US.liq.gal/h")]
    UsLiquidGallonPerHour,
    #[nserde(rename = "Imp.gal")]
    ImperialGallon,
    #[nserde(rename = "Imp.gal/h")]
    ImperialGallonPerHour,
    #[nserde(rename = "Btu")]
    BritishThermalUnit,
    #[nserde(rename = "Btu/h")]
    BritishThermalUnitPerHour,
    #[nserde(rename = "Ah")]
    AmpereHour,
    #[nserde(rename = "kg/Wh")]
    KilogramPerWattHour,
}

/* EEBus_SPINE_TS_CommandFrame.xsd ****************************************************************/

#[derive(Clone, Debug, PartialEq)]
pub struct Cmd {
    pub function: Option<Function>,
    pub filter: Vec<Filter>,
    /// xs:hexBinary format: [0-9a-fA-F]* (binary data as hex string)
    pub manufacturer_specific_extension: Option<String>,
    pub last_update_at: Option<AbsoluteOrRelativeTime>,
    pub data: Option<Data>,
}

#[cfg(feature = "defmt")]
impl defmt::Format for Cmd {
    fn format(&self, fmt: defmt::Formatter) {
        defmt::write!(fmt, "(cmd)");
    }
}
impl SerJson for Cmd {
    fn ser_json(&self, d: usize, s: &mut SerJsonState) {
        s.out.push('[');
        let mut first_element = true;
        if let Some(function) = &self.function {
            s.out.push_str("{\"function\":");
            function.ser_json(d + 1, s);
            s.out.push('}');
            first_element = false;
        }
        if !self.filter.is_empty() {
            match first_element {
                true => first_element = false,
                false => s.out.push(','),
            }
            s.out.push_str("{\"filter\":");
            self.filter.ser_json(d + 1, s);
            s.out.push('}');
        }
        if let Some(manufacturer_specific_extension) = &self.manufacturer_specific_extension {
            match first_element {
                true => first_element = false,
                false => s.out.push(','),
            }
            s.out.push_str("{\"manufacturerSpecificExtension\":");
            manufacturer_specific_extension.ser_json(d + 1, s);
            s.out.push('}');
        }
        if let Some(last_update_at) = &self.last_update_at {
            match first_element {
                true => first_element = false,
                false => s.out.push(','),
            }
            s.out.push_str("{\"lastUpdateAt\":");
            last_update_at.ser_json(d + 1, s);
            s.out.push('}');
        }
        if let Some(data) = &self.data {
            if !first_element {
                s.out.push(',');
            }
            data.ser_json(d + 1, s);
        }
        s.out.push(']');
    }
}

impl DeJson for Cmd {
    fn de_json(s: &mut DeJsonState, i: &mut Chars) -> Result<Self, DeJsonErr> {
        let mut function = None;
        let mut filter = None;
        let mut manufacturer_specific_extension = None;
        let mut last_update_at = None;
        let mut data = None;

        s.block_open(i)?;
        while s.tok != DeJsonTok::BlockClose {
            s.curly_open(i)?;
            let key = String::de_json(s, i)?;
            s.colon(i)?;
            match key.as_str() {
                "function" => {
                    if function.is_some() {
                        return Err(unexpected_field(s, "function"));
                    }
                    function = Some(DeJson::de_json(s, i)?);
                }
                "filter" => {
                    if filter.is_some() {
                        return Err(unexpected_field(s, "filter"));
                    }
                    filter = Some(DeJson::de_json(s, i)?);
                }
                "manufacturerSpecificExtension" => {
                    if manufacturer_specific_extension.is_some() {
                        return Err(unexpected_field(s, "manufacturerSpecificExtension"));
                    }
                    manufacturer_specific_extension = Some(DeJson::de_json(s, i)?);
                }
                "lastUpdateAt" => {
                    if last_update_at.is_some() {
                        return Err(unexpected_field(s, "lastUpdateAt"));
                    }
                    last_update_at = Some(DeJson::de_json(s, i)?);
                }
                k => {
                    if data.is_some() {
                        return Err(unexpected_field(s, k));
                    }
                    data = Some(match k {
                        "deviceConfigurationKeyValueDescriptionListData" => {
                            Data::DeviceConfigurationKeyValueDescriptionListData(DeJson::de_json(
                                s, i,
                            )?)
                        }
                        "deviceConfigurationKeyValueListData" => {
                            Data::DeviceConfigurationKeyValueListData(DeJson::de_json(s, i)?)
                        }
                        "deviceDiagnosisHeartbeatData" => {
                            Data::DeviceDiagnosisHeartbeatData(DeJson::de_json(s, i)?)
                        }
                        "electricalConnectionCharacteristicListData" => {
                            Data::ElectricalConnectionCharacteristicListData(DeJson::de_json(s, i)?)
                        }
                        "electricalConnectionDescriptionListData" => {
                            Data::ElectricalConnectionDescriptionListData(DeJson::de_json(s, i)?)
                        }
                        "electricalConnectionParameterDescriptionListData" => {
                            Data::ElectricalConnectionParameterDescriptionListData(DeJson::de_json(
                                s, i,
                            )?)
                        }
                        "loadControlLimitDescriptionListData" => {
                            Data::LoadControlLimitDescriptionListData(DeJson::de_json(s, i)?)
                        }
                        "loadControlLimitListData" => {
                            Data::LoadControlLimitListData(DeJson::de_json(s, i)?)
                        }
                        "measurementConstraintsListData" => {
                            Data::MeasurementConstraintsListData(DeJson::de_json(s, i)?)
                        }
                        "measurementDescriptionListData" => {
                            Data::MeasurementDescriptionListData(DeJson::de_json(s, i)?)
                        }
                        "measurementListData" => Data::MeasurementListData(DeJson::de_json(s, i)?),
                        "nodeManagementBindingRequestCall" => {
                            Data::NodeManagementBindingRequestCall(DeJson::de_json(s, i)?)
                        }
                        "nodeManagementDetailedDiscoveryData" => {
                            Data::NodeManagementDetailedDiscoveryData(DeJson::de_json(s, i)?)
                        }
                        "nodeManagementSubscriptionRequestCall" => {
                            Data::NodeManagementSubscriptionRequestCall(DeJson::de_json(s, i)?)
                        }
                        "nodeManagementUseCaseData" => {
                            Data::NodeManagementUseCaseData(DeJson::de_json(s, i)?)
                        }
                        "resultData" => Data::ResultData(DeJson::de_json(s, i)?),
                        "smartEnergyManagementPsData" => {
                            Data::SmartEnergyManagementPsData(DeJson::de_json(s, i)?)
                        }
                        key => Err(unexpected_field(s, key))?,
                    });
                }
            }
            s.curly_close(i)?;
            s.eat_comma_block(i)?;
        }
        s.block_close(i)?;

        let filter = filter.unwrap_or(Vec::new());

        Ok(Self {
            function,
            filter,
            manufacturer_specific_extension,
            last_update_at,
            data,
        })
    }
}

#[derive(Clone, Debug, Default, PartialEq)]
pub struct Filter {
    pub filter_id: Option<FilterId>,
    pub cmd_control: Option<CmdControl>,
    pub data_selectors: Vec<DataSelectors>,
    pub data_element: Option<DataElement>,
}

impl SerJson for Filter {
    fn ser_json(&self, d: usize, s: &mut SerJsonState) {
        s.out.push('[');
        let mut first_element = true;
        if let Some(filter_id) = &self.filter_id {
            s.out.push_str("{\"filterId\":");
            filter_id.ser_json(d + 1, s);
            s.out.push('}');
            first_element = false;
        }
        if let Some(cmd_control) = &self.cmd_control {
            match first_element {
                true => first_element = false,
                false => s.out.push(','),
            }
            s.out.push_str("{\"cmdControl\":");
            cmd_control.ser_json(d + 1, s);
            s.out.push('}');
        }
        for data_selector in &self.data_selectors {
            match first_element {
                true => first_element = false,
                false => s.out.push(','),
            }
            data_selector.ser_json(d + 1, s);
        }
        if let Some(data_element) = &self.data_element {
            if !first_element {
                s.out.push(',');
            }
            data_element.ser_json(d + 1, s);
        }
        s.out.push(']');
    }
}

impl DeJson for Filter {
    fn de_json(s: &mut DeJsonState, i: &mut Chars) -> Result<Self, DeJsonErr> {
        let mut filter_id = None;
        let mut cmd_control = None;
        let mut data_selectors = Vec::new();
        let mut data_element = None;

        s.block_open(i)?;
        while s.tok != DeJsonTok::BlockClose {
            s.curly_open(i)?;
            let key = String::de_json(s, i)?;
            s.colon(i)?;
            match key.as_str() {
                "filterId" => {
                    if filter_id.is_some() {
                        return Err(unexpected_field(s, "filterId"));
                    }
                    filter_id = Some(DeJson::de_json(s, i)?);
                }
                "cmdControl" => {
                    if cmd_control.is_some() {
                        return Err(unexpected_field(s, "cmdControl"));
                    }
                    cmd_control = Some(DeJson::de_json(s, i)?);
                }
                k if k.ends_with("Selectors") => data_selectors.push(match k {
                    "deviceConfigurationKeyValueDescriptionListDataSelectors" => {
                        DataSelectors::DeviceConfigurationKeyValueDescriptionListDataSelectors(
                            DeJson::de_json(s, i)?,
                        )
                    }
                    "deviceConfigurationKeyValueListDataSelectors" => {
                        DataSelectors::DeviceConfigurationKeyValueListDataSelectors(
                            DeJson::de_json(s, i)?,
                        )
                    }
                    "electricalConnectionCharacteristicListDataSelectors" => {
                        DataSelectors::ElectricalConnectionCharacteristicListDataSelectors(
                            DeJson::de_json(s, i)?,
                        )
                    }
                    "electricalConnectionDescriptionListDataSelectors" => {
                        DataSelectors::ElectricalConnectionDescriptionListDataSelectors(
                            DeJson::de_json(s, i)?,
                        )
                    }
                    "electricalConnectionParameterDescriptionListDataSelectors" => {
                        DataSelectors::ElectricalConnectionParameterDescriptionListDataSelectors(
                            DeJson::de_json(s, i)?,
                        )
                    }
                    "loadControlLimitDescriptionListDataSelectors" => {
                        DataSelectors::LoadControlLimitDescriptionListDataSelectors(
                            DeJson::de_json(s, i)?,
                        )
                    }
                    "loadControlLimitListDataSelectors" => {
                        DataSelectors::LoadControlLimitListDataSelectors(DeJson::de_json(s, i)?)
                    }
                    "measurementConstraintsListDataSelectors" => {
                        DataSelectors::MeasurementConstraintsListDataSelectors(DeJson::de_json(
                            s, i,
                        )?)
                    }
                    "measurementDescriptionListDataSelectors" => {
                        DataSelectors::MeasurementDescriptionListDataSelectors(DeJson::de_json(
                            s, i,
                        )?)
                    }
                    "measurementListDataSelectors" => {
                        DataSelectors::MeasurementListDataSelectors(DeJson::de_json(s, i)?)
                    }
                    "nodeManagementBindingDataSelectors" => {
                        DataSelectors::NodeManagementBindingDataSelectors(DeJson::de_json(s, i)?)
                    }
                    "nodeManagementDetailedDiscoveryDataSelectors" => {
                        DataSelectors::NodeManagementDetailedDiscoveryDataSelectors(
                            DeJson::de_json(s, i)?,
                        )
                    }
                    "nodeManagementSubscriptionDataSelectors" => {
                        DataSelectors::NodeManagementSubscriptionDataSelectors(DeJson::de_json(
                            s, i,
                        )?)
                    }
                    "nodeManagementUseCaseDataSelectors" => {
                        DataSelectors::NodeManagementUseCaseDataSelectors(DeJson::de_json(s, i)?)
                    }
                    "smartEnergyManagementPsDataSelectors" => {
                        DataSelectors::SmartEnergyManagementPsDataSelectors(DeJson::de_json(s, i)?)
                    }
                    _ => Err(unexpected_field(s, k))?,
                }),

                k if k.ends_with("Elements") => {
                    if data_element.is_some() {
                        return Err(unexpected_field(s, "dataElements"));
                    }
                    data_element = Some(match k {
                        "deviceConfigurationKeyValueDataElements" => {
                            DataElement::DeviceConfigurationKeyValueDataElements(DeJson::de_json(
                                s, i,
                            )?)
                        }
                        "deviceConfigurationKeyValueDescriptionDataElements" => {
                            DataElement::DeviceConfigurationKeyValueDescriptionDataElements(
                                DeJson::de_json(s, i)?,
                            )
                        }
                        "deviceDiagnosisHeartbeatDataElements" => {
                            DataElement::DeviceDiagnosisHeartbeatDataElements(DeJson::de_json(
                                s, i,
                            )?)
                        }
                        "electricalConnectionCharacteristicDataElements" => {
                            DataElement::ElectricalConnectionCharacteristicDataElements(
                                DeJson::de_json(s, i)?,
                            )
                        }
                        "electricalConnectionDescriptionDataElements" => {
                            DataElement::ElectricalConnectionDescriptionDataElements(
                                DeJson::de_json(s, i)?,
                            )
                        }
                        "electricalConnectionParameterDescriptionDataElements" => {
                            DataElement::ElectricalConnectionParameterDescriptionDataElements(
                                DeJson::de_json(s, i)?,
                            )
                        }
                        "loadControlLimitDataElements" => {
                            DataElement::LoadControlLimitDataElements(DeJson::de_json(s, i)?)
                        }
                        "loadControlLimitDescriptionDataElements" => {
                            DataElement::LoadControlLimitDescriptionDataElements(DeJson::de_json(
                                s, i,
                            )?)
                        }
                        "measurementConstraintsDataElements" => {
                            DataElement::MeasurementConstraintsDataElements(DeJson::de_json(s, i)?)
                        }
                        "measurementDataElements" => {
                            DataElement::MeasurementDataElements(DeJson::de_json(s, i)?)
                        }
                        "measurementDescriptionDataElements" => {
                            DataElement::MeasurementDescriptionDataElements(DeJson::de_json(s, i)?)
                        }
                        "nodeManagementBindingRequestCallElements" => {
                            DataElement::NodeManagementBindingRequestCallElements(DeJson::de_json(
                                s, i,
                            )?)
                        }
                        "nodeManagementDetailedDiscoveryDataElements" => {
                            DataElement::NodeManagementDetailedDiscoveryDataElements(
                                DeJson::de_json(s, i)?,
                            )
                        }
                        "nodeManagementSubscriptionRequestCallElements" => {
                            DataElement::NodeManagementSubscriptionRequestCallElements(
                                DeJson::de_json(s, i)?,
                            )
                        }
                        "nodeManagementUseCaseDataElements" => {
                            DataElement::NodeManagementUseCaseDataElements(DeJson::de_json(s, i)?)
                        }
                        "smartEnergyManagementPsDataElements" => {
                            DataElement::SmartEnergyManagementPsDataElements(DeJson::de_json(s, i)?)
                        }
                        _ => Err(unexpected_field(s, k))?,
                    });
                }
                _ => Err(unexpected_field(s, &key))?,
            }
            s.curly_close(i)?;
            s.eat_comma_block(i)?;
        }
        s.block_close(i)?;

        Ok(Self {
            filter_id,
            cmd_control,
            data_selectors,
            data_element,
        })
    }
}

// add more functions from `DataChoiceGroup` here when they are deemed necessary
// when doing so, don't forget to change the Cmd: Deserialize implementation
#[derive(Clone, Debug, PartialEq)]
pub enum Data {
    DeviceConfigurationKeyValueDescriptionListData(DeviceConfigurationKeyValueDescriptionListData),
    DeviceConfigurationKeyValueListData(DeviceConfigurationKeyValueListData),
    DeviceDiagnosisHeartbeatData(DeviceDiagnosisHeartbeatData),
    ElectricalConnectionCharacteristicListData(ElectricalConnectionCharacteristicListData),
    ElectricalConnectionDescriptionListData(ElectricalConnectionDescriptionListData),
    ElectricalConnectionParameterDescriptionListData(
        ElectricalConnectionParameterDescriptionListData,
    ),
    LoadControlLimitDescriptionListData(LoadControlLimitDescriptionListData),
    LoadControlLimitListData(LoadControlLimitListData),
    MeasurementConstraintsListData(MeasurementConstraintsListData),
    MeasurementDescriptionListData(MeasurementDescriptionListData),
    MeasurementListData(MeasurementListData),
    NodeManagementBindingRequestCall(NodeManagementBindingRequestCall),
    NodeManagementDetailedDiscoveryData(NodeManagementDetailedDiscoveryData),
    NodeManagementSubscriptionRequestCall(NodeManagementSubscriptionRequestCall),
    NodeManagementSubscriptionData(NodeManagementSubscriptionData),
    NodeManagementUseCaseData(NodeManagementUseCaseData),
    ResultData(ResultData),
    SmartEnergyManagementPsData(SmartEnergyManagementPsData),
}

#[cfg(feature = "defmt")]
#[rustfmt::skip]
impl defmt::Format for Data {
    fn format(&self, fmt: defmt::Formatter) {
        match self {
            Self::DeviceConfigurationKeyValueDescriptionListData(_) => defmt::write!(fmt, "DeviceConfigurationKeyValueDescriptionListData"),
            Self::DeviceConfigurationKeyValueListData(_) => defmt::write!(fmt, "DeviceConfigurationKeyValueListData"),
            Self::DeviceDiagnosisHeartbeatData(_) => defmt::write!(fmt, "DeviceDiagnosisHeartbeatData"),
            Self::ElectricalConnectionCharacteristicListData(_) => defmt::write!(fmt, "ElectricalConnectionCharacteristicListData"),
            Self::ElectricalConnectionDescriptionListData(_) => defmt::write!(fmt, "ElectricalConnectionDescriptionListData"),
            Self::ElectricalConnectionParameterDescriptionListData(_) => defmt::write!(fmt, "ElectricalConnectionParameterDescriptionListData"),
            Self::LoadControlLimitDescriptionListData(_) => defmt::write!(fmt, "LoadControlLimitDescriptionListData"),
            Self::LoadControlLimitListData(_) => defmt::write!(fmt, "LoadControlLimitListData"),
            Self::MeasurementConstraintsListData(_) => defmt::write!(fmt, "MeasurementConstraintsListData"),
            Self::MeasurementDescriptionListData(_) => defmt::write!(fmt, "MeasurementDescriptionListData"),
            Self::MeasurementListData(_) => defmt::write!(fmt, "MeasurementListData"),
            Self::NodeManagementBindingRequestCall(_) => defmt::write!(fmt, "NodeManagementBindingRequestCall"),
            Self::NodeManagementDetailedDiscoveryData(_) => defmt::write!(fmt, "NodeManagementDetailedDiscoveryData"),
            Self::NodeManagementSubscriptionRequestCall(_) => defmt::write!(fmt, "NodeManagementSubscriptionRequestCall"),
            Self::NodeManagementSubscriptionData(_) => defmt::write!(fmt, "NodeManagementSubscriptionData"),
            Self::NodeManagementUseCaseData(_) => defmt::write!(fmt, "NodeManagementUseCaseData"),
            Self::ResultData(_) => defmt::write!(fmt, "ResultData"),
            Self::SmartEnergyManagementPsData(_) => defmt::write!(fmt, "SmartEnergyManagementPsData"),
        }
    }
}

impl SerJson for Data {
    fn ser_json(&self, d: usize, s: &mut SerJsonState) {
        match self {
            Self::DeviceConfigurationKeyValueDescriptionListData(x) => {
                s.out
                    .push_str("{\"deviceConfigurationKeyValueDescriptionListData\":");
                x.ser_json(d + 1, s);
            }
            Self::DeviceConfigurationKeyValueListData(x) => {
                s.out.push_str("{\"deviceConfigurationKeyValueListData\":");
                x.ser_json(d + 1, s);
            }
            Self::DeviceDiagnosisHeartbeatData(x) => {
                s.out.push_str("{\"deviceDiagnosisHeartbeatData\":");
                x.ser_json(d + 1, s);
            }
            Self::ElectricalConnectionCharacteristicListData(x) => {
                s.out
                    .push_str("{\"electricalConnectionCharacteristicListData\":");
                x.ser_json(d + 1, s);
            }
            Self::ElectricalConnectionDescriptionListData(x) => {
                s.out
                    .push_str("{\"electricalConnectionDescriptionListData\":");
                x.ser_json(d + 1, s);
            }
            Self::ElectricalConnectionParameterDescriptionListData(x) => {
                s.out
                    .push_str("{\"electricalConnectionParameterDescriptionListData\":");
                x.ser_json(d + 1, s);
            }
            Self::LoadControlLimitDescriptionListData(x) => {
                s.out.push_str("{\"loadControlLimitDescriptionListData\":");
                x.ser_json(d + 1, s);
            }
            Self::LoadControlLimitListData(x) => {
                s.out.push_str("{\"loadControlLimitListData\":");
                x.ser_json(d + 1, s);
            }
            Self::MeasurementConstraintsListData(x) => {
                s.out.push_str("{\"measurementConstraintsListData\":");
                x.ser_json(d + 1, s);
            }
            Self::MeasurementDescriptionListData(x) => {
                s.out.push_str("{\"measurementDescriptionListData\":");
                x.ser_json(d + 1, s);
            }
            Self::MeasurementListData(x) => {
                s.out.push_str("{\"measurementListData\":");
                x.ser_json(d + 1, s);
            }
            Self::NodeManagementBindingRequestCall(x) => {
                s.out.push_str("{\"nodeManagementBindingRequestCall\":");
                x.ser_json(d + 1, s);
            }
            Self::NodeManagementDetailedDiscoveryData(x) => {
                s.out.push_str("{\"nodeManagementDetailedDiscoveryData\":");
                x.ser_json(d + 1, s);
            }
            Self::NodeManagementSubscriptionRequestCall(x) => {
                s.out
                    .push_str("{\"nodeManagementSubscriptionRequestCall\":");
                x.ser_json(d + 1, s);
            }
            Self::NodeManagementSubscriptionData(x) => {
                s.out.push_str("{\"nodeManagementSubscriptionData\":");
                x.ser_json(d + 1, s);
            }
            Self::NodeManagementUseCaseData(x) => {
                s.out.push_str("{\"nodeManagementUseCaseData\":");
                x.ser_json(d + 1, s);
            }
            Self::ResultData(x) => {
                s.out.push_str("{\"resultData\":");
                x.ser_json(d + 1, s);
            }
            Self::SmartEnergyManagementPsData(x) => {
                s.out.push_str("{\"smartEnergyManagementPsData\":");
                x.ser_json(d + 1, s);
            }
        }
        s.out.push('}');
    }
}

impl DeJson for Data {
    fn de_json(s: &mut DeJsonState, i: &mut Chars) -> Result<Self, DeJsonErr> {
        s.curly_open(i)?;
        let key = String::de_json(s, i)?;
        s.colon(i)?;
        let res = match key.as_str() {
            "deviceConfigurationKeyValueDescriptionListData" => {
                Self::DeviceConfigurationKeyValueDescriptionListData(DeJson::de_json(s, i)?)
            }
            "deviceConfigurationKeyValueListData" => {
                Self::DeviceConfigurationKeyValueListData(DeJson::de_json(s, i)?)
            }
            "deviceDiagnosisHeartbeatData" => {
                Self::DeviceDiagnosisHeartbeatData(DeJson::de_json(s, i)?)
            }
            "electricalConnectionCharacteristicListData" => {
                Self::ElectricalConnectionCharacteristicListData(DeJson::de_json(s, i)?)
            }
            "electricalConnectionDescriptionListData" => {
                Self::ElectricalConnectionDescriptionListData(DeJson::de_json(s, i)?)
            }
            "electricalConnectionParameterDescriptionListData" => {
                Self::ElectricalConnectionParameterDescriptionListData(DeJson::de_json(s, i)?)
            }
            "loadControlLimitDescriptionListData" => {
                Self::LoadControlLimitDescriptionListData(DeJson::de_json(s, i)?)
            }
            "loadControlLimitListData" => Self::LoadControlLimitListData(DeJson::de_json(s, i)?),
            "measurementConstraintsListData" => {
                Self::MeasurementConstraintsListData(DeJson::de_json(s, i)?)
            }
            "measurementDescriptionListData" => {
                Self::MeasurementDescriptionListData(DeJson::de_json(s, i)?)
            }
            "measurementListData" => Self::MeasurementListData(DeJson::de_json(s, i)?),
            "nodeManagementBindingRequestCall" => {
                Self::NodeManagementBindingRequestCall(DeJson::de_json(s, i)?)
            }
            "nodeManagementDetailedDiscoveryData" => {
                Self::NodeManagementDetailedDiscoveryData(DeJson::de_json(s, i)?)
            }
            "nodeManagementSubscriptionRequestCall" => {
                Self::NodeManagementSubscriptionRequestCall(DeJson::de_json(s, i)?)
            }
            "nodeManagementUseCaseData" => Self::NodeManagementUseCaseData(DeJson::de_json(s, i)?),
            "resultData" => Self::ResultData(DeJson::de_json(s, i)?),
            "smartEnergyManagementPsData" => {
                Self::SmartEnergyManagementPsData(DeJson::de_json(s, i)?)
            }
            key => Err(unexpected_field(s, key))?,
        };
        s.curly_close(i)?;
        Ok(res)
    }
}

// add more elements from `DataElementsChoiceGroup` here when they are deemed necessary
// when doing so, don't forget to change the Filter: Deserialize implementation
#[derive(Clone, Debug, PartialEq)]
pub enum DataElement {
    DeviceConfigurationKeyValueDataElements(DeviceConfigurationKeyValueDataElements),
    DeviceConfigurationKeyValueDescriptionDataElements(
        DeviceConfigurationKeyValueDescriptionDataElements,
    ),
    DeviceDiagnosisHeartbeatDataElements(DeviceDiagnosisHeartbeatDataElements),
    ElectricalConnectionCharacteristicDataElements(ElectricalConnectionCharacteristicDataElements),
    ElectricalConnectionDescriptionDataElements(ElectricalConnectionDescriptionDataElements),
    ElectricalConnectionParameterDescriptionDataElements(
        ElectricalConnectionParameterDescriptionDataElements,
    ),
    LoadControlLimitDataElements(LoadControlLimitDataElements),
    LoadControlLimitDescriptionDataElements(LoadControlLimitDescriptionDataElements),
    MeasurementConstraintsDataElements(MeasurementConstraintsDataElements),
    MeasurementDataElements(MeasurementDataElements),
    MeasurementDescriptionDataElements(MeasurementDescriptionDataElements),
    NodeManagementBindingRequestCallElements(NodeManagementBindingRequestCallElements),
    NodeManagementDetailedDiscoveryDataElements(NodeManagementDetailedDiscoveryDataElements),
    NodeManagementSubscriptionRequestCallElements(NodeManagementSubscriptionRequestCallElements),
    NodeManagementUseCaseDataElements(NodeManagementUseCaseDataElements),
    SmartEnergyManagementPsDataElements(SmartEnergyManagementPsDataElements),
}

impl SerJson for DataElement {
    fn ser_json(&self, d: usize, s: &mut SerJsonState) {
        match self {
            Self::DeviceConfigurationKeyValueDataElements(x) => {
                s.out
                    .push_str("{\"deviceConfigurationKeyValueDataElements\":");
                x.ser_json(d + 1, s);
            }
            Self::DeviceConfigurationKeyValueDescriptionDataElements(x) => {
                s.out
                    .push_str("{\"deviceConfigurationKeyValueDescriptionDataElements\":");
                x.ser_json(d + 1, s);
            }
            Self::DeviceDiagnosisHeartbeatDataElements(x) => {
                s.out.push_str("{\"deviceDiagnosisHeartbeatDataElements\":");
                x.ser_json(d + 1, s);
            }
            Self::ElectricalConnectionCharacteristicDataElements(x) => {
                s.out
                    .push_str("{\"electricalConnectionCharacteristicDataElements\":");
                x.ser_json(d + 1, s);
            }
            Self::ElectricalConnectionDescriptionDataElements(x) => {
                s.out
                    .push_str("{\"electricalConnectionDescriptionDataElements\":");
                x.ser_json(d + 1, s);
            }
            Self::ElectricalConnectionParameterDescriptionDataElements(x) => {
                s.out
                    .push_str("{\"electricalConnectionParameterDescriptionDataElements\":");
                x.ser_json(d + 1, s);
            }
            Self::LoadControlLimitDataElements(x) => {
                s.out.push_str("{\"loadControlLimitDataElements\":");
                x.ser_json(d + 1, s);
            }
            Self::LoadControlLimitDescriptionDataElements(x) => {
                s.out
                    .push_str("{\"loadControlLimitDescriptionDataElements\":");
                x.ser_json(d + 1, s);
            }
            Self::MeasurementConstraintsDataElements(x) => {
                s.out.push_str("{\"measurementConstraintsDataElements\":");
                x.ser_json(d + 1, s);
            }
            Self::MeasurementDataElements(x) => {
                s.out.push_str("{\"measurementDataElements\":");
                x.ser_json(d + 1, s);
            }
            Self::MeasurementDescriptionDataElements(x) => {
                s.out.push_str("{\"measurementDescriptionDataElements\":");
                x.ser_json(d + 1, s);
            }
            Self::NodeManagementBindingRequestCallElements(x) => {
                s.out
                    .push_str("{\"nodeManagementBindingRequestCallElements\":");
                x.ser_json(d + 1, s);
            }
            Self::NodeManagementDetailedDiscoveryDataElements(x) => {
                s.out
                    .push_str("{\"nodeManagementDetailedDiscoveryDataElements\":");
                x.ser_json(d + 1, s);
            }
            Self::NodeManagementSubscriptionRequestCallElements(x) => {
                s.out
                    .push_str("{\"nodeManagementSubscriptionRequestCallElements\":");
                x.ser_json(d + 1, s);
            }
            Self::NodeManagementUseCaseDataElements(x) => {
                s.out.push_str("{\"nodeManagementUseCaseDataElements\":");
                x.ser_json(d + 1, s);
            }
            Self::SmartEnergyManagementPsDataElements(x) => {
                s.out.push_str("{\"smartEnergyManagementPsDataElements\":");
                x.ser_json(d + 1, s);
            }
        }
        s.out.push('}');
    }
}

impl DeJson for DataElement {
    fn de_json(s: &mut DeJsonState, i: &mut Chars) -> Result<Self, DeJsonErr> {
        s.curly_open(i)?;
        let key = String::de_json(s, i)?;
        s.colon(i)?;
        let res = match key.as_str() {
            "deviceConfigurationKeyValueDataElements" => {
                Self::DeviceConfigurationKeyValueDataElements(DeJson::de_json(s, i)?)
            }
            "deviceConfigurationKeyValueDescriptionDataElements" => {
                Self::DeviceConfigurationKeyValueDescriptionDataElements(DeJson::de_json(s, i)?)
            }
            "deviceDiagnosisHeartbeatDataElements" => {
                Self::DeviceDiagnosisHeartbeatDataElements(DeJson::de_json(s, i)?)
            }
            "electricalConnectionCharacteristicDataElements" => {
                Self::ElectricalConnectionCharacteristicDataElements(DeJson::de_json(s, i)?)
            }
            "electricalConnectionDescriptionDataElements" => {
                Self::ElectricalConnectionDescriptionDataElements(DeJson::de_json(s, i)?)
            }
            "electricalConnectionParameterDescriptionDataElements" => {
                Self::ElectricalConnectionParameterDescriptionDataElements(DeJson::de_json(s, i)?)
            }
            "loadControlLimitDataElements" => {
                Self::LoadControlLimitDataElements(DeJson::de_json(s, i)?)
            }
            "loadControlLimitDescriptionDataElements" => {
                Self::LoadControlLimitDescriptionDataElements(DeJson::de_json(s, i)?)
            }
            "measurementConstraintsDataElements" => {
                Self::MeasurementConstraintsDataElements(DeJson::de_json(s, i)?)
            }
            "measurementDataElements" => Self::MeasurementDataElements(DeJson::de_json(s, i)?),
            "measurementDescriptionDataElements" => {
                Self::MeasurementDescriptionDataElements(DeJson::de_json(s, i)?)
            }
            "nodeManagementBindingRequestCallElements" => {
                Self::NodeManagementBindingRequestCallElements(DeJson::de_json(s, i)?)
            }
            "nodeManagementDetailedDiscoveryDataElements" => {
                Self::NodeManagementDetailedDiscoveryDataElements(DeJson::de_json(s, i)?)
            }
            "nodeManagementSubscriptionRequestCallElements" => {
                Self::NodeManagementSubscriptionRequestCallElements(DeJson::de_json(s, i)?)
            }
            "nodeManagementUseCaseDataElements" => {
                Self::NodeManagementUseCaseDataElements(DeJson::de_json(s, i)?)
            }
            "smartEnergyManagementPsDataElements" => {
                Self::SmartEnergyManagementPsDataElements(DeJson::de_json(s, i)?)
            }
            key => Err(unexpected_field(s, key))?,
        };
        s.curly_close(i)?;
        Ok(res)
    }
}

// add more selectors from `DataSelectorsChoiceGroup` here when they are deemed necessary
// when doing so, don't forget to change the Filter: Deserialize implementation
#[derive(Clone, Debug, PartialEq)]
pub enum DataSelectors {
    DeviceConfigurationKeyValueDescriptionListDataSelectors(
        DeviceConfigurationKeyValueDescriptionListDataSelectors,
    ),
    DeviceConfigurationKeyValueListDataSelectors(DeviceConfigurationKeyValueListDataSelectors),
    ElectricalConnectionCharacteristicListDataSelectors(
        ElectricalConnectionCharacteristicListDataSelectors,
    ),
    ElectricalConnectionDescriptionListDataSelectors(
        ElectricalConnectionDescriptionListDataSelectors,
    ),
    ElectricalConnectionParameterDescriptionListDataSelectors(
        ElectricalConnectionParameterDescriptionListDataSelectors,
    ),
    LoadControlLimitDescriptionListDataSelectors(LoadControlLimitDescriptionListDataSelectors),
    LoadControlLimitListDataSelectors(LoadControlLimitListDataSelectors),
    MeasurementConstraintsListDataSelectors(MeasurementConstraintsListDataSelectors),
    MeasurementDescriptionListDataSelectors(MeasurementDescriptionListDataSelectors),
    MeasurementListDataSelectors(MeasurementListDataSelectors),
    NodeManagementBindingDataSelectors(NodeManagementBindingDataSelectors),
    NodeManagementDetailedDiscoveryDataSelectors(NodeManagementDetailedDiscoveryDataSelectors),
    NodeManagementSubscriptionDataSelectors(NodeManagementSubscriptionDataSelectors),
    NodeManagementUseCaseDataSelectors(NodeManagementUseCaseDataSelectors),
    SmartEnergyManagementPsDataSelectors(SmartEnergyManagementPsDataSelectors),
}

impl SerJson for DataSelectors {
    fn ser_json(&self, d: usize, s: &mut SerJsonState) {
        match self {
            Self::DeviceConfigurationKeyValueDescriptionListDataSelectors(x) => {
                s.out
                    .push_str("{\"deviceConfigurationKeyValueDescriptionListDataSelectors\":");
                x.ser_json(d + 1, s);
            }
            Self::DeviceConfigurationKeyValueListDataSelectors(x) => {
                s.out
                    .push_str("{\"deviceConfigurationKeyValueListDataSelectors\":");
                x.ser_json(d + 1, s);
            }
            Self::ElectricalConnectionCharacteristicListDataSelectors(x) => {
                s.out
                    .push_str("{\"electricalConnectionCharacteristicListDataSelectors\":");
                x.ser_json(d + 1, s);
            }
            Self::ElectricalConnectionDescriptionListDataSelectors(x) => {
                s.out
                    .push_str("{\"electricalConnectionDescriptionListDataSelectors\":");
                x.ser_json(d + 1, s);
            }
            Self::ElectricalConnectionParameterDescriptionListDataSelectors(x) => {
                s.out
                    .push_str("{\"electricalConnectionParameterDescriptionListDataSelectors\":");
                x.ser_json(d + 1, s);
            }
            Self::LoadControlLimitDescriptionListDataSelectors(x) => {
                s.out
                    .push_str("{\"loadControlLimitDescriptionListDataSelectors\":");
                x.ser_json(d + 1, s);
            }
            Self::LoadControlLimitListDataSelectors(x) => {
                s.out.push_str("{\"loadControlLimitListDataSelectors\":");
                x.ser_json(d + 1, s);
            }
            Self::MeasurementConstraintsListDataSelectors(x) => {
                s.out
                    .push_str("{\"measurementConstraintsListDataSelectors\":");
                x.ser_json(d + 1, s);
            }
            Self::MeasurementDescriptionListDataSelectors(x) => {
                s.out
                    .push_str("{\"measurementDescriptionListDataSelectors\":");
                x.ser_json(d + 1, s);
            }
            Self::MeasurementListDataSelectors(x) => {
                s.out.push_str("{\"measurementListDataSelectors\":");
                x.ser_json(d + 1, s);
            }
            Self::NodeManagementBindingDataSelectors(x) => {
                s.out.push_str("{\"nodeManagementBindingDataSelectors\":");
                x.ser_json(d + 1, s);
            }
            Self::NodeManagementDetailedDiscoveryDataSelectors(x) => {
                s.out
                    .push_str("{\"nodeManagementDetailedDiscoveryDataSelectors\":");
                x.ser_json(d + 1, s);
            }
            Self::NodeManagementSubscriptionDataSelectors(x) => {
                s.out
                    .push_str("{\"nodeManagementSubscriptionDataSelectors\":");
                x.ser_json(d + 1, s);
            }
            Self::NodeManagementUseCaseDataSelectors(x) => {
                s.out.push_str("{\"nodeManagementUseCaseDataSelectors\":");
                x.ser_json(d + 1, s);
            }
            Self::SmartEnergyManagementPsDataSelectors(x) => {
                s.out.push_str("{\"smartEnergyManagementPsDataSelectors\":");
                x.ser_json(d + 1, s);
            }
        }
        s.out.push('}');
    }
}

impl DeJson for DataSelectors {
    fn de_json(s: &mut DeJsonState, i: &mut Chars) -> Result<Self, DeJsonErr> {
        s.curly_open(i)?;
        let key = String::de_json(s, i)?;
        s.colon(i)?;
        let res = match key.as_str() {
            "deviceConfigurationKeyValueDescriptionListDataSelectors" => {
                Self::DeviceConfigurationKeyValueDescriptionListDataSelectors(DeJson::de_json(
                    s, i,
                )?)
            }
            "deviceConfigurationKeyValueListDataSelectors" => {
                Self::DeviceConfigurationKeyValueListDataSelectors(DeJson::de_json(s, i)?)
            }
            "electricalConnectionCharacteristicListDataSelectors" => {
                Self::ElectricalConnectionCharacteristicListDataSelectors(DeJson::de_json(s, i)?)
            }
            "electricalConnectionDescriptionListDataSelectors" => {
                Self::ElectricalConnectionDescriptionListDataSelectors(DeJson::de_json(s, i)?)
            }
            "electricalConnectionParameterDescriptionListDataSelectors" => {
                Self::ElectricalConnectionParameterDescriptionListDataSelectors(DeJson::de_json(
                    s, i,
                )?)
            }
            "loadControlLimitDescriptionListDataSelectors" => {
                Self::LoadControlLimitDescriptionListDataSelectors(DeJson::de_json(s, i)?)
            }
            "loadControlLimitListDataSelectors" => {
                Self::LoadControlLimitListDataSelectors(DeJson::de_json(s, i)?)
            }
            "measurementConstraintsListDataSelectors" => {
                Self::MeasurementConstraintsListDataSelectors(DeJson::de_json(s, i)?)
            }
            "measurementDescriptionListDataSelectors" => {
                Self::MeasurementDescriptionListDataSelectors(DeJson::de_json(s, i)?)
            }
            "measurementListDataSelectors" => {
                Self::MeasurementListDataSelectors(DeJson::de_json(s, i)?)
            }
            "nodeManagementBindingDataSelectors" => {
                Self::NodeManagementBindingDataSelectors(DeJson::de_json(s, i)?)
            }
            "nodeManagementDetailedDiscoveryDataSelectors" => {
                Self::NodeManagementDetailedDiscoveryDataSelectors(DeJson::de_json(s, i)?)
            }
            "nodeManagementSubscriptionDataSelectors" => {
                Self::NodeManagementSubscriptionDataSelectors(DeJson::de_json(s, i)?)
            }
            "nodeManagementUseCaseDataSelectors" => {
                Self::NodeManagementUseCaseDataSelectors(DeJson::de_json(s, i)?)
            }
            "smartEnergyManagementPsDataSelectors" => {
                Self::SmartEnergyManagementPsDataSelectors(DeJson::de_json(s, i)?)
            }
            key => Err(unexpected_field(s, key))?,
        };
        s.curly_close(i)?;
        Ok(res)
    }
}

#[derive(Clone, Debug, PartialEq)]
pub enum CmdControl {
    Delete,
    Partial,
}

impl SerJson for CmdControl {
    fn ser_json(&self, _d: usize, s: &mut SerJsonState) {
        match self {
            Self::Delete => s.out.push_str("[{\"delete\":[]}]"),
            Self::Partial => s.out.push_str("[{\"partial\":[]}]"),
        }
    }
}

impl DeJson for CmdControl {
    fn de_json(s: &mut DeJsonState, i: &mut Chars) -> Result<Self, DeJsonErr> {
        s.block_open(i)?;
        s.curly_open(i)?;
        let key = String::de_json(s, i)?;
        s.colon(i)?;
        s.block_open(i)?;
        s.block_close(i)?;
        s.curly_close(i)?;
        s.block_close(i)?;
        match key.as_str() {
            "delete" => Ok(Self::Delete),
            "partial" => Ok(Self::Partial),
            key => Err(invalid_variant(s, key)),
        }
    }
}

/* nested data type compatibility annoyances ******************************************************/

pub type NodeManagementSpecificationVersionListSpecificationVersion = SpecificationVersionData;

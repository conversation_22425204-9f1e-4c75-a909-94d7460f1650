use std::fmt;

use nanoserde::DeJsonErr;

#[derive(Debug)]
pub enum ShipError {
    InvalidCmi,
    PeerAborted,
    TrustFailed,
    InvalidHelloPhase,
    UnexpectedMessage,
    Timeout,
    ConnectionClosed,
    Abort,
    UnsupportedProtocol,
    Unimplemented,
    ConnectionError(String),
    InvalidMessage,
    InvalidUtf8,
    MissingHttpHeader,
    <PERSON><PERSON>(DeJsonErr),
    Io(std::io::Error),
}

#[cfg(feature = "defmt")]
impl defmt::Format for ShipError {
    fn format(&self, fmt: defmt::Formatter) {
        match self {
            ShipError::InvalidCmi => defmt::write!(fmt, "Invalid CMI message"),
            ShipError::InvalidHelloPhase => defmt::write!(fmt, "Invalid ConnectionHello phase"),
            ShipError::UnexpectedMessage => defmt::write!(fmt, "Unexpected message received"),
            ShipError::ConnectionError(s) => defmt::write!(fmt, "Connection error: {}", s.as_str()),
            ShipError::PeerAborted => defmt::write!(fmt, "Peer aborted connection"),
            ShipError::TrustFailed => defmt::write!(fmt, "Trust failed"),
            ShipError::Timeout => defmt::write!(fmt, "Timeout"),
            ShipError::ConnectionClosed => defmt::write!(fmt, "Connection closed"),
            ShipError::Abort => {
                defmt::write!(fmt, "Abort due to incomplete or invalid ship hadshake")
            }
            ShipError::UnsupportedProtocol => {
                defmt::write!(fmt, "Unsupported protocol version or format")
            }
            ShipError::Unimplemented => defmt::write!(fmt, "Not implemented"),
            ShipError::InvalidMessage => defmt::write!(fmt, "Invalid message"),
            ShipError::InvalidUtf8 => defmt::write!(fmt, "Invalid utf8"),
            ShipError::MissingHttpHeader => defmt::write!(fmt, "Missing http header"),
            ShipError::Json(_) => defmt::write!(fmt, "JSON error"),
            ShipError::Io(e) => defmt::write!(fmt, "IO error: {}", e.to_string()),
        }
    }
}

impl fmt::Display for ShipError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ShipError::InvalidCmi => write!(f, "Invalid CMI message"),
            ShipError::InvalidHelloPhase => write!(f, "Invalid ConnectionHello phase"),
            ShipError::UnexpectedMessage => write!(f, "Unexpected message received"),
            ShipError::ConnectionError(s) => write!(f, "Connection error: {}", s),
            ShipError::PeerAborted => write!(f, "Peer aborted connection"),
            ShipError::TrustFailed => write!(f, "Trust failed"),
            ShipError::Timeout => write!(f, "Timeout"),
            ShipError::ConnectionClosed => write!(f, "Connection closed"),
            ShipError::Abort => write!(f, "Abort due to incomplete or invalid ship hadshake"),
            ShipError::UnsupportedProtocol => write!(f, "Unsupported protocol version or format"),
            ShipError::Unimplemented => write!(f, "Not implemented"),
            ShipError::InvalidMessage => write!(f, "Invalid message"),
            ShipError::InvalidUtf8 => write!(f, "Invalid utf8"),
            ShipError::MissingHttpHeader => write!(f, "Missing http header"),
            ShipError::Json(error) => write!(f, "JSON error: {}", error),
            ShipError::Io(error) => write!(f, "IO error: {}", error),
        }
    }
}

impl std::error::Error for ShipError {}

impl From<DeJsonErr> for ShipError {
    fn from(error: DeJsonErr) -> Self {
        ShipError::Json(error)
    }
}

impl From<std::io::Error> for ShipError {
    fn from(error: std::io::Error) -> Self {
        ShipError::Io(error)
    }
}

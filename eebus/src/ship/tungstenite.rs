use std::sync::Arc;

use async_tungstenite::{
    tungstenite::{protocol::Role, Message},
    WebSocketStream,
};
use embassy_futures::select::{select, Either};
use embassy_sync::{blocking_mutex::raw::RawMutex, channel::Channel};
use futures_lite::{AsyncRead, AsyncWrite, StreamExt as _};

use super::{websocket_handshake, Ski};
use crate::{
    log::*,
    ship::{self, ShipConnection, ShipError},
};

pub struct Connection<M: RawMutex> {
    pub(crate) outgoing: Arc<Channel<M, Message, 4>>,
    pub(crate) incoming: Arc<Channel<M, Message, 4>>,
    ip: std::net::IpAddr,
    port: u16,
    ski: Ski,
}

impl<M: RawMutex + 'static> Clone for Connection<M> {
    fn clone(&self) -> Self {
        Self {
            outgoing: self.outgoing.clone(),
            incoming: self.incoming.clone(),
            ip: self.ip.clone(),
            port: self.port.clone(),
            ski: self.ski.clone(),
        }
    }
}

impl<M: RawMutex + 'static> Connection<M> {
    pub async fn new<'a>(
        mut stream: impl AsyncRead + AsyncWrite + Unpin + 'a,
        ip: std::net::IpAddr,
        port: u16,
        ski: Ski,
        base64_sha1: impl FnOnce(String) -> String,
    ) -> Result<(Self, impl Future<Output = Result<(), ShipError>> + 'a), ShipError> {
        websocket_handshake(&mut stream, base64_sha1).await?;

        let incoming = Arc::new(Channel::new());
        let outgoing = Arc::new(Channel::new());

        let mut web_socket = WebSocketStream::from_raw_socket(stream, Role::Server, None).await;

        Ok((
            Connection {
                incoming: incoming.clone(),
                outgoing: outgoing.clone(),
                ip,
                port,
                ski,
            },
            async move {
                loop {
                    match select(web_socket.next(), outgoing.receive()).await {
                        Either::First(msg) => {
                            trace!("received websocket msg");
                            let Some(Ok(msg)) = msg else {
                                error!("web socket closed");
                                break;
                            };
                            incoming.send(msg).await
                        }
                        Either::Second(msg) => {
                            web_socket
                                .send(msg)
                                .await
                                .map_err(|e| ShipError::ConnectionError(e.to_string()))?;
                            trace!("sent websocket msg");
                        }
                    }
                }

                Ok(())
            },
        ))
    }
}

impl<M: RawMutex + Send + Sync + 'static> ShipConnection for Connection<M> {
    async fn read_message(&self) -> Result<ship::MessageValue, ShipError> {
        loop {
            let message = self.incoming.receive().await;

            #[cfg(feature = "log")]
            info!("read message: {:?}", message);
            #[cfg(feature = "defmt")]
            info!("read message: {:?}", defmt::Debug2Format(&message));

            match message {
                Message::Binary(bytes) => return ship::parse_ship_message(&bytes),
                Message::Ping(ping) => {
                    info!("ping pong");
                    self.outgoing.send(Message::Pong(ping)).await
                }
                Message::Close(_) => {
                    info!("received websocket close msg");
                    return Err(ShipError::ConnectionClosed);
                }
                _ => return Err(ShipError::InvalidMessage),
            }
        }
    }

    fn write_message(
        &self,
        msg: ship::MessageValue,
    ) -> impl Future<Output = Result<(), ShipError>> + std::marker::Send {
        let send_msg = self.outgoing.clone();
        async move {
            let msg = ship::serialize_ship_message(&msg);

            match msg[0] {
                0 => info!("write message: {:?}", &msg[1..]),
                _ => info!(
                    "write message: {:?}",
                    std::str::from_utf8(&msg[1..]).unwrap()
                ),
            }

            send_msg.send(Message::Binary(msg.into())).await;
            Ok(())
        }
    }

    async fn close(&self) -> Result<(), ShipError> {
        self.outgoing.send(Message::Close(None)).await;
        Ok(())
    }

    fn device_info(&self) -> ship::DeviceInfo {
        ship::DeviceInfo {
            ip: self.ip,
            port: self.port,
            ski: self.ski,
        }
    }
}

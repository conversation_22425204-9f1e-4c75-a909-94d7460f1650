#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub enum MessageValue {
    Init(MsgTypeInit),
    Control(MsgTypeControl),
    Data(MsgTypeData),
    End(MsgTypeEnd),
}

#[cfg(feature = "defmt")]
impl defmt::Format for MessageValue {
    fn format(&self, fmt: defmt::Formatter) {
        match self {
            Self::Init(_) => defmt::write!(fmt, "init"),
            Self::Control(_) => defmt::write!(fmt, "control"),
            Self::Data(_) => defmt::write!(fmt, "data"),
            Self::End(_) => defmt::write!(fmt, "end"),
        }
    }
}

impl SerJson for MessageValue {
    fn ser_json(&self, d: usize, s: &mut SerJsonState) {
        match self {
            Self::Init(x) => x.ser_json(d, s),
            Self::Control(x) => x.ser_json(d, s),
            Self::Data(x) => x.ser_json(d, s),
            Self::End(x) => x.ser_json(d, s),
        }
    }
}

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct MsgTypeInit {
    pub cmi: u8,
}

#[derive(Debug, Clone)]
pub enum MsgTypeControl {
    ConnectionHello(ConnectionHello),
    MessageProtocolHandshake(MessageProtocolHandshake),
    MessageProtocolHandshakeError(MessageProtocolHandshakeError),
    ConnectionPinState(ConnectionPinState),
    ConnectionPinInput(ConnectionPinInput),
    ConnectionPinError(ConnectionPinError),
    AccessMethodsRequest(AccessMethodsRequest),
    AccessMethods(AccessMethods),
}

impl SerJson for MsgTypeControl {
    fn ser_json(&self, d: usize, s: &mut SerJsonState) {
        match self {
            Self::ConnectionHello(x) => {
                s.out.push_str("{\"connectionHello\":");
                x.ser_json(d + 1, s);
            }
            Self::MessageProtocolHandshake(x) => {
                s.out.push_str("{\"messageProtocolHandshake\":");
                x.ser_json(d + 1, s);
            }
            Self::MessageProtocolHandshakeError(x) => {
                s.out.push_str("{\"messageProtocolHandshakeError\":");
                x.ser_json(d + 1, s);
            }
            Self::ConnectionPinState(x) => {
                s.out.push_str("{\"connectionPinState\":");
                x.ser_json(d + 1, s);
            }
            Self::ConnectionPinInput(x) => {
                s.out.push_str("{\"connectionPinInput\":");
                x.ser_json(d + 1, s);
            }
            Self::ConnectionPinError(x) => {
                s.out.push_str("{\"connectionPinError\":");
                x.ser_json(d + 1, s);
            }
            Self::AccessMethodsRequest(x) => {
                s.out.push_str("{\"accessMethodsRequest\":");
                x.ser_json(d + 1, s);
            }
            Self::AccessMethods(x) => {
                s.out.push_str("{\"accessMethods\":");
                x.ser_json(d + 1, s);
            }
        }
        s.out.push('}');
    }
}

impl DeJson for MsgTypeControl {
    fn de_json(s: &mut DeJsonState, i: &mut std::str::Chars) -> Result<Self, DeJsonErr> {
        s.curly_open(i)?;
        let key = String::de_json(s, i)?;
        s.colon(i)?;
        let res = match key.as_str() {
            "connectionHello" => Self::ConnectionHello(DeJson::de_json(s, i)?),
            "messageProtocolHandshake" => Self::MessageProtocolHandshake(DeJson::de_json(s, i)?),
            "messageProtocolHandshakeError" => {
                Self::MessageProtocolHandshakeError(DeJson::de_json(s, i)?)
            }
            "connectionPinState" => Self::ConnectionPinState(DeJson::de_json(s, i)?),
            "connectionPinInput" => Self::ConnectionPinInput(DeJson::de_json(s, i)?),
            "connectionPinError" => Self::ConnectionPinError(DeJson::de_json(s, i)?),
            "accessMethodsRequest" => Self::AccessMethodsRequest(DeJson::de_json(s, i)?),
            "accessMethods" => Self::AccessMethods(DeJson::de_json(s, i)?),
            key => Err(unexpected_field(s, key))?,
        };
        s.curly_close(i)?;
        Ok(res)
    }
}

#[derive(Debug, Clone)]
pub enum MsgTypeData {
    Data(Box<Data>),
}

impl SerJson for MsgTypeData {
    fn ser_json(&self, d: usize, s: &mut SerJsonState) {
        s.out.push_str("{\"data\":");
        match self {
            Self::Data(x) => x.ser_json(d + 1, s),
        }
        s.out.push('}');
    }
}

impl DeJson for MsgTypeData {
    fn de_json(s: &mut DeJsonState, i: &mut std::str::Chars) -> Result<Self, DeJsonErr> {
        s.curly_open(i)?;
        let key = String::de_json(s, i)?;
        if key != "data" {
            Err(unexpected_field(s, &key))?;
        }
        s.colon(i)?;
        let res = Self::Data(Box::new(DeJson::de_json(s, i)?));
        s.curly_close(i)?;
        Ok(res)
    }
}

#[derive(Debug, Clone)]
pub enum MsgTypeEnd {
    ConnectionClose(ConnectionClose),
}

impl SerJson for MsgTypeEnd {
    fn ser_json(&self, d: usize, s: &mut SerJsonState) {
        s.out.push_str("{\"connectionClose\":");
        match self {
            Self::ConnectionClose(x) => x.ser_json(d + 1, s),
        }
        s.out.push('}');
    }
}

impl DeJson for MsgTypeEnd {
    fn de_json(s: &mut DeJsonState, i: &mut std::str::Chars) -> Result<Self, DeJsonErr> {
        s.curly_open(i)?;
        let key = String::de_json(s, i)?;
        if key != "connectionClose" {
            Err(unexpected_field(s, &key))?;
        }
        s.colon(i)?;
        let res = Self::ConnectionClose(DeJson::de_json(s, i)?);
        s.curly_close(i)?;
        Ok(res)
    }
}

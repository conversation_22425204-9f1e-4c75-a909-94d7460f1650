use embassy_time::{Duration, Timer};
use futures_lite::{io::BufReader, pin, AsyncBufReadExt, AsyncRead, AsyncWrite, AsyncWriteExt};

use super::ShipError;
use crate::log::*;

/// Perform serverside websocket handshake on TLS stream
pub async fn websocket_handshake(
    mut stream: impl AsyncRead + AsyncWrite + Unpin,
    base64_sha1: impl FnOnce(String) -> String,
) -> Result<(), ShipError> {
    let mut headers = String::new();

    {
        let stream = BufReader::new(&mut stream);
        pin!(stream);

        loop {
            let mut line = String::new();
            stream.read_line(&mut line).await.unwrap();
            if line.is_empty() {
                return Err(ShipError::ConnectionClosed);
            }
            if line == "\r\n" {
                break;
            }
            headers.push_str(&line);
            info!("http header: {}", line.trim_end());
        }
    }

    info!("validating headers");

    // 🔍 Validate required WebSocket headers
    if !headers.contains("Upgrade: websocket")
        || !headers.contains("Connection: Upgrade")
        || !headers.contains("Sec-WebSocket-Version: 13")
        || !headers.contains("Sec-WebSocket-Protocol: ship")
    {
        let _ = stream
            .write_all(b"HTTP/1.1 400 Bad Request\r\n\r\nMissing required SHIP headers")
            .await;

        return Err(ShipError::MissingHttpHeader);
    }

    let key = headers
        .lines()
        .find(|line| line.starts_with("Sec-WebSocket-Key"))
        .and_then(|line| line.split(':').nth(1))
        .map(str::trim)
        .unwrap_or("");

    let accept_key = {
        // use base64::Engine;
        // use base64::engine::general_purpose::STANDARD as base64_engine;
        // use sha1::{Digest, Sha1};

        // let mut hasher = Sha1::new();
        // hasher.update(format!("{key}258EAFA5-E914-47DA-95CA-C5AB0DC85B11"));
        // base64_engine.encode(hasher.finalize())
        base64_sha1(format!("{key}258EAFA5-E914-47DA-95CA-C5AB0DC85B11"))
    };

    let response = format!(
        "HTTP/1.1 101 Switching Protocols\r\n\
         Upgrade: websocket\r\n\
         Connection: Upgrade\r\n\
         Sec-WebSocket-Accept: {accept_key}\r\n\
         Sec-WebSocket-Protocol: ship\r\n\r\n"
    );

    // tokio::io::AsyncWriteExt::write_all(&mut stream, response.as_bytes()).await?;
    stream.write_all(response.as_bytes()).await?;
    stream.flush().await?;

    debug!("Sent websocket upgrade");

    Timer::after(Duration::from_millis(200)).await;
    // flush again to detect potential FIN
    stream.flush().await?;

    info!("SHIP handshake complete");

    Ok(())
}

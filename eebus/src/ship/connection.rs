use std::{fmt::Display, str::FromStr};

use embassy_futures::select::{select, Either};
use embassy_time::{with_deadline, with_timeout, Duration, Instant};

use crate::{
    log::*,
    ship::{error::ShipError, msg::*},
};

pub trait ShipConnection: Clone + Send + Sync + 'static {
    async fn read_message(&self) -> Result<MessageValue, ShipError>;

    async fn read_message_timeout(&self, timeout: Duration) -> Result<MessageValue, ShipError> {
        with_timeout(timeout, self.read_message())
            .await
            .map_err(|_| ShipError::Timeout)?
    }

    async fn read_message_deadline(&self, timeout: Instant) -> Result<MessageValue, ShipError> {
        with_deadline(timeout, self.read_message())
            .await
            .map_err(|_| ShipError::Timeout)?
    }

    fn write_message(
        &self,
        msg: MessageValue,
    ) -> impl std::future::Future<Output = Result<(), ShipError>> + std::marker::Send;
    async fn close(&self) -> Result<(), ShipError>;

    fn device_info(&self) -> DeviceInfo;
}

pub struct DeviceInfo {
    pub ip: std::net::IpAddr,
    pub port: u16,
    pub ski: Ski,
}

#[derive(Clone, Copy, Debug, Eq, Hash, Ord, PartialEq, PartialOrd)]
pub struct Ski {
    pub bytes: [u8; 20],
}

impl FromStr for Ski {
    type Err = hex::FromHexError;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        let mut bytes = [0u8; 20];
        hex::decode_to_slice(s, &mut bytes)?;
        Ok(Self { bytes })
    }
}

impl From<[u8; 20]> for Ski {
    fn from(bytes: [u8; 20]) -> Self {
        Self { bytes }
    }
}

impl Display for Ski {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let mut output = [0u8; 40];
        hex::encode_to_slice(self.bytes, &mut output).unwrap();
        write!(f, "{}", std::str::from_utf8(&output).unwrap())
    }
}

pub trait Trust: Send + Sync + 'static {
    async fn wait_trust_update(&self, info: &DeviceInfo, from: TrustLevel) -> TrustLevel;
    async fn trust_level(&self, info: &DeviceInfo) -> TrustLevel;
    async fn has_trust(&self, info: &DeviceInfo) -> bool {
        matches!(self.trust_level(info).await, TrustLevel::Trust)
    }
}

// TODO: what happens if the user already distrusted the peer from the start?
#[derive(Clone, Copy, Debug, Eq, Hash, PartialEq)]
#[cfg_attr(feature = "defmt", derive(defmt::Format))]
pub enum TrustLevel {
    Trust,
    Distrust,
    Pending,
}

#[derive(Clone, Debug)]
pub struct Config {
    /// between 10 and 30 seconds according to spec line 1403
    pub cmi_timeout: Duration,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            cmi_timeout: Duration::from_secs(30),
        }
    }
}

pub const HELLO_TIMEOUT: Duration = Duration::from_secs(60);
pub const HELLO_TIMEOUT_INC: Duration = HELLO_TIMEOUT;
pub const HELLO_PROLONG_THRESH: Duration = Duration::from_secs(30);
pub const HELLO_PROLONG_GAP: Duration = Duration::from_secs(15);
pub const HELLO_PROLONG_MIN: Duration = Duration::from_secs(1);

/// Returns true if PIN is required
pub async fn init_ship_connection<C: ShipConnection>(
    connection: &C,
    trust: &impl Trust,
    config: Config,
    is_server: bool,
) -> Result<bool, ShipError> {
    handle_cmi(connection, &config, is_server).await?;
    info!("completed cmi");
    handle_hello(connection, trust).await?;
    info!("completed hello");
    handle_protocol_handshake(connection, is_server).await?;
    info!("completed handshake");
    send_pin_request(connection).await?;
    let pin_required = handle_pin_exchange(connection, Duration::from_secs(10)).await?;
    info!("completed pin exchange");

    Ok(pin_required)
}

pub async fn handle_cmi<C: ShipConnection>(
    connection: &C,
    config: &Config,
    is_server: bool,
) -> Result<(), ShipError> {
    if !is_server {
        // Client sends the initial init message.
        let init_msg = MessageValue::Init(MsgTypeInit { cmi: 0 });
        connection.write_message(init_msg).await?;
    }

    // both client and server expect a cmi message
    let peer_init = connection.read_message_timeout(config.cmi_timeout).await?;
    let res = match peer_init {
        MessageValue::Init(MsgTypeInit { cmi: 0 }) => Ok(()),
        _ => Err(ShipError::UnexpectedMessage),
    };
    if is_server {
        // server always replies with cmi 0
        connection
            .write_message(MessageValue::Init(MsgTypeInit { cmi: 0 }))
            .await?;
    }
    // close connection if we didn't receive cmi message with cmi 0
    res
}

pub async fn handle_hello<C: ShipConnection>(
    connection: &C,
    trust: &impl Trust,
) -> Result<(), ShipError> {
    let device_info = connection.device_info();
    let mut trust_level = trust.trust_level(&device_info).await;
    let write_update = |connection: C, trust_level, ready_deadline: Instant| async move {
        connection
            .write_message(MessageValue::Control(MsgTypeControl::ConnectionHello(
                ConnectionHello {
                    phase: match trust_level {
                        TrustLevel::Trust => ConnectionHelloPhase::Ready,
                        TrustLevel::Pending => ConnectionHelloPhase::Pending,
                        TrustLevel::Distrust => ConnectionHelloPhase::Aborted,
                    },
                    waiting: Some((ready_deadline - Instant::now()).as_secs() as u32 * 1000),
                    prolongation_request: None,
                },
            )))
            .await
    };

    let mut ready_deadline = Some(Instant::now() + HELLO_TIMEOUT);
    let mut send_prolong_at = None; // tracks when to send prolongation request
    let mut prolongation_reply_deadline = None; // tracks partner's ready_deadline (only in case we prolong it (???))
    write_update(connection.clone(), trust_level, ready_deadline.unwrap()).await?;

    let mut peer_is_ready = false;
    let mut last_waiting = None;

    let ok = loop {
        let timers = [ready_deadline, send_prolong_at, prolongation_reply_deadline];
        let next_timer = timers.into_iter().flatten().min().unwrap();
        let msg = match with_deadline(
            next_timer,
            select(
                trust.wait_trust_update(&device_info, trust_level),
                connection.read_message(),
            ),
        )
        .await
        {
            Err(_) => {
                let now = Instant::now();
                let expired = timers
                    .into_iter()
                    .rev()
                    .map(|t| t.map(|t| now >= t).unwrap_or(false))
                    .fold(0, |acc, x| acc << 1 | x as u8);
                const READY: u8 = 0b001;
                const PROLONG: u8 = 0b010;
                const REPLY: u8 = 0b100;
                info!("timers expired: 0b{:b}", expired);
                if trust_level == TrustLevel::Trust {
                    if expired & READY != 0 {
                        break false;
                    }
                } else {
                    if expired & READY != 0 {
                        break false;
                    }
                    if expired & PROLONG != 0 {
                        send_prolong_at = None;
                        println!("sending prolongation");
                        connection
                            .write_message(MessageValue::Control(MsgTypeControl::ConnectionHello(
                                ConnectionHello {
                                    phase: match trust_level {
                                        TrustLevel::Trust => ConnectionHelloPhase::Ready,
                                        TrustLevel::Pending => ConnectionHelloPhase::Pending,
                                        TrustLevel::Distrust => ConnectionHelloPhase::Aborted,
                                    },
                                    waiting: None,
                                    prolongation_request: Some(true),
                                },
                            )))
                            .await?;
                        // TODO: this is not accurate with line 1590
                        prolongation_reply_deadline = Some(
                            Instant::now()
                                + Duration::from_millis(
                                    last_waiting.unwrap_or(HELLO_TIMEOUT.as_millis() as u32) as u64,
                                ),
                        );
                    }
                    if expired & REPLY != 0 {
                        break false;
                    }
                }
                continue;
            }
            Ok(Either::First(new_level)) => {
                info!("trust update {:?} -> {:?}", trust_level, new_level);
                if trust_level == TrustLevel::Trust {
                    warn!("cannot change from trusted level");
                    return Err(ShipError::TrustFailed);
                }

                // disable timers
                send_prolong_at = None;
                prolongation_reply_deadline = None;
                // we switch from Pending to Trust or Distrust
                trust_level = new_level;
                write_update(connection.clone(), trust_level, ready_deadline.unwrap()).await?;
                if trust_level == TrustLevel::Trust && peer_is_ready {
                    break true;
                }
                continue;
            }
            Ok(Either::Second(msg)) => msg?,
        };
        let msg = match msg {
            MessageValue::Control(MsgTypeControl::ConnectionHello(hello)) => hello,
            _ => {
                warn!("expected ConnectionHello, got {:?}", msg);
                break false;
            }
        };
        peer_is_ready = matches!(msg.phase, ConnectionHelloPhase::Ready);
        last_waiting = last_waiting.or(msg.waiting);

        if trust_level == TrustLevel::Trust {
            match msg.phase {
                ConnectionHelloPhase::Ready => break true,
                ConnectionHelloPhase::Aborted => break false,
                ConnectionHelloPhase::Pending if msg.prolongation_request.unwrap_or(false) => {
                    // process prolongation request
                    *ready_deadline.as_mut().unwrap() += HELLO_TIMEOUT_INC; // TODO: can this panic?
                    write_update(connection.clone(), trust_level, ready_deadline.unwrap()).await?;
                }
                _ => (),
            }
        } else if let Some(waiting) = msg.waiting
            && matches!(
                msg.phase,
                ConnectionHelloPhase::Ready | ConnectionHelloPhase::Pending
            )
        {
            if waiting >= HELLO_PROLONG_THRESH.as_millis() as u32 {
                send_prolong_at = Some(
                    Instant::now() + Duration::from_millis(waiting as u64) - HELLO_PROLONG_GAP,
                );
            }
        } else if matches!(msg.phase, ConnectionHelloPhase::Pending)
            && msg.prolongation_request.unwrap_or(false)
        {
            // process prolongation request
            *ready_deadline.as_mut().unwrap() += HELLO_TIMEOUT_INC; // TODO: can this panic?
            write_update(connection.clone(), trust_level, ready_deadline.unwrap()).await?;
        } else {
            warn!("unexpected hello msg, aborting");
            break false;
        }
    };
    if !ok {
        connection
            .write_message(MessageValue::Control(MsgTypeControl::ConnectionHello(
                ConnectionHello {
                    phase: ConnectionHelloPhase::Aborted,
                    waiting: None,
                    prolongation_request: Some(false),
                },
            )))
            .await?;
        return Err(ShipError::Abort);
    }

    Ok(())
}

pub async fn handle_protocol_handshake<C: ShipConnection>(
    connection: &C,
    is_server: bool,
) -> Result<(), ShipError> {
    if !is_server {
        let handshake = MessageProtocolHandshake {
            handshake_type: ProtocolHandshake::AnnounceMax,
            version: MessageProtocolHandshakeVersion { major: 1, minor: 0 },
            formats: MessageProtocolFormats {
                format: vec!["JSON-UTF8".to_string()],
            },
        };
        let handshake_msg =
            MessageValue::Control(MsgTypeControl::MessageProtocolHandshake(handshake));
        connection.write_message(handshake_msg).await?;

        let selection = recv_protocol_handshake_message(connection).await?;

        match &selection {
            MessageValue::Control(MsgTypeControl::MessageProtocolHandshake(
                MessageProtocolHandshake {
                    handshake_type: ProtocolHandshake::Select,
                    version: MessageProtocolHandshakeVersion { major: 1, minor: 0 },
                    formats: MessageProtocolFormats { format },
                },
            )) if format.len() == 1 && format[0] == "JSON-UTF8" => (),
            _ => {
                error!(
                    "SHIP Server: Unexpected message during protocol selection: {:?}",
                    selection
                );
                let msg = MessageValue::Control(MsgTypeControl::MessageProtocolHandshakeError(
                    MessageProtocolHandshakeError {
                        error: 3, // selection mismatch
                    },
                ));
                connection.write_message(msg).await?;
                return Err(ShipError::UnsupportedProtocol);
            }
        };

        connection.write_message(selection).await?;
    } else {
        let handshake_msg = recv_protocol_handshake_message(connection).await?;

        let handshake_msg = match handshake_msg {
            MessageValue::Control(MsgTypeControl::MessageProtocolHandshake(
                handshake_msg @ MessageProtocolHandshake {
                    handshake_type: ProtocolHandshake::AnnounceMax,
                    ..
                },
            )) => handshake_msg,
            _ => {
                error!(
                    "SHIP Server: Unexpected message during protocol handshake: {:?}",
                    handshake_msg
                );
                let msg = MessageValue::Control(MsgTypeControl::MessageProtocolHandshakeError(
                    MessageProtocolHandshakeError {
                        error: 2, // unexpected message
                    },
                ));
                connection.write_message(msg).await?;
                return Err(ShipError::UnexpectedMessage);
            }
        };

        if !handshake_msg
            .formats
            .format
            .iter()
            .any(|f| f == "JSON-UTF8")
        {
            error!("peer does not support JSON-UTF8");
            let msg = MessageValue::Control(MsgTypeControl::MessageProtocolHandshakeError(
                MessageProtocolHandshakeError {
                    // here, there seems to be an error in the eebus ship spec. it specifies the usage of an "unexpected
                    // format" error message, but that error message does not exist in the specification... so i guess
                    // the next closest thing is "unexpected message".
                    error: 2, // unexpected message
                },
            ));
            connection.write_message(msg).await?;
            return Err(ShipError::UnsupportedProtocol);
        }

        let msg = MessageValue::Control(MsgTypeControl::MessageProtocolHandshake(
            MessageProtocolHandshake {
                handshake_type: ProtocolHandshake::Select,
                version: MessageProtocolHandshakeVersion { major: 1, minor: 0 },
                formats: MessageProtocolFormats {
                    format: vec![String::from("JSON-UTF8")],
                },
            },
        ));
        connection.write_message(msg).await?;

        let selection = recv_protocol_handshake_message(connection).await?;

        match selection {
            MessageValue::Control(MsgTypeControl::MessageProtocolHandshake(
                MessageProtocolHandshake {
                    handshake_type: ProtocolHandshake::Select,
                    version: MessageProtocolHandshakeVersion { major: 1, minor: 0 },
                    formats: MessageProtocolFormats { format },
                },
            )) if format.len() == 1 && format[0] == "JSON-UTF8" => (),
            _ => {
                // ship spec does not specify what we should do in this case (because it should not happen), but this
                // seems like a good solution.
                error!(
                    "SHIP Server: Unexpected message during protocol selection: {:?}",
                    selection
                );
                let msg = MessageValue::Control(MsgTypeControl::MessageProtocolHandshakeError(
                    MessageProtocolHandshakeError {
                        error: 3, // selection mismatch
                    },
                ));
                connection.write_message(msg).await?;
                return Err(ShipError::UnsupportedProtocol);
            }
        };
    }
    Ok(())
}

async fn recv_protocol_handshake_message<C: ShipConnection>(
    connection: &C,
) -> Result<MessageValue, ShipError> {
    const TIMEOUT: Duration = Duration::from_secs(10);

    match connection.read_message_timeout(TIMEOUT).await {
        Err(_) => {
            error!("protocol handshake timeout");
            let msg = MessageValue::Control(MsgTypeControl::MessageProtocolHandshakeError(
                MessageProtocolHandshakeError {
                    error: 1, // timeout
                },
            ));
            connection.write_message(msg).await?;
            Err(ShipError::Timeout)
        }
        ok => ok,
    }
}

async fn send_pin_request<C: ShipConnection>(connection: &C) -> Result<(), ShipError> {
    // TODO: implement proper PIN CHECK states instead of just specifying "none"
    //       (see eebus/docs/_self/pin-exchange-state-chart.png)

    let msg = MessageValue::Control(MsgTypeControl::ConnectionPinState(ConnectionPinState {
        pin_state: PinState::None,
        input_permission: None,
    }));

    connection.write_message(msg).await
}

pub async fn send_pin_input<C: ShipConnection>(
    connection: &C,
    pin: String,
) -> Result<(), ShipError> {
    let input = ConnectionPinInput { pin };
    let msg = MessageValue::Control(MsgTypeControl::ConnectionPinInput(input));
    connection.write_message(msg).await
}

/// Returns true if PIN is required
pub async fn handle_pin_exchange<C: ShipConnection>(
    connection: &C,
    timeout: Duration,
) -> Result<bool, ShipError> {
    let mut timeout = Some(Instant::now() + timeout);

    let msg = 'outer_recv: loop {
        let msg = 'inner_recv: loop {
            let msg = match timeout {
                Some(timeout) => connection.read_message_deadline(timeout).await?,
                None => connection.read_message().await?,
            };

            match msg {
                MessageValue::Control(MsgTypeControl::ConnectionPinState(msg)) => {
                    timeout = None;
                    break 'inner_recv msg;
                }
                MessageValue::Control(MsgTypeControl::ConnectionPinError(_)) => {
                    // TODO: is this really correct?
                    // the specification for ASK specifies that when an error is received, one should wait for a new pin
                    // state message. however, CHECK specifies that the pin state message is only transmitted if a
                    // penalty is imposed. if no penalty is imposed, no pin state message is transmitted. so this is
                    // probably incorrect... idfk
                    timeout = None;
                    continue 'inner_recv;
                }
                _ => continue 'inner_recv,
            }
        };

        // pinState = none or pinOk -> inputPermission = none
        // pinState = optional or required -> inputPermission = some
        // other combinations are invalid
        if matches!(msg.pin_state, PinState::None | PinState::PinOk)
            != msg.input_permission.is_none()
        {
            error!("invalid pin parameter combination");
            return Err(ShipError::InvalidMessage);
        }

        // peer does not accept pin submission yet -> wait for updates.
        // instead of waiting indefinitely for new pin state messages, one
        // could also query the peer every once in a while by sending pin
        // input messages, but that is not recommended by the spec. instead,
        // one should ideally just wait it out.
        if matches!(msg.input_permission, Some(PinInputPermission::Busy)) {
            continue 'outer_recv;
        }

        break 'outer_recv msg;
    };

    let pin_required = matches!(msg.pin_state, PinState::Required | PinState::Optional);

    Ok(pin_required)
}

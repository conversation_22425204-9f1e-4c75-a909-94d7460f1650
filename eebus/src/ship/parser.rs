use nanoserde::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>sonState};

use super::{error::ShipError, msg::MessageValue, MsgTypeInit};

pub fn parse_ship_message(message: &[u8]) -> Result<MessageValue, ShipError> {
    message
        .split_first()
        .map_or(Err(ShipError::InvalidMessage), |(msg_type, bytes)| {
            let data = std::str::from_utf8(bytes).map_err(|_| ShipError::InvalidUtf8);
            match msg_type {
                0 => Ok(MessageValue::Init(MsgTypeInit {
                    cmi: *bytes.first().ok_or(ShipError::InvalidMessage)?,
                })),
                1 => Ok(MessageValue::Control(DeJson::deserialize_json(data?)?)),
                2 => Ok(MessageValue::Data(DeJson::deserialize_json(data?)?)),
                3 => Ok(MessageValue::End(DeJson::deserialize_j<PERSON>(data?)?)),
                _ => Err(ShipError::InvalidMessage),
            }
        })
}

pub fn serialize_ship_message(message: &MessageValue) -> Vec<u8> {
    let mut s = SerJsonState::new(String::from("X"));
    match message {
        MessageValue::Init(data) => return vec![0, data.cmi],
        MessageValue::Control(data) => data.ser_json(0, &mut s),
        MessageValue::Data(data) => data.ser_json(0, &mut s),
        MessageValue::End(data) => data.ser_json(0, &mut s),
    }
    let mut result = Vec::from(s.out);
    result[0] = match message {
        MessageValue::Init(_) => 0,
        MessageValue::Control(_) => 1,
        MessageValue::Data(_) => 2,
        MessageValue::End(_) => 3,
    };
    result
}

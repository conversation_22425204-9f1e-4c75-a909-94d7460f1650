use std::{fs::read_to_string, io, ops::RangeInclusive, path::Path};

use indexmap::IndexMap;
use serde_derive::Deserialize;

#[derive(Deserialize)]
#[serde(transparent)]
pub struct HexNumber(#[serde(deserialize_with = "helper::hex_to_u32")] pub u32);

#[derive(Default, Deserialize)]
pub struct WhiteList {
    #[serde(default)]
    pub addresses: Vec<HexNumber>,
    #[serde(default)]
    pub info_numbers: Vec<u16>,
    #[serde(default)]
    pub ranges: Vec<RangeInclusive<u16>>,
}

#[derive(Deserialize)]
pub struct WhiteListConfig {
    pub whitelists: IndexMap<u32, WhiteList>,
}

pub fn read_white_list_config(path: impl AsRef<Path>) -> Result<WhiteListConfig, io::Error> {
    let s = read_to_string(path)?;

    serde_json::from_str(&s).map_err(Into::into)
}

mod helper {
    use serde::{Deserialize, Deserializer};

    pub fn hex_to_u32<'de, D>(deserializer: D) -> Result<u32, D::Error>
    where
        D: Deserializer<'de>,
    {
        use serde::de::Error;

        String::deserialize(deserializer).and_then(|string| {
            u32::from_str_radix(&string[2..], 16).map_err(|err| Error::custom(err.to_string()))
        })
    }
}

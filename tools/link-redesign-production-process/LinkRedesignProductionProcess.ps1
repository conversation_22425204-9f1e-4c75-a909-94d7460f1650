# Link Redesign production process (deployment and function test)

$LinkToolPath = "C:\link-tool-0.3.1-fw-4.50.0-CMS"
$LinkCertificatesPath = "C:\certs"

$SerialPortEspProg = "COM10"
$SerialPortWolfServiceCable = "COM10"
# $SerialPortEbusLogger = "COM9"

$LinkStartUpTimeMs = 9000

$TestModeSsid = "SSID" # Wifi name, cmsguest
$TestModePassword = "Password" # Wifi password, cmsguest corresponding password

# This function is only used to have a timestamp on the console output
function Write-OutputWithTimestamp {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory, Position = 0)]
        [string]$Message
    )

    $timestamp = [System.DateTime]::Now.ToString("HH:mm:ss.fffffff")
    Write-Output "[$timestamp] $Message"
    
}

try {
    Push-Location

    Set-Location $LinkToolPath

    # Maybe some delay until everything is set up
    # Start-Sleep -Milliseconds 500


    # Deploy
    #(IO0 and EN have to be connected)
    Write-OutputWithTimestamp "Start deployment"

    # prod
    .\link-tool.exe deploy --cert-pool-path $LinkCertificatesPath --prod .\images\esp-firmware-prod.bin -p $SerialPortEspProg
    
    # dev
    # .\link-tool.exe deploy images\esp-firmware-dev.bin -p $SerialPortEspProg

    If ($LASTEXITCODE -ne 0) {
        Write-OutputWithTimestamp "Deployment failed. EspProg not fully connected (IO0 and EN) or Link already deployed"

        # Restart Link module
        Write-OutputWithTimestamp "Restart Link to exit download mode"
        .\link-tool.exe reset -p $SerialPortEspProg

        # Wait until Link is fully restarted
        Write-OutputWithTimestamp "Wait until Link is fully restarted"
        Start-Sleep -Milliseconds $LinkStartUpTimeMs
        
        # Read firmware version
        Write-OutputWithTimestamp "Try to read firmware version"
        .\link-tool.exe cmd version full -p $SerialPortEspProg # full version command due to backward compatibility

        If ($LASTEXITCODE -eq 0) {
            Write-OutputWithTimestamp "Reading firmware version was successful. Link is already deployed"
        }
        else {
            Write-OutputWithTimestamp "Reading firmware version failed. Link is not deployed"
        }

        Exit 1
    }

    Write-OutputWithTimestamp "Deployment successfully finished"


    # Wait until Link is fully restarted
    Write-OutputWithTimestamp "Wait until Link is fully restarted"
    Start-Sleep -Milliseconds $LinkStartUpTimeMs


    # Read version
    Write-OutputWithTimestamp "Read version"
    $Response = .\link-tool.exe cmd version customer -p $SerialPortWolfServiceCable | Out-String

    If ($LASTEXITCODE -ne 0) {
        Write-OutputWithTimestamp "Read version failed"
        Exit 1
    }

    $LinkVersion = $null

    if ($Response -match "^\s*([0-9]+\.[0-9]{2})\s*$") {
        # \s* to ignore whitespace
        $LinkVersion = $Matches[1]
        Write-OutputWithTimestamp "Version: $LinkVersion"
    }
    else {
        Write-OutputWithTimestamp "Invalid version response: $Response"
        Exit 1
    }


    # Read MAC
    Write-OutputWithTimestamp "Read MAC (serial number)"
    $Response = .\link-tool.exe cmd read-mac -p $SerialPortWolfServiceCable | Out-String

    If ($LASTEXITCODE -ne 0) {
        Write-OutputWithTimestamp "Read MAC (serial number) failed"
        Exit 1
    }

    $LinkSerialNumber = $null

    if ($Response -match "^\s*([a-zA-Z0-9]{12})\s*$") {
        $LinkSerialNumber = $Matches[1]
        Write-OutputWithTimestamp "Serial number: $LinkSerialNumber"
    }
    else {
        Write-OutputWithTimestamp "Invalid MAC (serial number) response: $Response"
        Exit 1
    }


    # Read password
    Write-OutputWithTimestamp "Read password"
    $Response = .\link-tool.exe cmd nvs-get factory_pass -p $SerialPortWolfServiceCable | Out-String

    If ($LASTEXITCODE -ne 0) {
        Write-OutputWithTimestamp "Read password failed"
        Exit 1
    }

    $LinkPassword = $null

    if ($Response -match "^\s*(Successfully retrieved data \(12 display bytes\):.*\n)([A-Za-z0-9]{12})\s*$") {
        $LinkPassword = $Matches[2]
        Write-OutputWithTimestamp "Password: $LinkPassword"
    }
    else {
        Write-OutputWithTimestamp "Invalid password response: $Response"
        Exit 1
    }


    # Set test mode ssid
    Write-OutputWithTimestamp "Set test mode ssid"
    .\link-tool.exe cmd nvs-set test_mode_ssid "$TestModeSsid" -p $SerialPortWolfServiceCable

    If ($LASTEXITCODE -ne 0) {
        Write-OutputWithTimestamp "Failed to set test mode ssid"
        Exit 1
    }


    # Set test mode pass
    Write-OutputWithTimestamp "Set test mode password"
    .\link-tool.exe cmd nvs-set test_mode_pass "$TestModePassword" -p $SerialPortWolfServiceCable

    If ($LASTEXITCODE -ne 0) {
        Write-OutputWithTimestamp "Failed to set test mode password"
        Exit 1
    }


    # Enter test mode
    # In test mode the interface module
    # - Connects to the configured Wifi (if no Ethernet connection is available)
    # - Establish a internet connect to the Smartset portal server (portal-logon-request with attribute v2-test = "true")
    # - On success (internet connection) repeatedly prints a line containing string "PRODUCTION_TEST_SUCCESS"
    # - All LEDs will be turned on for the whole time
    # - No button presses will be handled
    # - Continous sends ebus telegrams
    # - DHCP for Wifi station mode interface
    # - Fixed network settings for Ethernet interface (************)
    # - Please note: As soon as the connection to the Smartset portal server has been successfully established, the interface module no longer attempts to send or receive data in test mode. This means that the interface module does not detect any connection interruptions and consequently does not attempt to re-establish the internet connection when switching from Wifi to Ethernet.
    Write-OutputWithTimestamp "Enter test mode"
    .\link-tool.exe cmd enter-test-mode -p $SerialPortWolfServiceCable

    If ($LASTEXITCODE -ne 0) {
        Write-OutputWithTimestamp "Failed to start test mode"
        Exit 1
    }


    # Wait until Link is fully restarted
    Write-OutputWithTimestamp "Wait until Link is fully restarted"
    Start-Sleep -Milliseconds $LinkStartUpTimeMs


    # Ebus
    # On production test bench Ebus data has to be read and checked for byte sequence 0xAA 0xFF 0x08 0x07 0x04
    Write-OutputWithTimestamp "Check Ebus. Successful if byte sequence 0xAA 0xFF 0x08 0x07 0x04 was received"


    # Check LEDs
    # On production test bench all 3 LEDs have to be checked regarding color, order and brightness.
    Write-OutputWithTimestamp "Check LEDs regarding color, order and brightness"


    # Check access point
    # On production test bench check if there is a Wifi network with ssid "WOLFLINK-{Last six digits of Link serial number}", e.g. WOLFLINK-05d08f"
    Write-OutputWithTimestamp "Check if there is a Wifi network with ssid WOLFLINK-{Last six digits of Link serial number}"


    # Wait until test mode was successful
    Write-OutputWithTimestamp "Wait until test mode was successful"
    .\link-tool.exe prod-test -p $SerialPortWolfServiceCable

    If ($LASTEXITCODE -ne 0) {
        Write-OutputWithTimestamp "Test mode failed. Link was not able to establish Wifi connection or internet connection. Check Wifi credentials and firewall rules"
        Exit 1
    }

    Write-OutputWithTimestamp "Test mode successfully established internet connection"


    # Plug in Ethernet cable
    $Response = read-host "Plug in the network cable and then press enter to continue or any other key (and then enter) to abort" # Only for testing purpose. On the production test bench proceed when the pneumatic network connector is plugged in.
    $Continue = ! [bool]$Response # If the user just presses enter, then the $Response will be empty. Powershell will convert an empty string to boolean false. Or you can just query for a particular character: $Aborted = $Continue -eq "a"
    
    If ($Continue -ne "True") {
        Write-OutputWithTimestamp "Aborted before Ethernet test."
        Exit 1
    }


    # Ping Ethernet
    # In test mode, the Ethernet interface of the interface module has fixed network settings (IP: ************, Subnet: *************). Wifi uses DHCP in test mode.
    # The computer of the production test bench must have a matching IP address (e.g. ************).
    Write-OutputWithTimestamp "Ping IP address ************"
    $Response = Test-Connection -Count 3 ************ -Quiet

    If ($Response -ne "True") {
        Write-OutputWithTimestamp "Ping IP address ************ failed"
        Exit 1
    }

    # Print labels
    # On production test bench print labels. Labeling and number of labels varies between Link home and Link pro
    Write-OutputWithTimestamp "Print label. Labeling and number of labels varies between Link home and Link pro"
    Write-OutputWithTimestamp "FW: $LinkVersion"
    Write-OutputWithTimestamp "SN: $LinkSerialNumber"
    Write-OutputWithTimestamp "PW: $LinkPassword"

    Write-OutputWithTimestamp "Link production process success"


    # Example console output:
    # [12:41:13.8117946] Start deployment
    # SN: a0a3b305d08f
    # PW: mMUTJE4RqsPm
    # [12/12] Success!
    # [12:42:26.4609615] Deployment successfully finished
    # [12:42:26.4619590] Wait until Link is fully restarted
    # [12:42:35.4645418] Read version
    # [12:42:35.7504655] Version: 4.50
    # [12:42:35.7514697] Read MAC (serial number)
    # [12:42:36.0331958] Serial number: a0a3b305d08f
    # [12:42:36.0341946] Read password
    # [12:42:36.3167991] Password: mMUTJE4RqsPm
    # [12:42:36.3177878] Set test mode ssid
    # Successfully set nvs entry "test_mode_ssid"
    # [12:42:36.5975336] Set test mode password
    # Successfully set nvs entry "test_mode_pass"
    # [12:42:36.8966535] Enter test mode
    # set test mode for next boot
    # Restarting...
    # [12:42:37.1759073] Wait until Link is fully restarted
    # [12:42:46.1873520] Check Ebus. Successful if byte sequence 0xAA 0xFF 0x08 0x07 0x04 was received
    # [12:42:46.1887082] Check LEDs regarding color, order and brightness
    # [12:42:46.1902150] Check if there is a Wifi network with ssid WOLFLINK-{Last six digits of Link serial number}
    # [12:42:46.1912144] Wait until test mode was successful
    # ........................................................................[12:43:03.4009854] Test mode successfully established internet connection
    # Plug in the network cable and then press enter to continue or any other key (and then enter) to abort:
    # [12:43:10.0284631] Ping IP address ************
    # [12:43:12.1942870] Print label. Labeling and number of labels varies between Link home and Link pro
    # [12:43:12.1952846] FW: 4.50
    # [12:43:12.1962830] SN: a0a3b305d08f
    # [12:43:12.1982770] PW: mMUTJE4RqsPm
    # [12:43:12.1992752] Link production process success
}
catch {
    Write-OutputWithTimestamp $_.Exception.Message
}
finally {
    Pop-Location
}

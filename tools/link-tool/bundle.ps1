param ([Parameter(Mandatory)] $Firmware)

# create directories
if (-Not (Test-Path target\zipped)) {
  New-Item -Name "target\zipped\config" -ItemType directory | Out-Null
  New-Item -Name "target\zipped\deps" -ItemType directory | Out-Null
  New-Item -Name "target\zipped\images" -ItemType directory | Out-Null
  New-Item -Name "target\zipped\partitions" -ItemType directory | Out-Null
  New-Item -Name "target\zipped\partitions\dev" -ItemType directory | Out-Null
  New-Item -Name "target\zipped\partitions\prod" -ItemType directory | Out-Null
  New-Item -Name "target\zipped\sec" -ItemType directory | Out-Null
  New-Item -Name "target\zipped\sec\keys" -ItemType directory | Out-Null
  New-Item -Name "target\zipped\sec\certs" -ItemType directory | Out-Null
  New-Item -Name "target\zipped\sec\certs\portal" -ItemType directory | Out-Null
  New-Item -Name "target\zipped\sec\certs\portal\pool" -ItemType directory | Out-Null
}

# compile link-tool
cargo build --release
Copy-Item -Path "..\..\target\release\link-tool.exe" -Destination "target\zipped\link-tool.exe"

Set-Location -Path target\zipped\deps

# download esptool
if (-Not (Test-Path "esptool")) {
  Invoke-WebRequest "https://github.com/espressif/esptool/releases/download/v4.7.0/esptool-v4.7.0-win64.zip" -OutFile esptool.zip
  Expand-Archive -Path esptool.zip -DestinationPath esptool
  Move-Item -Path "esptool\esptool-win64\esptool.exe"   -Destination esptool
  Move-Item -Path "esptool\esptool-win64\espefuse.exe"  -Destination esptool
  Move-Item -Path "esptool\esptool-win64\espsecure.exe" -Destination esptool
  Remove-Item -Path "esptool\esptool-win64" -Recurse -Force
  Remove-Item -Path esptool.zip
}

# download nvs partition generator tool
if (-Not (Test-Path "nvsgen")) {
  git clone "https://github.com/espressif/esp-idf-nvs-partition-gen"
  Set-Location "esp-idf-nvs-partition-gen\esp_idf_nvs_partition_gen"
  if (-Not (Get-Command pyinstaller -ErrorAction SilentlyContinue)) {
    python -m pip install pyinstaller
  }
  python -m pip install cryptography
  python -m PyInstaller -F .\nvs_partition_gen.py
  Set-Location ..\..
  New-Item -Name "nvsgen" -ItemType directory | Out-Null
  Copy-Item -Path "esp-idf-nvs-partition-gen\esp_idf_nvs_partition_gen\dist\nvs_partition_gen.exe" -Destination "nvsgen\nvsgen.exe"
  Remove-Item -Path "esp-idf-nvs-partition-gen" -Recurse -Force
}

Set-Location -Path ..\..\..

# copy production firmware
if ($Firmware -Ne "NONE") {
  Copy-Item -Path $Firmware -Destination "target\zipped\images\esp-firmware-prod.bin"
  Copy-Item -Path "..\..\firmware\partitions\prod\bootloader.bin" -Destination "target\zipped\partitions\prod\bootloader.bin"
  Copy-Item -Path "..\..\firmware\partitions\prod\partition-table.bin" -Destination "target\zipped\partitions\prod\partition-table.bin"
  Copy-Item -Path "..\..\firmware\partitions\dev\bootloader.bin" -Destination "target\zipped\partitions\dev\bootloader.bin"
  Copy-Item -Path "..\..\firmware\partitions\dev\partition-table.bin" -Destination "target\zipped\partitions\dev\partition-table.bin"
}

# copy other files
Copy-Item -Path "..\..\firmware\sec\keys\secure-boot-digest.bin" -Destination "target\zipped\sec\keys\secure-boot-digest.bin"
Copy-Item -Path "..\..\firmware\sec\keys\otw-signing-key.der"    -Destination "target\zipped\sec\keys\otw-signing-key.der"
Copy-Item -Path "..\..\firmware\sec\certs\portal\lucon-ca.der"   -Destination "target\zipped\sec\certs\portal\lucon-ca.der"
Copy-Item -Path "..\..\firmware\config\partitions.dev.csv"       -Destination "target\zipped\config\partitions.dev.csv"
Copy-Item -Path "..\..\firmware\config\partitions.prod.csv"      -Destination "target\zipped\config\partitions.prod.csv"

# create zip
$Metadata = cargo metadata --format-version 1 | ConvertFrom-Json
$Tooldata = $Metadata.packages | Where-Object -Property name -Eq "link-tool"
$Version = $Tooldata.version
$FirmwareVersionStr = ""
if ($Firmware -Ne "NONE") {
  $Filename = Split-Path $Firmware -Leaf
  $FirmwareVersion = $Filename.replace("esp-firmware-", "").replace("-prod.bin", "")
  $FirmwareVersionStr = "-fw-$FirmwareVersion"
}
if (Test-Path "target\link-tool-$Version$FirmwareVersionStr.zip") {
  Remove-Item -Path "target\link-tool-$Version$FirmwareVersionStr.zip" -Force
}
Compress-Archive -Path target\zipped\* -DestinationPath "target\link-tool-$Version$FirmwareVersionStr.zip"

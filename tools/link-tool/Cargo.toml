[package]
name = "link-tool"
version = "2.0.0"
edition = "2021"

[dependencies]
addr2line = "0.22.0"
anyhow = "1.0.75"
clap = { version = "4.4.8", features = ["derive", "wrap_help"] }
cobs2 = "0.1.4"
crossterm = "0.27.0"
ctrlc = "3.4.1"
defmt-decoder = "1.0.0"
defmt-parser = "1.0.0"
dialoguer = "0.11.0"
esp-idf-part = "0.5.0"
indicatif = "0.17.7"
lazy_static = "1.4.0"
rand = "0.8.5"
regex = "1.10.2"
rsa = { version = "0.9.6", features = ["sha2"] }
rustc-demangle = "0.1.23"
rustyline = { version = "13.0.0", features = ["derive"] }
serialport = "4.2.2"
thiserror = "2.0.12"

use std::{
    env,
    fmt::Write,
    fs,
    io::{self, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>},
    path::{Path, PathBuf},
    process::{Command, Output},
    time::{Duration, Instant},
};

use addr2line::{
    gimli::{EndianRcSlice, RunTimeEndian},
    object::{read::File, Object, ObjectSegment, ObjectSymbol},
    Context as FileContext, LookupResult,
};
use anyhow::{anyhow, bail, Context, Result};
use dialoguer::{theme::ColorfulTheme, Select};
use esp_idf_part::PartitionTable;
use rand::{
    distributions::{DistString, Distribution},
    Rng,
};
use serialport::{SerialPort, SerialPortType};

use crate::paths;

/* tools and dependencies *********************************************************************************************/

pub fn get_idf_path() -> Result<PathBuf> {
    env::var("IDF_PATH")
        .map(PathBuf::from)
        .context("esp-idf tools not exported")
}

pub fn get_python() -> Result<&'static str> {
    match Command::new("python").args(["--version"]).output() {
        Ok(s) if s.stdout.starts_with(b"Python 3.") => return Ok("python"),
        _ => (),
    }

    match Command::new("python3").args(["--version"]).output() {
        Ok(_) => Ok("python3"),
        _ => bail!("failed to get valid python executable"),
    }
}

pub struct ScriptPaths {
    pub tool: PathBuf,
    pub efuse: PathBuf,
    pub secure: PathBuf,
    pub nvsgen: PathBuf,
}

pub fn get_idf_scripts() -> Result<ScriptPaths> {
    let idf_path = get_idf_path()?;
    Ok(ScriptPaths {
        tool: get_rel_path(&idf_path, paths::IDF_ESPTOOL).context("esptool not found")?,
        efuse: get_rel_path(&idf_path, paths::IDF_ESPEFUSE).context("espefuse not found")?,
        secure: get_rel_path(&idf_path, paths::IDF_ESPSECURE).context("espsecure not found")?,
        nvsgen: get_rel_path(&idf_path, paths::IDF_NVSGEN).context("nvsgen not found")?,
    })
}

/* checks *************************************************************************************************************/

pub fn check_dependencies() -> Result<()> {
    match get_idf_path() {
        Ok(idf_path) => {
            let python = get_python().context("Could not find Python installation.")?;
            let esptool = get_rel_path(&idf_path, paths::IDF_ESPTOOL)
                .context("Could not find esp-idf toolchain")?;
            let mut cmd = Command::new(python);
            cmd.args([esptool.to_str().unwrap(), "version"]);
            check_cmd(cmd).map_err(|_| {
                anyhow!(
                    "Please export ESP-IDF tools and ensure that ESP-IDF's python is being used"
                )
            })
        }
        Err(_) => {
            match Path::new(paths::DEP_ESPTOOL).exists()
                && Path::new(paths::DEP_ESPEFUSE).exists()
                && Path::new(paths::DEP_ESPSECURE).exists()
                && Path::new(paths::DEP_NVSGEN).exists()
            {
                true => Ok(()),
                false => bail!("Bundled ESP-IDF tools not found"),
            }
        }
    }
}

pub fn check_firmware_compiled() -> Result<()> {
    let required = [
        Path::new(paths::APP_ELF_DEV),
        Path::new(paths::BOOTLOADER_BIN_DEV),
        Path::new(paths::PART_TABLE_BIN_DEV),
    ];

    for path in &required {
        if !path.is_file() {
            bail!("File {path:?} does not exist, please compile the firmware.");
        }
    }

    Ok(())
}

/* partition table ****************************************************************************************************/

pub fn get_part_table(prod: bool) -> Result<PartitionTable> {
    let (src_path, bin_path) = match prod {
        true => (paths::PART_TABLE_BIN_PROD, paths::IMG_PART_TABLE_PROD),
        false => (paths::PART_TABLE_BIN_DEV, paths::IMG_PART_TABLE_DEV),
    };

    if let Ok(bin) = fs::read(bin_path) {
        return PartitionTable::try_from_bytes(bin)
            .map_err(|e| anyhow!("invalid partition table: {e:?}"));
    }

    if let Ok(bin) = fs::read(src_path) {
        return PartitionTable::try_from_bytes(bin)
            .map_err(|e| anyhow!("invalid partition table: {e:?}"));
    }

    let csv = if prod {
        paths::PART_TABLE_CSV_PROD
    } else {
        paths::PART_TABLE_CSV_DEV
    };
    if let Ok(str) = fs::read_to_string(csv) {
        return PartitionTable::try_from_str(str)
            .map_err(|e| anyhow!("invalid partition table: {e:?}"));
    }

    bail!("Could not find partition table");
}

pub fn get_partition_table_offset(part_table: &PartitionTable) -> u32 {
    // this assumes that CONFIG_PARTITION_TABLE_OFFSET is equal to (first partition offset - 0x1000).
    let first_part = part_table
        .partitions()
        .iter()
        .min_by(|a, b| a.offset().cmp(&b.offset()))
        .unwrap();
    first_part.offset() - 0x1000
}

/* command execution **************************************************************************************************/

pub fn check_cmd(mut cmd: Command) -> Result<()> {
    check_cmd_output(
        cmd.output()?,
        cmd.get_program().to_str().unwrap(),
        &cmd.get_args()
            .map(|x| x.to_str().unwrap())
            .collect::<Vec<_>>(),
    )
}

pub fn check_cmd_output(output: Output, program: &str, args: &[&str]) -> Result<()> {
    match output.status.success() {
        true => Ok(()),
        false => bail!(
            "failed to execute command\n - command: {:?}\n - args: {:?}\n - stdout:\n{}\n - stderr:\n{}",
            program,
            args,
            String::from_utf8_lossy(&output.stdout),
            String::from_utf8_lossy(&output.stderr)
        ),
    }
}

/* serial communication ***********************************************************************************************/

pub fn serial_get_port() -> Result<String> {
    if let Ok(env_port) = std::env::var("ESPPORT") {
        return Ok(env_port);
    }

    let available_ports = serialport::available_ports()?;
    let mut available_ports = available_ports
        .into_iter()
        .filter(|x| {
            matches!(x.port_type, SerialPortType::UsbPort(_))
                && (!cfg!(target_os = "macos") || x.port_name.contains("/dev/cu."))
        })
        .collect::<Vec<_>>();
    if available_ports.is_empty() {
        bail!("No serial ports found!")
    }
    if available_ports.len() == 1 {
        println!("Using serial port {}...", available_ports[0].port_name);
        return Ok(available_ports[0].port_name.clone());
    }
    available_ports.sort_by(|a, b| a.port_name.cmp(&b.port_name));

    let mut selections = vec![];

    for p in &available_ports {
        let mut s = String::new();
        s.push_str(&p.port_name);

        if let SerialPortType::UsbPort(usb_port) = &p.port_type {
            if let Some(product) = &usb_port.product {
                s.push_str(" - ");
                s.push_str(product);
            }
        }

        selections.push(s);
    }

    selections.sort();

    let idx = Select::with_theme(&ColorfulTheme::default())
        .with_prompt("Select the UART port that should be used:")
        .default(1.min(selections.len() - 1))
        .items(&selections)
        .interact_opt()
        .unwrap();
    let Some(idx) = idx else {
        std::process::exit(130);
    };

    Ok(available_ports[idx].port_name.clone())
}

pub fn serial_read_line(
    serial: &mut Box<dyn SerialPort>,
    remainder: String,
    timeout: Option<Duration>,
) -> io::Result<(String, String)> {
    let mut res = remainder;
    let started = Instant::now();
    loop {
        if let Some(line_end) = res.find('\n') {
            let remainder = String::from(&res[(line_end + 1)..]);
            res.truncate(line_end + 1);
            return Ok((res, remainder));
        };
        let mut buf = [0; 1024];
        let count = match serial.read(&mut buf) {
            Ok(count) => count,
            Err(e) if e.kind() == ErrorKind::TimedOut => {
                if timeout.is_some() && started.elapsed() > timeout.unwrap() {
                    return Err(e);
                }
                0
            }
            Err(e) if e.kind() == ErrorKind::Interrupted => continue,
            e => e?,
        };
        let buf_str = String::from_utf8_lossy(&buf[..count]);
        res.push_str(&buf_str);
    }
}

// Ok(true) => legacy serial communiction
// Ok(false) => new serial communication
pub fn check_for_legacy_serial_communication(port: &str, baud: u32) -> Result<bool> {
    let mut serial = serialport::new(port, baud)
        .timeout(Duration::from_millis(100))
        .open()?;

    serial.write_all(b"version\n")?;

    let mut serial = BufReader::new(serial);
    let start = Instant::now();
    loop {
        let mut line = String::new();
        match serial.read_line(&mut line) {
            Ok(_) => {
                let line = line.trim();
                if let Some(cmd_output) = line.strip_prefix("[cmdline]")
                    && let Some(cmd_output) = cmd_output.strip_suffix("[/cmdline]")
                {
                    if cmd_output == "CMD_EXECUTION_START" {
                        continue;
                    }
                    println!("Detected legacy version ({cmd_output})");
                    return Ok(true);
                }
            }
            Err(e) if e.kind() == ErrorKind::TimedOut => {
                if start.elapsed() >= Duration::from_secs(5) {
                    println!("Assuming version with new serial communication protocol");
                    return Ok(false);
                }
            }
            Err(e) => Err(e)?,
        }
    }
}

/* miscellaneous ******************************************************************************************************/

pub fn get_rel_path(root: &Path, rel: &str) -> Option<PathBuf> {
    let mut root = root.to_path_buf();
    root.push(rel);
    match root.exists() {
        true => Some(root),
        false => None,
    }
}

pub fn get_version(binary: &[u8]) -> Result<&str> {
    // read version directly from binary, see https://docs.espressif.com/projects/esp-idf/en/latest/esp32/api-reference/system/app_image_format.html#application-description
    let version_str = std::str::from_utf8(&binary[48..80])?;
    let end = version_str.find('\0').unwrap_or(version_str.len());
    let version = &version_str[..end];
    Ok(version)
}

pub fn get_output_bin_with_version(app_bin: &str, output_template: &str) -> Result<String> {
    let mut file = fs::File::open(app_bin)?;
    let mut buf = [0; 80];
    file.read_exact(&mut buf)?;
    let version = get_version(&buf)?;
    let output = output_template.replace("{}", version);
    Ok(output)
}

pub fn create_dir_for_file<P: AsRef<Path>>(file: P) -> Result<()> {
    let dir = file
        .as_ref()
        .parent()
        .context("File has no parent directory")?;
    fs::create_dir_all(dir)?;
    Ok(())
}

pub fn to_temp_path(path: &str) -> String {
    path.replace("{}", std::env::temp_dir().to_str().unwrap())
}

/* password generation ************************************************************************************************/

struct PasswordDistribution;

impl Distribution<u8> for PasswordDistribution {
    fn sample<R: Rng + ?Sized>(&self, rng: &mut R) -> u8 {
        const CHARSET: &[u8] = b"ACEFGHJKLMNPQRTUVWXYabcdefghijkmnopqrstuvwxyz34679";
        const RANGE: u32 = CHARSET.len() as u32;
        loop {
            let var = rng.next_u32() >> (32 - 6);
            if var < RANGE {
                return CHARSET[var as usize];
            }
        }
    }
}

impl DistString for PasswordDistribution {
    fn append_string<R: Rng + ?Sized>(&self, rng: &mut R, string: &mut String, len: usize) {
        unsafe {
            let v = string.as_mut_vec();
            v.extend(self.sample_iter(rng).take(len));
        }
    }
}

pub fn generate_password() -> String {
    loop {
        let pass = PasswordDistribution.sample_string(&mut rand::thread_rng(), 12);
        if is_valid_password(&pass) {
            return pass;
        }
    }
}

pub fn is_valid_password(pass: &str) -> bool {
    let (mut lower, mut upper, mut special) = (false, false, false);
    for i in pass.chars() {
        match i {
            'a'..='z' => lower = true,
            'A'..='Z' => upper = true,
            ' '..='@' | '['..='`' | '{'..='~' => special = true, // includes '0'..='9'
            _ => return false,
        }
    }
    lower && upper && special
}

/* backtrace decoding *************************************************************************************************/

pub struct TraceDecoder<'a> {
    file: File<'a, &'a [u8]>,
    ctx: FileContext<EndianRcSlice<RunTimeEndian>>,
}

impl<'a> TraceDecoder<'a> {
    pub fn new(bytes: &'a [u8]) -> Result<Self> {
        let file = File::parse(bytes)?;
        let ctx = FileContext::new(&file)?;

        Ok(Self { file, ctx })
    }

    pub fn decode(&self, trace: &str) -> String {
        let addrs = trace
            .split(' ')
            .map(|x| x.split(':').next().unwrap().trim_start_matches("0x"))
            .filter_map(|x| u64::from_str_radix(x, 16).ok())
            .collect::<Vec<_>>();

        let mut res = String::new();
        for addr in &addrs {
            self.decode_addr(addr, &mut res);
        }
        res
    }

    fn decode_addr(&self, addr: &u64, res: &mut String) {
        if !self
            .file
            .segments()
            .any(|segment| (segment.address()..(segment.address() + segment.size())).contains(addr))
        {
            return;
        }

        let mut frames = match self.ctx.find_frames(*addr) {
            LookupResult::Output(res) => res.unwrap(),
            LookupResult::Load { .. } => unimplemented!(),
        };

        let function = match frames.next().ok().flatten() {
            Some(frame) => frame
                .function
                .and_then(|name| name.demangle().map(|s| s.into_owned()).ok()),
            None => {
                let sym = self
                    .file
                    .symbols()
                    .find(|sym| (sym.address()..=(sym.address() + sym.size())).contains(addr));

                if let Some(sym) = sym
                    && let Ok(name) = sym.name()
                    && !name.is_empty()
                {
                    Some(
                        addr2line::demangle_auto(std::borrow::Cow::Borrowed(name), None)
                            .to_string(),
                    )
                } else {
                    None
                }
            }
        };

        let loc = self.ctx.find_location(*addr).ok().flatten();

        write!(res, "\x1b[34m{addr:#08x}\x1b[0m ").unwrap();
        match function {
            Some(function) => write!(res, "\x1b[32m{function}()\x1b[0m").unwrap(),
            None => write!(res, "\x1b[32m<unknown function>\x1b[0m").unwrap(),
        };
        match loc {
            Some(loc) => {
                let file = match loc.file {
                    None => "?".into(),
                    Some(f) if f.len() > 100 => format!("...{}", &f[(f.len() - 100)..]),
                    Some(f) => f.into(),
                };
                write!(
                    res,
                    " at \x1b[33m{file}\x1b[0m:\x1b[33m{}\x1b[0m\r\n",
                    loc.line.map(|x| x.to_string()).unwrap_or("?".into())
                )
                .unwrap()
            }
            None => write!(res, "\r\n").unwrap(),
        };
    }
}

#![allow(clippy::explicit_write)]

use std::{
    borrow::Cow,
    fs,
    io::{self, Write},
    path::Path,
    process,
    time::Duration,
};

use anyhow::{Context, Result};
use crossterm::{
    cursor::{MoveTo, RestorePosition, SavePosition},
    event::{poll, read, Event, KeyCode, KeyEventKind, KeyModifiers},
    terminal::{EnterAlternateScreen, LeaveAlternateScreen},
};
use defmt_parser::Level;
use lazy_static::lazy_static;
use regex::Regex;
use rustyline::{
    error::ReadlineError, highlight::<PERSON>light<PERSON>, history::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Completer, Editor,
    Helper, Hinter, Validator,
};

use crate::{
    serial::{ExecuteCommandError, FrameError, LogMessage, SerialConnection},
    util::TraceDecoder,
};

#[derive(Default)]
pub struct MonitorOptions<'a> {
    pub port: &'a str,
    pub baud: u32,
    pub filter: Option<String>,
    pub elf_file: Option<&'a str>,
    pub raw: bool,
    pub colored: bool,
    pub no_reset: bool,
    pub dev: bool,
}

pub fn run(
    opts: MonitorOptions,
    trace_decoder: Option<TraceDecoder>,
    signing_key_file: impl AsRef<Path>,
) -> Result<()> {
    let raw_signing_key = fs::read(signing_key_file).context("failed to read signing key file")?;

    println!("Starting monitor...");

    let mut serial = SerialConnection::connect(
        opts.port,
        opts.baud,
        &raw_signing_key,
        !opts.no_reset,
        false,
        opts.dev,
    )
    .context("failed to connect to device")?;

    if !opts.dev {
        serial
            .enable_monitor()
            .context("failed to enable monitor")?;
    }

    println!(
        "Monitor started for port {}. Press <F1> for a list of available keybinds.",
        opts.port
    );

    if opts.colored {
        // enable ansi escape codes on windows
        #[cfg(windows)]
        let _ = crossterm::ansi_support::supports_ansi();
    }

    let _ = crossterm::terminal::enable_raw_mode();

    let mut printer = Printer::new(&opts, trace_decoder);
    let mut prev_was_malformed_frame = false;

    loop {
        let msg = serial.read_log_message();
        if !matches!(msg, Err(FrameError::MalformedFrame { .. })) && prev_was_malformed_frame {
            printer.push_raw("\r\n");
            prev_was_malformed_frame = false;
        }

        match msg {
            Ok(msg) => {
                let is_restart = msg.message.eq_ignore_ascii_case("Restarting...");
                if opts.raw {
                    if opts.filter.is_none() && msg.message.contains(opts.filter.as_ref().unwrap())
                    {
                        printer.push_raw(msg.message);
                    }
                } else {
                    printer.push(msg);
                }
                printer.flush();
                if is_restart && !opts.dev {
                    write!(std::io::stdout(), "Reinitiating connection...\r\n").unwrap();
                    if !opts.dev {
                        serial.reinitiate_connection()?;
                        serial
                            .enable_monitor()
                            .context("failed to enable monitor")?;
                    }
                    write!(std::io::stdout(), "Connection reestablished\r\n").unwrap();
                }
            }
            Err(FrameError::Timeout) => (),
            Err(FrameError::MalformedFrame { bytes }) => {
                if !prev_was_malformed_frame {
                    printer.push_raw(format!("\x1b[31mE\x1b[0m (monitor): malformed frame: "));
                    prev_was_malformed_frame = true;
                }
                printer.push_raw(&bytes);
                printer.flush();
            }
            Err(FrameError::MalformedLog) => printer.push_error("malformed log"),
            Err(e) => {
                printer.push_error(&e.to_string());
                printer.flush();
                let _ = crossterm::terminal::disable_raw_mode();
                return Ok(());
            }
        }

        if !poll(Duration::from_secs(0))? {
            continue;
        }

        let Event::Key(event) = read()? else {
            continue;
        };

        if event.kind != KeyEventKind::Press {
            continue;
        }

        match (event.modifiers, event.code) {
            (_, KeyCode::F(1)) => show_help(),
            (_, KeyCode::F(f)) if f == 2 || f == 3 => {
                if let Err(e) = open_cmd(f == 2, &mut serial) {
                    printer.push_raw(format!("\x1b[31mE\x1b[0m (cmdline): {e}\r\n\r\n"));
                    printer.flush();
                }
            }
            (KeyModifiers::CONTROL, KeyCode::Char('r')) => {
                write!(std::io::stdout(), "Restarting via EN pin...\r\n").unwrap();
                serial.reset()?;
                if !opts.dev {
                    write!(std::io::stdout(), "Reinitiating connection...\r\n").unwrap();
                    serial.reinitiate_connection()?;
                    serial
                        .enable_monitor()
                        .context("failed to enable monitor")?;
                    write!(std::io::stdout(), "Connection reestablished\r\n").unwrap();
                }
            }
            (KeyModifiers::CONTROL, KeyCode::Char('f')) => {
                write!(std::io::stdout(), "Reinitiating connection...\r\n").unwrap();
                serial.reinitiate_connection()?;
                if !opts.dev {
                    serial
                        .enable_monitor()
                        .context("failed to enable monitor")?;
                }
                write!(std::io::stdout(), "Connection reestablished\r\n").unwrap();
            }
            (KeyModifiers::CONTROL, KeyCode::Char('c')) => {
                let _ = crossterm::terminal::disable_raw_mode();
                process::exit(130);
            }
            _ => (),
        }
    }
}

fn open_cmd(single: bool, serial: &mut SerialConnection) -> Result<()> {
    let _ = crossterm::terminal::disable_raw_mode();
    if !single {
        let _ = crossterm::execute!(
            io::stdout(),
            SavePosition,
            EnterAlternateScreen,
            MoveTo(0, 0)
        );
    }

    let result = open_cmd_internal(single, serial);

    if !single {
        crossterm::execute!(io::stdout(), LeaveAlternateScreen, RestorePosition)?;
    }
    let _ = crossterm::terminal::enable_raw_mode();

    result
}

fn open_cmd_internal(single: bool, serial: &mut SerialConnection) -> Result<()> {
    let mut readline = CustomEditor::new()?;
    readline.set_helper(Some(PromptHighlighter {}));

    loop {
        println!();
        let input = match readline.readline("link > ") {
            Ok(input) => {
                readline.add_history_entry(&input)?;
                input
            }
            Err(ReadlineError::Interrupted | ReadlineError::Eof) => "quit".into(),
            Err(e) => Err(e)?,
        };
        let input = input.trim();
        if matches!(input, "quit" | "exit" | "!quit" | "!exit") {
            break;
        }

        // TODO: parse quotes in order to allow whitespace in arguments
        let mut input = input.split_ascii_whitespace();
        let Some(cmd) = input.next() else {
            continue;
        };
        let args = input.map(str::as_bytes).collect::<Vec<_>>();

        match serial.execute_command(false, cmd, &args) {
            Ok(resp) => println!("{}\r\n", resp.trim()),
            Err(ExecuteCommandError::ErrorFromDevice(e)) => {
                println!("\x1b[31mError:\x1b[0m {e}\r\n")
            }
            Err(e) => Err(e)?,
        }

        if single {
            break;
        }
    }

    Ok(())
}

fn show_help() {
    println!("-----------------------------------------------------");
    println!("Usage:");
    println!(" - F2:     open prompt for executing a single command");
    println!(" - F3:     open interactive command line");
    println!(" - Ctrl+R: reset device (requires rts connection)");
    println!(" - Ctrl+F: reinitiate connection after device restart");
    println!(" - Ctrl+C: terminate monitor");
    println!("-----------------------------------------------------");
}

lazy_static! {
    static ref COLOR_REGEX: Regex = Regex::new("\x1b\\[[0-9;]*m").unwrap();
}

struct Printer<'a> {
    opts: &'a MonitorOptions<'a>,
    trace_decoder: Option<TraceDecoder<'a>>,
    buffer: String,
}

impl<'a> Printer<'a> {
    fn new(opts: &'a MonitorOptions<'a>, trace_decoder: Option<TraceDecoder<'a>>) -> Self {
        Printer {
            opts,
            trace_decoder,
            buffer: String::new(),
        }
    }

    fn push(&mut self, msg: LogMessage) {
        if msg.message.starts_with("Backtrace: ") && self.trace_decoder.is_some() {
            self.push_backtrace(&msg.message);
            return;
        }

        if let Some(filter) = &self.opts.filter
            && !msg.message.contains(filter)
        {
            return;
        }

        if let Some(level) = &msg.level {
            self.push_raw(match level {
                Level::Trace => "\x1b[35mTRACE\x1b[0m ",
                Level::Debug => "\x1b[34mDEBUG\x1b[0m ",
                Level::Info => "\x1b[32mINFO \x1b[0m ",
                Level::Warn => "\x1b[33mWARN \x1b[0m ",
                Level::Error => "\x1b[31mERROR\x1b[0m ",
            });
        }

        if let Some(timestamp) = &msg.timestamp {
            self.push_raw("\x1b[90m");
            for _ in 0..(10 - timestamp.len()) {
                self.push_raw(" ");
            }
            self.push_raw(timestamp);
            if let Some(target) = &msg.target {
                self.push_raw(" [");
                self.push_raw(target);
                self.push_raw("]");
            };
            self.push_raw("\x1b[0m ");
        }

        let msg_highlight = match &msg.level {
            Some(Level::Trace) => "\x1b[90m",
            Some(Level::Debug) => "\x1b[2m",
            Some(Level::Info) => "",
            Some(Level::Warn) => "\x1b[33m",
            Some(Level::Error) => "\x1b[31m",
            None => "",
        };
        self.push_raw(msg_highlight);
        self.push_raw(&msg.message);
        self.push_raw("\x1b[0m\r\n");
    }

    fn push_error(&mut self, err: &str) {
        self.push_raw(format!("\x1b[31mE\x1b[0m (monitor): {err}\r\n"));
    }

    fn push_backtrace<S: AsRef<str>>(&mut self, line: S) {
        let trace = line.as_ref().trim_start_matches("Backtrace: ").trim();
        let parsed = self.trace_decoder.as_ref().unwrap().decode(trace);
        self.push_raw(&parsed);
        self.push_raw("\r\n");
    }

    fn push_raw<S: AsRef<str>>(&mut self, line: S) {
        let line = line.as_ref();
        if self.opts.colored {
            self.buffer.push_str(line);
        } else {
            self.buffer.push_str(&COLOR_REGEX.replace_all(line, ""));
        }
    }

    fn flush(&mut self) {
        write!(std::io::stdout(), "{}", self.buffer).unwrap();
        self.buffer.clear();
    }
}

#[derive(Completer, Helper, Hinter, Validator)]
struct PromptHighlighter {}

impl Highlighter for PromptHighlighter {
    fn highlight_prompt<'b, 's: 'b, 'p: 'b>(
        &'s self,
        prompt: &'p str,
        default: bool,
    ) -> Cow<'b, str> {
        if default {
            Cow::Owned(format!("\x1b[36m{prompt}\x1b[0m"))
        } else {
            Cow::Borrowed(prompt)
        }
    }
}

type CustomEditor = Editor<PromptHighlighter, DefaultHistory>;

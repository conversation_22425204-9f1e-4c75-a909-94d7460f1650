use clap::{Args, Parser, Subcommand};

use crate::paths;

/* cli ****************************************************************************************************************/

#[derive(Parser)]
#[command(version, about, long_about = None)]
#[command(propagate_version = true)]
pub struct Cli {
    #[command(subcommand)]
    pub command: Commands,

    #[clap(flatten)]
    pub default_args: DefaultArgs,
}

#[derive(Args)]
pub struct DefaultArgs {
    /// The directory of the firmware (defaults to current directory). The tool will perform all
    /// actions relative to that directory
    #[arg(long, default_value_t = String::from("."))]
    pub firmware_dir: String,
}

/* subcommands ********************************************************************************************************/

#[derive(Subcommand)]
pub enum Commands {
    /// Open the interactive command line or execute a single command and print its output
    Cmd(CommandArgs),

    /// Generate an image for OTA updates
    #[command(visible_alias("c"))]
    Create(CreateArgs),

    /// Flash the device completely, including NVS. When production mode is selected, the relevant
    /// security measures will be activated and eFuses will be burned
    #[command(visible_alias("d"))]
    Deploy(DeployArgs),

    /// Erase the entire flash or certain partitions of the device
    Erase(EraseArgs),

    /// Flash the application image to the device (for development). Does not include NVS.
    #[command(visible_alias("f"))]
    Flash(FlashArgs),

    /// Start a serial monitor
    #[command(visible_alias("m"))]
    Monitor(MonitorArgs),

    /// Validate internet connection in test mode
    ProdTest(ProdTestArgs),

    /// Read the Ethernet MAC address (= serial number) of the device
    ReadMac(ReadMacArgs),

    /// Read the version from a binary application image
    ReadVersion(ReadVersionArgs),

    /// Reset the device using EN pin
    Reset(ResetArgs),

    /// Decode a stack trace
    TraceDecode(TraceDecodeArgs),

    /// Perform an OTW update
    #[command(visible_alias("u"))]
    Update(UpdateArgs),
}

/* subcommand arguments ***********************************************************************************************/

#[derive(Args)]
pub struct CommandArgs {
    #[clap(flatten)]
    pub connect_args: ConnectArgs,

    /// Specifies the command that should be executed. The output of this
    /// command will be printed. When multiple commands are specified, they will
    /// be concatenated with a space as a delimiter in order to form one big command
    pub command: Vec<String>,

    /// The path to the commuication signing key
    #[arg(short, default_value = paths::SERIAL_SIGNING_KEY)]
    pub signing_key: String,
}

#[derive(Args)]
pub struct CreateArgs {
    /// The path to the output file (.bin). When no path is specified, the image will be
    /// placed in `images/`, with the firmware version appended to the filename
    #[arg(short, long)]
    pub output: Option<String>,

    /// Specifies that a production image should be generated
    #[arg(long)]
    pub prod: bool,
}

#[derive(Args)]
pub struct DeployArgs {
    #[clap(flatten)]
    pub connect_args: ConnectArgs,

    /// Enables security features and burns eFuses. For production only
    #[arg(long)]
    pub prod: bool,

    /// Start a serial monitor after flashing completed
    #[arg(short, long)]
    pub monitor: bool,

    /// Specifies the (case-insensitive) filter for the monitor
    #[arg(long, requires = "monitor")]
    pub monitor_filter: Option<String>,

    /// Do not apply formatting to monitor output
    #[arg(long, requires = "monitor")]
    pub monitor_raw: bool,

    /// Do not auto-reset after flashing completed
    #[arg(long, short = 'r', requires = "monitor")]
    pub no_reset: bool,

    /// Perform flashing of partitions and burning of eFuses as single steps instead of in bulk.
    /// Can be useful for debugging, but drastically increases the time required for deploying the
    /// firmware
    #[arg(long)]
    pub step: bool,

    /// Path to the portal client certificate. Specify AUTO in order to automatically take a
    /// certificate and key from the certificate pool. When deploying in prod mode, this defaults
    /// to AUTO, as custom certificate locations are not allowed.
    #[arg(short, long, default_value_t = String::from(paths::PORTAL_DEV_CERT))]
    pub cert: String,

    /// Path to the portal private key
    #[arg(short, long, default_value_t = String::from(paths::PORTAL_DEV_KEY))]
    pub key: String,

    /// Path to the portal certificate pool
    #[arg(long, default_value_t = String::from(paths::PORTAL_CERT_POOL_DEFAULT))]
    pub cert_pool_path: String,

    /// The factory password. When omitted, a password will be generated (and printed)
    #[arg(short = 'w', long)]
    pub password: Option<String>,

    #[arg(short = 'A', long)]
    pub after_deploy: Option<String>,

    /// Path to the (signed) esp-firmware.bin file
    pub bin: String,

    /// The path to the commuication signing key
    #[arg(short, default_value = paths::SERIAL_SIGNING_KEY)]
    pub signing_key: String,
}

#[derive(Args)]
pub struct EraseArgs {
    #[clap(flatten)]
    pub connect_args: ConnectArgs,

    /// The list of partitions to erase. If none are specified, the entire flash will be erased
    pub partitions: Vec<String>,
}

#[derive(Args)]
pub struct FlashArgs {
    #[clap(flatten)]
    pub connect_args: ConnectArgs,

    /// Start a serial monitor after flashing completed
    #[arg(short, long)]
    pub monitor: bool,

    /// Specifies the (case-insensitive) filter for the monitor
    #[arg(long, requires = "monitor")]
    pub monitor_filter: Option<String>,

    /// Do not apply formatting to monitor output
    #[arg(long, requires = "monitor")]
    pub monitor_raw: bool,

    /// Do not auto-reset after flashing completed
    #[arg(long, short = 'r', requires = "monitor")]
    pub no_reset: bool,

    /// When specified, bootloader and partition table will also be flashed
    #[arg(short, long)]
    pub all: bool,

    /// Use OTW updates for flashing firmware
    #[arg(short, long)]
    pub otw: bool,

    /// Not used, but passed by `cargo run`
    #[arg(hide = true)]
    pub firmware_path: Option<String>,

    /// The path to the commuication signing key
    #[arg(short, default_value = paths::SERIAL_SIGNING_KEY)]
    pub signing_key: String,
}

#[derive(Args)]
pub struct MonitorArgs {
    #[clap(flatten)]
    pub connect_args: ConnectArgs,

    /// When specified, only lines that match the (case-insensitive) filter will be displayed
    #[arg(short, long)]
    pub filter: Option<String>,

    /// When specified, esp-idf's built-in monitor will be used instead of the custom one
    #[arg(long)]
    pub legacy: bool,

    /// Do not apply formatting to monitor output
    #[arg(long)]
    pub raw: bool,

    /// Display log without colourful formatting
    #[arg(long)]
    pub no_color: bool,

    /// The path to the ELF file of the currently active image. Used for decoding backtraces
    #[arg(long)]
    pub elf_file: Option<String>,

    /// Do not reset the device when starting the monitor
    #[arg(long, short = 'r')]
    pub no_reset: bool,

    /// The path to the commuication signing key
    #[arg(short, default_value = paths::SERIAL_SIGNING_KEY)]
    pub signing_key: String,

    /// When specified, the monitor will be run in "dev mode", i.e. there will be no attempt to enable the log output
    /// when starting the monitor, which facilitates development. Only works with dev firmware
    #[arg(long)]
    pub dev: bool,
}

#[derive(Args)]
pub struct ProdTestArgs {
    #[clap(flatten)]
    pub connect_args: ConnectArgs,

    /// The path to the commuication signing key
    #[arg(short, default_value = paths::SERIAL_SIGNING_KEY)]
    pub signing_key: String,
}

#[derive(Args)]
pub struct ReadMacArgs {
    #[clap(flatten)]
    pub connect_args: ConnectArgs,
}

#[derive(Args)]
pub struct ReadVersionArgs {
    pub path: String,
}

#[derive(Args)]
pub struct ResetArgs {
    #[clap(flatten)]
    pub connect_args: ConnectArgs,
}

#[derive(Args)]
pub struct TraceDecodeArgs {
    /// The stack trace to decode
    pub trace: String,

    /// The path to the ELF file
    #[arg(long, short = 'f', default_value_t = String::from(paths::APP_ELF_DEV))]
    pub elf_file: String,
}

#[derive(Args)]
pub struct UpdateArgs {
    #[clap(flatten)]
    pub connect_args: ConnectArgs,

    /// The path to the update image
    pub image_path: String,

    /// The path to the commuication signing key
    #[arg(short, default_value = paths::SERIAL_SIGNING_KEY)]
    pub signing_key: String,

    /// Show debug messages (UART output of device)
    #[arg(long)]
    pub debug: bool,

    /// OTA Protocol version. Use `AUTO` for automatic detection based on the
    /// connected device's firmware (this is the default behaviour). When `AUTO`
    /// does not work, use `0` when the device to be updated is currently
    /// running on firmware version 4.50.1 or older, or use `1` for devices
    /// running firmware version 4.50.2 or newer
    #[arg(long, default_value_t = String::from("AUTO"))]
    pub protocol: String,
}

/* reusable argument components ***************************************************************************************/

#[derive(Args)]
pub struct ConnectArgs {
    /// The port used for flashing. When this flag is omitted and only one port is available, this
    /// port will be used. If multiple ports are available, a selection dialog will be displayed
    #[arg(short, long)]
    pub port: Option<String>,

    /// The baudrate for flashing
    #[arg(short, long, default_value_t = 921600)]
    pub baud: usize,
}

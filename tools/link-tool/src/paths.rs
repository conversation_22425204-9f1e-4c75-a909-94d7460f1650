// compiled binary locations
pub const APP_ELF_PROD: &str = "target/xtensa-esp32-espidf/release/esp-firmware";
pub const BOOTLOADER_BIN_PROD: &str = "target/xtensa-esp32-espidf/release/bootloader.bin";
pub const PART_TABLE_BIN_PROD: &str = "target/xtensa-esp32-espidf/release/partition-table.bin";
pub const APP_ELF_DEV: &str = "target/xtensa-esp32-espidf/debug/esp-firmware";
pub const BOOTLOADER_BIN_DEV: &str = "target/xtensa-esp32-espidf/debug/bootloader.bin";
pub const PART_TABLE_BIN_DEV: &str = "target/xtensa-esp32-espidf/debug/partition-table.bin";

// build artifacts
pub const APP_BIN_DEV: &str = "{}/link-tool-build-esp-firmware-dev.bin";
pub const APP_BIN_PROD: &str = "{}/link-tool-build-esp-firmware-prod.bin";
pub const APP_ENC: &str = "{}/link-tool-build-esp-firmware-enc.bin";
pub const BOOTLOADER_ENC: &str = "{}/link-tool-build-bootloader-enc.bin";
pub const PART_TABLE_ENC: &str = "{}/link-tool-build-partition-table-enc.bin";
pub const NVS_KEY_BIN: &str = "{}/keys/nvs-key.bin";
pub const NVS_KEY_ENC: &str = "{}/link-tool-build-nvs-key-enc.bin";
pub const NVS_BIN: &str = "{}/link-tool-build-nvs-data.bin";
pub const NVS_CSV: &str = "{}/link-tool-build-nvs-data.csv";
pub const FLASH_ENC_KEY: &str = "{}/link-tool-build-flash-encryption-key.bin";

// images
pub const IMG_BOOTLOADER_DEV: &str = "partitions/dev/bootloader.bin";
pub const IMG_PART_TABLE_DEV: &str = "partitions/dev/partition-table.bin";
pub const IMG_APP_DEV: &str = "images/esp-firmware-{}-dev.bin";
pub const IMG_BOOTLOADER_PROD: &str = "partitions/prod/bootloader.bin";
pub const IMG_PART_TABLE_PROD: &str = "partitions/prod/partition-table.bin";
pub const IMG_APP_PROD: &str = "images/esp-firmware-{}-prod.bin";

// config
pub const PART_TABLE_CSV_DEV: &str = "config/partitions.dev.csv";
pub const PART_TABLE_CSV_PROD: &str = "config/partitions.prod.csv";

// security (keys and certs)
pub const PORTAL_DEV_CERT: &str = "sec/certs/portal/wolflink-dev-cert.der";
pub const PORTAL_DEV_KEY: &str = "sec/certs/portal/wolflink-dev-key.der";
pub const PORTAL_CA: &str = "sec/certs/portal/lucon-ca.der";
pub const PORTAL_CERT_POOL_DEFAULT: &str = "sec/certs/portal/pool/";
pub const SERIAL_SIGNING_KEY: &str = "sec/keys/otw-signing-key.der";
pub const SEC_BOOT_KEY: &str = "sec/keys/secure-boot-key.pem";
pub const SEC_BOOT_DIGEST: &str = "sec/keys/secure-boot-digest.bin";

// esp tools (from esp-idf)
pub const IDF_ESPTOOL: &str = "components/esptool_py/esptool/esptool.py";
pub const IDF_ESPSECURE: &str = "components/esptool_py/esptool/espsecure.py";
pub const IDF_ESPEFUSE: &str = "components/esptool_py/esptool/espefuse.py";
pub const IDF_NVSGEN: &str = "components/nvs_flash/nvs_partition_generator/nvs_partition_gen.py";

// esp tools (from bundled deps folder)
pub const DEP_ESPTOOL: &str = "deps/esptool/esptool.exe";
pub const DEP_ESPSECURE: &str = "deps/esptool/espsecure.exe";
pub const DEP_ESPEFUSE: &str = "deps/esptool/espefuse.exe";
pub const DEP_NVSGEN: &str = "deps/nvsgen/nvsgen.exe";

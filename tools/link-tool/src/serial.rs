use std::{
    collections::VecDeque,
    fs,
    io::{self, <PERSON><PERSON>r<PERSON><PERSON>},
    thread,
    time::{Duration, Instant},
};

use anyhow::{bail, Context, Result};
use defmt_decoder::{Locations, Table};
use defmt_parser::Level;
use rsa::{
    pkcs1::DecodeRsaPrivateKey,
    pkcs1v15::SigningKey,
    sha2::Sha256,
    signature::{SignatureEncoding, Signer},
    RsaPrivateKey,
};
use serialport::{ClearBuffer, SerialPort};
use thiserror::Error;

pub struct SerialConnection {
    serial: Box<dyn SerialPort>,
    decoder: Table,
    locations: Locations,
    frames: VecDeque<Result<Frame, FrameError>>,
    bytebuf: Vec<u8>,
    log_message_queue: Option<VecDeque<LogMessage>>,
    signing_key: SigningKey<Sha256>,
    challenge_code: Option<[u8; 16]>,
    cmd_id: u8,
}

impl SerialConnection {
    pub fn connect(
        port: &str,
        baud: u32,
        raw_signing_key: &[u8],
        reset_before_connect: bool,
        ignore_log_messages_when_executing_command: bool,
        dev: bool,
    ) -> Result<Self> {
        let private_key = RsaPrivateKey::from_pkcs1_der(raw_signing_key)
            .context("failed to parse signing key")?;
        let signing_key = SigningKey::<Sha256>::new(private_key);

        let mut serial = serialport::new(port, baud)
            .timeout(Duration::from_millis(100))
            .open()
            .context("failed to open serial port")?;

        if reset_before_connect {
            serial.write_data_terminal_ready(false)?;
            serial.write_request_to_send(true)?;
            thread::sleep(Duration::from_millis(100));
            serial.write_request_to_send(false)?;
        }

        let elf = fs::read("target/xtensa-esp32-espidf/debug/esp-firmware").unwrap();
        let decoder = Table::parse(&elf).unwrap().unwrap();
        let locations = decoder.get_locations(&elf).unwrap();

        let log_message_queue = match ignore_log_messages_when_executing_command {
            true => None,
            false => Some(VecDeque::new()),
        };

        let mut this = Self {
            serial,
            decoder,
            locations,
            frames: VecDeque::new(),
            bytebuf: Vec::new(),
            log_message_queue,
            signing_key,
            challenge_code: None,
            cmd_id: b'~',
        };

        if !dev {
            this.initiate_communication()
                .context("failed to initiate secure communication")?;
        }

        Ok(this)
    }

    pub fn read_log_message(&mut self) -> Result<LogMessage, FrameError> {
        loop {
            if let Some(frame) = self.frames.pop_front() {
                match frame {
                    Ok(Frame::Log(log)) => return Ok(log),
                    Ok(_) => continue,
                    Err(e) => return Err(e),
                }
            }
            self.read();
        }
    }

    pub fn execute_command(
        &mut self,
        is_otw: bool,
        cmd: &str,
        args: &[&[u8]],
    ) -> Result<String, ExecuteCommandError> {
        const COMMAND_TIMEOUT: Duration = Duration::from_secs(5);
        self.execute_command_with_timeout(is_otw, cmd, args, COMMAND_TIMEOUT)
    }

    pub fn execute_command_with_timeout(
        &mut self,
        is_otw: bool,
        cmd: &str,
        args: &[&[u8]],
        timeout: Duration,
    ) -> Result<String, ExecuteCommandError> {
        let mut command_was_retried = false;
        'retry_command_exeuction: while !command_was_retried {
            self.write(is_otw, cmd, args)
                .map_err(ExecuteCommandError::WriteError)?;

            let start = Instant::now();
            let resp = 'read: loop {
                if let Some(frame) = self.frames.pop_front() {
                    match frame {
                        Ok(Frame::Log(msg)) => {
                            if let Some(ref mut queue) = self.log_message_queue {
                                queue.push_back(msg);
                            }
                            continue 'read;
                        }
                        Ok(Frame::CmdResp(resp)) if !is_otw => break 'read resp,
                        Ok(Frame::OtaResp(resp)) if is_otw => break 'read resp,
                        Ok(Frame::CmdResp(_) | Frame::OtaResp(_)) => continue 'read,
                        Err(FrameError::MalformedFrame { .. }) => continue 'retry_command_exeuction,
                        Err(FrameError::MalformedCmdResp) => continue 'retry_command_exeuction,
                        Err(FrameError::RecvBadCobsrFrame) => continue 'retry_command_exeuction,
                        Err(FrameError::MalformedLog) => continue 'read,
                        Err(FrameError::Timeout) if start.elapsed() < timeout => continue 'read,
                        Err(FrameError::Timeout) => return Err(ExecuteCommandError::Timeout),
                        Err(FrameError::ReadError(e)) => {
                            return Err(ExecuteCommandError::ReadError(e))
                        }
                    }
                }
                self.read();
            };

            match resp {
                CmdOtaResp::Ok(cmd_id, _)
                | CmdOtaResp::Err(cmd_id, _)
                | CmdOtaResp::VerificationFailed(cmd_id)
                    if cmd_id != self.cmd_id =>
                {
                    continue 'retry_command_exeuction
                }
                CmdOtaResp::Ok(_, msg) => return Ok(msg),
                CmdOtaResp::Err(_, err) => return Err(ExecuteCommandError::ErrorFromDevice(err)),
                CmdOtaResp::VerificationFailed(_) => {
                    if command_was_retried {
                        break 'retry_command_exeuction;
                    } else {
                        command_was_retried = true;
                        continue 'retry_command_exeuction;
                    }
                }
            }
        }

        Err(ExecuteCommandError::VerificationFailed)
    }

    pub fn enable_monitor(&mut self) -> Result<()> {
        //self.execute_command(false, "log-level", &[b"*", b"info"])?;
        Ok(())
    }

    pub fn reset(&mut self) -> Result<()> {
        self.serial.write_data_terminal_ready(false)?;
        self.serial.write_request_to_send(true)?;
        thread::sleep(Duration::from_millis(100));
        self.serial.write_request_to_send(false)?;
        Ok(())
    }

    pub fn reinitiate_connection(&mut self) -> Result<()> {
        self.initiate_communication()
    }

    fn read(&mut self) {
        let mut buf = [0; 1024];
        let len = match self.serial.read(&mut buf) {
            Ok(len) => len,
            Err(e) => {
                self.frames.push_back(Err(match e.kind() {
                    ErrorKind::TimedOut => FrameError::Timeout,
                    _ => FrameError::ReadError(e),
                }));
                return;
            }
        };

        let mut out = Vec::new();
        for &c in &buf[..len] {
            if c == 0 {
                let out_len = cobs2::cobsr::decode_max_output_size(self.bytebuf.len());
                out.resize(out_len, 0);
                let frame = match cobs2::cobsr::decode_array(&mut out[..], &self.bytebuf) {
                    Ok(frame) => self.decode_frame(frame),
                    Err(_) => {
                        let bytes = String::from_utf8_lossy(&self.bytebuf).into_owned();
                        Err(FrameError::MalformedFrame { bytes })
                    }
                };
                self.frames.push_back(frame);
                self.bytebuf.clear();
            } else {
                self.bytebuf.push(c);
            }
        }
    }

    fn decode_frame(&mut self, frame: &[u8]) -> Result<Frame, FrameError> {
        match frame.get(0).copied() {
            Some(IDENT_LOG) => self.decode_log(&frame[1..]).map(Frame::Log),
            Some(IDENT_CMD) => self.decode_cmd(&frame[1..]).map(Frame::CmdResp),
            Some(IDENT_OTW) => self.decode_cmd(&frame[1..]).map(Frame::OtaResp),
            Some(IDENT_BAD_COBSR) => Err(FrameError::RecvBadCobsrFrame),
            _ => {
                let bytes = String::from_utf8_lossy(&self.bytebuf).into_owned();
                Err(FrameError::MalformedFrame { bytes })
            }
        }
    }

    fn decode_log(&mut self, frame: &[u8]) -> Result<LogMessage, FrameError> {
        self.decoder
            .decode(frame)
            .map(|(f, _)| LogMessage {
                level: f.level(),
                timestamp: f.display_timestamp().map(|t| t.to_string()),
                message: f.display_message().to_string(),
                target: self.get_log_module(&f),
            })
            .map_err(|_| FrameError::MalformedLog)
    }

    fn get_log_module(&self, frame: &defmt_decoder::Frame<'_>) -> Option<String> {
        match self.locations.get(&frame.index()) {
            Some(location) => {
                let file = location.file.to_str().unwrap();
                let target = match file.rsplit_once("link-redesign/firmware/src/") {
                    Some((_, f)) => f.trim_end_matches(".rs").to_owned(),
                    None => String::from("?"),
                };
                Some(target)
            }
            None => None,
        }
    }

    fn decode_cmd(&mut self, frame: &[u8]) -> Result<CmdOtaResp, FrameError> {
        if frame.len() < 18 {
            return Err(FrameError::MalformedCmdResp);
        }
        let cmd_id = frame[0];
        let challenge = &frame[1..][..16];
        self.challenge_code = Some(challenge.try_into().unwrap());
        let status_code = frame[17];
        let msg = &frame[18..];
        match status_code {
            b'O' => Ok(CmdOtaResp::Ok(
                cmd_id,
                String::from_utf8_lossy(&msg).into_owned(),
            )),
            b'E' => Ok(CmdOtaResp::Err(
                cmd_id,
                String::from_utf8_lossy(&msg).into_owned(),
            )),
            b'V' => Ok(CmdOtaResp::VerificationFailed(cmd_id)),
            _ => Err(FrameError::MalformedCmdResp),
        }
    }

    fn write(&mut self, is_otw: bool, cmd: &str, args: &[&[u8]]) -> Result<()> {
        let mut raw_msg = Vec::new();
        raw_msg.push(if is_otw { IDENT_OTW } else { IDENT_CMD });

        let id = self.next_cmd_id();
        raw_msg.push(id);

        let signature = self.get_challenge_code_signature()?;
        raw_msg.extend_from_slice(&signature);

        let cmd_len = cmd.len().try_into().context("too long command")?;
        raw_msg.push(cmd_len);
        raw_msg.extend_from_slice(cmd.as_bytes());

        let arg_count = args.len().try_into().context("too many args")?;
        raw_msg.push(arg_count);
        for arg in args {
            let arg_len: u16 = arg.len().try_into().context("too long arg")?;
            let arg_len_bytes = arg_len.to_be_bytes();
            raw_msg.extend_from_slice(&arg_len_bytes);
            raw_msg.extend_from_slice(arg);
        }

        let mut encoded = cobs2::cobsr::encode_vector(&raw_msg).unwrap();
        encoded.push(0);

        // TODO: debug messages
        self.serial.clear(ClearBuffer::Input)?;
        self.serial.write_all(&encoded)?;
        Ok(())
    }

    fn next_cmd_id(&mut self) -> u8 {
        // ensures that cmd_id is in ascii range (b' ' .. b'}')
        self.cmd_id = ((self.cmd_id - 32 + 1) % 94) + 32;
        self.cmd_id
    }

    fn get_challenge_code_signature(&mut self) -> Result<Vec<u8>> {
        if self.challenge_code.is_none() {
            self.reinitiate_connection()?;
        }
        let challenge_code = self.challenge_code.as_ref().unwrap();
        let vec = self.signing_key.sign(challenge_code).to_vec();
        assert!(vec.len() == 256, "got incorrect challenge code signature");
        Ok(vec)
    }

    fn initiate_communication(&mut self) -> Result<()> {
        #[rustfmt::skip]
        const INIT_MSG: &[u8] = &[
            /* Target Identifier       */ IDENT_CMD,
            /* ID                      */ b'~',
            /* Challenge Code Response */ 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            /* Command Length          */ 4,
            /* Command                 */ b'i', b'n', b'i', b't',
            /* ArgumentCount           */ 0
        ];

        const RETRY_TIMEOUT: Duration = Duration::from_secs(1);
        const CONNECTION_TIMEOUT: Duration = Duration::from_secs(15);

        self.serial.clear(ClearBuffer::Input)?;
        thread::sleep(Duration::from_millis(50));
        let start = Instant::now();
        let mut init_encoded = cobs2::cobsr::encode_vector(INIT_MSG).unwrap();
        init_encoded.push(0);

        'write_attempt: while start.elapsed() < CONNECTION_TIMEOUT {
            self.serial.write_all(&init_encoded)?;
            let start = Instant::now();
            'read: loop {
                if let Some(frame) = self.frames.pop_front() {
                    match frame {
                        Ok(Frame::CmdResp(CmdOtaResp::Ok(b'~', _))) => return Ok(()),
                        Ok(_) => continue 'read,
                        Err(FrameError::Timeout) if start.elapsed() > RETRY_TIMEOUT => {
                            continue 'write_attempt
                        }
                        Err(FrameError::Timeout) => continue 'read,
                        Err(FrameError::MalformedLog) => continue 'read,
                        Err(FrameError::MalformedFrame { .. }) => continue 'read,
                        Err(FrameError::MalformedCmdResp) => continue 'read,
                        Err(e) => Err(e)?,
                    }
                }
                self.read();
            }
        }

        bail!("timeout")
    }
}

const IDENT_LOG: u8 = 0x2A;
const IDENT_CMD: u8 = 0x45;
const IDENT_OTW: u8 = 0x89;
const IDENT_BAD_COBSR: u8 = 0xF6;

#[derive(Debug)]
enum Frame {
    Log(LogMessage),
    CmdResp(CmdOtaResp),
    OtaResp(CmdOtaResp),
}

#[derive(Debug)]
enum CmdOtaResp {
    Ok(CmdId, String),
    Err(CmdId, String),
    VerificationFailed(CmdId),
}

#[derive(Debug)]
pub struct LogMessage {
    pub level: Option<Level>,
    pub timestamp: Option<String>,
    pub message: String,
    pub target: Option<String>,
}

type CmdId = u8;

#[derive(Debug, Error)]
pub enum FrameError {
    #[error("malformed frame")]
    MalformedFrame { bytes: String },
    #[error("malformed log")]
    MalformedLog,
    #[error("malformed cmd response")]
    MalformedCmdResp,
    #[error("received bad cobsr frame")]
    RecvBadCobsrFrame,
    #[error("timeout")]
    Timeout,
    #[error("read error: {0}")]
    ReadError(io::Error),
}

#[derive(Debug, Error)]
pub enum ExecuteCommandError {
    #[error("write error: {0}")]
    WriteError(anyhow::Error),
    #[error("read error: {0}")]
    ReadError(std::io::Error),
    #[error("error: {0}")]
    ErrorFromDevice(String),
    #[error("verification failed")]
    VerificationFailed,
    #[error("timeout")]
    Timeout,
}

use std::{
    ffi::OsStr,
    fs::{self, File},
    io::Write,
    path::Path,
    process::{Command, Stdio},
    rc::Rc,
    sync::Mutex,
    thread,
    time::Duration,
};

use anyhow::{bail, Context, Result};
use esp_idf_part::PartitionTable;
use indicatif::ProgressBar;

use crate::{
    cli::ConnectArgs,
    monitor::{self, MonitorOptions},
    paths,
    serial::{ExecuteCommandError, SerialConnection},
    util::{self, ScriptPaths},
};

pub struct Esp {
    scripts: Option<ScriptPaths>,
    python: Option<&'static str>,
    pub port: Option<String>,
    baud: Option<String>,
    part_table: Option<PartitionTable>,
}

impl Esp {
    /* initialization *************************************************************************************************/

    pub fn new(
        connect_args: Option<&ConnectArgs>,
        part_table: Option<PartitionTable>,
    ) -> Result<Self> {
        let port = match connect_args.map(|args| args.port.as_ref()) {
            Some(Some(port)) => Some(port.clone()),
            Some(None) => Some(util::serial_get_port()?),
            None => None,
        };

        Ok(Self {
            scripts: util::get_idf_scripts().ok(),
            python: util::get_python().ok(),
            port,
            baud: connect_args.map(|args| args.baud.to_string()),
            part_table,
        })
    }

    pub fn try_connect(&self, bar: &ProgressBar) -> Result<()> {
        if let Ok(()) = self.send_dummy_request(false) {
            return Ok(());
        }
        bar.println("\x1b[31;1mFailed to connect to the device. Ensure that the device is connected and in flash mode!\x1b[0m");
        self.send_dummy_request(true)
    }

    fn send_dummy_request(&self, infinite: bool) -> Result<()> {
        let mut cmd = self.get_esptool_default();
        cmd.args([
            "--connect-attempts",
            if infinite { "0" } else { "3" },
            "chip_id",
        ]);
        util::check_cmd(cmd)
    }

    /* flashing *******************************************************************************************************/

    pub fn flash_part(&self, part: &str, file: &str) -> Result<()> {
        let offset = self
            .part_table
            .as_ref()
            .unwrap()
            .find(part)
            .context(format!("partition {part} does not exist"))?
            .offset();
        self.flash(offset, file)
    }

    pub fn flash_bootloader(&self, file: &str) -> Result<()> {
        self.flash(0x1000, file)
    }

    pub fn flash_part_table(&self, file: &str) -> Result<()> {
        self.flash(
            util::get_partition_table_offset(self.part_table.as_ref().unwrap()),
            file,
        )
    }

    fn flash(&self, offset: u32, file: &str) -> Result<()> {
        let mut cmd = self.get_esptool_default();
        cmd.args([
            "write_flash",
            &offset.to_string(),
            file,
            "--flash_size",
            "16MB",
            "--force",
        ]);
        util::check_cmd(cmd)
    }

    pub fn flash_all(&self, bin_file: &str) -> Result<()> {
        let part_table = self.part_table.as_ref().unwrap();
        let mut cmd = self.get_esptool_default();
        cmd.args([
            "write_flash",
            "0x1000",
            paths::IMG_BOOTLOADER_DEV,
            &util::get_partition_table_offset(part_table).to_string(),
            paths::IMG_PART_TABLE_DEV,
            &part_table.find("ota_0").unwrap().offset().to_string(),
            bin_file,
            &part_table.find("nvs").unwrap().offset().to_string(),
            &util::to_temp_path(paths::NVS_BIN),
            "--flash_size",
            "16MB",
            "--force",
        ]);
        util::check_cmd(cmd)
    }

    pub fn flash_all_enc(&self) -> Result<()> {
        let part_table = self.part_table.as_ref().unwrap();
        let mut cmd = self.get_esptool_default();
        cmd.args([
            "write_flash",
            "0x1000",
            &util::to_temp_path(paths::BOOTLOADER_ENC),
            &util::get_partition_table_offset(part_table).to_string(),
            &util::to_temp_path(paths::PART_TABLE_ENC),
            &part_table.find("ota_0").unwrap().offset().to_string(),
            &util::to_temp_path(paths::APP_ENC),
            &part_table.find("nvs").unwrap().offset().to_string(),
            &util::to_temp_path(paths::NVS_BIN),
            &part_table.find("nvs_key").unwrap().offset().to_string(),
            &util::to_temp_path(paths::NVS_KEY_ENC),
            "--flash_size",
            "16MB",
            "--force",
        ]);
        util::check_cmd(cmd)
    }

    /* erase flash ****************************************************************************************************/

    pub fn erase_full(&self) -> Result<()> {
        let mut cmd = self.get_esptool_default();
        cmd.arg("erase_flash");
        util::check_cmd(cmd)
    }

    pub fn erase_part(&self, name: &str) -> Result<()> {
        let Some(partition) = self.part_table.as_ref().unwrap().find(name) else {
            bail!("partition {name} does not exist");
        };

        let mut cmd = self.get_esptool_default();
        cmd.args([
            "erase_region",
            &partition.offset().to_string(),
            &partition.size().to_string(),
        ]);
        util::check_cmd(cmd)
    }

    /* secure boot ****************************************************************************************************/

    pub fn sign(&self, input: &str, output: &str) -> Result<()> {
        util::create_dir_for_file(output)?;
        let mut cmd = self.get_espsecure();
        cmd.args([
            "sign_data",
            "--version",
            "2",
            "--keyfile",
            paths::SEC_BOOT_KEY,
            "--output",
            output,
            input,
        ]);
        util::check_cmd(cmd)
    }

    /* nvs ************************************************************************************************************/

    pub fn gen_nvs(
        &self,
        bar: &ProgressBar,
        key: &str,
        cert: &str,
        password: Option<String>,
        prod: bool,
        pass_out: Rc<Mutex<Option<String>>>,
        cert_pool_path: &str,
    ) -> Result<()> {
        let password = password.unwrap_or_else(util::generate_password);
        bar.println(format!("PW: {password}"));
        *pass_out.lock().unwrap() = Some(password.clone());

        let ca_cert = paths::PORTAL_CA;

        let (cert_file, key_file) = match cert {
            "AUTO" => self.get_cert_and_key_from_pool(cert_pool_path)?,
            _ => (cert.into(), key.into()),
        };

        let csv = format!(
            "key,type,encoding,value\n\
             nvs,namespace,,\n\
             ca_cert,file,binary,{ca_cert}\n\
             client_key,file,binary,{key_file}\n\
             client_cert,file,binary,{cert_file}\n\
             factory_pass,data,string,{password}"
        );

        let nvs_csv_path = util::to_temp_path(paths::NVS_CSV);
        let mut file = File::create(&nvs_csv_path)?;
        file.write_all(csv.as_bytes())?;

        let size = self
            .part_table
            .as_ref()
            .unwrap()
            .find("nvs")
            .unwrap()
            .size();
        let mut cmd = self.get_nvsgen();
        cmd.args([
            if prod { "encrypt" } else { "generate" },
            &nvs_csv_path,
            &util::to_temp_path(paths::NVS_BIN),
            &size.to_string(),
        ]);
        if prod {
            cmd.arg("--inputkey");
            cmd.arg(util::to_temp_path(paths::NVS_KEY_BIN));
        }
        util::check_cmd(cmd)?;

        if cert == "AUTO" {
            fs::remove_file(cert_file)?;
            fs::remove_file(key_file)?;
        }

        Ok(())
    }

    fn get_cert_and_key_from_pool(&self, cert_pool_path: &str) -> Result<(String, String)> {
        let mut paths =
            fs::read_dir(cert_pool_path).context("failed to open certificate pool directory")?;

        let file = paths.nth(0).context("no certificates available")?;
        let path = file.unwrap().path().to_str().unwrap().to_owned();
        if path.ends_with("-key.der") {
            Ok((path.replace("-key.der", "-cert.der"), path))
        } else if path.ends_with("-cert.der") {
            let key = path.replace("-cert.der", "-key.der");
            Ok((path, key))
        } else {
            bail!("Invalid certificate / key file: {path}");
        }
    }

    pub fn gen_nvs_key(&self) -> Result<()> {
        let mut cmd = self.get_nvsgen();
        cmd.args([
            "generate-key",
            "--outdir",
            std::env::temp_dir().to_str().unwrap(),
            "--keyfile",
            "nvs-key.bin", // this will actually place the key in $(temp_dir)/keys/nvs-key.bin!!! (see paths::NVS_KEY_BIN)
        ]);
        util::check_cmd(cmd)
    }

    /* flash encryption ***********************************************************************************************/

    pub fn gen_flash_enc_key(&self) -> Result<()> {
        let mut cmd = self.get_espsecure();
        cmd.args([
            "generate_flash_encryption_key",
            &util::to_temp_path(paths::FLASH_ENC_KEY),
        ]);
        util::check_cmd(cmd)
    }

    pub fn encrypt_bootloader(&self) -> Result<()> {
        self.encrypt(
            paths::IMG_BOOTLOADER_PROD,
            &util::to_temp_path(paths::BOOTLOADER_ENC),
            0x1000,
        )
    }

    pub fn encrypt_part_table(&self) -> Result<()> {
        self.encrypt(
            paths::IMG_PART_TABLE_PROD,
            &util::to_temp_path(paths::PART_TABLE_ENC),
            util::get_partition_table_offset(self.part_table.as_ref().unwrap()),
        )
    }

    pub fn encrypt_part(&self, input: &str, output: &str, part: &str) -> Result<()> {
        let part_table = self.part_table.as_ref().unwrap();
        let Some(part) = part_table.find(part) else {
            bail!("partition {part} does not exist");
        };
        self.encrypt(input, output, part.offset())
    }

    fn encrypt(&self, input: &str, output: &str, address: u32) -> Result<()> {
        let mut cmd = self.get_espsecure();
        cmd.args([
            "encrypt_flash_data",
            "--keyfile",
            &util::to_temp_path(paths::FLASH_ENC_KEY),
            "--address",
            &address.to_string(),
            "--output",
            output,
            input,
        ]);
        util::check_cmd(cmd)
    }

    /* efuses *********************************************************************************************************/

    pub fn burn_flash_enc_key(&self) -> Result<()> {
        self.call_efuse_cmd([
            "burn_key",
            "flash_encryption",
            &util::to_temp_path(paths::FLASH_ENC_KEY),
            "--no-protect-key",
        ])
    }

    pub fn burn_sec_boot_key(&self) -> Result<()> {
        self.call_efuse_cmd(["burn_key", "secure_boot_v2", paths::SEC_BOOT_DIGEST])
    }

    pub fn burn_flash_enc_efuses(&self) -> Result<()> {
        self.call_efuse_cmd([
            "burn_efuse",
            "FLASH_CRYPT_CNT",
            "127",
            "FLASH_CRYPT_CONFIG",
            "0xF",
        ])
    }

    pub fn burn_sec_boot_efuses(&self) -> Result<()> {
        self.call_efuse_cmd(["burn_efuse", "ABS_DONE_1"])
    }

    pub fn burn_security_efuses(&self) -> Result<()> {
        self.call_efuse_cmd([
            "burn_efuse",
            "DISABLE_DL_ENCRYPT",
            "0x1",
            "DISABLE_DL_DECRYPT",
            "0x1",
            "DISABLE_DL_CACHE",
            "0x1",
            "JTAG_DISABLE",
            "0x1",
        ])
    }

    pub fn write_protect_efuses(&self) -> Result<()> {
        self.call_efuse_cmd(["write_protect_efuse", "RD_DIS"])?;
        self.call_efuse_cmd(["write_protect_efuse", "MAC"])
    }

    pub fn burn_all_efuses(&self) -> Result<()> {
        self.call_efuse_cmd([
            "burn_key",
            "flash_encryption",
            &util::to_temp_path(paths::FLASH_ENC_KEY),
            "--no-protect-key",
            "burn_efuse",
            "FLASH_CRYPT_CNT",
            "127",
            "FLASH_CRYPT_CONFIG",
            "0xF",
            "burn_key",
            "secure_boot_v2",
            paths::SEC_BOOT_DIGEST,
            "burn_efuse",
            "ABS_DONE_1",
            "burn_efuse",
            "DISABLE_DL_ENCRYPT",
            "0x1",
            "DISABLE_DL_DECRYPT",
            "0x1",
            "DISABLE_DL_CACHE",
            "0x1",
            "JTAG_DISABLE",
            "0x1",
            "write_protect_efuse",
            "RD_DIS",
            "write_protect_efuse",
            "MAC",
        ])
    }

    fn call_efuse_cmd<I, S>(&self, args: I) -> Result<()>
    where
        I: IntoIterator<Item = S>,
        S: AsRef<OsStr>,
    {
        let mut cmd = self.get_espefuse();
        cmd.args(["--port", self.port.as_ref().unwrap(), "--chip", "esp32"]);
        cmd.args(args);
        let mut child = cmd.stdin(Stdio::piped()).stdout(Stdio::piped()).spawn()?;

        // espefuse.py requires the user to enter "BURN" before it burns eFuses as a security measure.
        let stdin = child.stdin.as_mut().unwrap();
        stdin.write_all(b"BURN\n")?;

        util::check_cmd_output(
            child.wait_with_output()?,
            cmd.get_program().to_str().unwrap(),
            &cmd.get_args()
                .map(|x| x.to_str().unwrap())
                .collect::<Vec<_>>(),
        )
    }

    /* miscellaneous **************************************************************************************************/

    pub fn app_elf_to_bin(&self, output: &str, prod: bool) -> Result<()> {
        let mut cmd = self.get_esptool();
        cmd.args([
            "--chip",
            "esp32",
            "elf2image",
            if prod {
                paths::APP_ELF_PROD
            } else {
                paths::APP_ELF_DEV
            },
            "--output",
            output,
        ]);
        util::check_cmd(cmd)
    }

    pub fn reset(&self) -> Result<()> {
        let port = self.port.as_ref().unwrap();
        let baud = self.baud.as_ref().unwrap().parse().unwrap();
        let mut serial = serialport::new(port, baud).open()?;
        serial.write_data_terminal_ready(false)?;
        serial.write_request_to_send(true)?;
        thread::sleep(Duration::from_millis(100));
        serial.write_request_to_send(false)?;
        Ok(())
    }

    pub fn monitor<'a>(
        &'a self,
        mut opts: MonitorOptions<'a>,
        signing_key_file: impl AsRef<Path>,
    ) -> Result<()> {
        opts.port = self.port.as_ref().unwrap();
        opts.baud = self.baud.as_ref().unwrap().parse().unwrap();
        match opts.elf_file {
            Some(path) => {
                let bytes = fs::read(path).unwrap();
                let trace_decoder = util::TraceDecoder::new(&bytes);
                monitor::run(opts, trace_decoder.ok(), signing_key_file)
            }
            None => monitor::run(opts, None, signing_key_file),
        }
    }

    pub fn exec_command(&self, cmd_and_args: Vec<String>, signing_key_path: &str) -> Result<()> {
        let signing_key =
            std::fs::read(signing_key_path).context("failed to read signing key file")?;
        let port = self.port.as_ref().unwrap();
        let baud = self.baud.as_ref().unwrap().parse().unwrap();

        let mut serial = SerialConnection::connect(port, baud, &signing_key, false, true, false)
            .context("failed to setup connection")?;

        let command = &cmd_and_args[0];
        let args = cmd_and_args[1..]
            .iter()
            .map(String::as_bytes)
            .collect::<Vec<_>>();
        match serial.execute_command(false, command, &args) {
            Ok(resp) => println!("{}", resp.trim()),
            Err(ExecuteCommandError::ErrorFromDevice(e)) => println!("Error: {e}"),
            Err(e) => Err(e)?,
        }

        Ok(())
    }

    pub fn read_mac(&self, bar: Option<&ProgressBar>) -> Result<()> {
        let out = Rc::new(Mutex::new(None));
        self.read_mac_with_output(bar, out)
    }

    pub fn read_mac_with_output(
        &self,
        bar: Option<&ProgressBar>,
        mac_out: Rc<Mutex<Option<String>>>,
    ) -> Result<()> {
        let mut cmd = self.get_esptool_default();
        cmd.arg("read_mac");
        let Ok(output) = cmd.output() else {
            bail!("failed to execute read_mac command");
        };

        match output.status.success() {
            true => {
                let out = String::from_utf8_lossy(&output.stdout);
                let line = out
                    .lines()
                    .filter(|x| x.starts_with("MAC: "))
                    .nth(0)
                    .unwrap();
                let mac_eth = &line[5..];
                let mut mac = mac_eth[..(mac_eth.len() - 1)].to_owned();
                mac.push(match mac_eth.chars().last().unwrap() {
                    '0' => '3',
                    '4' => '7',
                    '8' => 'b',
                    'c' => 'f',
                    _ => bail!("invalid eth mac address: {mac_eth}"),
                });
                let mac = mac.replace(':', "");
                match bar {
                    Some(bar) => bar.println(format!("SN: {mac}")),
                    None => println!("{mac}"),
                }
                *mac_out.lock().unwrap() = Some(mac);
            }
            false => {
                *mac_out.lock().unwrap() = None;
                bail!(
                    "failed to read mac address\n - stdout:\n{}\n - stderr:\n{}",
                    String::from_utf8_lossy(&output.stdout),
                    String::from_utf8_lossy(&output.stderr)
                );
            }
        }

        Ok(())
    }

    /* utilities ******************************************************************************************************/

    fn get_esptool(&self) -> Command {
        match &self.scripts {
            Some(scripts) => {
                let python = self.python.as_ref().expect("python installation not found");
                let mut cmd = Command::new(python);
                cmd.arg(&scripts.tool);
                cmd
            }
            None => Command::new(paths::DEP_ESPTOOL),
        }
    }

    fn get_espefuse(&self) -> Command {
        match &self.scripts {
            Some(scripts) => {
                let python = self.python.as_ref().expect("python installation not found");
                let mut cmd = Command::new(python);
                cmd.arg(&scripts.efuse);
                cmd
            }
            None => Command::new(paths::DEP_ESPEFUSE),
        }
    }

    fn get_espsecure(&self) -> Command {
        match &self.scripts {
            Some(scripts) => {
                let python = self.python.as_ref().expect("python installation not found");
                let mut cmd = Command::new(python);
                cmd.arg(&scripts.secure);
                cmd
            }
            None => Command::new(paths::DEP_ESPSECURE),
        }
    }

    fn get_nvsgen(&self) -> Command {
        match &self.scripts {
            Some(scripts) => {
                let python = self.python.as_ref().expect("python installation not found");
                let mut cmd = Command::new(python);
                cmd.arg(&scripts.nvsgen);
                cmd
            }
            None => Command::new(paths::DEP_NVSGEN),
        }
    }

    fn get_esptool_default(&self) -> Command {
        let mut cmd = self.get_esptool();
        cmd.args([
            "--port",
            self.port.as_ref().unwrap(),
            "--baud",
            self.baud.as_ref().unwrap(),
            "--after",
            "no_reset",
            "--chip",
            "esp32",
        ]);
        cmd
    }
}

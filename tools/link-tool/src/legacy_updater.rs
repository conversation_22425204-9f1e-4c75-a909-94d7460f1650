use std::{
    fs::File,
    io::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Read, Write},
    process::exit,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, Mutex,
    },
    time::{Duration, Instant},
};

use anyhow::{bail, Context, Result};
use dialoguer::{theme::ColorfulTheme, Confirm};
use indicatif::{ProgressBar, ProgressState, ProgressStyle};
use rsa::{
    pkcs1::DecodeRsaPrivateKey,
    pkcs1v15::SigningKey,
    sha2::Sha256,
    signature::{RandomizedSigner, SignatureEncoding},
    RsaPrivateKey,
};
use serialport::{ClearBuffer, SerialPort};

use crate::{cli::UpdateArgs, util};

enum Response {
    Success(Option<String>),
    Failure(String),
    IncorrectType,
    Timeout,
}

type Serial = Arc<Mutex<Option<Box<dyn SerialPort>>>>;

const MAX_RETRIES: usize = 10;
const CHUNK_SIZE: usize = 500;
const SERIAL_TIMEOUT: Duration = Duration::from_millis(1);
const ESCAPE_CHAR: u8 = 0xBE;

static CANCEL_UPDATE: AtomicBool = AtomicBool::new(false);

pub fn start(args: UpdateArgs) -> Result<()> {
    let port = match args.connect_args.port {
        Some(ref port) => port.clone(),
        None => util::serial_get_port()?,
    };
    let baud = args.connect_args.baud as u32;

    let serial = serialport::new(port, baud).timeout(SERIAL_TIMEOUT).open()?;

    update(serial, args)
}

fn update(mut serial: Box<dyn SerialPort>, args: UpdateArgs) -> Result<()> {
    let mut file = File::open(&args.image_path)?;
    let mut buf = Vec::new();
    file.read_to_end(&mut buf)?;

    let version = util::get_version(&buf)?;

    println!("\nFlashing firmware {}...", version);

    // make sure that OTW messages actually get recognized as OTW messages and not commands
    let _ = serial.write(b"XXXXX\n");

    let serial = Arc::new(Mutex::new(Some(serial)));
    let serial_clone = Arc::clone(&serial);
    ctrlc::set_handler(move || {
        if serial_clone.lock().unwrap().is_some() {
            CANCEL_UPDATE.store(true, Ordering::Relaxed);
            println!("Cancelling update...");
            let _ = cancel_update(&serial_clone, args.debug);
            println!("Update cancelled.");
        }
        exit(1);
    })?;

    let serial_clone = Arc::clone(&serial);
    let res = do_update(serial, &buf, version, &args);
    let mut serial = serial_clone.lock().unwrap();
    *serial = None;
    res
}

fn do_update(serial: Serial, firmware: &[u8], version: &str, args: &UpdateArgs) -> Result<()> {
    start_update(&serial, version, args)?;

    let progress_style = ProgressStyle::with_template("{spinner:.green} [{elapsed_precise}] [{wide_bar:.cyan/blue}] {bytes}/{total_bytes} ({eta} left)")
        .unwrap()
        .with_key("eta", |state: &ProgressState, w: &mut dyn std::fmt::Write| write!(w, "{}s", state.eta().as_secs_f32() as u32).unwrap())
        .progress_chars("#>-");

    let progress_bar = ProgressBar::new(firmware.len() as u64).with_style(progress_style);

    let mut num_retries = 0;
    let transmission_start = Instant::now();

    let chunks = firmware.chunks(CHUNK_SIZE).collect::<Vec<_>>();
    let mut id = 0;
    'outer: while id < chunks.len() {
        let chunk = chunks[id];
        'retry: for retry in 0..MAX_RETRIES {
            if CANCEL_UPDATE.load(Ordering::Relaxed) {
                bail!("Update cancelled, stop write request");
            }
            match write_chunk(&serial, id, chunk, args, &progress_bar) {
                Ok(None) => break 'retry,
                Ok(Some(expected_id)) => {
                    num_retries += 1;
                    if args.debug {
                        let s = format!(
                            "\x1b[36m(debug|chunk)\x1b[0m chunk {id} (size {}) {chunk:?}",
                            chunk.len()
                        );
                        match Some(&progress_bar) {
                            Some(bar) => bar.println(&s),
                            None => print!("{s}"),
                        }
                    }
                    if retry == MAX_RETRIES - 1 {
                        let _ = cancel_update(&serial, args.debug);
                        bail!("too many retries for chunk {id}");
                    } else if id == expected_id {
                        continue 'retry;
                    } else {
                        id = expected_id;
                        continue 'outer;
                    }
                }
                Err(e) => {
                    println!("Error: {e} in chunk {id}");
                    cancel_update(&serial, args.debug)?;
                    println!("\nUpdate cancelled.");
                    return Ok(());
                }
            }
        }
        id += 1;
        progress_bar.inc(chunk.len() as u64);
    }
    progress_bar.finish();

    let transmission_duration = transmission_start.elapsed();
    let speed_kbps = (firmware.len() as f32) / transmission_duration.as_secs_f32() / 1000.0;
    let retry_percentage = (num_retries as f32) / (chunks.len() as f32) * 100.0;

    if CANCEL_UPDATE.load(Ordering::Relaxed) {
        bail!("Update cancelled, stop finish request");
    }
    // TODO: retry finish update?
    finish_update(&serial, args)?;
    println!("\nSuccessfully updated device.");

    println!("\nStatistics:");
    println!(" - number of chunks transmitted: {}", chunks.len());
    println!(" - number of retries: {num_retries} ({retry_percentage:.1} %)");
    println!(" - average transmission speed: {speed_kbps:.1} kB/s");

    Ok(())
}

fn start_update(serial: &Serial, version: &str, args: &UpdateArgs) -> Result<()> {
    if CANCEL_UPDATE.load(Ordering::Relaxed) {
        bail!("Update cancelled, stop start request");
    }

    Sender::new(serial, args.debug).add(b"OTWS").send()?;

    let resp = read_response(serial, 'S', Duration::from_secs(10), args.debug, None)?;

    let challenge = match resp {
        Response::Success(challenge) => challenge,
        Response::IncorrectType => bail!("Incorrect response from device"),
        Response::Timeout => bail!("Response timeout"),
        Response::Failure(f) if f == "unexpected ota start message" => None,
        Response::Failure(f) => bail!("Error from device: '{f}'"),
    };

    if let Some(challenge) = challenge {
        return send_verify(serial, challenge, version, args);
    };

    println!("\nThe device is currently being updated.");
    let cancel = Confirm::with_theme(&ColorfulTheme::default())
        .with_prompt("Cancel the ongoing update?")
        .default(true)
        .interact()
        .unwrap();
    println!();

    if !cancel {
        bail!("The device is currently being updated.");
    }

    Sender::new(serial, args.debug).add(b"OTWC").send()?;

    let resp = read_response(serial, 'C', Duration::from_secs(10), args.debug, None)?;

    match resp {
        Response::Success(_) => (),
        Response::IncorrectType => bail!("Incorrect response from device"),
        Response::Timeout => bail!("Response timeout"),
        Response::Failure(f) => bail!("Error from device: '{f}'"),
    }

    start_update(serial, version, args)
}

fn send_verify(serial: &Serial, challenge: String, version: &str, args: &UpdateArgs) -> Result<()> {
    let mut signature_data = Vec::with_capacity(version.len() + 32 + 1);
    signature_data.push(version.len() as u8 + 32);
    signature_data.extend(challenge.as_bytes());
    signature_data.extend(version.as_bytes());

    let mut file = File::open(&args.signing_key).context("otw update signing key not found")?;
    let mut signing_key_raw = Vec::new();
    file.read_to_end(&mut signing_key_raw)?;

    let mut rng = rand::thread_rng();
    let private_key = RsaPrivateKey::from_pkcs1_der(&signing_key_raw)?;
    let signing_key = SigningKey::<Sha256>::new(private_key);
    let signature = signing_key.sign_with_rng(&mut rng, &signature_data);

    Sender::new(serial, args.debug)
        .add(b"OTWV")
        .add(&signature_data)
        .add(&signature.to_bytes())
        .send()?;

    let resp = read_response(serial, 'V', Duration::from_secs(10), args.debug, None)?;

    match resp {
        Response::Success(_) => return Ok(()),
        Response::IncorrectType => bail!("Incorrect response from device"),
        Response::Timeout => bail!("Response timeout"),
        Response::Failure(f) if f == "ack" => (),
        Response::Failure(f) => bail!("Error from device: '{f}'"),
    }

    let resp = read_response(serial, 'V', Duration::from_secs(30), args.debug, None)?;

    match resp {
        Response::Success(_) => Ok(()),
        Response::IncorrectType => bail!("Incorrect response from device"),
        Response::Timeout => bail!("Response timeout"),
        Response::Failure(f) => bail!("Error from device: '{f}'"),
    }
}

fn write_chunk(
    serial: &Serial,
    id: usize,
    data: &[u8],
    args: &UpdateArgs,
    bar: &ProgressBar,
) -> Result<Option<usize>> {
    Sender::new(serial, args.debug)
        .add(b"OTWW")
        .add_char((id >> 8) as u8)
        .add_char(id as u8)
        .add_char((data.len() >> 8) as u8)
        .add_char(data.len() as u8)
        .add(data)
        .send()?;

    let resp = read_response(serial, 'W', Duration::from_secs(1), args.debug, Some(bar))?;

    match resp {
        Response::Success(_) => Ok(None),
        Response::IncorrectType => Ok(Some(id)),
        Response::Timeout => {
            println!("timeout");

            Ok(Some(id))
        }
        Response::Failure(f) if f.starts_with("crc ") || f.starts_with("retry ") => {
            let space = f.find(' ').unwrap();
            match f[(space + 1)..].parse::<usize>() {
                Ok(x) => Ok(Some(x)),
                Err(_) => Ok(Some(id)),
            }
        }
        Response::Failure(f) => bail!("Error from device: '{f}'"),
    }
}

fn finish_update(serial: &Serial, args: &UpdateArgs) -> Result<()> {
    println!("\nFinishing update...");

    Sender::new(serial, args.debug).add(b"OTWF").send()?;

    let resp = read_response(serial, 'F', Duration::from_secs(300), args.debug, None)?;

    match resp {
        Response::Success(_) => Ok(()),
        Response::IncorrectType => bail!("Incorrect response from device"),
        Response::Timeout => bail!("Response timeout"),
        Response::Failure(f) => bail!("Error from device: '{f}'"),
    }
}

fn cancel_update(serial: &Serial, debug: bool) -> Result<()> {
    Sender::new(serial, debug).add(b"OTWC").send()?;

    let resp = read_response(serial, 'C', Duration::from_secs(10), debug, None)?;

    match resp {
        Response::Success(_) => Ok(()),
        Response::IncorrectType => bail!("Incorrect response from device"),
        Response::Timeout => bail!("Response timeout"),
        Response::Failure(f) => bail!("Error from device: '{f}'"),
    }
}

struct Sender<'a> {
    #[allow(dead_code)]
    debug: bool,
    serial: &'a Serial,
    buf: Vec<u8>,
    crc: u16,
}

impl<'a> Sender<'a> {
    fn new(serial: &'a Serial, debug: bool) -> Self {
        Self {
            debug,
            serial,
            buf: Vec::new(),
            crc: 0xFFFF,
        }
    }

    fn add(mut self, buf: &[u8]) -> Self {
        for c in buf {
            self = self.add_char(*c);
        }
        self
    }

    fn add_char(mut self, c: u8) -> Self {
        self.add_char_to_buf(c);
        self.crc = (self.crc << 8) ^ CRC_LUT[((self.crc >> 8) ^ (c as u16)) as usize];
        self
    }

    fn send(mut self) -> Result<()> {
        let crc = self.crc;
        if false {
            println!(
                "\x1b[36m(debug|send)\x1b[0m {:?} crc 0x{:x} ([{}, {}])",
                &self.buf,
                crc,
                (crc >> 8),
                crc & 0xFF
            );
            // println!(
            //     "\x1b[36m(debug|send)\x1b[0m {}",
            //     String::from_utf8_lossy(
            //         &self
            //             .buf
            //             .iter()
            //             .map(|x| {
            //                 if *x < 0x32 {
            //                     b'?'
            //                 } else {
            //                     *x
            //                 }
            //             })
            //             .take(64)
            //             .collect::<Vec<_>>()
            //     )
            // );
        }
        self.add_char_to_buf((crc >> 8) as u8);
        self.add_char_to_buf(crc as u8);
        self.buf.push(b'\r');
        self.buf.push(b'\n');
        let mut serial = self.serial.lock().unwrap();
        serial.as_mut().unwrap().clear(ClearBuffer::Input)?;
        let _ = serial.as_mut().unwrap().write(&self.buf)?;
        Ok(())
    }

    fn add_char_to_buf(&mut self, c: u8) {
        match c {
            b'\n' | b'\r' | ESCAPE_CHAR => self.buf.extend_from_slice(&[ESCAPE_CHAR, c]),
            x => self.buf.push(x),
        }
    }
}

fn read_response(
    serial: &Serial,
    expect_msg_type: char,
    timeout: Duration,
    debug: bool,
    bar: Option<&ProgressBar>,
) -> Result<Response> {
    let mut guard = serial.lock().unwrap();
    let mut remainder = String::new();

    let line = loop {
        match util::serial_read_line(guard.as_mut().unwrap(), remainder, Some(timeout)) {
            Ok(res) => {
                let (line, new_remainder) = res;
                remainder = new_remainder;
                if debug {
                    let s = format!("\x1b[36m(debug)\x1b[0m {line}");
                    match bar {
                        Some(bar) => bar.println(&s),
                        None => print!("{s}"),
                    }
                }
                if line.contains("otw: OTW") {
                    break line;
                }
            }
            Err(ref e) if e.kind() == ErrorKind::InvalidData => {
                // ignore messages with non-UTF-8 characters
                remainder = String::new();
            }
            Err(ref e) if e.kind() == ErrorKind::TimedOut => return Ok(Response::Timeout),
            Err(e) => {
                eprintln!("\n{e:?}");
                return Err(e.into());
            }
        }
    };

    let line = &line[line.find("OTW").unwrap()..];
    let line = line.trim().trim_end_matches("\x1b[0m");

    if line.len() < 7 {
        return Ok(Response::IncorrectType);
    }

    let resp_msg_type = line.chars().nth(3).unwrap();
    if !(resp_msg_type == expect_msg_type || resp_msg_type == '?') {
        return Ok(Response::IncorrectType);
    }

    if line[4..].starts_with(" ok") {
        return Ok(Response::Success(line.get(8..).map(str::to_string)));
    }

    if line[4..].starts_with(" err") {
        return if line.len() <= 9 {
            Ok(Response::Failure("?".into()))
        } else {
            Ok(Response::Failure(line[9..].into()))
        };
    }

    Ok(Response::IncorrectType)
}

const CRC_LUT: [u16; 256] = [
    0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50a5, 0x60c6, 0x70e7, 0x8108, 0x9129, 0xa14a, 0xb16b,
    0xc18c, 0xd1ad, 0xe1ce, 0xf1ef, 0x1231, 0x0210, 0x3273, 0x2252, 0x52b5, 0x4294, 0x72f7, 0x62d6,
    0x9339, 0x8318, 0xb37b, 0xa35a, 0xd3bd, 0xc39c, 0xf3ff, 0xe3de, 0x2462, 0x3443, 0x0420, 0x1401,
    0x64e6, 0x74c7, 0x44a4, 0x5485, 0xa56a, 0xb54b, 0x8528, 0x9509, 0xe5ee, 0xf5cf, 0xc5ac, 0xd58d,
    0x3653, 0x2672, 0x1611, 0x0630, 0x76d7, 0x66f6, 0x5695, 0x46b4, 0xb75b, 0xa77a, 0x9719, 0x8738,
    0xf7df, 0xe7fe, 0xd79d, 0xc7bc, 0x48c4, 0x58e5, 0x6886, 0x78a7, 0x0840, 0x1861, 0x2802, 0x3823,
    0xc9cc, 0xd9ed, 0xe98e, 0xf9af, 0x8948, 0x9969, 0xa90a, 0xb92b, 0x5af5, 0x4ad4, 0x7ab7, 0x6a96,
    0x1a71, 0x0a50, 0x3a33, 0x2a12, 0xdbfd, 0xcbdc, 0xfbbf, 0xeb9e, 0x9b79, 0x8b58, 0xbb3b, 0xab1a,
    0x6ca6, 0x7c87, 0x4ce4, 0x5cc5, 0x2c22, 0x3c03, 0x0c60, 0x1c41, 0xedae, 0xfd8f, 0xcdec, 0xddcd,
    0xad2a, 0xbd0b, 0x8d68, 0x9d49, 0x7e97, 0x6eb6, 0x5ed5, 0x4ef4, 0x3e13, 0x2e32, 0x1e51, 0x0e70,
    0xff9f, 0xefbe, 0xdfdd, 0xcffc, 0xbf1b, 0xaf3a, 0x9f59, 0x8f78, 0x9188, 0x81a9, 0xb1ca, 0xa1eb,
    0xd10c, 0xc12d, 0xf14e, 0xe16f, 0x1080, 0x00a1, 0x30c2, 0x20e3, 0x5004, 0x4025, 0x7046, 0x6067,
    0x83b9, 0x9398, 0xa3fb, 0xb3da, 0xc33d, 0xd31c, 0xe37f, 0xf35e, 0x02b1, 0x1290, 0x22f3, 0x32d2,
    0x4235, 0x5214, 0x6277, 0x7256, 0xb5ea, 0xa5cb, 0x95a8, 0x8589, 0xf56e, 0xe54f, 0xd52c, 0xc50d,
    0x34e2, 0x24c3, 0x14a0, 0x0481, 0x7466, 0x6447, 0x5424, 0x4405, 0xa7db, 0xb7fa, 0x8799, 0x97b8,
    0xe75f, 0xf77e, 0xc71d, 0xd73c, 0x26d3, 0x36f2, 0x0691, 0x16b0, 0x6657, 0x7676, 0x4615, 0x5634,
    0xd94c, 0xc96d, 0xf90e, 0xe92f, 0x99c8, 0x89e9, 0xb98a, 0xa9ab, 0x5844, 0x4865, 0x7806, 0x6827,
    0x18c0, 0x08e1, 0x3882, 0x28a3, 0xcb7d, 0xdb5c, 0xeb3f, 0xfb1e, 0x8bf9, 0x9bd8, 0xabbb, 0xbb9a,
    0x4a75, 0x5a54, 0x6a37, 0x7a16, 0x0af1, 0x1ad0, 0x2ab3, 0x3a92, 0xfd2e, 0xed0f, 0xdd6c, 0xcd4d,
    0xbdaa, 0xad8b, 0x9de8, 0x8dc9, 0x7c26, 0x6c07, 0x5c64, 0x4c45, 0x3ca2, 0x2c83, 0x1ce0, 0x0cc1,
    0xef1f, 0xff3e, 0xcf5d, 0xdf7c, 0xaf9b, 0xbfba, 0x8fd9, 0x9ff8, 0x6e17, 0x7e36, 0x4e55, 0x5e74,
    0x2e93, 0x3eb2, 0x0ed1, 0x1ef0,
];

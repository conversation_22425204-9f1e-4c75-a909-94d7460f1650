#![allow(clippy::redundant_closure)]
#![feature(let_chains)]

use std::{
    env,
    fmt::Write as FmtWrite,
    fs,
    io::{stdout, Read, Write},
    path::Path,
    process::Command,
    rc::Rc,
    sync::Mutex,
    time::Duration,
};

use anyhow::{anyhow, bail, Context, Result};
use clap::Parser;
use indicatif::{ProgressBar, ProgressState, ProgressStyle};

mod cli;
mod esp;
mod legacy_updater;
mod monitor;
mod paths;
mod serial;
mod updater;
mod util;

use cli::*;
use esp::Esp;
use monitor::MonitorOptions;
use serial::{FrameError, SerialConnection};

fn main() {
    let cli = Cli::parse();

    env::set_current_dir(&cli.default_args.firmware_dir).unwrap();

    if let Err(e) = match cli.command {
        Commands::Cmd(args) => command(args),
        Commands::Create(args) => create(args),
        Commands::Deploy(args) => deploy(args),
        Commands::Erase(args) => erase(args),
        Commands::Flash(args) => flash(args),
        Commands::Monitor(args) => monitor(args),
        Commands::ReadMac(args) => read_mac(args),
        Commands::ReadVersion(args) => read_version(args),
        Commands::Reset(args) => reset(args),
        Commands::TraceDecode(args) => trace_decode(args),
        Commands::Update(args) => update(args),
        Commands::ProdTest(args) => prod_test(args),
    } {
        println!("\x1b[31mError:\x1b[0m {e:?}");
        std::process::exit(1);
    };
}

fn command(args: CommandArgs) -> Result<()> {
    if args.command.is_empty() {
        bail!("no command specified");
    }

    let esp = Esp::new(Some(&args.connect_args), None)?;
    esp.exec_command(args.command, &args.signing_key)
}

fn create(args: CreateArgs) -> Result<()> {
    let mut queue = WorkQueue::new();
    let esp = Esp::new(None, None)?;

    util::check_dependencies()?;

    let (part_table_output, bootloader_output, part_table, bootloader, app_bin) = match args.prod {
        true => (
            paths::IMG_PART_TABLE_PROD,
            paths::IMG_BOOTLOADER_PROD,
            paths::PART_TABLE_BIN_PROD,
            paths::BOOTLOADER_BIN_PROD,
            paths::APP_BIN_PROD,
        ),
        false => (
            paths::IMG_PART_TABLE_DEV,
            paths::IMG_BOOTLOADER_DEV,
            paths::PART_TABLE_BIN_DEV,
            paths::BOOTLOADER_BIN_DEV,
            paths::APP_BIN_DEV,
        ),
    };
    let app_bin = util::to_temp_path(app_bin);

    fn get_output(args: &CreateArgs, app_bin: &str) -> Result<String> {
        let app_output_template = match args.prod {
            true => paths::IMG_APP_PROD,
            false => paths::IMG_APP_DEV,
        };
        Ok(match &args.output {
            Some(output) => output.clone(),
            None => util::get_output_bin_with_version(app_bin, app_output_template)?,
        })
    }

    util::create_dir_for_file(paths::IMG_APP_DEV)?;
    util::create_dir_for_file(paths::IMG_APP_PROD)?;
    util::create_dir_for_file(bootloader_output)?;

    let elf_to_bin = |_: &_| esp.app_elf_to_bin(&app_bin, args.prod);
    let sign_image = |_: &_| esp.sign(&app_bin, &get_output(&args, &app_bin)?);
    let copy_part_table = |_: &_| {
        util::create_dir_for_file(part_table_output)?;
        fs::copy(part_table, part_table_output)
            .map(|_| ())
            .map_err(|e| anyhow!(e))
    };
    let sign_bootloader = |_: &_| esp.sign(bootloader, bootloader_output);

    queue.add("Converting application ELF into image", &elf_to_bin);
    queue.add("Signing application image", &sign_image);
    queue.add("Signing bootloader binary", &sign_bootloader);
    queue.add("Copying partition table", &copy_part_table);

    queue.execute()?;

    println!("Image: {}", get_output(&args, &app_bin)?);

    Ok(())
}

fn deploy(args: DeployArgs) -> Result<()> {
    // NOTE: this does not actually check the contents of the firmware image, since this cannot be
    // done easily from what I could gather. Thus, a simple filename check will have to do...
    if args.prod && args.bin.contains("dev") {
        bail!("cannot flash dev firmware with --prod");
    } else if !args.prod && args.bin.contains("prod") {
        bail!("cannot flash prod firmware without --prod");
    }

    let mut queue = WorkQueue::new();
    let part_table = util::get_part_table(args.prod)?;
    let esp = Esp::new(Some(&args.connect_args), Some(part_table))?;

    util::check_dependencies()?;

    let (key, cert) = match (args.prod, args.key.as_str(), args.cert.as_str()) {
        (true, paths::PORTAL_DEV_KEY | "AUTO", paths::PORTAL_DEV_CERT | "AUTO") => ("AUTO", "AUTO"),
        (false, k, c) => (k, c),
        _ => bail!("only certificates from the certificate pool can be used in production"),
    };

    let mac = Rc::new(Mutex::new(None));
    let pass = Rc::new(Mutex::new(None));

    let app_enc = util::to_temp_path(paths::APP_ENC);
    let bootloader_enc = util::to_temp_path(paths::BOOTLOADER_ENC);
    let part_table_enc = util::to_temp_path(paths::PART_TABLE_ENC);
    let nvs_key_enc = util::to_temp_path(paths::NVS_KEY_ENC);
    let nvs_key_bin = util::to_temp_path(paths::NVS_KEY_BIN);
    let nvs_bin = util::to_temp_path(paths::NVS_BIN);

    let read_mac = |bar: &_| esp.read_mac_with_output(Some(bar), Rc::clone(&mac));
    let erase_flash = |_: &_| esp.erase_full();
    let gen_nvs_key = |_: &_| esp.gen_nvs_key();
    let gen_nvs = |bar: &_| {
        esp.gen_nvs(
            bar,
            key,
            cert,
            args.password.clone(),
            args.prod,
            Rc::clone(&pass),
            &args.cert_pool_path,
        )
    };

    let flash_nvs_key = |_: &_| esp.flash_part("nvs_key", &nvs_key_bin);
    let flash_nvs_bin = |_: &_| esp.flash_part("nvs", &nvs_bin);
    let flash_bootloader = |_: &_| esp.flash_bootloader(paths::IMG_BOOTLOADER_DEV);
    let flash_part_table = |_: &_| esp.flash_part_table(paths::IMG_PART_TABLE_DEV);
    let flash_application = |_: &_| esp.flash_part("ota_0", &args.bin);
    let flash_all = |_: &_| esp.flash_all(&args.bin);

    let gen_flash_enc_key = |_: &_| esp.gen_flash_enc_key();
    let enc_bootloader = |_: &_| esp.encrypt_bootloader();
    let enc_part_table = |_: &_| esp.encrypt_part_table();
    let enc_nvs_key = |_: &_| esp.encrypt_part(&nvs_key_bin, &nvs_key_enc, "nvs_key");
    let enc_application = |_: &_| esp.encrypt_part(&args.bin, &app_enc, "ota_0");
    let flash_enc_bootloader = |_: &_| esp.flash_bootloader(&bootloader_enc);
    let flash_enc_part_table = |_: &_| esp.flash_part_table(&part_table_enc);
    let flash_enc_application = |_: &_| esp.flash_part("ota_0", &app_enc);
    let flash_enc_nvs_key = |_: &_| esp.flash_part("nvs_key", &nvs_key_enc);
    let flash_all_enc = |_: &_| esp.flash_all_enc();
    let burn_flash_enc_key = |_: &_| esp.burn_flash_enc_key();
    let burn_flash_enc_efuses = |_: &_| esp.burn_flash_enc_efuses();
    let burn_sec_boot_key = |_: &_| esp.burn_sec_boot_key();
    let burn_sec_boot_efuses = |_: &_| esp.burn_sec_boot_efuses();
    let burn_security_efuses = |_: &_| esp.burn_security_efuses();
    let write_protect_efuses = |_: &_| esp.write_protect_efuses();
    let burn_all_efuses = |_: &_| esp.burn_all_efuses();

    let reset_device = |_: &_| esp.reset();

    queue.add("Trying to connect to device", &read_mac);
    queue.add("Erasing flash", &erase_flash);
    if args.prod {
        queue.add("Generating NVS encryption key", &gen_nvs_key);
    }
    queue.add("Generating NVS partition binary", &gen_nvs);

    match args.prod {
        true => {
            queue.add("Generating flash encryption key", &gen_flash_enc_key);
            queue.add("Encrypting bootloader", &enc_bootloader);
            queue.add("Encrypting partition table", &enc_part_table);
            queue.add("Encrypting NVS key", &enc_nvs_key);
            queue.add("Encrypting application", &enc_application);

            if args.step {
                queue.add("Flashing encrypted bootloader", &flash_enc_bootloader);
                queue.add("Flashing encrypted partition table", &flash_enc_part_table);
                queue.add("Flashing encrypted NVS key", &flash_enc_nvs_key);
                queue.add("Flashing encrypted application", &flash_enc_application);
                queue.add("Flashing NVS binary", &flash_nvs_bin);

                queue.add("Burning flash encryption key", &burn_flash_enc_key);
                queue.add("Burning flash encryption efuses", &burn_flash_enc_efuses);
                queue.add("Burning secure boot key digest", &burn_sec_boot_key);
                queue.add("Burning secure boot efuses", &burn_sec_boot_efuses);
                queue.add("Burning security efuses", &burn_security_efuses);
                queue.add("Write-protecting efuses", &write_protect_efuses);
            } else {
                queue.add("Flashing partitions", &flash_all_enc);
                queue.add("Burning efuses", &burn_all_efuses);
            }
        }
        false => {
            if args.step {
                queue.add("Flashing NVS encryption key", &flash_nvs_key);
                queue.add("Flashing NVS binary", &flash_nvs_bin);
                queue.add("Flashing bootloader", &flash_bootloader);
                queue.add("Flashing partition table", &flash_part_table);
                queue.add("Flashing application", &flash_application);
            } else {
                queue.add("Flashing partitions", &flash_all);
            }
        }
    }
    if (!args.monitor || args.after_deploy.is_some()) && !args.no_reset {
        queue.add("Resetting device using RTS", &reset_device);
    }

    queue.execute()?;
    // required because terminal is broken otherwise for whatever reason
    let _ = crossterm::terminal::enable_raw_mode();
    let _ = crossterm::terminal::disable_raw_mode();

    if let Some(after_deploy) = args.after_deploy {
        println!(" Executing --after-deploy command...");
        let mac = mac.lock().unwrap();
        let pass = pass.lock().unwrap();
        let _ = Command::new(&after_deploy)
            .arg(mac.as_deref().unwrap_or("?"))
            .arg(pass.as_deref().unwrap_or("?"))
            .spawn()
            .and_then(|mut child| child.wait())
            .inspect_err(|e| println!("ERROR: Failed to execute --after-deploy command: {e:?}"));
    }

    if args.monitor {
        let elf = if args.prod {
            paths::APP_ELF_PROD
        } else {
            paths::APP_ELF_DEV
        };
        let elf_exists = Path::new(elf).is_file();
        let opts = MonitorOptions {
            filter: args.monitor_filter,
            raw: args.monitor_raw,
            elf_file: elf_exists.then_some(elf),
            colored: true,
            no_reset: args.no_reset,
            dev: !args.prod,
            ..Default::default()
        };
        esp.monitor(opts, args.signing_key)?;
    }

    Ok(())
}

fn erase(args: EraseArgs) -> Result<()> {
    let mut queue = WorkQueue::new();
    let part_table = util::get_part_table(false)?;
    let esp = Esp::new(Some(&args.connect_args), Some(part_table))?;

    util::check_dependencies()?;

    let try_connect = |bar: &_| esp.try_connect(bar);

    let erase_full = |_: &_| esp.erase_full();
    let erase_parts = args
        .partitions
        .iter()
        .map(|part_name| {
            let label = format!("Erasing partition {part_name}");
            (label, |_: &_| esp.erase_part(part_name))
        })
        .collect::<Vec<_>>();

    queue.add("Trying to connect to device", &try_connect);

    if args.partitions.is_empty() {
        queue.add("Erasing flash", &erase_full);
    } else {
        for i in &erase_parts {
            queue.add(&i.0, &i.1);
        }
    }

    queue.execute()
}

fn flash(mut args: FlashArgs) -> Result<()> {
    if let Some(fw) = args.firmware_path
        && (fw.contains("release") || fw.contains("prod"))
    {
        bail!("cannot flash prod firmware for development");
    }

    let mut queue = WorkQueue::new();
    let part_table = util::get_part_table(false)?;
    let esp = Esp::new(Some(&args.connect_args), Some(part_table))?;

    if args.all && args.otw {
        bail!("--all and --otw cannot be used together");
    }

    util::check_dependencies()?;
    util::check_firmware_compiled()?;

    let app_bin = util::to_temp_path(paths::APP_BIN_DEV);

    let try_connect = |bar: &_| esp.try_connect(bar);
    let erase_ota_1 = |_: &_| esp.erase_part("ota_1");
    let app_elf_to_bin = |_: &_| esp.app_elf_to_bin(&app_bin, false);
    let flash_bootloader = |_: &_| esp.flash_bootloader(paths::BOOTLOADER_BIN_DEV);
    let flash_part_table = |_: &_| esp.flash_part_table(paths::PART_TABLE_BIN_DEV);
    let flash_application = |_: &_| esp.flash_part("ota_0", &app_bin);

    if args.otw {
        queue.add("Converting application ELF into image", &app_elf_to_bin);
    } else {
        queue.add("Trying to connect to device", &try_connect);
        queue.add("Converting application ELF into image", &app_elf_to_bin);
        queue.add("Erasing ota_1 partition", &erase_ota_1);
        queue.add("Flashing application", &flash_application);
    }

    if args.all {
        queue.add("Flashing bootloader", &flash_bootloader);
        queue.add("Flashing partition table", &flash_part_table);
    }

    queue.execute()?;

    args.connect_args.port.clone_from(&esp.port);

    if args.otw {
        update(UpdateArgs {
            connect_args: args.connect_args,
            image_path: app_bin,
            signing_key: String::from(paths::SERIAL_SIGNING_KEY),
            debug: false,
            protocol: "AUTO".into(),
        })?;
    }

    if args.monitor {
        let opts = MonitorOptions {
            filter: args.monitor_filter,
            raw: args.monitor_raw,
            elf_file: Some(paths::APP_ELF_DEV),
            colored: true,
            no_reset: args.no_reset || args.otw,
            dev: true,
            ..Default::default()
        };
        esp.monitor(opts, args.signing_key)?;
    }

    Ok(())
}

fn monitor(args: MonitorArgs) -> Result<()> {
    if args.filter.is_some() && args.legacy {
        bail!("legacy monitor does not support filtering");
    }

    if args.legacy {
        util::check_dependencies()?;
    }

    let elf = match args.elf_file.as_deref() {
        Some(x) => Some(x),
        None if Path::new(paths::APP_ELF_DEV).is_file() => Some(paths::APP_ELF_DEV),
        None if Path::new(paths::APP_ELF_PROD).is_file() => Some(paths::APP_ELF_PROD),
        None => None,
    };
    let esp = Esp::new(Some(&args.connect_args), None)?;
    let opts = MonitorOptions {
        filter: args.filter,
        elf_file: elf,
        no_reset: args.no_reset,
        raw: args.raw,
        colored: !args.no_color,
        dev: args.dev,
        ..Default::default()
    };
    esp.monitor(opts, args.signing_key)
}

fn prod_test(args: ProdTestArgs) -> Result<()> {
    std::thread::spawn(|| {
        std::thread::sleep(Duration::from_secs(120));

        eprintln!("timeout");
        std::process::exit(124);
    });

    let baud = args.connect_args.baud as u32;
    let port = args
        .connect_args
        .port
        .unwrap_or_else(|| util::serial_get_port().unwrap());

    let signing_key = std::fs::read(args.signing_key).context("failed to read signing key")?;
    let mut serial = SerialConnection::connect(&port, baud, &signing_key, false, false, false)
        .context("failed to establish connection")?;
    serial
        .enable_monitor()
        .context("failed to enable monitor")?;

    loop {
        match serial.read_log_message() {
            Ok(msg) if msg.message.contains("PRODUCTION_TEST_SUCCESS") => break,
            Ok(_) => {
                let mut p = stdout().lock();
                p.write_all(b".")?;
                p.flush()?;
            }
            Err(FrameError::Timeout) => continue,
            Err(e) => Err(e)?,
        }
    }

    Ok(())
}

fn read_mac(args: ReadMacArgs) -> Result<()> {
    let esp = Esp::new(Some(&args.connect_args), None)?;
    esp.read_mac(None)
}

fn read_version(args: ReadVersionArgs) -> Result<()> {
    let mut file = fs::File::open(args.path)?;
    let mut buf = [0; 80];
    file.read_exact(&mut buf)?;
    let version = util::get_version(&buf)?;
    println!("{version}");
    Ok(())
}

fn reset(args: ResetArgs) -> Result<()> {
    let esp = Esp::new(Some(&args.connect_args), None)?;
    esp.reset()
}

fn trace_decode(args: TraceDecodeArgs) -> Result<()> {
    if !Path::new(&args.elf_file).is_file() {
        bail!("ELF file does not exist: {}", args.elf_file);
    }

    let bytes = fs::read(&args.elf_file).unwrap();
    let decoder = util::TraceDecoder::new(&bytes)?;
    println!("{}", decoder.decode(&args.trace));

    Ok(())
}

fn update(mut args: UpdateArgs) -> Result<()> {
    if args.connect_args.port.is_none() {
        args.connect_args.port = Some(util::serial_get_port()?);
    };
    let port = args.connect_args.port.as_ref().unwrap();
    let baud = args.connect_args.baud as u32;

    let legacy = match args.protocol.as_str() {
        "0" => true,
        "1" => false,
        "AUTO" => util::check_for_legacy_serial_communication(port, baud)
            .context("failed to determine device version")?,
        other => bail!("invalid protocol {other}, expected `0`, `1`, or `AUTO`"),
    };

    match legacy {
        true => legacy_updater::start(args),
        false => updater::start(args),
    }
}

type WorkQueueFn<'a> = &'a dyn Fn(&ProgressBar) -> Result<()>;

struct WorkQueue<'a> {
    tasks: Vec<(String, WorkQueueFn<'a>)>,
}

impl<'a> WorkQueue<'a> {
    fn new() -> Self {
        Self { tasks: Vec::new() }
    }

    fn add(&mut self, ident: &str, fun: &'a dyn Fn(&ProgressBar) -> Result<()>) {
        self.tasks.push((ident.into(), fun));
    }

    fn execute(mut self) -> Result<()> {
        let bar = ProgressBar::new(self.tasks.len() as u64);
        let style = ProgressStyle::with_template("\x1b[36m[{posplusone}/{len}]\x1b[0m {msg:.bold}")
            .unwrap()
            .with_key(
                "posplusone",
                |state: &ProgressState, w: &mut dyn FmtWrite| {
                    write!(w, "{}", state.len().unwrap().min(state.pos() + 1)).unwrap()
                },
            );
        bar.set_style(style);

        for task in self.tasks.drain(..) {
            bar.set_message(task.0);
            (task.1)(&bar)?;
            bar.inc(1);
        }

        bar.finish_with_message("Success!");
        println!();

        Ok(())
    }
}

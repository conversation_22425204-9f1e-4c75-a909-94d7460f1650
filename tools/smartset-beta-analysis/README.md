# smartset-beta-analysis

## Usage

**1.** Run `odbcad32.exe` from Win+R prompt, go to drivers section and check if "ODBC Driver 18 for SQL Server" is installed. If it is not installed, download
the ODBC driver from [here](https://learn.microsoft.com/en-us/sql/connect/odbc/download-odbc-driver-for-sql-server?view=sql-server-ver16#download-for-windows).
Note: The installation requires elevated privileges.

**2.** Install dependencies:
```powershell
python -m venv .venv
.venv\Scripts\activate.bat   # on linux, use `. .venv/bin/activate` instead
pip install pyodbc
```

**3.** Copy `data-sample.py` to `data.py` and enter the relevant credentials.

**4.** Run script:
```powershell
python smartset-beta-analysis.py [gatewayId]
```

## Extra Utilities

- `create-report-folder.ps1`: Runs the script for a list of gateways and uses the results to create a "report" folder.
- `show-disconnects.sh`: Analyses the files in a report folder and counts up the individual disconnect reasons in total.
- `show-disconnect-details.sh`: Analyses the files in a report folder and counts up the individual disconnect reasons per device.

import datetime
import pyodbc
import sys

## CONFIGURATION ###############################################################

# gateway from `select id from gatewaybo where softwareversion like '4%'`
GATEWAYS=(668, 672, 677, 678, 679, 680, 681, 682, 683, 684, 685, 686, 687, 688, 690, 693, 698, 700, 701, 702, 707, 714, 721, 723, 725, 726, 727, 731, 736, 737, 738)
NUM_DAYS = 1
CONNECTION_BAR_WIDTH = 150
COLOUR = True

## CODE ########################################################################

def fetch(ids):
    import data
    conn = pyodbc.connect(f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                          f"SERVER={data.HOST};"
                          f"DATABASE={data.DB};"
                          f"UID={data.USER};"
                          f"PWD={data.PASS};"
                          f"TrustServerCertificate=YES")

    cursor = conn.cursor()
    cursor.execute(f"SELECT GatewayId, [Timestamp], GatewayActionCode, Message "
                   f"FROM WolfPortal.dbo.GatewayHistoryBO "
                   f"WHERE GatewayId IN {ids} "
                   #f"AND [Timestamp] >= DATEADD(day, -{NUM_DAYS}, GETUTCDATE()) "
                   f"ORDER BY [Timestamp] ASC")
    history_resp = cursor.fetchall()
    #cursor.execute(f"SELECT g.Id, g.GatewayUserName, g.SoftwareVersion, g.IsOnline, g.IsWlanConnected, u.UserName, u.Email, s.Name, g.LastDisconnect "
    cursor.execute(f"SELECT g.Id, g.GatewayUserName, g.V2Version, 1, g.IsWlanConnected, u.UserName, u.Email, s.Name, g.LastDisconnect "
                   f"FROM WolfPortal.dbo.GatewayBO g "
                   f"JOIN WolfPortal.dbo.[System] s ON s.GatewayId = g.Id "
                   f"JOIN WolfPortal.dbo.PortalUser u ON s.UserId = u.Id "
                   f"WHERE g.Id IN {ids}")
    gateway_resp = cursor.fetchall()

    data = dict()
    gateway_info = dict()
    for i in history_resp:
        gateway = i[0]
        timestamp = i[1]
        action_code = i[2]
        message = i[3]
        if gateway not in data.keys():
            data[gateway] = list()
        data[gateway].append({ "timestamp": timestamp, "action_code": action_code, "message": message })
    for i in gateway_resp:
        gateway_id = i[0]
        serial = i[1]
        version = i[2]
        is_online = i[3]
        is_wifi = i[4]
        user_name = i[5]
        email = i[6]
        system_name = i[7]
        last_disconnect = i[8]
        gateway_info[gateway_id] = { "serial": serial, "version": version, "is_online": is_online, "is_wifi": is_wifi, "user_name": user_name, "email": email, "system_name": system_name, "last_disconnect": last_disconnect }
        if gateway_id in data.keys():
            data[gateway_id].append({ "timestamp": datetime.datetime.utcnow(), "action_code": 1, "message": None })
        elif is_online:
            data[gateway_id] = [
                { "timestamp": datetime.datetime.utcnow() - datetime.timedelta(days=NUM_DAYS), "action_code": 1, "message": None },
                { "timestamp": datetime.datetime.utcnow(), "action_code": 1, "message": None }
            ]
        else:
            data[gateway_id] = [
                { "timestamp": datetime.datetime.utcnow() - datetime.timedelta(days=NUM_DAYS), "action_code": 2, "message": "???" },
                { "timestamp": datetime.datetime.utcnow(), "action_code": 1, "message": None }
            ]

    return (data, gateway_info)

def table():
    data, gateway_info = fetch(GATEWAYS)

    #print("Gateway\tOnline?\tNetwork\t#Discon\t%Online\tMaxDisc\tAvgDisc\tVersion        \tSerialNumber   \tLastDisconnect \tConTime\tUser                           \tEmail                                  \tSystemName")
    print("Gateway\tNetwork\t#Discon\t%Online\tMaxDisc\tAvgDisc\tVersion        \tSerialNumber   \tLastDisconnect \tConTime\tUser                           \tEmail                                  \tSystemName")
    print("=======\t=======\t=======\t=======\t=======\t=======\t===============\t===============\t===============\t=======\t===============================\t=======================================\t=======================================================")
    for gateway in sorted(list(data.keys())):
        if gateway not in gateway_info.keys():
            continue
        entries = data[gateway]
        total_disconnects = 0
        time_connected = 0
        time_disconnected = 0
        max_disconnect = 0
        last_time = None
        last_action = 0
        for entry in entries:
            this_time: datetime.datetime = entry["timestamp"]
            this_action = entry["action_code"]
            cutoff = datetime.datetime.utcnow() - datetime.timedelta(days=NUM_DAYS)
            if this_time >= cutoff:
                if this_action == 2:
                    total_disconnects += 1
                if last_time:
                    if last_time < cutoff:
                        last_time = cutoff
                    assert this_time >= last_time, "ORDER BY in query not working"
                    diff = (this_time - last_time).total_seconds()
                    if last_action == 2:
                        time_disconnected += diff
                        max_disconnect = max(max_disconnect, diff)
                    elif last_action == 1:
                        time_connected += diff
            last_time = this_time
            last_action = this_action
        if time_connected + time_disconnected == 0:
            time_connected_percentage = "\x1b[31m0.0%\x1b[0m" if COLOUR else "0.0%"
        elif COLOUR and time_connected / (time_connected + time_disconnected) < 0.99:
            time_connected_percentage = "\x1b[31m" + str(round(time_connected / (time_connected + time_disconnected) * 100, 1)) + "%\x1b[0m"
        else:
            time_connected_percentage = str(round(time_connected / (time_connected + time_disconnected) * 100, 1)) + "%"
        if total_disconnects == 0:
            avg_disconnect_mins = "--"
            max_disconnect_mins = "--"
        else:
            avg_disconnect_mins = str(int(time_disconnected / total_disconnects / 60)) + "m"
            if COLOUR and max_disconnect / 60 > 8:
                max_disconnect_mins = "\x1b[31m" + str(int(max_disconnect / 60)) + "m\x1b[0m"
            else:
                max_disconnect_mins = str(int(max_disconnect / 60)) + "m"
        is_wifi_value = "wifi" if gateway_info[gateway]["is_wifi"] else "eth"
        version = gateway_info[gateway]["version"]
        serial = gateway_info[gateway]["serial"]
        user = gateway_info[gateway]["user_name"]
        email = gateway_info[gateway]["email"]
        system = gateway_info[gateway]["system_name"]
        #if COLOUR and not gateway_info[gateway]["is_online"] and (datetime.datetime.utcnow() - gateway_info[gateway]["last_disconnect"]).total_seconds() / 60 / 60 > 1:
        if False:
            last_disconnect = "\x1b[31m" + gateway_info[gateway]["last_disconnect"].strftime("%y-%m-%d %H:%M") + "\x1b[0m"
        else:
            last_disconnect = gateway_info[gateway]["last_disconnect"].strftime("%y-%m-%d %H:%M")
        if COLOUR:
            is_online_value = "yes" if gateway_info[gateway]["is_online"] else "\x1b[31mno\x1b[0m"
            total_disconnects = f"\x1b[31m{total_disconnects}\x1b[0m" if total_disconnects > 2 else str(total_disconnects)
        else:
            is_online_value = "yes" if gateway_info[gateway]["is_online"] else "no"
        if gateway_info[gateway]["is_online"]:
            delta = datetime.datetime.utcnow() - gateway_info[gateway]["last_disconnect"]
            total_hours = delta.total_seconds() / 60 / 60
            days = int(total_hours / 24)
            hours = int(total_hours % 24)
            if days == 0:
                con_time = f"    {hours:2}h"
            else:
                con_time = f"{days:2}d {hours:2}h"
        else:
            con_time = ""
        #print(f"{gateway}\t{is_online_value}\t{is_wifi_value}\t{total_disconnects}\t{time_connected_percentage}\t{max_disconnect_mins}\t{avg_disconnect_mins}\t{version}\t{serial}\t{last_disconnect:15}\t{con_time}\t{user:39}\t{email:39}\t{system}")
        print(f"{gateway}\t{is_wifi_value}\t{total_disconnects}\t{time_connected_percentage}\t{max_disconnect_mins}\t{avg_disconnect_mins}\t{version:15}\t{serial}\t{last_disconnect:15}\t{con_time}\t{user:31}\t{email:39}\t{system}")

def individual(gateway_id):
    data, info = fetch("(" + str(gateway_id) + ")")
    if len(data.keys()) == 0:
        return
    data = data[gateway_id]
    info = info[gateway_id]

    print("GENERAL INFORMATION")
    print("================================================")
    print(f"User\t{info['user_name']}")
    print(f"Email\t{info['email']}")
    print(f"System\t{info['system_name']}")
    print(f"Serial\t{info['serial']}")
    print(f"Version\t{info['version']}")
    print(f"Network\t{'wifi' if info['is_wifi'] else 'eth'}")
    #print(f"Online\t{'yes' if info['is_online'] else 'no'}")
    print()

    print("CONNECTION VISUALIZATION")
    last_time = None
    last_action = 0
    for entry in data:
        this_time: datetime.datetime = entry["timestamp"]
        cutoff = datetime.datetime.utcnow() - datetime.timedelta(days=NUM_DAYS)
        if this_time >= cutoff and last_time:
            if last_time < cutoff:
                last_time = cutoff
            assert this_time >= last_time, "ORDER BY in query not working"
            diff = (this_time - last_time).total_seconds()
            length = max(1, int(diff / (NUM_DAYS * 24 * 60 * 60) * CONNECTION_BAR_WIDTH))
            if COLOUR:
                colour = "32" if last_action == 1 else "31"
                print(f"\x1b[{colour}m" + "█" * length, end="")
            else:
                print(("O" if last_action == 1 else ".") * length, end="")
        last_time = this_time
        last_action = entry["action_code"]
    if COLOUR:
        print("\x1b[0m", end="")
    print("\n")

    print("CONNECTION DATA")
    print("State  \tStartTime      \tEndTime        \tDuratio\tMessage")
    print("=======\t===============\t===============\t=======\t================================================")
    last_time = None
    last_action = 0
    last_message = None
    for entry in data:
        this_time: datetime.datetime = entry["timestamp"]
        cutoff = datetime.datetime.utcnow() - datetime.timedelta(days=NUM_DAYS)
        if this_time >= cutoff and last_time:
            if last_time < cutoff:
                last_time = cutoff
            assert this_time >= last_time, "ORDER BY in query not working"
            diff = (this_time - last_time).total_seconds()
            if COLOUR:
                colour = "\x1b[32m" if last_action == 1 else "\x1b[31m"
                reset = "\x1b[0m"
            else:
                colour = ""
                reset = ""
            conn = "CONNECT" if last_action == 1 else "DISCONN"
            last = last_time.strftime("%y-%m-%d %H:%M")
            this = this_time.strftime("%y-%m-%d %H:%M")
            diff = str(int(diff / 60)) + "m"
            msg = last_message if last_message else ""
            print(f"{colour}{conn}{reset}\t{last:15}\t{this:15}\t{colour}{diff:7}{reset}\t{msg}")
        last_time = entry["timestamp"]
        last_action = entry["action_code"]
        last_message = entry["message"]
    print(f"{reset}", end="")

if len(sys.argv) == 2:
    individual(int(sys.argv[1]))
else:
    table()

> **BELOW IS THE SPECIFICATION OF THE OLD OTW UPDATE PROTOCOL. THIS VERSION WAS USED FROM FIRMWARE**
> **VERSION 4.50.2 TO FIRMWARE VERSION 4.50.?. SINCE THEN, THIS PROTOCOL HAS BEEN REPLACED BY A**
> **NEW PROTOCOL. THIS IS LEFT HERE IN ORDER TO PRESERVE INFORMATION ABOUT THE OLD PROTOCOL.**

# Command Line Protocol

## Communication Scheme

```
+------+                       +------+
| USER |                       | LINK |
+------+                       +------+
   |                               |
   |    init                       |
   |  >------------------------>>  |
   |                               |
   |                    init ok    |
   |           challenge code A    |
   |  <<------------------------<  |
   |                               |
   |    command                    |
   |    signature A                |
   |  >------------------------>>  |
   |                               |
   |                 command ok    |
   |           challenge code B    |
   |  <<------------------------<  |
   |                               |
   |    command                    |
   |    signature B                |
   |  >------------------------>>  |
   |                               |
   |              ...              |
   |                               |
```

## Request (User → Link)

| Byte Count | Description                                  | Example    |
| ---------- | -------------------------------------------- | ---------- |
| 1          | Message ID                                   | 84         |
| 256        | Challenge code signature                     | *(bytes)*  |
| 1          | Command length *(= cmdlen)*                  | 7          |
| *cmdlen*   | Command identifier                           | "version"  |
| 1          | Argument count                               | 1          |
| 2          | Argument 1 length, big-endian *(= arg1_len)* | 8          |
| *arg1_len* | Argument 1 data                              | "customer" |
| 2          | Argument 2 length, big-endian *(= arg2_len)* |            |
| *arg2_len* | Argument 2 data                              |            |
| ...        | ...                                          |            |

## Response (Link → User)

| Byte Count | Description                 | Example   |
| ---------- | --------------------------- | --------- |
| 1          | Message ID                  | 84        |
| 16         | Next challenge code request | *(bytes)* |
| 1          | Status code ('O', 'E', 'V') | 'O'       |
| *any*      | Message                     | "4.50"    |
| 1 or 2     | Newline ("\n" or "\r\n")    | "\r\n"    |

Status code options:
- 'O': OK, command executed successfully
- 'E': Error, command failed
- 'V': Verification failed, wrong challenge code signature

## Special Commands

- `init`: No challenge code signature required. Used for obtaining the first challenge code.

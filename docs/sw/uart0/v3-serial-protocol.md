# V3 Serial Protocol

This document describes the protocol that is used for the serial interface of the WOLF Link.

## Previous Versions

- **V1**: _(up to firmware 4.50.1)_
  - Regular esp\_log output
  - Commands are sent in plain text, end of command is given by newline character
  - Command responses are sent via `println!` with delimiters `[cmdline]msg[/cmdline]` and
    `CMD_RESPONSE_DONE` to signify the end of command execution
  - OTW uses separate handling, as described in [v1-ota-via-uart-protocol.txt](./v1-ota-via-uart-protocol.txt)
  - Only the initial OTW start message is signed

- **V2**: _(firmware 4.50.2 – 4.50.?)_
  - Regular esp\_log output
  - Unification of command and OTW requests and responses
  - All messages (both commands and OTW) are signed
  - Protocol described in [v2-cmdline-protocol.md](./v2-cmdline-protocol.md)

- **V3**: _(since firmware 4.50.?)_
  - _Described in this document_
  - Uses defmt for logging
  - Uses COBS/R for message framing
  - Better separation of logs and command line / OTW

## Physical Layer

The serial interface is running on the UART0 peripheral of the ESP32. It uses a baud rate of 921600.

## Data Link Layer

Messages are framed using COBS/R (Consistent overhead byte stuffing – Reduced). At the end of every
message, a zero byte is transmitted to delimit the individual frames.

Examples for message framing:

| Raw message       | Encoded (incl. zero byte) |
| ----------------- | ------------------------- |
| 2F A2 00 92 73 02 | 03 2F A2 04 92 73 02 00   |
| 2F A2 00 92 73 26 | 03 2F A2 26 92 73 00      |

## Network Layer

The first byte of the message indicates the type of message. Other messages are ignored.

| Byte | Type              | Direction        |
| ---- | ----------------- | ---------------- |
| 0x2A | Log message       | only link → tool |
| 0x45 | Command line      | duplex           |
| 0x89 | OTW               | duplex           |
| 0xF6 | Bad COBS/R format | only link → tool |

The last kind, "Bad COBS/R format" is sent by the WOLF Link without any additional data when it
received a frame with invalid COBS/R encoding, likely due to a communication error.

## Application Layer

### Log Messages

The log messages are encoded using defmt.

### Command Line and OTW

#### Communication Scheme

```
+------+                       +------+
| USER |                       | LINK |
+------+                       +------+
   |                               |
   |    init                       |
   |  >------------------------>>  |
   |                               |
   |                    init ok    |
   |           challenge code A    |
   |  <<------------------------<  |
   |                               |
   |    command                    |
   |    signature A                |
   |  >------------------------>>  |
   |                               |
   |                 command ok    |
   |           challenge code B    |
   |  <<------------------------<  |
   |                               |
   |    command                    |
   |    signature B                |
   |  >------------------------>>  |
   |                               |
   |              ...              |
   |                               |
```

#### Request (User → Link)

| Byte Count | Description                                  | Example    |
| ---------- | -------------------------------------------- | ---------- |
| 1          | Message ID                                   | 84         |
| 256        | Challenge code signature                     | *(bytes)*  |
| 1          | Command length *(= cmdlen)*                  | 7          |
| *cmdlen*   | Command identifier                           | "version"  |
| 1          | Argument count                               | 1          |
| 2          | Argument 1 length, big-endian *(= arg1_len)* | 8          |
| *arg1_len* | Argument 1 data                              | "customer" |
| 2          | Argument 2 length, big-endian *(= arg2_len)* |            |
| *arg2_len* | Argument 2 data                              |            |
| ...        | ...                                          |            |

The challenge code signature uses PKCS#1 (version 1.5) with an RSA key and SHA-256.

When the challenge code signature is invalid, a "verification failed" response is given. The
exception to this is the "init" command, which is used to initialise the secure connection. It
ignores the signature and returns OK with the next challenge code.

#### Response (Link → User)

| Byte Count | Description                 | Example   |
| ---------- | --------------------------- | --------- |
| 1          | Message ID                  | 84        |
| 16         | Next challenge code request | *(bytes)* |
| 1          | Status code                 | 'O'       |
| *any*      | Message                     | "4.50"    |

The following status codes are valid:

| Status Code | Description         |
| ----------- | ------------------- |
| O           | OK / success        |
| E           | error               |
| V           | verification failed |

## Example

```
Command:                      n v s - s e t          p a s s w o r d       1 2 3 4

Bytes:                        6e76732d736574         70617373776f7264      31323334

Layer 7:       01 01020304 07 6e76732d736574 02 0008 70617373776f7264 0004 31323334
               |  |        |  |              |  |    |                |    |
               |  |        |  command        |  |    arg1             |    arg2
               |  |        command length    |  arg1 length           arg2 length
               |  signature                  argument count
               command id

Layer 3:    45 01 01020304 07 6e76732d736574 02 0008 70617373776f7264 0004 31323334
            |  +-------------------------------------------------------------------
            |  |
            |  command data
            target identifier

Layer 2: 16 45 01 01020304 07 6e76732d736574 02 1008 70617373776f7264 3404 31323300
         ==                                     ==                    ==         ==
```

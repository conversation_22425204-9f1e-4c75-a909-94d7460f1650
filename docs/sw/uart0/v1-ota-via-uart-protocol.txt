************************************************************************
*** BELOW IS THE SPECIFICATION OF THE OLD OTW UPDATE PROTOCOL. THIS  ***
*** V<PERSON>SION WAS USED UP TO FIRMWARE VERSION 4.50.1. SINCE THEN, THIS ***
*** PROTOCOL HAS BEEN REPLACED BY A NEW PROTOCOL, MORE TIGHTLY       ***
*** INTEGRATED INTO THE COMMAND PROTOCOL. THIS IS LEFT HERE IN ORDER ***
*** TO PRESERVE INFORMATION ABOUT THE OLD OTW UPDATE PROTOCOL.       ***
************************************************************************



                      OTA Updates via UART Protocol


1. Communication

   The communication uses 921600 Baud.  Messages start with the magic
   value "OTW" (= "Over The Wire"), followed by the function code and
   data.


1.1. Requests

   Request messages end with a 16-bit CRC, followed by a newline
   character (`\n`).  The CRC uses the polynom 0x1021 together with the
   initial value 0xFFFF.  The high byte of the CRC is transmitted first.

   When either `\n`, `\r` or 0xFF is contained in the data, it needs to
   be escaped by prepending it with a 0xFF byte.

   Example Request:
     [ 'O', 'T', 'W', 'V', ..., <crc_hi>, <crc_lo>, '\n' ]


1.2. Responses

   The device responds to requests by repeating the same function code
   as it received in the request.  When the execution of the request
   succeeded, "ok" and an optional status message will be appended.
   In case of an error, "err" with an optional error message will be
   appended.  The parts of the message (preamble, status, message) are
   delimited by spaces.

   Example Response: "OTWS err unexpected ota start message\n"

   The response does not include a CRC.  It is terminated with a
   newline.

   The responses are logged via esp-idf-svc's logger. Thus, additional
   text is added from the logger. Responses then take the form of the
   following example:

     "\x1b[0;32mI (12345) otw: OTWS err unexpected ota start message\x1b[0m\n"

   TODO: we should find a way to do this without the additional garabge
   added by esp-idf-svc.


2. Messages

2.1. Start

   Code: S  (=> preamble "OTWS")

   Data: none

   Responses:

     - "OTWS ok <challenge>":
       Update started successfully, proceed to signing the challenge
       code provided by <challenge>.

     - "OTWS err unexpected ota start message"
       The update cannot be started since there currently is an update
       in progress.  The update can be cancelled using an update cancel
       request in order to start a new update.

     - "OTWS err ..."
       Other error.


2.2. Verify

   Code: V  (=> preamble "OTWV")

   Data:
     - combined length of version and challenge code (u8)
     - challenge code (u8[32])
     - version (u8[])
     - signature of length, challenge code and version (u8[])

   Responses:

     - "OTWV ok":
       Signature verified, update started successfully, proceed to
       writing data.

     - "OTWV err incorrect signature"
       Failed to verify the signature.

     - "OTWV err ..."
       Other error.

   In addition to the regular responses, the device will send an ACK
   response ("OTWV err ack")  b e f o r e  the actual response. The
   reasoning behind this is as follows:

   Starting the update can take quite some time (about 15 seconds),
   since it also validates some partitions. In order to inform the
   update tool that the device actually received the request, an ACK is
   sent. This allows it to quickly resend a new OTA start request when
   the device does not respond without waiting a long timeout that is
   required for the "update started" response to arrive.


2.3. Write

   Code: W  (=> preamble "OTWW")

   Data:
     - chunk id (u16, high byte first)
     - len (u16, high byte first)
     - data (u8[], data.len() == len)

   Responses:

     - "OTWW ok <chunk_id>":
       The chunk with id `chunk_id` was written successfully.

     - "OTWW err crc <chunk_id>":
       The device detected an incorrect message via CRC.  The chunk with
       id `chunk_id` needs to be retried.

     - "OTWW err retry <chunk_id>":
       The device received a chunk with a different ID than the one it
       expected.  The chunk with id `chunk_id` needs to be retried.

     - "OTWW err ...":
       Other error.


2.4. Finish

   Code: F  (=> preamble "OTWF")

   Data: none

   Responses:

     - "OTWF ok":
       The update was completed successfully and the device reboots.

     - "OTWF err ...":
       The update did not complete successfully.


2.5. Cancel

   Code: C  (=> preamble "OTWC")

   Data: none

   Responses:

     - "OTWC ok":
       The ongoing update was cancelled, or there was no ongoing update.

     - "OTWC err ...":
       Failed to cancel the update.

Logs in the link firmware should adhere to the following guidelines:

## Level definitions

* **ERROR** – error that is not expected in the happy path
* **WARN** – error that is expected during normal operation but could indicate an issue
* **INFO** – high-level state change or milestone
* **DEBUG** – detailed event useful when debugging
* **TRACE** – extremely fine-grained detail, tends to flood output

---

## Message grammar

1. **Start** with a **single, capitalized, imperative verb**
2. **Then** the **resource** or **action target**
3. **Append** any **key=value** pairs (comma-separated if many)
4. **No trailing period**, **one sentence only**

---

## Examples

| Level | Message                              |
| :---: | :----------------------------------- |
| ERROR | Failed to connect to ssid=blahblu    |
| WARN  | Retrying request attempt=2, max=3    |
| INFO  | Connected ssid=blahblu               |
| DEBUG | Read info num info=10323, value=0xFF |
| TRACE | Received byte=0xA5                   |

Messages should not include the domain, which is instead contained in the logging target:

```
[ebus] Retrying request attempt=2, max=3
```

## Informationsquellen

* Systemerkennung (CHA / FHA)
    * eBus-Addresse: 0x08
    * Device ID:
        * 0x29 (CHA)
        * 0x40 (FHA)
* Info "PV/EVU oder SG" 13016
    * 0 => PV/EVU (default)
    * 1 => SG (SG-Ready beinhaltet die EVU-Funktionalität)
* Info "Status PV" 10815
    * 0 => Aus (keiner weiß was das bedeutet => Normalbetrieb)
    * 1 => Normalbetrieb (PV und EVU im Normalbetrieb)
    * 2 => EVU-Sperre (PV = Normalbetrieb)
    * 3 => Einschaltempfehlung (hier nicht anwendbar)
    * 4 => Einschaltbefehl (PV Anhebung aktiv, EVU Normalbetrieb)
    * TODO: Werte korrigieren (einer ungültig)
* Info "EVU-Modus" (bzw. SG-Sperremodus) 13106
    * 0 => Aus (Einstellung: falls EVU Signal, WP aus)
    * 1 => Auto (Einstellung: Reduktion auf x% der Geräteleistung)
    * 2 => Wert (Einstellung: Reduktion auf Wert nach 13107)
* Info "EVU-Wert" 13107
    * Wertbereich 0-200 -> fixed point, 1 Nachkomma
* Info "Status SG" 10800
    * 0 => Aus
    * 1 => Normalbetrieb
    * 2 => SG-Sperre (funktioniert EVU-Sperre)
    * 3 => Einschaltempfehlung
    * 4 => Einschaltbefehl
* EEBUS use cases?

## Events

PV = Photovoltaic
EVU = Enerverorgungsunternehmen
SG = SmartGrid

PV Normalbetrieb
PV Einschaltbefehl
EVU Normalbetrieb 
EVU Limitierung {inkl. Wert}
EVU Sperre Automatik nach Nennleistung
EVU Sperre
SG Normalbetrieb
SG Einschaltempfehlung
SG EVU Limitierung {inkl. Wert}
SG EVU Sperre Automatik nach Nennleistung
SG EVU Sperre
SG Einschaltbefehl
EEBUS LPC Normalbetrieb
EEBUS LPC Leistungsreduzierung {inkl. Wert aus EEBus Vorgabe}
EEBUS OHPCF Normalbetrieb
EEBUS OHPCF Einschaltbefehl {mit Zeitstempel (00:00 = sofort)}
EEBus Gerät verbunden {Name/SKI von Gerät}
EEBus Gerät getrennt {Name/SKI von Gerät}

## What is included in events

- energy events

## What is not included in events



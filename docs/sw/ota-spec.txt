FIRMWARE UPDATE REQUEST
=======================

Smartset sends a firmware-update-request with telegram type 18:

  <firmware-update-request>
    <version>4.05.2</version>
    <security-version>0</security-version>
    <size>1748992/size>
    <stream>1</stream>
  </firmware-update-request>

The field `stream` is expected to be set to 1.

For ESP32/esp-idf applications, both `version` and `security-version`
can be read directly from the binary data of the firmware image.  The
following table shows the layout of the binary including image headers
and the application description.  The notation for ranges (x..y)
considers both x and y to be inclusive.  Fields that might be relevant
are marked with an arrow.  Note that everything is encoded as little-endian,
meaning that a uint32_t value of 0x12345678 will be stored as four bytes in the
order 0x78, 0x56, 0x34, 0x12.

   Bytes    | Data Type                  | Usage
  ----------+----------------------------+---------------------
     0.. 23 | esp_image_header_t         | Image header
    24.. 31 | esp_image_segment_header_t | DROM segment header
    32.. 35 | uint32_t                   | Magic word
    36.. 39 | uint32_t                   | Secure version        <==
    40.. 47 | uint32_t[2]                | Reserved
    48.. 79 | char[32]                   | Application version   <==
    80.. 95 | char[16]                   | Compile time          <==
    96..111 | char[16]                   | Compile date          <==
   112..143 | char[32]                   | ESP-IDF version
   144..175 | uint8_t[32]                | sha256 of elf file
   176..255 | uint32_t[20]               | Reserved



FIRMWARE UPDATE RESPONSE
========================

The firmware update response uses the telegram type 19.

If the device is currently being updated (either via OTA or locally via
UART), the OTA update will be cancelled and the the following response
is sent to Smartset:

  <firmware-update-response>
    <intent>REJECT</intent>
    <reject-reason>BUSY</reject-reason>
  </firmware-update-response>

If the security version specified in the firmware update request is
smaller than the security version of the currently running firmware on
the deviec, the OTA update will be cancelled and the following response
is sent to Smartset:

  <firmware-update-response>
    <intent>REJECT</intent>
    <reject-reason>SECURITY</reject-reason>
  </firmware-update-response>

If the device has OTA updates disabled, the OTA update will be cancelled
and the following response is sent:

  <firmware-update-response>
    <intent>REJECT</intent>
    <reject-reason>UPDATES_DISABLED</reject-reason>
  </firmware-update-response>

If the stream id specified in the firmware update request is invalid
(currently only the value 1 is considered valid), the OTA update is
cancelled and the following response is sent:

  <firmware-update-response>
    <intent>REJECT</intent>
    <reject-reason>BAD_STREAM_ID</reject-reason>
  </firmware-update-response>

If the device encounters an internal error, the OTA update is cancelled
and the following response is sent:

  <firmware-update-response>
    <intent>REJECT</intent>
    <reject-reason>INTERNAL_ERROR</reject-reason>
  </firmware-update-response>

Otherwise (if the OTA update could be started successfully), the
following response is returned.  The device is now waiting for binary
data to be sent.  The field `chunk-size` informs Smartset about what
chunk size the device expects.  In practice, it does not matter how big
the chunks sent to the device are: only the total size is used to
determine if an update was successful.  However, the chunk size should
orient itself to the TCP maximum segment size (MSS), which is 1460
bytes.  Considering how the binary data telegrams are structured, this
leaves a chunk size of 1450 bytes.

  <firmware-update-response>
    <intent>ACCEPT</intent>
    <chunk-size>1450</chunk-size>
  </firmware-update-response>



BINARY DATA TELEGRAMS
=====================

The transmission of binary data chunks from Smartset to the device and
from device to Smartset uses telegram type 14.  The contents of the
telegram are as follows, with the range notation (x..y) being the same
as above.

   Bytes | Data Type | Usage
  -------+-----------+----------------------------
    0..3 | uint32_t  | Length of payload
    4..5 | uint16_t  | Message type (= 14)
    6    | uint8_t   | Stream id (= 1)
    7    | uint8_t   | Type
    8..9 | uint16_t  | Chunk id (high byte first)
   10..  | uint8_t[] | Binary Data

The type can accept the following values:

   Type | Name           | Direction        | Usage
  ------+----------------+------------------+-----------------------------
      1 | SEND           | Smartset -> Link | Send data bytes to device
      2 | ACK            | Link -> Smartset | Data processed successfully
      3 | ABORT_DEVICE   | Link -> Smartset | Device aborted OTA update
      5 | ABORT_SMARTSET | Smartset -> Link | Smartset aborted OTA update

When SEND is specified as a type, binary data is expected to be present
in the telegram.  For all other types, the binary data is expected to be
empty.

Smartset will send data bytes with type = SEND.  The chunk id will be
increased sequentially: the first chunk has chunk id 0, the second one
has chunk id 1, and so on.

When the device receives a binary data telegram with type = SEND, it
will store the received binary data and respond with an acknowledgement.
The acknowledgement is a binary data telegram with type = ACK and the
chunk id set to the id of the received chunk, unless the chunk was the
last chunk of the update binary.  The very first chunk must be at least 80
bytes long in order to allow for the version check to work.

If an error occurs during that process, a binary data telegram with type
= ABORT_DEVICE will be sent.  The chunk id is set to 0xFFFF, but can be
ignored.  Possible errors include:

  - OTA update not started yet
  - OTA update interrupted (e.g. by a local update via UART)
  - Incorrect chunk id
  - Error while storing chunk
  - More data received than promised in firmware update request

When the exact number of bytes specified in the firmware update request
is received, the device will finish the update and respond with a
firmware update completion status telegram (see next section).

When the device receives a binary data telegram with type =
ABORT_SMARTSET, it will cancel the update and respond with a binary data
telegram of type = ACK and chunk id = 0xFFFF (chunk id can be ignored).

When the device receives either an ACK or an ABORT_DEVICE (message types
which are not supposed to be sent by Smartset), an ABORT_DEVICE will be
sent back.

When Smartset receives an ABORT_DEVICE message, the OTA update will be
aborted.  When Smartset receives an ACK message as a response to a SEND
message, the next chunk will be sent.



FIRMWARE UPDATE COMPLETION STATUS
=================================

When the last chunk of the firmware is transmitted, the device will not
respond with a binary data telegram of type = ACK.  Instead, it will
respond with a firmware update completion status telegram.  This type of
telegram uses the telegram type 20.

When the update succeeded, the following telegram will be sent:

  <firmware-update-completion-status>
    <status>SUCCESS</status>
  </firmware-update-completion-status>

Otherwise, the following telegram will be sent:

  <firmware-update-completion-status>
    <status>FAILURE</status>
    <message>(error message)</message>
  </firmware-update-completion-status>

Additionally, the following response is reserved in case the update gets
aborted.  This is currently not implemented, but should be treated the
same way as a FAILURE response.

  <firmware-update-completion-status>
    <status>ABORT</status>
    <message>(error message)</message>
  </firmware-update-completion-status>

When the update is finished, the new firmware is ready to be booted,
but the old (current) firmware will run until the device restarts.

The device can be restarted at the server's discretion to avoid
downtime when user interaction is expected. To issue a restart, Smartset
sends telegram 21 with the following XML:

  <restart />

In the case that the firmware update completion status
telegram is not transmitted (e.g. due to loss of connection between
receiving the last chunk and sending the completion status telegram),
Smartset will still need to be able to detect if the firmware update was
successful by evaluating the portal-logon-request telegram that is sent
upon restart of the device. Similarly, Smartset also needs to detect if the
newly flashed firmware was able to boot even if the status already signalled
success.

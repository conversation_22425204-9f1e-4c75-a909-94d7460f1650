NVS KEY           TYPE     PERM  SET  CATEGORY       USAGE                            DEFAULT BEHAVIOUR
----------------  -------  ----  ---  -------------  -------------------------------  ----------------------------------
ap_authmethod     u8       rw         access point   authorization method             7 (WPA2WPA3Personal)
ap_channel        u8       rw         access point   wifi channel                     1
ap_dhcp           bool     rw         access point   DHCP support                     true
ap_dns1           ipv4     rw         access point   primary DNS server               *******
ap_dns2           ipv4     rw         access point   secondary DNS server             *******
ap_enabled        bool     rw         access point   enabled                          true
ap_gateway        ipv4     rw         access point   gateway                          ***********
ap_mask           u8       rw         access point   subnet mask                      24
ap_ssid           str<32>  rw         access point   ssid                             WOLFLINKR
ca_cert           blob     --    y    smartset       CA certificate
client_cert       blob     --    y    smartset       client certificate
client_key        blob     --    y    smartset       client private
eebus_cert        blob     --         eebus          eebus certificate
eebus_enabled     bool     rw         eebus          whether eebus is enabled         false        
eebus_key         blob     --         eebus          eebus certificate
error_history     blob     rw         persistence    store ebus fault messages        
factory_pass      str<64>  r-    y    misc           factory password                 Wolf84048
net_dhcp          bool     rw         netif          DHCP enabled                     true
net_dns1          ipv4     rw         netif (fixed)  primary DNS server               *******
net_dns2          ipv4     rw         netif (fixed)  secondary DNS server             *******
net_gateway       ipv4     rw         netif (fixed)  gateway                          ***********
net_hostname      str<30>  rw         netif (dhcp)   hostname                         WOLFLINK
net_ip            ipv4     rw         netif (fixed)  static IP address                ************
net_mask          u8       rw         netif (fixed)  subnet mask                      24
ota_disabled      bool     rw         misc           ota updates disabled             false
password          str<64>  rw         misc           custom password                  (fallback to factory_pass)
portal_enabled    bool     rw         smartset       portal connection enabled        false
smartset_host     str<58>  rw         smartset       server hostname                  beta.wolf-smartset.com
smartset_port     u16      rw         smartset       server port                      56154
system_name       str<32>  rw         smartset       system name                      Wolf Link
test_mode         bool     rw         test           temporary test state             false
test_mode_pass    str<64>  rw         test           test mode wifi passphrase
test_mode_ssid    str<32>  rw         test           test mode wifi SSID
tls_skip_cn       bool     rw         tls            skip common name verification    false
wifi_authmethod   u8       rw         wifi           authorization method             0
wifi_pass         str<64>  rw         wifi           passphrase
wifi_ssid         str<32>  rw         wifi           SSID

Notes:
 * PERM = User permissions: whether the user is allowed to read or write the value, e.g. via command line. The restrictions do not apply to the system itself.
 * SET = Whether the entry is already set after the initial flash of the device.


CUSTOM TYPE  INTERNAL REPR
-----------  -------------
ipv4         u32

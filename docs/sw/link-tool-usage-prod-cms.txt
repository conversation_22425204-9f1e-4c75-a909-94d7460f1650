LINK-TOOL USAGE FOR PRODUCTION
==============================

1. Ensure that bootloader, partition table and firmware binaries are
   available.  They should be put in the following locations:

     - bootloader:
       partitions\prod\bootloader.bin

     - partition table:
       partitions\prod\partition-table.bin

     - firmware:
       images\esp-firmware-prod.bin

   Also ensure that the `sec\certs\portal\pool` folder contains enough TLS
   certificates and private keys.  Certificates and private keys can only be
   used once and will be deleted after use.

2. Open powershell or cmd.exe in the link-tool folder.

3. Ensure that the device is in flash mode.  When using properly
   connected ESP-Prog, this happens automatically.  Otherwise, the EN
   pin needs to be pulled low while GPIO0 is also pulled low.

4. Enter the following command:

     .\link-tool.exe deploy --prod --cert AUTO images\esp-firmware-prod.bin

   During the flashing process, the serial number and factory password
   will be printed to the terminal.  In the case that this information
   got lost, both can be recovered after step 5 using the following
   commands.  This requires that the device completed its initial boot.

     - Read serial number:
       .\link-tool.exe cmd "read-mac"

     - Read factory password:
       .\link-tool.exe cmd "nvs-get factory_pass"

5. Boot the device and check if it is working:

     .\link-tool.exe monitor

   The monitor can be closed with Ctrl+C.  If an ESP-Prog is used for
   communication, the device will boot automatically.  Otherwise, the
   EN pin needs to be pulled low.

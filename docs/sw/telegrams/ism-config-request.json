{"$schema": "http://json-schema.org/draft-07/schema#", "title": "ISM Configuration Request", "description": "Request to read or modify Link configuration", "type": "object", "xml": {"name": "ism-config-request"}, "required": ["sid"], "properties": {"sid": {"type": "integer", "description": "Session ID", "xml": {"attribute": true}}, "dhcp-active": {"type": "boolean", "description": "DHCP enabled status", "xml": {"name": "dhcp-active"}}, "ip-address": {"type": "string", "description": "IP address", "format": "ipv4", "xml": {"name": "ip-address"}}, "subnetmask": {"type": "boolean", "description": "Subnet mask", "xml": {"name": "subnetmask"}}, "default-gateway": {"type": "string", "description": "Default gateway IP address", "format": "ipv4", "xml": {"name": "default-gateway"}}, "dns-name-server": {"type": "string", "description": "DNS server IP address", "format": "ipv4", "xml": {"name": "dns-name-server"}}, "dest-server": {"type": "string", "description": "Destination server address", "xml": {"name": "dest-server"}}, "dest-port": {"type": "boolean", "description": "Destination port", "xml": {"name": "dest-port"}}, "installationname": {"type": "string", "description": "System name", "xml": {"name": "installationname"}}, "connect-portal": {"type": "boolean", "description": "Portal connection enabled status", "xml": {"name": "connect-portal"}}}}
{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Event Push", "description": "Push events to Smartset with event data", "type": "object", "xml": {"name": "event-push"}, "properties": {"event-push": {"type": "object", "properties": {"reset": {"type": "boolean", "description": "Indicates that the event history has been reset and the last known event ID is no longer valid", "xml": {"attribute": true}}, "event": {"type": "array", "description": "List of events to push", "xml": {"name": "event"}, "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "Unique event ID (monotonically increasing, starting from 1)", "minimum": 1, "xml": {"attribute": true}}, "type": {"type": "string", "description": "Event type identifier", "enum": ["pv-normal", "pv-reduction", "evu-normal", "evu-limitation", "evu-lock-auto", "evu-lock", "sg-normal", "sg-recommendation", "sg-limitation", "sg-lock-auto", "sg-lock", "sg-order", "eebus-lpc-normal", "eebus-lpc-reduction", "eebus-ohpcf-normal", "eebus-ohpcf-power", "eebus-device-added", "eebus-device-removed"], "xml": {"attribute": true}}, "payload": {"description": "Optional JSON payload (can be a number, string, or object)", "oneOf": [{"type": "number"}, {"type": "string"}, {"type": "object"}], "xml": {"text": true}}}, "required": ["id", "type"], "additionalProperties": false}}, "additionalProperties": false}}}, "required": ["event-push"], "additionalProperties": false, "examples": [{"event-push": {"event": [{"id": 12346, "type": "pv-normal"}, {"id": 12347, "type": "evu-normal"}]}}, {"event-push": {"event": [{"id": 12346, "type": "evu-limitation", "payload": 1000}, {"id": 12347, "type": "something-happened", "payload": {"foo": "bar"}}]}}, {"event-push": {"reset": true, "event": [{"id": 1, "type": "pv-normal"}]}}]}
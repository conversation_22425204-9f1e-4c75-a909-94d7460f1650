{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Firmware Update Completion Status", "description": "Status of firmware update completion", "type": "object", "xml": {"name": "firmware-update-completion-status"}, "required": ["status"], "properties": {"status": {"type": "string", "description": "Update completion status", "enum": ["SUCCESS", "FAILURE", "ABORT"], "xml": {"name": "status"}}, "message": {"type": "string", "description": "Message used for errors", "xml": {"name": "message"}}}}
{"$schema": "http://json-schema.org/draft-07/schema#", "title": "System Configuration Request", "description": "Request to retrieve system configuration", "type": "object", "xml": {"name": "read-systemconfig-request"}, "required": ["sid"], "properties": {"sid": {"type": "integer", "description": "Session ID for tracking the connection", "xml": {"attribute": true}}, "system-config-addresses": {"type": "string", "description": "Optional list of addresses to include in the configuration", "xml": {"name": "system-config-addresses"}}}}
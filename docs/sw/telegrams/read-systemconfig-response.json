{"$schema": "http://json-schema.org/draft-07/schema#", "title": "System Configuration Response", "description": "Response with connected device information", "type": "object", "xml": {"name": "read-systemconfig-response"}, "required": ["sid", "gateway", "busconfig"], "properties": {"sid": {"type": "integer", "description": "Session ID for authentication", "xml": {"attribute": true}}, "gateway": {"type": "object", "description": "Gateway information", "xml": {"name": "gateway"}, "required": ["type", "softwareNumber", "softwareVersion", "wlan", "g3"], "properties": {"type": {"type": "string", "description": "Gateway type (Link)", "xml": {"attribute": true}}, "softwareNumber": {"type": "integer", "description": "Software number", "xml": {"attribute": true}}, "softwareVersion": {"type": "integer", "description": "Software version", "xml": {"attribute": true}}, "wlan": {"type": "boolean", "description": "WLAN connected", "xml": {"attribute": true}}, "g3": {"type": "boolean", "description": "Mobile data connected", "xml": {"attribute": true}}}}, "errormsg": {"type": "string", "description": "Optional error message", "xml": {"name": "errormsg"}}, "busconfig": {"type": "array", "description": "eBus configuration", "xml": {"name": "busconfig", "wrapped": true}, "items": {"type": "object", "required": ["type", "busDevices"], "properties": {"type": {"type": "string", "description": "Bus type (ebus or wbus)", "enum": ["ebus", "wbus"], "xml": {"attribute": true}}, "busDevices": {"type": "object", "xml": {"name": "busDevices"}, "properties": {"busDevice": {"type": "array", "xml": {"name": "busDevice"}, "items": {"type": "object", "required": ["ba", "sv", "sr", "cfg", "did"], "properties": {"ba": {"type": "string", "description": "Bus address (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "sv": {"type": "string", "description": "Software version (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "sr": {"type": "string", "description": "Software revision (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "cfg": {"type": "string", "description": "Configuration (hex)", "pattern": "^0x[0-9A-Fa-f]{1,4}$", "xml": {"attribute": true}}, "did": {"type": "string", "description": "Device ID (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}}}}}}}}}, "modbusconfig": {"type": "object", "description": "Modbus configuration", "xml": {"name": "modbusconfig"}, "properties": {"busDevices": {"type": "object", "xml": {"name": "busDevices"}, "properties": {"modBusDevice": {"type": "array", "xml": {"name": "modBusDevice"}, "items": {"type": "object", "required": ["ba"], "properties": {"ba": {"type": "string", "description": "Bus address (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "sv": {"type": "string", "description": "Software version (hex)", "pattern": "^0x[0-9A-Fa-f]{1,4}$", "xml": {"attribute": true}}, "ty": {"type": "string", "description": "Device type (hex)", "pattern": "^0x[0-9A-Fa-f]{1,4}$", "xml": {"attribute": true}}}}}}}}}}}
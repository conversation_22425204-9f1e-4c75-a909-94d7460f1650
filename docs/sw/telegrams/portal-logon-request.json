{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Portal Logon Request", "description": "Encrypted authentication request to Portal", "type": "object", "xml": {"name": "portal-logon-request"}, "required": ["sid", "installationname", "passwd", "serialnumber", "date-time", "ism-softwareversion", "ism-softwarenumber", "ism-hardwareversion", "wlan-connected", "type", "v2-version", "v2-commit-hash", "v2-disconnect-reason"], "properties": {"rnd": {"type": "integer", "description": "Random number for legacy security considerations", "xml": {"attribute": true}}, "installationname": {"type": "string", "description": "System name", "xml": {"name": "installationname"}}, "passwd": {"type": "string", "description": "Authentication password (default password characteristics: alphanumeric, length 12)", "xml": {"name": "passwd"}}, "serialnumber": {"type": "string", "description": "Device serial number (MAC address)", "xml": {"name": "serialnumber"}}, "date-time": {"type": "string", "description": "Current date and time", "format": "date-time", "xml": {"name": "date-time"}}, "ism-softwareversion": {"type": "integer", "description": "ISM software version", "xml": {"name": "ism-softwareversion"}}, "ism-softwarenumber": {"type": "integer", "description": "ISM software number", "xml": {"name": "ism-softwarenumber"}}, "ism-hardwareversion": {"type": "string", "description": "Hardware version, e.g. linkr-4", "xml": {"name": "ism-hardwareversion"}}, "wlan-connected": {"type": "boolean", "description": "WLAN connection status", "xml": {"name": "wlan-connected"}}, "type": {"type": "string", "description": "Device type", "xml": {"name": "type"}}, "v2-version": {"type": "string", "description": "Version 2 full version", "xml": {"name": "v2-version"}}, "v2-commit-hash": {"type": "string", "description": "Version 2 commit hash", "xml": {"name": "v2-commit-hash"}}, "v2-test": {"type": "boolean", "description": "Test mode flag", "xml": {"name": "v2-test"}}, "v2-dev": {"type": "boolean", "description": "Development mode flag", "xml": {"name": "v2-dev"}}, "v2-disconnect-reason": {"type": "string", "description": "Reason for previous disconnection", "xml": {"name": "v2-disconnect-reason"}}}}
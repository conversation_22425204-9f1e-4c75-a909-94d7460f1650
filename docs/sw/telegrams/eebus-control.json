{"$schema": "http://json-schema.org/draft-07/schema#", "title": "EEBUS Control", "description": "Control EEBUS functionality and device trust", "type": "object", "xml": {"name": "eebus-control"}, "properties": {"eebus-control": {"type": "object", "properties": {"set-trust": {"type": "object", "xml": {"name": "set-trust"}, "properties": {"ski": {"type": "string", "description": "Subject Key Identifier of the device to change trust status (40 hex characters)", "pattern": "^[0-9a-fA-F]{40}$", "xml": {"attribute": true}}, "status": {"type": "string", "description": "New trust status to set for the device", "enum": ["TRUSTED", "DISTRUSTED", "PENDING"], "xml": {"attribute": true}}}, "required": ["ski", "status"], "additionalProperties": false}, "set-pin": {"type": "object", "xml": {"name": "set-pin"}, "properties": {"ski": {"type": "string", "description": "Subject Key Identifier of the device to set PIN for (40 hex characters)", "pattern": "^[0-9a-fA-F]{40}$", "xml": {"attribute": true}}, "pin": {"type": "string", "description": "PIN to set for the device", "xml": {"attribute": true}}}, "required": ["ski", "pin"], "additionalProperties": false}, "state": {"type": "string", "description": "Control the EEBUS functionality", "enum": ["on", "off"], "xml": {"name": "state"}}}, "additionalProperties": false}}, "required": ["eebus-control"], "additionalProperties": false, "examples": [{"eebus-control": {"set-trust": {"ski": "abcdef1234567890abcdef1234567890abcdef12", "status": "TRUSTED"}}}, {"eebus-control": {"set-pin": {"ski": "abcdef1234567890abcdef1234567890abcdef12", "pin": "123456"}}}, {"eebus-control": {"state": "on"}}]}
{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Direct Logon Request", "description": "Local authentication request with password", "type": "object", "xml": {"name": "direct-logon-request"}, "required": ["password"], "properties": {"Type": {"type": "integer", "description": "Identifier used for smart home devices (wibutler). Not set for local Smartset connection.", "xml": {"attribute": true}}, "Version": {"type": "integer", "description": "Version of smart home device (wibutler). Not set for local Smartset connection.", "xml": {"attribute": true}}, "password": {"type": "string", "description": "Authentication password - Link password or smart home specific password", "xml": {"name": "password"}}}}
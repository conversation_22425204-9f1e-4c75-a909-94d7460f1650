{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Event Subscription", "description": "Subscribe to events with last known event ID", "type": "object", "xml": {"name": "event-subscription"}, "required": ["last-id"], "properties": {"last-id": {"type": "integer", "description": "Last known event ID. The Wolf Link will send all events with a higher ID. Use 0 if no events are known.", "minimum": 0, "xml": {"attribute": true}}}, "additionalProperties": false, "examples": [{"last-id": 12345}, {"last-id": 0}]}
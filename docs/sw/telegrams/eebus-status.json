{"$schema": "http://json-schema.org/draft-07/schema#", "title": "EEBUS Status", "description": "Status of EEBUS and connected devices", "type": "object", "xml": {"name": "eebus-status"}, "properties": {"eebus-status": {"type": "object", "properties": {"state": {"type": "string", "description": "Overall EEBUS state", "enum": ["on", "off"], "xml": {"attribute": true}}, "id": {"type": "string", "description": "SHIP ID, needed for QR code", "xml": {"attribute": true}}, "device": {"type": "array", "description": "List of EEBUS devices", "xml": {"name": "device"}, "items": {"type": "object", "properties": {"ski": {"type": "string", "description": "Subject Key Identifier of the device (40 hex characters)", "pattern": "^[0-9a-fA-F]{40}$", "xml": {"attribute": true}}, "ip": {"type": "string", "description": "IP address of the device", "xml": {"attribute": true}}, "status": {"type": "string", "description": "Trust status of the device", "enum": ["TRUSTED", "DISTRUSTED", "PENDING", "CONNECTED", "PIN_REQUIRED"], "xml": {"attribute": true}}}, "required": ["ski", "status"], "additionalProperties": false}}}, "required": ["state"], "additionalProperties": false}}, "required": ["eebus-status"], "additionalProperties": false, "examples": [{"eebus-status": {"state": "on", "device": [{"ski": "abcdef1234567890abcdef1234567890abcdef12", "ip": "*************", "status": "TRUSTED"}, {"ski": "0987654321fedcba0987654321fedcba09876543", "ip": "*************", "status": "PENDING"}]}}, {"eebus-status": {"state": "off"}}]}
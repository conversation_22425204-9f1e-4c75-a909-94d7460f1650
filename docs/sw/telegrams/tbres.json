{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Bundle Response", "description": "Bundle response with data point values", "type": "object", "xml": {"name": "tbres"}, "required": ["bn", "gw", "st"], "properties": {"bn": {"type": "integer", "description": "Bundle ID", "xml": {"attribute": true}}, "gw": {"type": "integer", "description": "Gateway ID - persistent identifier associated with the device's serial number", "xml": {"attribute": true}}, "st": {"type": "string", "description": "Status", "enum": ["OK", "BU", "ER"], "xml": {"attribute": true}}, "ts": {"type": "string", "description": "Timestamp", "format": "date-time", "xml": {"attribute": true}}, "emsg": {"type": "string", "description": "Error message", "xml": {"attribute": true}}, "irs": {"type": "array", "description": "Info responses", "xml": {"name": "irs"}, "items": {"type": "object", "required": ["se", "ba", "in", "dl", "dh", "st"], "properties": {"se": {"type": "string", "description": "Sequence identifier matching the request (A/B/D for single operations, C for consistent data groups, A with incremented numbers for E multi-operations)", "pattern": "^[A-D];[0-9]+$", "xml": {"attribute": true}}, "ba": {"type": "string", "description": "Bus address (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "snr": {"type": "string", "description": "Service number (hex)", "pattern": "^[0-9A-Fa-f]{4}$", "xml": {"attribute": true}}, "in": {"type": "integer", "description": "Info number", "xml": {"attribute": true}}, "dl": {"type": "string", "description": "Data low byte (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "dh": {"type": "string", "description": "Data high byte (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "st": {"type": "string", "description": "Status", "enum": ["OK", "ER", "TO"], "xml": {"attribute": true}}, "max": {"type": "string", "description": "Maximum value (hex)", "pattern": "^0x[0-9A-Fa-f]{1,4}$", "xml": {"attribute": true}}, "min": {"type": "string", "description": "Minimum value (hex)", "pattern": "^0x[0-9A-Fa-f]{1,4}$", "xml": {"attribute": true}}}}}, "iac": {"type": "array", "description": "Info acknowledgements", "xml": {"name": "iac"}, "items": {"type": "object", "required": ["se", "ba", "in", "dl", "dh", "st", "snr"], "properties": {"se": {"type": "string", "description": "Sequence identifier matching the request (A/B/D for single operations, C for consistent data groups, A with incremented numbers for E multi-operations)", "pattern": "^[A-D];[0-9]+$", "xml": {"attribute": true}}, "ba": {"type": "string", "description": "Bus address (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "in": {"type": "integer", "description": "Info number", "xml": {"attribute": true}}, "dl": {"type": "string", "description": "Data low byte (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "dh": {"type": "string", "description": "Data high byte (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "st": {"type": "string", "description": "Status", "enum": ["OK", "ER", "TO"], "xml": {"attribute": true}}, "snr": {"type": "string", "description": "Service number (hex)", "pattern": "^[0-9A-Fa-f]{4}$", "xml": {"attribute": true}}}}}, "s03f2rs": {"type": "array", "description": "Service 03F2 responses (heating programs)", "xml": {"name": "s03f2rs"}, "items": {"type": "object", "required": ["se", "ba", "hp", "ty", "hk", "d", "db", "st"], "properties": {"se": {"type": "string", "description": "Sequence identifier matching the request (A/B/D for single operations, C for consistent data groups, A with incremented numbers for E multi-operations)", "pattern": "^[A-D];[0-9]+$", "xml": {"attribute": true}}, "ba": {"type": "string", "description": "Bus address (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "hp": {"type": "integer", "description": "Heating program", "xml": {"attribute": true}}, "ty": {"type": "integer", "description": "Heating program type", "xml": {"attribute": true}}, "hk": {"type": "integer", "description": "Heating circuit", "xml": {"attribute": true}}, "d": {"type": "integer", "description": "Day of week", "xml": {"attribute": true}}, "db": {"type": "string", "description": "Data bytes (hex)", "xml": {"attribute": true}}, "st": {"type": "string", "description": "Status", "enum": ["OK", "ER", "TO"], "xml": {"attribute": true}}}}}, "s03f3ac": {"type": "array", "description": "Service 03F3 acknowledgements (heating programs)", "xml": {"name": "s03f3ac"}, "items": {"type": "object", "required": ["se", "ba", "st", "hp", "ty", "hk", "d"], "properties": {"se": {"type": "string", "description": "Sequence identifier matching the request (A/B/D for single operations, C for consistent data groups, A with incremented numbers for E multi-operations)", "pattern": "^[A-D];[0-9]+$", "xml": {"attribute": true}}, "ba": {"type": "string", "description": "Bus address (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "st": {"type": "string", "description": "Status", "enum": ["OK", "ER", "TO"], "xml": {"attribute": true}}, "hp": {"type": "integer", "description": "Heating program", "xml": {"attribute": true}}, "ty": {"type": "integer", "description": "Heating program type", "xml": {"attribute": true}}, "hk": {"type": "integer", "description": "Heating circuit", "xml": {"attribute": true}}, "d": {"type": "integer", "description": "Day of week", "xml": {"attribute": true}}}}}, "ers": {"type": "array", "description": "Error responses", "xml": {"name": "ers"}, "items": {"type": "object", "required": ["se", "ba", "index", "code", "active"], "properties": {"se": {"type": "integer", "description": "Sequence number for error responses (note: this is an integer, not a string with prefix like other sequence identifiers)", "xml": {"attribute": true}}, "ba": {"type": "string", "description": "Bus address (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "index": {"type": "integer", "description": "Index", "xml": {"attribute": true}}, "code": {"type": "integer", "description": "Error code", "xml": {"attribute": true}}, "active": {"type": "boolean", "description": "Active status", "xml": {"attribute": true}}, "start": {"type": "string", "description": "Start timestamp", "format": "date-time", "xml": {"attribute": true}}, "stop": {"type": "string", "description": "Stop timestamp", "format": "date-time", "xml": {"attribute": true}}}}}, "srs": {"type": "array", "description": "Service responses", "xml": {"name": "srs"}, "items": {"type": "object", "required": ["ba", "se", "snr", "tbi", "db", "st"], "properties": {"ba": {"type": "string", "description": "Bus address (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "se": {"type": "string", "description": "Sequence identifier matching the request (A/B/D for single operations, C for consistent data groups, A with incremented numbers for E multi-operations)", "pattern": "^[A-D];[0-9]+$", "xml": {"attribute": true}}, "snr": {"type": "string", "description": "Service number (hex)", "pattern": "^[0-9A-Fa-f]{4}$", "xml": {"attribute": true}}, "tbi": {"type": "integer", "description": "Telegram byte index", "xml": {"attribute": true}}, "db": {"type": "string", "description": "Data bytes (hex)", "xml": {"attribute": true}}, "st": {"type": "string", "description": "Status", "enum": ["OK", "ER", "TO"], "xml": {"attribute": true}}}}}, "mrs": {"type": "array", "description": "Modbus responses", "xml": {"name": "mrs"}, "items": {"type": "object", "required": ["se", "ba", "fc", "adr", "st"], "properties": {"se": {"type": "string", "description": "Sequence identifier matching the request (A/B/D for single operations, C for consistent data groups, A with incremented numbers for E multi-operations)", "pattern": "^[A-D];[0-9]+$", "xml": {"attribute": true}}, "ba": {"type": "string", "description": "Bus address (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "fc": {"type": "integer", "description": "Function code", "xml": {"attribute": true}}, "adr": {"type": "integer", "description": "Start address", "xml": {"attribute": true}}, "count": {"type": "integer", "description": "Count", "xml": {"attribute": true}}, "db": {"type": "string", "description": "Data bytes (hex)", "xml": {"attribute": true}}, "st": {"type": "string", "description": "Status", "enum": ["OK", "ER", "TO"], "xml": {"attribute": true}}}}}, "mac": {"type": "array", "description": "Modbus acknowledgements", "xml": {"name": "mac"}, "items": {"type": "object", "required": ["se", "ba", "fc", "adr", "st"], "properties": {"se": {"type": "string", "description": "Sequence number", "xml": {"attribute": true}}, "ba": {"type": "string", "description": "Bus address (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "fc": {"type": "integer", "description": "Function code", "xml": {"attribute": true}}, "adr": {"type": "integer", "description": "Start address", "xml": {"attribute": true}}, "st": {"type": "string", "description": "Status", "enum": ["OK", "ER", "TO"], "xml": {"attribute": true}}}}}}}
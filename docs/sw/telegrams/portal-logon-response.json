{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Portal Logon Response", "description": "Authentication response with session ID from Portal", "type": "object", "xml": {"name": "portal-logon-response"}, "required": ["state", "sid"], "properties": {"state": {"type": "string", "description": "Connection state", "enum": ["ok", "busy", "error", "UnknownRegistrationData"], "xml": {"attribute": true}}, "sid": {"type": "integer", "description": "Session ID for tracking the connection", "xml": {"attribute": true}}, "next-connect-params": {"type": "object", "description": "Optional connection parameters for retry if needed (currently unimplemented, would only be sent with state='busy')", "xml": {"name": "next-connect-params"}, "properties": {"dest-server": {"type": "string", "description": "Server address for next connection attempt", "xml": {"name": "dest-server"}}, "dest-port": {"type": "integer", "description": "Server port for next connection attempt", "xml": {"name": "dest-port"}}, "delay_sec": {"type": "integer", "description": "Delay in seconds before next connection attempt", "xml": {"name": "delay_sec"}}, "max_try": {"type": "integer", "description": "Maximum number of connection attempts", "xml": {"name": "max_try"}}}, "required": ["dest-server", "dest-port", "delay_sec", "max_try"]}}}
{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Bundle Request", "description": "Bundle request for data point operations", "type": "object", "xml": {"name": "tbreq"}, "required": ["bn", "gw", "ty"], "properties": {"bn": {"type": "integer", "description": "Bundle ID", "xml": {"attribute": true}}, "gw": {"type": "integer", "description": "Gateway ID - persistent identifier associated with the device's serial number", "xml": {"attribute": true}}, "ty": {"type": "string", "description": "Bundle type", "enum": ["pull", "push", "write", "remove"], "xml": {"attribute": true}}, "ird": {"type": "array", "description": "Info read requests", "xml": {"name": "ird"}, "items": {"type": "object", "required": ["se", "ba", "in"], "properties": {"se": {"type": "string", "description": "Sequence identifier with special prefix meanings: A/B/D for single operations, C for consistent data groups, E for multi-operations that will receive individual A responses", "pattern": "^([A-D];[0-9]+)|(E;[0-9]+;[0-9]+;[0-9]+;[0-9]+;[0-9]+)$", "xml": {"attribute": true}}, "ba": {"type": "string", "description": "Bus address (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "in": {"type": ["integer", "string"], "description": "Info number or numbers (space-separated)", "xml": {"attribute": true}}, "is": {"type": "integer", "description": "Interval in seconds", "default": 60, "xml": {"attribute": true}}, "snr": {"type": "string", "description": "Service number (hex)", "pattern": "^[0-9A-Fa-f]{4}$", "default": "5022", "xml": {"attribute": true}}, "hy": {"type": "integer", "description": "Hysteresis", "xml": {"attribute": true}}}}}, "iwr": {"type": "array", "description": "Info write requests", "xml": {"name": "iwr"}, "items": {"type": "object", "required": ["se", "ba", "in", "dl", "dh"], "properties": {"se": {"type": "string", "description": "Sequence identifier with special prefix meanings: A/B/D for single operations, C for consistent data groups, E for multi-operations that will receive individual A responses", "pattern": "^([A-D];[0-9]+)|(E;[0-9]+;[0-9]+;[0-9]+;[0-9]+;[0-9]+)$", "xml": {"attribute": true}}, "ba": {"type": "string", "description": "Bus address (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "in": {"type": "integer", "description": "Info number", "xml": {"attribute": true}}, "dl": {"type": "string", "description": "Data low byte (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "dh": {"type": "string", "description": "Data high byte (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "snr": {"type": "string", "description": "Service number (hex)", "pattern": "^[0-9A-Fa-f]{4}$", "default": "5023", "xml": {"attribute": true}}}}}, "iwrx": {"type": "array", "description": "Info write multiple requests", "xml": {"name": "iwrx"}, "items": {"type": "object", "required": ["se", "ba", "in", "db"], "properties": {"se": {"type": "string", "description": "Sequence identifier, typically using E prefix for multi-operations that will receive individual A responses", "pattern": "^E;[0-9]+;[0-9]+;[0-9]+;[0-9]+;[0-9]+$", "xml": {"attribute": true}}, "ba": {"type": "string", "description": "Bus address (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "in": {"type": "string", "description": "Info numbers", "xml": {"attribute": true}}, "db": {"type": "string", "description": "Data bytes (hex)", "xml": {"attribute": true}}, "snr": {"type": "string", "description": "Service number (hex)", "pattern": "^[0-9A-Fa-f]{4}$", "default": "5023", "xml": {"attribute": true}}}}}, "s03f1rd": {"type": "array", "description": "Service 03F1 read requests (heating programs)", "xml": {"name": "s03f1rd"}, "items": {"type": "object", "required": ["se", "ba", "hp", "ty", "hk", "d"], "properties": {"se": {"type": "string", "description": "Sequence identifier with special prefix meanings: A/B/D for single operations, C for consistent data groups, E for multi-operations that will receive individual A responses", "pattern": "^([A-D];[0-9]+)|(E;[0-9]+;[0-9]+;[0-9]+;[0-9]+;[0-9]+)$", "xml": {"attribute": true}}, "ba": {"type": "string", "description": "Bus address (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "hp": {"type": "integer", "description": "Heating program", "xml": {"attribute": true}}, "ty": {"type": "integer", "description": "Heating program type", "xml": {"attribute": true}}, "hk": {"type": "integer", "description": "Heating circuit", "xml": {"attribute": true}}, "d": {"type": "integer", "description": "Day of week", "xml": {"attribute": true}}}}}, "s03f3wr": {"type": "array", "description": "Service 03F3 write requests (heating programs)", "xml": {"name": "s03f3wr"}, "items": {"type": "object", "required": ["se", "ba", "hp", "ty", "hk", "d", "db"], "properties": {"se": {"type": "string", "description": "Sequence identifier with special prefix meanings: A/B/D for single operations, C for consistent data groups, E for multi-operations that will receive individual A responses", "pattern": "^([A-D];[0-9]+)|(E;[0-9]+;[0-9]+;[0-9]+;[0-9]+;[0-9]+)$", "xml": {"attribute": true}}, "ba": {"type": "string", "description": "Bus address (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "hp": {"type": "integer", "description": "Heating program", "xml": {"attribute": true}}, "ty": {"type": "integer", "description": "Heating program type", "xml": {"attribute": true}}, "hk": {"type": "integer", "description": "Heating circuit", "xml": {"attribute": true}}, "d": {"type": "integer", "description": "Day of week", "xml": {"attribute": true}}, "db": {"type": "string", "description": "Data bytes (hex)", "xml": {"attribute": true}}}}}, "srd": {"type": "array", "description": "Service read requests", "xml": {"name": "srd"}, "items": {"type": "object", "required": ["ba", "se", "snr", "tbi"], "properties": {"ba": {"type": "string", "description": "Bus address (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "se": {"type": "string", "description": "Sequence identifier with special prefix meanings: A/B/D for single operations, C for consistent data groups, E for multi-operations that will receive individual A responses", "pattern": "^([A-D];[0-9]+)|(E;[0-9]+;[0-9]+;[0-9]+;[0-9]+;[0-9]+)$", "xml": {"attribute": true}}, "snr": {"type": "string", "description": "Service number (hex)", "pattern": "^0x[0-9A-Fa-f]{1,4}$", "xml": {"attribute": true}}, "tbi": {"type": "integer", "description": "Telegram byte index", "xml": {"attribute": true}}, "hy": {"type": "integer", "description": "Hysteresis", "xml": {"attribute": true}}, "ty": {"type": "integer", "description": "Type", "xml": {"attribute": true}}}}}, "erd": {"type": "array", "description": "Error read requests", "xml": {"name": "erd"}, "items": {"type": "object", "required": ["se", "li"], "properties": {"se": {"type": "integer", "description": "Sequence number for error read requests (note: this is an integer, not a string with prefix like other sequence identifiers)", "xml": {"attribute": true}}, "li": {"type": "string", "description": "Numbers", "xml": {"attribute": true}}}}}, "mrd": {"type": "array", "description": "Modbus read requests", "xml": {"name": "mrd"}, "items": {"type": "object", "required": ["se", "ba", "fc", "adr", "count"], "properties": {"se": {"type": "string", "description": "Sequence identifier with special prefix meanings: A/B/D for single operations, C for consistent data groups, E for multi-operations that will receive individual A responses", "pattern": "^([A-D];[0-9]+)|(E;[0-9]+;[0-9]+;[0-9]+;[0-9]+;[0-9]+)$", "xml": {"attribute": true}}, "ba": {"type": "string", "description": "Bus address (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "fc": {"type": "integer", "description": "Function code", "xml": {"attribute": true}}, "adr": {"type": "integer", "description": "Start address", "xml": {"attribute": true}}, "count": {"type": "integer", "description": "Count", "xml": {"attribute": true}}, "is": {"type": "integer", "description": "Interval in seconds", "default": 60, "xml": {"attribute": true}}}}}, "mwr": {"type": "array", "description": "Modbus write requests", "xml": {"name": "mwr"}, "items": {"type": "object", "required": ["se", "ba", "fc", "adr", "count", "db"], "properties": {"se": {"type": "string", "description": "Sequence identifier with special prefix meanings: A/B/D for single operations, C for consistent data groups, E for multi-operations that will receive individual A responses", "pattern": "^([A-D];[0-9]+)|(E;[0-9]+;[0-9]+;[0-9]+;[0-9]+;[0-9]+)$", "xml": {"attribute": true}}, "ba": {"type": "string", "description": "Bus address (hex)", "pattern": "^0x[0-9A-Fa-f]{1,2}$", "xml": {"attribute": true}}, "fc": {"type": "integer", "description": "Function code", "xml": {"attribute": true}}, "adr": {"type": "integer", "description": "Start address", "xml": {"attribute": true}}, "count": {"type": "integer", "description": "Count", "xml": {"attribute": true}}, "db": {"type": "string", "description": "Data bytes (hex)", "xml": {"attribute": true}}}}}}}
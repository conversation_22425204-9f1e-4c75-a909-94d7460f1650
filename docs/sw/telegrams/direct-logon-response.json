{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Direct Logon Response", "description": "Local authentication response with device info", "type": "object", "xml": {"name": "direct-logon-response"}, "required": ["state"], "properties": {"state": {"type": "string", "description": "Authentication state", "enum": ["pending", "invalidCredentials", "ok"], "xml": {"attribute": true}}, "sid": {"type": "integer", "description": "session ID", "xml": {"attribute": true}}, "installationname": {"type": "string", "description": "System name", "xml": {"name": "installationname"}}, "serialnumber": {"type": "string", "description": "Serial number", "xml": {"name": "serialnumber"}}, "date-time": {"type": "string", "description": "Current date and time", "format": "date-time", "xml": {"name": "date-time"}}, "ism-softwareversion": {"type": "string", "description": "ISM software version", "xml": {"name": "ism-softwareversion"}}, "ism-hardwareversion": {"type": "string", "description": "ISM hardware version", "xml": {"name": "ism-hardwareversion"}}, "wlan-connected": {"type": "boolean", "description": "WLAN connection status", "xml": {"name": "wlan-connected"}}, "type": {"type": "string", "description": "Device type", "xml": {"name": "type"}}}}
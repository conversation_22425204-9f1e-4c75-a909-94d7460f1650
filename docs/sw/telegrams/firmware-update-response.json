{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Firmware Update Response", "description": "Response to firmware update request", "type": "object", "xml": {"name": "firmware-update-response"}, "required": ["intent"], "properties": {"intent": {"type": "string", "description": "Update intent", "enum": ["ACCEPT", "REJECT"], "xml": {"name": "intent", "wrapped": false}}, "chunk-size": {"type": "integer", "description": "Preferred chunk size for firmware data", "xml": {"name": "chunk-size"}}, "reject-reason": {"type": "string", "description": "Reason for rejection", "enum": ["SECURITY", "BUSY", "UPDATES_DISABLED", "BAD_STREAM_ID", "INTERNAL_ERROR"], "xml": {"name": "reject-reason"}}}}
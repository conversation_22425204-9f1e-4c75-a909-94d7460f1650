{"$schema": "http://json-schema.org/draft-07/schema#", "title": "ISM Configuration Response", "description": "Response with Link configuration parameters", "type": "object", "xml": {"name": "ism-config-response"}, "required": ["sid", "dhcp-active", "ip-address", "subnetmask", "default-gateway", "dns-nameserver", "dest-server", "dest-port", "installationname", "connect-portal", "ism-serialnumber", "ism-softwareversion", "ism-hardwareversion", "wlan-active", "wlan-signal-strength-mW", "mac-address-lan", "mac-address-wlan", "v2-version", "v2-commit-hash"], "properties": {"sid": {"type": "integer", "description": "Session ID for authentication", "xml": {"attribute": true}}, "dhcp-active": {"type": "boolean", "description": "DHCP enabled status", "xml": {"name": "dhcp-active"}}, "ip-address": {"type": "string", "description": "IP address", "format": "ipv4", "xml": {"name": "ip-address"}}, "subnetmask": {"type": "string", "description": "Subnet mask", "format": "ipv4", "xml": {"name": "subnetmask"}}, "default-gateway": {"type": "string", "description": "Default gateway IP address", "format": "ipv4", "xml": {"name": "default-gateway"}}, "dns-nameserver": {"type": "string", "description": "DNS server IP address", "format": "ipv4", "xml": {"name": "dns-nameserver"}}, "dest-server": {"type": "string", "description": "Destination server address", "xml": {"name": "dest-server"}}, "dest-port": {"type": "integer", "description": "Destination port", "xml": {"name": "dest-port"}}, "installationname": {"type": "string", "description": "System name", "xml": {"name": "installationname"}}, "connect-portal": {"type": "boolean", "description": "Portal connection enabled status", "xml": {"name": "connect-portal"}}, "date-time": {"type": "string", "description": "Current date and time", "format": "date-time", "xml": {"name": "date-time"}}, "ism-serialnumber": {"type": "string", "description": "ISM serial number", "maxLength": 12, "xml": {"name": "ism-serialnumber"}}, "ism-softwareversion": {"type": "string", "description": "ISM software version", "xml": {"name": "ism-softwareversion"}}, "ism-hardwareversion": {"type": "string", "description": "ISM hardware version", "xml": {"name": "ism-hardwareversion"}}, "wlan-active": {"type": "boolean", "description": "WLAN active status", "xml": {"name": "wlan-active"}}, "wlan-signal-strength-mW": {"type": "integer", "description": "WLAN signal strength in mW", "xml": {"name": "wlan-signal-strength-mW"}}, "mac-address-lan": {"type": "string", "description": "LAN MAC address", "maxLength": 12, "xml": {"name": "mac-address-lan"}}, "mac-address-wlan": {"type": "string", "description": "WLAN MAC address", "maxLength": 12, "xml": {"name": "mac-address-wlan"}}, "v2-version": {"type": "string", "description": "Version 2", "xml": {"name": "v2-version"}}, "v2-commit-hash": {"type": "string", "description": "Version 2 commit hash", "xml": {"name": "v2-commit-hash"}}}}
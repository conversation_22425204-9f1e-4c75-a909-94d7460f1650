# Wolf Link and Smartset Interface Documentation

## Table of Contents

- [Wolf Link and Smartset Interface Documentation](#wolf-link-and-smartset-interface-documentation)
  - [Table of Contents](#table-of-contents)
  - [Introduction](#introduction)
  - [Connection Flow](#connection-flow)
    - [Portal Connection (Internet)](#portal-connection-internet)
    - [Local Connection (Home Network)](#local-connection-home-network)
    - [Local Discovery Protocol](#local-discovery-protocol)
  - [XML Message Format](#xml-message-format)
    - [Telegram Types](#telegram-types)
    - [Message Structure](#message-structure)
  - [Authentication](#authentication)
    - [Portal Login](#portal-login)
    - [Local Login](#local-login)
  - [Data Exchange](#data-exchange)
    - [System Configuration](#system-configuration)
    - [Bundle Requests](#bundle-requests)
      - [Sequence Identifiers (`se`)](#sequence-identifiers-se)
      - [Bundle Request and Response Elements](#bundle-request-and-response-elements)
      - [Info Number Read Operations (`ird` → `irs`)](#info-number-read-operations-ird--irs)
      - [Info Number Write Operations (`iwr` → `iac`)](#info-number-write-operations-iwr--iac)
      - [Multiple Info Number Write Operations (`iwrx` → `iac`)](#multiple-info-number-write-operations-iwrx--iac)
      - [Error Read Operations (`erd` → `ers`)](#error-read-operations-erd--ers)
      - [Time Program Read Operations (`s03f1rd` → `s03f2rs`)](#time-program-read-operations-s03f1rd--s03f2rs)
      - [Time Program Write Operations (`s03f3wr` → `s03f3ac`)](#time-program-write-operations-s03f3wr--s03f3ac)
      - [Modbus Read Operations (`mrd` → `mrs`)](#modbus-read-operations-mrd--mrs)
      - [Modbus Write Operations (`mwr` → `mac`)](#modbus-write-operations-mwr--mac)
    - [Bundle Processing Flow](#bundle-processing-flow)
    - [Transmission Modes](#transmission-modes)
  - [Firmware Updates](#firmware-updates)
    - [1. Firmware Update Request](#1-firmware-update-request)
    - [2. Firmware Update Response](#2-firmware-update-response)
    - [3. Binary Data Transfer](#3-binary-data-transfer)
    - [4. Firmware Update Completion](#4-firmware-update-completion)
    - [5. Device Restart](#5-device-restart)
  - [EEBUS Device Management](#eebus-device-management)
    - [1. EEBUS Status](#1-eebus-status)
    - [2. EEBUS Control](#2-eebus-control)
  - [Events](#events)
    - [Event subscription](#event-subscription)
    - [Event pushing](#event-pushing)
    - [Event IDs](#event-ids)
    - [Event types](#event-types)
  - [Version Number Formats](#version-number-formats)
    - [Git-based Version Format](#git-based-version-format)
    - [Parsed Version Formats](#parsed-version-formats)
    - [Link Firmware Version in Messages](#link-firmware-version-in-messages)
    - [Version Progression Rules](#version-progression-rules)
  - [Session Management](#session-management)
    - [Server Endpoints](#server-endpoints)
    - [Connection Identifiers](#connection-identifiers)
  - [Security Measures](#security-measures)
  - [XML Schema Reference](#xml-schema-reference)

## Introduction

This document describes the interface between the Wolf Link device and the Smartset applications. The Wolf Link is a communication gateway that connects Wolf heating and air handling unit (AHU) systems to the Smartset cloud service and local applications. The interface uses XML-based messages over TCP/IP connections.

## Connection Flow

The Wolf Link device can establish two types of connections:

1. **Portal Connection**: An outgoing connection from the Wolf Link to the Smartset cloud service over the internet
2. **Local Connection**: An incoming connection from a local application (PC or mobile app) to the Wolf Link over the local network

### Portal Connection (Internet)

The portal connection is established as follows:

1. The Wolf Link initiates an outgoing TCP/IP socket connection to the Smartset server
   - Server URL ISM7 FW1.xx: `www.wolf-smartset.de` (port 45165), SSLv3
   - Server URL ISM7 FW2.xx, Link FW3.xx, Link FW4.xx: `www.wolf-smartset.de` (port 56154), TLS 1.2 with mutual authentication
   - Server URL Link FW4.60+: `devices.wolf-smartset.com` (port 56155) TLS 1.2 with mutual authentication
2. The connection is secured with TLS 1.2 encryption (except ISM7 FW1.xx)
3. The Wolf Link verifies the server certificate
4. The server verifies the Wolf Link's client certificate (except ISM7 FW1.xx)
5. After establishing the secure connection, the server sends a `<startSession/>` message
6. The Wolf Link responds with an encrypted `portal-logon-request-encrypted` containing:
   - Random number for session security
   - System name
   - Password
   - Serial number
   - Date and time
   - Software and hardware version information
   - Connection status
7. The server responds with a `portal-logon-response` containing:
   - Connection state (ok, busy, error)
   - Session ID (`sid`) for tracking the connection
   - Optional `next-connect-params` element (currently unimplemented, would only be sent with state="busy")
8. The Wolf Link sends its `eebus-status` (optional, if EEBUS functionality is present in the firmware)

### Local Connection (Home Network)

The local connection is established as follows:

1. The Smartset application (PC or mobile app) discovers the Wolf Link on the local network using UDP broadcast:
   - The application sends a UDP broadcast packet to port 30303 with the content "Discovery: Who is out there?"
   - The Wolf Link responds with device information including MAC address, device type ("ESP32-LinkR-A"), hostname ("WOLFLINK"), and IP address
2. The application initiates a TCP/IP socket connection to the Wolf Link using the discovered IP address
3. The connection is secured with TLS encryption
4. The application verifies the Wolf Link's server certificate
5. The Wolf Link verifies the application's client certificate
6. The application sends a `direct-logon-request` containing the device password
7. The Wolf Link verifies the password and responds with a `direct-logon-response` containing:
   - Connection state (ok, pending, invalidCredentials)
   - System information (name, serial number, software version, etc.)
6. The Wolf Link sends its `eebus-status` (optional, if EEBUS functionality is present in the firmware)

**Note**: Although the Smartset application is technically the TCP client in a local connection, it acts in the role of "server" from a protocol perspective. This means the communication flow and message directions are consistent between portal and local connections.

### Local Discovery Protocol

The Wolf Link implements a simple UDP-based discovery protocol to allow local applications to find Wolf Link devices on the local network without requiring any prior configuration:

1. **Discovery Request**:
   - The client application sends a UDP broadcast packet to the broadcast address for each network interface (port 30303)
   - The packet contains the string: `Discovery: Who is out there?`

2. **Discovery Response**:
   - The Wolf Link responds with a structured binary message containing device information
   - The response format consists of several fields, each prefixed with a byte identifier:
     - `0x02` + MAC address (6 bytes) + `\r\n`
     - `0x03` + Device type string (`ESP32-LinkR-A`) + `\r\n`
     - `0x04` + Hostname (`WOLFLINK`) + `\r\n`
     - `0x05` + IP address (4 bytes in network byte order) + `\r\n`
   - The device type string includes `-A` suffix to identify the device as Wolf Link (not ISM7)
   - The IP address is the address that clients should use to connect to the device

## XML Message Format

The communication between Wolf Link and Smartset uses XML-based messages. Each message consists of:

1. A 4-byte integer with the length of the XML data (in network byte order)
2. A 2-byte integer with the telegram type (in network byte order)
3. The XML data

### Telegram Types

The following telegram types are defined:

| Type ID | XML Root Element                  | Description                                                                  | Direction     | Since Version |
| ------- | --------------------------------- | ---------------------------------------------------------------------------- | ------------- | ------------- |
| 1       | portal-logon-response             | Authentication response with session ID from Portal                          | Server → Link | 3.00          |
| 2       | read-systemconfig-request         | Request to retrieve system configuration                                     | Server → Link | 3.00          |
| 3       | read-systemconfig-response        | Response with connected device information                                   | Link → Server | 3.00          |
| 4       | tbreq                             | Bundle request for data point operations                                     | Server → Link | 3.00          |
| 5       | tbres                             | Bundle response with data point values                                       | Link → Server | 3.00          |
| 6       | ism-config-request                | Request to read or modify Link configuration                                 | Server → Link | 3.00          |
| 7       | ism-config-response               | Response with Link configuration parameters                                  | Link → Server | 3.00          |
| 8       | direct-logon-request              | Local authentication request with password                                   | Server → Link | 3.00          |
| 9       | direct-logon-response             | Local authentication response with device info                               | Link → Server | 3.00          |
| 14      | BinaryData                        | Binary data transfer for firmware updates                                    | Bidirectional | 3.00          |
| 15      | KeepAlive                         | Connection keep-alive with counter                                           | Bidirectional | 3.00          |
| 16      | portal-logon-request-encrypted    | Authentication request to Portal (encrypted)                                 | Link → Server | 3.00          |
| 17      | startSession                      | Initiates communication session                                              | Server → Link | 3.00          |
| 18      | firmware-update-request           | Request to initiate firmware update                                          | Server → Link | 4.30          |
| 19      | firmware-update-response          | Response to firmware update request                                          | Link → Server | 4.30          |
| 20      | firmware-update-completion-status | Status of firmware update completion                                         | Link → Server | 4.30          |
| 21      | restart                           | Command to restart the Link device                                           | Server → Link | 3.00          |
| 22      | eebus-status                      | Status of EEBUS and connected devices (sent after connection and on changes) | Link → Server | 4.60          |
| 23      | eebus-control                     | Control EEBUS functionality and device trust                                 | Server → Link | 4.60          |

### Message Structure

The XML messages follow specific schemas defined for each message type. The main message types include:

- **Authentication**: `portal-logon-request-encrypted`, `portal-logon-response`, `direct-logon-request`, `direct-logon-response`
- **Configuration**: `read-systemconfig-request`, `read-systemconfig-response`, `ism-config-request`, `ism-config-response`
- **Data Exchange**: `tgr-bundle-request`, `tgr-bundle-response`
- **Firmware Update**: `firmware-update-request`, `firmware-update-response`
- **EEBUS Management**: `eebus-status`, `eebus-control`

## Authentication

### Portal Login

The portal login process uses encrypted communication:

1. The server sends a `<startSession/>` message to initiate the login process
2. The Wolf Link sends a `portal-logon-request-encrypted` message containing the login information. This message is encrypted using AES-128-CBC with a shared key and initialization vector known to both the Wolf Link and Smartset. This encryption is used for legacy reasons due to security concerns with SSLv3 that was used in earlier versions.

   The decrypted content is:
   ```xml
   <portal-logon-request rnd="12345678">
     <installationname>My Heating System</installationname>
     <passwd>password123</passwd>
     <serialnumber>00:11:22:33:44:55</serialnumber>
     <date-time>2023-01-01T12:00:00</date-time>
     <ism-softwareversion>4001</ism-softwareversion>
     <ism-softwarenumber>1234</ism-softwarenumber>
     <ism-hardwareversion>linkr-4</ism-hardwareversion>
     <wlan-connected>true</wlan-connected>
     <type>Link</type>
     <v2-version>4.0.0</v2-version>
     <v2-commit-hash>abcdef123456</v2-commit-hash>
     <v2-disconnect-reason>Reboot:PowerOn</v2-disconnect-reason>
   </portal-logon-request>
   ```

3. The server responds with a `portal-logon-response`:
   ```xml
   <portal-logon-response state="ok" sid="12345678">
   </portal-logon-response>
   ```

   If the server is busy, it would respond with (note: currently unimplemented):
   ```xml
   <portal-logon-response state="busy" sid="12345678">
     <next-connect-params>
       <dest-server>devices2.wolf-smartset.com</dest-server>
       <dest-port>56155</dest-port>
       <delay_sec>60</delay_sec>
       <max_try>3</max_try>
     </next-connect-params>
   </portal-logon-response>
   ```

### Local Login

The local login process is simpler:

1. The server (Smartset application) sends a `direct-logon-request`:
   ```xml
   <direct-logon-request>
     <password>password123</password>
   </direct-logon-request>
   ```

2. The Wolf Link responds with a `direct-logon-response`:
   ```xml
   <direct-logon-response state="ok">
     <installationname>My Heating System</installationname>
     <serialnumber>00:11:22:33:44:55</serialnumber>
     <date-time>2023-01-01T12:00:00</date-time>
     <ism-softwareversion>4.20.9</ism-softwareversion>
     <ism-hardwareversion>linkr-1</ism-hardwareversion>
     <wlan-connected>true</wlan-connected>
     <type>Link</type>
   </direct-logon-response>
   ```

   Note that in the direct login response, `ism-softwareversion` uses the `TAG` format (e.g., `4.20.9`) for the Link firmware version - the version without commit information, which is different from the format used in the portal login request.

   TODO: Special handling of Smart Home

## Data Exchange

After successful authentication, the server can request the system configuration and exchange data with the Wolf Link.

### System Configuration

The system configuration provides information about all devices connected to the bus:

1. The server sends a `read-systemconfig-request`:
   ```xml
   <read-systemconfig-request sid="12345678">
     <system-config-addresses>optional_addresses</system-config-addresses>
   </read-systemconfig-request>
   ```

2. The Wolf Link responds with a `read-systemconfig-response`:
   ```xml
   <read-systemconfig-response sid="12345678">
     <gateway type="Link" softwareNumber="9" softwareVersion="420" wlan="true" g3="false"/>
     <busconfig type="ebus">
       <busDevices>
         <busDevice ba="0x03" sv="0x88" sr="0x01" cfg="0x1234" did="0x1A"/>
         <!-- Additional bus devices -->
       </busDevices>
     </busconfig>
     <modbusconfig>
       <busDevices>
         <modBusDevice ba="0x11" sv="0x88" ty="0x1"/>
         <!-- Additional Modbus devices -->
       </busDevices>
     </modbusconfig>
   </read-systemconfig-response>
   ```

   Note the Link firmware version numbers in the gateway element:
   - `softwareNumber="9"` - Uses `SOFTWARE_NUM` (patch version)
   - `softwareVersion="420"` - Uses `SHORT_NUM` (major and minor versions)

### Bundle Requests

Data exchange is performed using bundle requests and responses. A bundle can contain multiple data point requests:

1. The server sends a `tbreq` (bundle request):
   ```xml
   <tbreq bn="123" gw="12345678" ty="pull">
     <ird se="A;1234" ba="0x03" in="328" is="60"/>
     <ird se="C;5678" ba="0x03" in="370" is="60"/>
     <ird se="C;5678" ba="0x03" in="371" is="60"/>
     <ird se="C;5678" ba="0x03" in="372" is="60"/>
     <ird se="E;9012;9013;9014;9015;9016" ba="0x03" in="380 381 382 383 384" is="60"/>
     <!-- Additional data point requests -->
   </tbreq>
   ```

2. The Wolf Link responds with a `tbres` (bundle response):
   ```xml
   <tbres bn="123" gw="12345678" st="OK" ts="2023-01-01T12:00:00">
     <irs se="A;1234" ba="0x03" in="328" dl="0x12" dh="0x34" st="OK"/>
     <irs se="C;5678" ba="0x03" in="370" dl="0x56" dh="0x78" st="OK"/>
     <irs se="C;5678" ba="0x03" in="371" dl="0x9A" dh="0xBC" st="OK"/>
     <irs se="C;5678" ba="0x03" in="372" dl="0xDE" dh="0xF0" st="OK"/>
     <irs se="A;9012" ba="0x03" in="380" dl="0x11" dh="0x22" st="OK"/>
     <irs se="A;9013" ba="0x03" in="381" dl="0x33" dh="0x44" st="OK"/>
     <irs se="A;9014" ba="0x03" in="382" dl="0x55" dh="0x66" st="OK"/>
     <irs se="A;9015" ba="0x03" in="383" dl="0x77" dh="0x88" st="OK"/>
     <irs se="A;9016" ba="0x03" in="384" dl="0x99" dh="0xAA" st="OK"/>
     <!-- Additional data point responses -->
   </tbres>
   ```

   #### Sequence Identifiers (`se`)

   The `se` attribute in bundle requests and responses is a sequence identifier that allows matching requests with their corresponding responses. The letter prefix has special meaning:

   - **A, B, D**: These prefixes are functionally equivalent and are used for single operations (one read or write). The response will contain the same sequence identifier as the request.
   - **C**: Used for datapoints that "belong together" and must always be read together for data consistency. The C sequence number will appear at least twice in requests and responses, with each datapoint having its own element but sharing the same sequence identifier. This ensures data consistency across related values.
   - **E**: Used for multi-operations. The E sequence MUST specify exactly 5 sequence numbers (e.g., `E;1234;2345;3456;4567;5678`). Unlike other sequence types, an `E` request will be replied to with multiple responses, each using an `A` prefix with one response per number contained in the original `E` request.
   - **F**: Used for Modbus group operations. The F sequence format is `F;groupId;subId` where `groupId` is a common identifier for all requests in the group, and `subId` is typically 0 for all elements. We don't know the exact purpose and the current implementation always uses the same F sequence for all Modbus requests (shared ID).

   The format is a letter (A, B, C, D, E, or F) followed by a semicolon and one or more numbers (e.g., `A;1234`, `C;5678`, or `F;660000;0`). The numbers are used as unique identifiers within each category.

   #### Bundle Request and Response Elements

   Bundle requests (`tbreq`) and responses (`tbres`) contain various sub-elements for different types of operations on the eBUS. Each request element in a bundle has a corresponding response element:

   | Request Element | Response Element | Description                                                | Max Responses per Request |
   | --------------- | ---------------- | ---------------------------------------------------------- | ------------------------- |
   | `erd`           | `ers`            | Error read requests/responses                              | 0 to 10                   |
   | `ird`           | `irs`            | Info number read requests/responses                        | 1 or 5                    |
   | `iwr`           | `iac`            | Info number write requests/acknowledgments                 | 1                         |
   | `iwrx`          | `iac`            | Multiple info number write requests/acknowledgments        | 5                         |
   | `s03f1rd`       | `s03f2rs`        | Service 03F1 (time program) read requests/responses        | 1                         |
   | `s03f3wr`       | `s03f3ac`        | Service 03F3 (time program) write requests/acknowledgments | 1                         |
   | `mrd`           | `mrs`            | Modbus read requests/responses                             | 1                         |
   | `mwr`           | `mac`            | Modbus write requests/acknowledgments                      | 1                         |

   #### Info Number Read Operations (`ird` → `irs`)

   Info number read operations retrieve data values from specific devices on the eBUS. Each info number corresponds to a specific parameter or sensor value in the heating system.

   **Single Info Number Request (`ird`):**
   ```xml
   <ird se="A;1234" ba="0x03" in="328" is="60" snr="0x5022"/>
   ```

   **Multiple Info Numbers with Consistency Requirement (`ird` with C sequence):**
   ```xml
   <ird se="C;5678" ba="0x03" in="370" is="60" snr="0x5022"/>
   <ird se="C;5678" ba="0x03" in="371" is="60" snr="0x5022"/>
   <ird se="C;5678" ba="0x03" in="372" is="60" snr="0x5022"/>
   ```

   **Multiple Independent Info Numbers (`ird` with E sequence):**
   ```xml
   <ird se="E;9012;9013;9014;9015;9016" ba="0x03" in="380 381 382 383 384" is="60" snr="0x5022"/>
   ```

   - `se`: Sequence identifier for matching request with response
   - `ba`: Bus address of the target device (hexadecimal)
   - `in`: Info number(s) to read (for E sequences, multiple values MUST be space-separated)
   - `is`: Interval in seconds for push mode (default: 60)
   - `snr`: Service number for the eBUS operation (default: 0x5022 for reads)
   - `hy`: Optional hysteresis value for push mode updates (UNIMPLEMENTED)

   **Response for Single Info Number or C Sequence (`irs`):**
   ```xml
   <irs se="A;1234" ba="0x03" in="328" dl="0x12" dh="0x34" st="OK"/>
   ```

   **Response for C Sequence with Multiple Info Numbers:**
   ```xml
   <irs se="C;5678" ba="0x03" in="370" dl="0x56" dh="0x78" st="OK"/>
   <irs se="C;5678" ba="0x03" in="371" dl="0x9A" dh="0xBC" st="OK"/>
   <irs se="C;5678" ba="0x03" in="372" dl="0xDE" dh="0xF0" st="OK"/>
   ```

   **Response for E Sequence with Multiple Info Numbers:**
   ```xml
   <irs se="A;9012" ba="0x03" in="380" dl="0x11" dh="0x22" st="OK"/>
   <irs se="A;9013" ba="0x03" in="381" dl="0x33" dh="0x44" st="OK"/>
   <irs se="A;9014" ba="0x03" in="382" dl="0x55" dh="0x66" st="OK"/>
   ```

   - `se`: Sequence identifier matching the request (for E sequences, responses use A prefix with incremented numbers)
   - `ba`: Bus address of the device (hexadecimal)
   - `in`: Info number that was read
   - `dl`: Data low byte (hexadecimal)
   - `dh`: Data high byte (hexadecimal)
   - `st`: Status of the operation (OK, ER for not whitelisted, TO for timeout)
   - `max`: Optional maximum value for the parameter (hexadecimal)
   - `min`: Optional minimum value for the parameter (hexadecimal)

   #### Info Number Write Operations (`iwr` → `iac`)

   Info number write operations send data values to specific devices on the eBUS to change settings or control the heating system.

   **Request (`iwr`):**
   ```xml
   <iwr se="C;5678" ba="0x03" in="328" dl="0x12" dh="0x34" snr="0x5023"/>
   ```

   - `se`: Sequence identifier for matching request with response
   - `ba`: Bus address of the target device (hexadecimal)
   - `in`: Info number to write to
   - `dl`: Data low byte to write (hexadecimal)
   - `dh`: Data high byte to write (hexadecimal)
   - `snr`: Service number for the eBUS operation (default: 0x5023 for writes)

   **Response (`iac`):**
   ```xml
   <iac se="C;5678" ba="0x03" in="328" dl="0x12" dh="0x34" st="OK" snr="0x5023"/>
   ```

   - `se`: Same sequence identifier as in the request
   - `ba`: Bus address of the device (hexadecimal)
   - `in`: Info number that was written
   - `dl`: Data low byte that was written (hexadecimal)
   - `dh`: Data high byte that was written (hexadecimal)
   - `st`: Status of the operation (OK, ER for not whitelisted, TO for timeout)
   - `snr`: Service number used for the operation

   The system performs a readback verification after writing to ensure the value was correctly set.

   #### Multiple Info Number Write Operations (`iwrx` → `iac`)

   Multiple info number write operations allow writing to multiple info numbers in a single request, which is more efficient for batch updates.

   **Request (`iwrx`):**
   ```xml
   <iwrx se="E;9012;9013;9014;9015;9016" ba="0x03" in="328 329 330 331 332" db="0102030405060708090A" snr="0x5023"/>
   ```

   - `se`: Sequence identifier for the multi-operation (MUST use the `E` prefix with exactly 5 sequence numbers)
   - `ba`: Bus address of the target device (hexadecimal)
   - `in`: List of info numbers to write to (exactly 5, MUST be space-separated)
   - `db`: Data bytes to write (hexadecimal string, 2 bytes per info number)
   - `snr`: Service number for the eBUS operation (default: 0x5023 for writes)

   **Response (`iac`):**
   ```xml
   <iac se="A;9012" ba="0x03" in="328" dl="0x01" dh="0x02" st="OK" snr="0x5023"/>
   <iac se="A;9013" ba="0x03" in="329" dl="0x03" dh="0x04" st="OK" snr="0x5023"/>
   <iac se="A;9014" ba="0x03" in="330" dl="0x05" dh="0x06" st="OK" snr="0x5023"/>
   <!-- Additional responses for each info number -->
   ```

   The response contains one `iac` element for each info number in the request. Note that for `E` sequence requests, the responses use the `A` prefix with individual sequence numbers for each info number in the original request.

   #### Error Read Operations (`erd` → `ers`)

   Error read operations retrieve error codes and status information from devices on the eBUS.

   **Request (`erd`):**
   ```xml
   <erd se="A;1357" ba="0x03"/>
   ```

   - `se`: Sequence identifier for matching request with response
   - `ba`: Bus address of the target device (hexadecimal)

   **Response (`ers`):**
   ```xml
   <ers se="1357" ba="0x03" index="1" code="123" active="true" start="2023-01-01T12:00:00" stop=""/>
   <ers se="1358" ba="0x03" index="2" code="456" active="false" start="2023-01-01T10:00:00" stop="2023-01-01T11:00:00"/>
   <!-- Additional error responses -->
   ```

   - `se`: Sequence number (incremented for each error)
   - `ba`: Bus address of the device (hexadecimal)
   - `index`: Error index
   - `code`: Error code
   - `active`: Whether the error is currently active
   - `start`: Timestamp when the error started
   - `stop`: Timestamp when the error was resolved (empty if still active)

   The system maintains a history of errors and only sends updates when the error status changes.

   #### Time Program Read Operations (`s03f1rd` → `s03f2rs`)

   Time program read operations retrieve heating program schedules from devices on the eBUS.

   **Request (`s03f1rd`):**
   ```xml
   <s03f1rd se="A;2468" ba="0x35" hp="1" ty="0" hk="0" d="1"/>
   ```

   - `se`: Sequence identifier for matching request with response
   - `ba`: Bus address of the target device (hexadecimal)
   - `hp`: Heating program number
   - `ty`: Heating program type
   - `hk`: Heating circuit
   - `d`: Day of week (1-7, where 1 is Monday)

   **Response (`s03f2rs`):**
   ```xml
   <s03f2rs se="A;2468" ba="0x35" hp="1" ty="0" hk="0" d="1" db="0102030405060708091011121314" st="OK"/>
   ```

   - `se`: Same sequence identifier as in the request
   - `ba`: Bus address of the device (hexadecimal)
   - `hp`: Heating program number
   - `ty`: Heating program type
   - `hk`: Heating circuit
   - `d`: Day of week
   - `db`: Data bytes containing the time program (hexadecimal string)
   - `st`: Status of the operation (OK, TO for timeout)

   The data bytes contain the time program schedule in a device-specific format.

   #### Time Program Write Operations (`s03f3wr` → `s03f3ac`)

   Time program write operations update heating program schedules on devices on the eBUS.

   **Request (`s03f3wr`):**
   ```xml
   <s03f3wr se="A;3579" ba="0x35" hp="1" ty="0" hk="0" d="1" db="0102030405060708091011121314"/>
   ```

   - `se`: Sequence identifier for matching request with response
   - `ba`: Bus address of the target device (hexadecimal)
   - `hp`: Heating program number
   - `ty`: Heating program type
   - `hk`: Heating circuit
   - `d`: Day of week (1-7, where 1 is Monday)
   - `db`: Data bytes containing the time program (hexadecimal string)

   **Response (`s03f3ac`):**
   ```xml
   <s03f3ac se="A;3579" ba="0x35" hp="1" ty="0" hk="0" d="1" st="OK"/>
   ```

   - `se`: Same sequence identifier as in the request
   - `ba`: Bus address of the device (hexadecimal)
   - `hp`: Heating program number
   - `ty`: Heating program type
   - `hk`: Heating circuit
   - `d`: Day of week
   - `st`: Status of the operation (OK, TO for timeout)

   #### Modbus Read Operations (`mrd` → `mrs`)

   Modbus read operations retrieve data from Modbus devices connected to the Wolf Link.

   **Single Modbus Read Request (`mrd`):**
   ```xml
   <mrd se="A;4680" ba="0x01" fc="3" adr="40001" count="10" is="60"/>
   ```

   **Grouped Modbus Read Requests (`mrd` with F sequence):**
   ```xml
   <mrd se="F;660000;0" ba="0x01" fc="1" adr="2" count="149" is="60" />
   <mrd se="F;660000;0" ba="0x01" fc="1" adr="165" count="30" is="60" />
   <mrd se="F;660000;0" ba="0x01" fc="1" adr="501" count="2" is="60" />
   <mrd se="F;660000;0" ba="0x01" fc="1" adr="592" count="49" is="60" />
   <mrd se="F;660000;0" ba="0x01" fc="1" adr="652" count="22" is="60" />
   ```

   - `se`: Sequence identifier for matching request with response
   - `ba`: Bus address of the target Modbus device (hexadecimal)
   - `fc`: Modbus function code (1 or 3)
   - `adr`: Starting address to read from
   - `count`: Number of registers to read
   - `is`: Interval in seconds for push mode (default: 60)

   **Response (`mrs`):**
   ```xml
   <mrs se="A;4680" ba="0x01" fc="3" adr="40001" count="10" db="0102030405060708091011121314151617181920" st="OK"/>
   ```

   For F sequence requests, each Modbus read operation in the group will have its own response with the same sequence identifier:
   ```xml
   <mrs se="F;660000;0" ba="0x01" fc="1" adr="2" count="149" db="..." st="OK"/>
   <mrs se="F;660000;0" ba="0x01" fc="1" adr="165" count="30" db="..." st="OK"/>
   <mrs se="F;660000;0" ba="0x01" fc="1" adr="501" count="2" db="..." st="OK"/>
   ```

   - `se`: Same sequence identifier as in the request
   - `ba`: Bus address of the device (hexadecimal)
   - `fc`: Modbus function code
   - `adr`: Starting address that was read
   - `count`: Number of registers that were read
   - `db`: Data bytes containing the register values (hexadecimal string)
   - `st`: Status of the operation (OK, TO for timeout)

   #### Modbus Write Operations (`mwr` → `mac`)

   Modbus write operations send data to Modbus devices connected to the Wolf Link.

   **Request (`mwr`):**
   ```xml
   <mwr se="A;5791" ba="0x01" fc="6" adr="40001" count="1" db="0102"/>
   ```

   - `se`: Sequence identifier for matching request with response
   - `ba`: Bus address of the target Modbus device (hexadecimal)
   - `fc`: Modbus function code (5, 6, 15, or 16)
   - `adr`: Starting address to write to
   - `count`: Number of registers to write
   - `db`: Data bytes to write (hexadecimal string)

   **Response (`mac`):**
   ```xml
   <mac se="A;5791" ba="0x01" fc="6" adr="40001" st="OK"/>
   ```

   - `se`: Same sequence identifier as in the request
   - `ba`: Bus address of the device (hexadecimal)
   - `fc`: Modbus function code
   - `adr`: Starting address that was written
   - `st`: Status of the operation (OK, TO for timeout)

### Bundle Processing Flow

When the Wolf Link receives a bundle request, it processes the request elements in a specific order and performs the corresponding operations on the eBUS. The processing flow is as follows:

1. **Whitelist Check**: Before executing any eBUS operation, the Wolf Link checks if the requested info number is whitelisted for the specific bus address. This is a security measure to prevent unauthorized access to certain parameters.

2. **eBUS Database Cache**: The Wolf Link maintains an in-memory cache of recently read info numbers to reduce bus traffic. For read operations, it checks if a recent value is available in the cache before sending a request to the eBUS.

3. **eBUS Communication**: The Wolf Link communicates with devices on the eBUS using master-slave telegrams with specific service numbers:
   - For reads: Service 0x5022 (default)
   - For writes: Service 0x5023 (default)
   - For time programs: Services 0x03F1 (read) and 0x03F3 (write)

4. **Response Handling**: After performing the eBUS operation, the Wolf Link processes the response:
   - For successful operations, it returns the data with status "OK"
   - For non-whitelisted parameters, it returns status "ER"
   - For timeout or communication errors, it returns status "TO"

5. **Push Mode Optimization**: In push mode, the Wolf Link only sends updates when values change, reducing network traffic.

### Transmission Modes

The Wolf Link supports three transmission modes:

1. **Pull Mode** (`ty="pull"`): The server requests data and waits for the response (one-time read). The Wolf Link executes the requested operations immediately and returns the results.

2. **Push Mode** (`ty="push"`): The server registers for data points. The Wolf Link sends the requested data points once after registration and then updates them periodically based on the specified interval or when values change significantly. Push bundles are stored in memory and processed according to their intervals.

3. **Write Mode** (`ty="write"`): The server sends data to be written to the heating system. The Wolf Link executes the write operations immediately and returns acknowledgments.

## Firmware Updates

The Wolf Link supports over-the-air (OTA) firmware updates. The update process involves several steps and message types:

### 1. Firmware Update Request

Smartset initiates the update process by sending a `firmware-update-request` (telegram type 18):

```xml
<firmware-update-request>
  <version>4.40.0</version>
  <security-version>0</security-version>
  <size>1748992</size>
  <stream>1</stream>
</firmware-update-request>
```

The fields are:
- `version`: The version of the new Link firmware (uses the `TAG` format, e.g., `4.40.0`)
- `security-version`: The security version of the new firmware
- `size`: Total size of the firmware in bytes
- `stream`: Stream ID (must be 1)

### 2. Firmware Update Response

The Wolf Link responds with a `firmware-update-response` (telegram type 19). If the update can proceed, it sends:

```xml
<firmware-update-response>
  <intent>ACCEPT</intent>
  <chunk-size>1450</chunk-size>
</firmware-update-response>
```

The `chunk-size` field informs Smartset about the preferred chunk size for data transmission.

If the update cannot proceed, the Wolf Link sends a rejection response with a reason:

```xml
<firmware-update-response>
  <intent>REJECT</intent>
  <reject-reason>REASON_CODE</reject-reason>
</firmware-update-response>
```

Possible rejection reasons include:
- `BUSY`: Device is currently being updated
- `SECURITY`: Security version is lower than current firmware
- `UPDATES_DISABLED`: OTA updates are disabled on the device
- `BAD_STREAM_ID`: Invalid stream ID (only 1 is valid)
- `INTERNAL_ERROR`: Device encountered an internal error

### 3. Binary Data Transfer

If the update is accepted, Smartset sends the firmware data in chunks using `BinaryData` telegrams (type 14). The binary data telegram has the following structure:

| Bytes | Data Type | Usage                      |
| ----- | --------- | -------------------------- |
| 0..3  | uint32_t  | Length of payload          |
| 4..5  | uint16_t  | Message type (= 14)        |
| 6     | uint8_t   | Stream ID (= 1)            |
| 7     | uint8_t   | Type                       |
| 8..9  | uint16_t  | Chunk ID (high byte first) |
| 10..  | uint8_t[] | Binary Data                |

The type field can have the following values:

| Type | Name           | Direction       | Usage                       |
| ---- | -------------- | --------------- | --------------------------- |
| 1    | SEND           | Smartset → Link | Send data bytes to device   |
| 2    | ACK            | Link → Smartset | Data processed successfully |
| 3    | ABORT_DEVICE   | Link → Smartset | Device aborted OTA update   |
| 5    | ABORT_SMARTSET | Smartset → Link | Smartset aborted OTA update |

For each chunk sent by Smartset (type = SEND), the Wolf Link responds with an acknowledgment (type = ACK) with the same chunk ID, except for the last chunk.

### 4. Firmware Update Completion

After receiving the last chunk, the Wolf Link sends a `firmware-update-completion-status` telegram (type 20):

```xml
<firmware-update-completion-status>
  <status>SUCCESS</status>
</firmware-update-completion-status>
```

If the update fails, the status will be `FAILURE` or `ABORT` with an optional error message:

```xml
<firmware-update-completion-status>
  <status>FAILURE</status>
  <message>Error message</message>
</firmware-update-completion-status>
```

### 5. Device Restart

After a successful update, the new firmware is ready but not active until the device restarts. Smartset can trigger a restart by sending a `restart` telegram (type 21):

```xml
<restart />
```

After restarting, the Wolf Link will connect to Smartset with the new firmware version.

## EEBUS Device Management

The Wolf Link supports EEBUS (Energy Efficiency Bus) for communication with compatible energy devices. The protocol includes two message types for managing EEBUS device connections and trust relationships.

### 1. EEBUS Status

The Wolf Link sends an `eebus-status` message (telegram type 22) in the following situations:

1. After connection establishment (immediately after the `portal-logon-response` or `direct-logon-response`), to ensure Smartset has the current status regardless of what was last sent
2. Whenever there is a change in the status of EEBUS devices or functionality

This ensures that Smartset always has the most up-to-date information about EEBUS devices:

```xml
<eebus-status state="on" id="wolflinkA3B4C6">
  <device ski="abcdef1234567890abcdef1234567890abcdef12" ip="*************" status="TRUSTED" />
  <device ski="0987654321fedcba0987654321fedcba09876543" ip="*************" status="PENDING" />
</eebus-status>
```

If EEBUS is disabled or there are no devices, the message will have an empty device list:

```xml
<eebus-status state="off" id="wolflinkA3B4C6" />
```

The attributes are:
- `state`: Overall EEBUS state, either "on" or "off"
- For each device:
  - `ski`: Subject Key Identifier - a unique identifier for the EEBUS device (40 hex characters)
  - `ip`: IP address of the device (optional, may be omitted if not available)
  - `status`: Trust status of the device, which can be one of:
    - `TRUSTED`: Device is trusted but not connected (offline)
    - `DISTRUSTED`: Device is explicitly not trusted
    - `PENDING`: Device is waiting for trust decision
    - `CONNECTED`: Device is trusted and connected
    - `PIN_REQUIRED`: Device requires a PIN to establish trust

### 2. EEBUS Control

Smartset can control EEBUS functionality by sending an `eebus-control` message (telegram type 23):

```xml
<eebus-control>
  <set-trust ski="abcdef1234567890abcdef1234567890abcdef12" status="TRUSTED" />
</eebus-control>
```

The `eebus-control` message can also be used to untrust a device:

```xml
<eebus-control>
  <set-trust ski="abcdef1234567890abcdef1234567890abcdef12" status="UNTRUSTED" />
</eebus-control>
```

Or to enable / disable the EEBUS functionality:


```xml
<eebus-control>
  <state>on</state>
</eebus-control>
```

The elements and attributes are:
- `set-trust`: Command to change the trust status of a device
  - `ski`: Subject Key Identifier of the device to change (40 hex characters)
  - `status`: New trust status to set, which can be either:
    - `TRUSTED`: Set the device as trusted
    - `DISTRUSTED`: Set the device as not trusted (kicks device if necessary)
    - `PENDING`: Forgets the device (kicks device if necessary)
- `set-pin`: Command to provide a PIN for a device that requires PIN authentication
  - `ski`: Subject Key Identifier of the device to set PIN for (40 hex characters)
  - `pin`: PIN to set for the device
- `state`: Control the EEBUS functionality, which can be either:
  - `on`: Enable EEBUS functionality
  - `off`: Disable EEBUS functionality

After receiving a `set-trust` command, the Wolf Link will update the trust status of the specified device and send an updated `eebus-status` message with the new status. After receiving a `set-pin` command, the Wolf Link will attempt to authenticate to the device using the provided PIN and update the device's status accordingly. After receiving a `state` command, the Wolf Link will enable or disable EEBUS functionality accordingly. Note that the setting will not become effective until the next restart.

When a device requires PIN authentication, the Wolf Link will include it in the `eebus-status` message with a status of `PIN_REQUIRED`. Smartset should then send an `eebus-control` message with a `set-pin` element to provide the PIN:

```xml
<eebus-control>
  <set-pin ski="abcdef1234567890abcdef1234567890abcdef12" pin="123456" />
</eebus-control>
```

## Events

The Wolf Link can send events to Smartset to notify about events of interests. These are individually defined according to customer requirements.

### Event subscription

Smartset can subscribe to events by sending an `event-subscription` message (telegram type 24).
The subscription message includes the last known event ID. The Wolf Link will send all events with a higher ID.
If no events are known, the last ID is 0.

```xml
<event-subscription last-id="12345" />
```

### Event pushing

The Wolf Link can push events to Smartset by sending an `event-push` message (telegram type 25).
The event message includes the event ID and the event data.

```xml
<event-push>
  <event id="12346" type="pv-normal" />
  <event id="12347" type="evu-normal" />
</event-push>
```

The event can include an optional payload which is a JSON payload (standalone primitives are allowed):

```xml
<event-push>
  <event id="12346" type="evu-limitation">
    1000
  </event>
  <event id="12347" type="something-happened">
    {"foo": "bar"}
  </event>
</event-push>
```

If the last known event ID is greater than the last event ID, the Wolf Link will send a reset attribute in the `event-push` message. This indicates that the event history has been reset and the last known event ID is no longer valid:

```xml
<event-push reset="true">
  <event id="1" type="pv-normal" />
</event-push>
```

### Event IDs

Event IDs are unique and monotonically increasing. The first event ID is 1.

### Event types

Here is a tabe of all valid event types and their payload format:

| Type                   | Payload format |
| ---------------------- | -------------- |
| `pv-normal`            | None           |
| `pv-reduction`         | `number`       |
| `evu-normal`           | None           |
| `evu-limitation`       | `number`       |
| `evu-lock-auto`        | None           |
| `evu-lock`             | None           |
| `sg-normal`            | None           |
| `sg-recommendation`    | None           |
| `sg-limitation`        | `number`       |
| `sg-lock-auto`         | None           |
| `sg-lock`              | None           |
| `sg-order`             | None           |
| `eebus-lpc-normal`     | None           |
| `eebus-lpc-reduction`  | `number`       |
| `eebus-ohpcf-normal`   | None           |
| `eebus-ohpcf-power`    | `number`       |
| `eebus-device-added`   | `string`       |
| `eebus-device-removed` | `string`       |

## Version Number Formats

The Wolf Link firmware uses several different version number formats for different purposes due to historical reasons. These version formats refer to the Link firmware itself, not the Smartset protocol. Understanding these formats is important when working with the interface.

### Git-based Version Format

The full version string follows the format: `<major>.<minor>.<patch>[-<additional>-g<commit>]`

Example: `4.20.9-7-g7917480`

Components:
- **Major**: Starts with 4, incremented at discretion when minor overflows (reaches 90+)
- **Minor**: Always uses two digits (e.g., 20), incremented by 10 for production releases
- **Patch**: Can use any number of digits, incremented for field test releases
- **Additional**: Number of commits since the last tag (optional)
- **Commit**: Git commit hash prefix (only appears with "additional")

### Parsed Version Formats

The firmware parses this version string into several formats:

1. **FULL**: Complete version string (e.g., `4.20.9-7-g7917480`)
2. **HASH**: Git commit hash (e.g., `7917480`)
3. **TAG**: Version without commit info (e.g., `4.20.9`)
4. **SHORT**: Major and minor only (e.g., `4.20`)
5. **SHORT_NUM**: Numeric representation of major and minor (e.g., `420`)
6. **SOFTWARE_NUM**: Numeric representation of patch (e.g., `9`)

### Link Firmware Version in Messages

The Wolf Link firmware version is represented in different formats across various messages in the protocol. The portal login request specifically uses:

```xml
<portal-logon-request rnd="12345678">
  <!-- other fields -->
  <ism-softwareversion>420</ism-softwareversion>   <!-- SHORT_NUM -->
  <ism-softwarenumber>9</ism-softwarenumber>       <!-- SOFTWARE_NUM -->
  <ism-hardwareversion>linkr-1</ism-hardwareversion>
  <!-- other fields -->
  <v2-version>4.20.9-7-g7917480</v2-version>       <!-- FULL -->
  <v2-commit-hash>a917e80</v2-commit-hash>         <!-- HASH -->
  <!-- optional fields -->
</portal-logon-request>
```

Specific Link firmware version formats used in this message:
- `ism-softwareversion`: Uses `SHORT_NUM` (e.g., `420`) - the numeric representation of major and minor versions
- `ism-softwarenumber`: Uses `SOFTWARE_NUM` (e.g., `9`) - the numeric representation of the patch version
- `v2-version`: Uses `FULL` - the complete version string including commit information if available
- `v2-commit-hash`: Uses `HASH` - just the Git commit hash

Additionally, the portal login request may include optional fields for test and development modes.

### Version Progression Rules

The following rules govern how version numbers progress through different release types:

- For internal releases: The additional number is increased (by 1 or more)
- For field test releases: The patch version is increased by 1 and the additional and commit parts are removed
- For production releases:
  - If minor < 90: Minor version is increased by 10
  - If minor >= 90: Major version is increased by 1 and minor reset to 0
  - Patch version is reset to 0

## Session Management

### Server Endpoints

The Wolf Link connects to the Smartset portal using the following server endpoints:

- **Server URL ISM7 FW1.xx:** `www.wolf-smartset.de` (port 45165)
- **Server URL ISM7 FW2.xx, Link FW3.xx, Link FW4.xx:** `www.wolf-smartset.de` (port 56154)
- **Server URL Link FW4.60+:** `devices.wolf-smartset.com` (port 56155)

The server migration was part of an infrastructure update. Newer firmware versions connect to the current server, while older versions may still use the legacy server.

### Connection Identifiers

The Wolf Link interface uses two important identifiers for managing connections and data exchange:

1. **Session ID (`sid`)**:
   - A temporary identifier assigned by the Smartset portal for each connection session
   - Used by the portal to track which connection a message belongs to
   - The Wolf Link obtains its session ID from either the portal-logon-response or the first system configuration request
   - Included in most request and response messages to associate them with a specific connection
   - Not primarily a security mechanism, but rather a connection tracking mechanism

2. **Gateway ID (`gw`)**:
   - A persistent identifier associated with the device's serial number
   - Stored in Smartset's database to identify the specific Wolf Link device
   - Used in bundle requests and responses to identify which device the data belongs to
   - Remains consistent across different connection sessions

**Note**: `sid` and `gw` currently contain the same value. It is unclear how they are actually being used, `sid` is missing in `tbreq` and `tbres`.

## Security Measures

The Wolf Link implements several security measures:

1. **TLS Encryption**: All connections use TLS 1.2 encryption
2. **Certificate Validation**: The Wolf Link validates the server certificate, which are verified to be signed by the Lucon Root CA, and the server validates the Wolf Link's client certificate
3. **Password Protection**: Users need to enter random link password to connect it to their account and local access requires a password
4. **Encrypted Login**: The portal login request is encrypted using AES-128-CBC with a shared key and initialization vector

## XML Schema Reference

Detailed JSON schema definitions for each telegram type are available in the `telegrams` directory. These schemas include XML structure information, required fields, property definitions, and enumeration values.

| Telegram Type | XML Root Element                  | Schema File                                                                                  |
| ------------- | --------------------------------- | -------------------------------------------------------------------------------------------- |
| 1             | portal-logon-response             | [portal-logon-response.json](./telegrams/portal-logon-response.json)                         |
| 2             | read-systemconfig-request         | [read-systemconfig-request.json](./telegrams/read-systemconfig-request.json)                 |
| 3             | read-systemconfig-response        | [read-systemconfig-response.json](./telegrams/read-systemconfig-response.json)               |
| 4             | tbreq                             | [tbreq.json](./telegrams/tbreq.json)                                                         |
| 5             | tbres                             | [tbres.json](./telegrams/tbres.json)                                                         |
| 6             | ism-config-request                | [ism-config-request.json](./telegrams/ism-config-request.json)                               |
| 7             | ism-config-response               | [ism-config-response.json](./telegrams/ism-config-response.json)                             |
| 8             | direct-logon-request              | [direct-logon-request.json](./telegrams/direct-logon-request.json)                           |
| 9             | direct-logon-response             | [direct-logon-response.json](./telegrams/direct-logon-response.json)                         |
| 16            | portal-logon-request-encrypted    | [portal-logon-request.json](./telegrams/portal-logon-request.json)                           |
| 17            | startSession                      | [startSession.json](./telegrams/startSession.json)                                           |
| 18            | firmware-update-request           | [firmware-update-request.json](./telegrams/firmware-update-request.json)                     |
| 19            | firmware-update-response          | [firmware-update-response.json](./telegrams/firmware-update-response.json)                   |
| 20            | firmware-update-completion-status | [firmware-update-completion-status.json](./telegrams/firmware-update-completion-status.json) |
| 21            | restart                           | [restart.json](./telegrams/restart.json)                                                     |
| 22            | eebus-status                      | [eebus-status.json](./telegrams/eebus-status.json) (sent after connection and on changes)    |
| 23            | eebus-control                     | [eebus-control.json](./telegrams/eebus-control.json)                                         |

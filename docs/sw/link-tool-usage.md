# Link Tool Usage

## Quick Reference

- **Deployment:**
  - Dev: `.\link-tool.exe deploy images\esp-firmware-<version>-dev.bin --monitor`
  - Dev with password: `.\link-tool.exe deploy images\esp-firmware-<version>-dev.bin --monitor --password <PASSWORD>`
  - Prod: `.\link-tool.exe deploy images\esp-firmware-<version>-prod.bin --monitor --prod`
  - Prod with password: `.\link-tool.exe deploy images\esp-firmware-<version>-prod.bin --monitor --prod --password <PASSWORD>`
  - Certificate test: `.\link-tool.exe deploy images\esp-firmware-<version>-dev.bin --monitor --cert <CERT> --key <KEY>`
- **Firmware Update:**
  - Default: `.\link-tool.exe update images\esp-firmware-<version>-prod.bin`
  - In case it fails, enable debugging: `.\link-tool.exe update images\esp-firmware-<version>-prod.bin --debug`
  - Custom signing key location: `.\link-tool.exe update images\esp-firmware-<version>-prod.bin --signing-key <KEY>`
- **Monitor:**
  - With reset: `.\link-tool.exe monitor` (requires ESP-Prog)
  - Without reset: `.\link-tool.exe monitor --no-reset`
  - Filter for Wi-Fi only: `.\link-tool.exe monitor --filter wifi`
  - Into file: `.\link-tool.exe monitor --raw --no-color --no-reset > monitor.txt`
- **Open Command Line:**
  - Execute single command: `.\link-tool.exe cmd <CMD> [ARG1] [ARG2...]`
  - Open interactive command line in monitor: `.\link-tool.exe monitor --no-reset`, then press `F2` or `F3`
- **Commands:** (use either directly in command line or via `.\link-tool.exe cmd <CMD> [ARG1] [ARG2...]`)
  - Restart: `restart`
  - Show version: `version`
  - Show serial number: `read-mac`
  - Show status: `status`
  - Show all settings: `nvs-overview`
  - Show event log: `event-log`
  - Read Wi-Fi settings: `nvs-overview wifi`
  - Read network settings: `nvs-overview net`
  - Read password: `nvs-get password`
  - Set Wi-Fi credentials: `nvs-set wifi_ssid <SSID>`, `nvs-set wifi_pass <PASS>`, `nvs-set wifi_authmethod 3` (3 = WPA2, see below), then restart
  - Set password: `nvs-set password <PASSWORD>`, then restart
  - Enable portal connection: `nvs-set portal_enabled true`, then restart
  - Change to Smartset beta server: `nvs-set smartset_host beta.wolf-smartset.com`, then restart
- **Test Mode:**
  - Set Wi-Fi credentials (command line): `nvs-set test_wifi_ssid <SSID>`, `nvs-set test_wifi_pass <PASS>`, `nvs-set test_wifi_auth <AUTH>`
  - Enter test mode (command line): `enter-test-mode`
  - Check test status: `.\link-tool.exe prod-test`

## General Notes

### Distribution

`link-tool` is distributed to non-developers via Teams. In order to use it,
download the archive and extract it. Once extracted, open a PowerShell instance
in the directory which contains `link-tool.exe`. Then, you can use the commands
from this manual in that PowerShell instance.

### Serial Ports

When using an ESP-Prog for the serial communication, there will be two serial
ports made available by the ESP-Prog. Here, choose the port with the higher
number. Ports that existed previously from other connected devices are
irrelevant. For example:

```
COM3
COM5  <-- added by ESP-Prog (JTAG)
COM6  <-- added by ESP-Prog (UART)  <== choose this one!
COM8
```

### Firmware Versions

In general, the following rules hold:

- If the device is currently running on firmware version **4.50.1 or below** or
  you want to deploy these firmware versions, use link-tool version **0.x.x**.
- If the device is currently running on firmware version **4.50.2 or above** or
  you want to deploy these firmware versions, use link-tool version **1.x.x**.

Starting with 4.50.2, a new serial communication protocol was introduce in
order to improve the security of the WOLF Link. Thus, old link tools are
incompatible with new versions and vice-versa. Subcommands of the link-tool
which do not depend on serial communication do theoretically work perfectly
fine on both versions, but it is still recommended to use the proper link-tool
versions in order to avoid confusion.

The only exception is the `update` command. Here, backwards compatibility was
retained in order to facilitate updating older devices (see
[here](#firmware-update)).

### Miscellaneous

In order to cancel any action of the link-tool, `Ctrl+C` can be used. If that
does not work, `Ctrl+Break` (German keyboards: `Strg+Untbr`) terminates the
programme.

To obtain a list of all possible commands and their options, use `--help`:

```powershell
.\link-tool.exe --help
.\link-tool.exe deploy --help
```

## Deploy

Deploying requires an ESP-Prog to be connected. (or alternatively, the EN and
IO0 pins can be toggled manually by connecting wires on the board)

> **WARNING:** *Dev firmware can only be flashed in dev mode, and prod firmware
> can only be flashed in prod mode. Flashing the wrong firmware can cause
> irreversible damage!*

### Development

The basic command for performing a dev deployment is as follows:

```powershell
.\link-tool.exe deploy images\esp-firmware-<version>-dev.bin [OPTIONS]
```

Notes:
- To start a monitor immediately after deploying (recommended!), add the `--monitor` (or `-m`) flag.
- In order to avoid having to manually select the serial port, `--port <PORT>` can be specified.
- This will generate a random password. In order to manually specify a password, use `--password <PASSWORD>`.
- The development certificates will be used automatically. See ["Certificate Handling"](#certificate-handling) for how to override the selection.

### Production

The basic command for performing a prod deployment is the same as for the dev
deployment, except that the `--prod` flag has to be used:

```powershell
.\link-tool.exe deploy images\esp-firmware-<version>-prod.bin --prod [OPTIONS]
```

Notes:
- To start a monitor immediately after deploying (recommended!), add the `--monitor` (or `-m`) flag.
- In order to avoid having to manually select the serial port, `--port <PORT>` can be specified.
- This will generate a random password. In order to manually specify a password, use `--password <PASSWORD>`.
- Certificates from the prod certificate pool will be used automatically. See ["Certificate Handling"](#certificate-handling) for how to override the selection.

> **WARNING:** *Performing a prod deployment is permanent and cannot be undone!*

### Certificate Handling

There are two main sources for certificates: development certificates and the
prod certificate pool.

The development certificates are located in the `sec/certs/portal` subdirectory
as DER files. They are intended to be used on dev deployments and for
development / testing use only! When performing a dev deployment, these will be
used automatically. When it is required to use a different certificate for a
dev deployment for whatever reason, it can be specified with the `--cert` and
`--key` flags, like this:

```powershell
.\link-tool.exe deploy images\esp-firmware-dev.bin --cert my-cert.der --key my-key.der
```

However, this should only be done when absolutely necessary. Usually, it is
better to let link-tool use the development certificates automatically.

The certificate pool is a collection of certificates and their respective keys
located in the `sec/certs/portal/pool` subdirectory. These certificates are
intended to be one-time use only! They can be used by specifying `--cert AUTO`
like this:

```powershell
.\link-tool.exe deploy images\esp-firmware-dev.bin --cert AUTO
```

For production deployments, this is the default: specifying `--cert AUTO` is
not necessary. In fact, all other certificate sources (development certificates
and manually specified certs via `--cert` and `--key`) are disabled in
production mode, and only the pool is available.

The default portal certificate pool (`sec/certs/portal/pool`) can be
overwritten with the `--cert-pool-path` flag.

### After-Deploy-Commands

The `--after-deploy` (short: `-A`) option provides a way to execute commands
after the deployment process has finished. This can for example be used to
configure the WOLF Link to one's liking for development or testing purposes.
The command is called with the serial number as its first argument and factory
password as its second argument.

For instance, one can create the file `after-deploy.ps1`:

```powershell
# print label for imaginary label printer because why not
$SerialNumber = $args[0]
$FactoryPassword = $args[1]
Print-Label -FirstLine "SN: $SerialNumber" -SecondLine "PW: $FactoryPassword"

# long delay to make sure that wolf link is fully booted
Start-Sleep -Seconds 15

# change settings of wolf link
link-tool cmd nvs-set password Password123
link-tool cmd nvs-set portal_enabled true
```

And then call the deploy command like this:

```powershell
.\link-tool.exe deploy -m -A .\after-deploy.ps1 images\esp-firmware-dev.bin
```

## Firmware Update

Firmware updates ("OTW updates") can be performed using the `update` subcommand:

```powershell
.\link-tool.exe update images\esp-firmware-<version>-prod.bin
```

> **WARNING:** *Only update to a dev version when the device is in dev mode, and only update to a prod version when the device is in prod mode. Updating to the wrong firmware can cause irreversible damage!*

If the update fails (even after restarting the WOLF Link), run the previous
command with `--debug` and send the log to the developers.

Starting with version 4.50.2, the WOLF Link uses a new protocol for serial
communication. The link-tool automatically determines which protocol to use. If
the detection fails, the following options can be used:

- `--protocol 0`: legacy protocol (for devices currently running version 4.50.1
  and below)
- `--protocol 1`: new protocol (for devices currently running version 4.50.2
  and above)
- `--protocol AUTO`: automatically determine protocol (default)

Note that the new protocol is only supported starting with link-tool version
1.0.0.

## Monitor

The monitor of the WOLF Link can be viewed with the monitor command:

```
.\link-tool.exe monitor [OPTIONS]
```

Many other commands include an option to automatically start the monitor after
their primary function completes. In particular, this is the case for `deploy`
(and `flash`), which permits the usage of `--monitor` (or short: `-m`) in order
to launch the monitor after deployment / flashing succeeds.

Notes:
- When a fully connected ESP-Prog is used, the tool will automatically perform a reset, which causes the WOLF Link to restart! To prevent this, use the `--no-reset` flag.
- In order to avoid having to manually select the serial port, `--port <PORT>` can be specified.
- The monitor can be filtered for specific messages using `--filter <PATTERN>`.

When logging into a file, the following command should be used:

```powershell
.\link-tool.exe monitor --raw --no-color --no-reset > log.txt
```

Within the monitor, `Ctrl+R` can be pressed to restart the device. This
requires an ESP-Prog.

In case the device restarts and the tool does not detect the restart, `Ctrl+F`
can be used to reinitiate the secure connection and to reenable the monitor
output.

The keys `F2` and `F3` can be used to open a [command line](#command-line).
`F3` opens a "persistent" command line in a separate buffer that allows for
multiple commands to be entered, while `F2` only allows for a single command to
be entered before returning to the monitor.

When the monitor detects an ELF file present in its usual location, it will
also print a decoded stack trace in the case of a panic. This unfortunately
only works for developers who have the ELF file. The stack trace can also be
decoded retroactively (by a developer) using the `trace-decode` subcommand.
Note that for the "retroactive decoding", ELF file and compiler must have the
exact same version as the one on the device.

## Command Line

The command line can be used for configuring the WOLF Link and viewing
important information via a serial connection.

### Usage

The command line can be opened with the following command:

```powershell
.\link-tool.exe cmd
```

This will open an interactive prompt. In order to execute a single command
without the interactive prompt, use the following:

```powershell
.\link-tool.exe cmd <CMD> [ARG1] [ARG2...]
```

As mentioned in the previous section, the command line can also be launched
from the monitor using the `F2` and `F3` keys.

The command line can be exited with either `Ctrl+C`, or the `quit` / `exit`
commands.

### Important Commands

- `help`: show all available commands
- `version [IDENTIFIER]`: prints the software and hardware version
- `read-mac`: print ethernet mac address / serial number
- `nvs-get <KEY>`: read a value from the [NVS](#nvs)
- `nvs-set <KEY> <VALUE>`: write a value to the [NVS](#nvs)
- `nvs-reset <KEY>`: reset a [NVS](#nvs) value
- `nvs-overview`: print all [NVS](#nvs) configuration items
- `nvs-overview [FILTER]`: apply a filter to `nvs-overview`
- `status`: print the current status
- `restart`: restart the device
- `rollback`: undo the last firmware update and rollback to previous version
- `factory-reset`: perform a factory reset
- `event-log [PAGE] [OFFSET] [LIMIT]`: view the event log

### Event Log

The command `event-log` can be used for viewing the event log. It accepts three
optional parameters: `page`, `offset` and `limit` (in that order).

- `page`: The event log is stored in multiple "pages". If space on one page runs out, the next one will be used. 0 = current page, 1 = previous page, 2 = previous previous page, etc. Default: 0.
- `offset`: The offset within the page. 0 = no offset = most recent entries on the page, N = omit the N most recent entries. Default: 0.
- `limit`: The maximum number of entries to show. Default: 25.

By default, `event-log` (without parameters) shows the 25 most recent entries
in the event log. If you want to go back in time even further, you need to
manually specify the offset (and sometimes even the page). To retrieve entries
26-50 (where 1 = most recent entry), use `event-log 0 25`. Then, to retrieve
the next oldest set of entries, use `event-log 0 50` and so on. If at one point
no more entries can be found, increase the page number by 1 and reset the
offset to 0 in order to retrieve the next oldest entries from the previous
page. To retrieve more values at once, increase the limit: `event-log 0 0 50`.
The following example illustrates a possible event log debugging session:

```
event-log 0 0
event-log 0 25
event-log 0 50
event-log 0 75   # no more entries, switch to previous page
event-log 1 0
event-log 1 25
...
```

`version` accepts the following identifiers: `all`, `full`, `hash`, `tag`,
`customer`, `smartset`, `softwarenum`, `hardware`. Can be omitted in order to
show only `full` and `hardware`.

## Test Mode

To enter test mode, configure the test mode Wi-Fi and start the test by
entering the following commands into the command line. SSID, PASS and AUTH
correspond to the used Wi-Fi network. AUTH uses the Wi-Fi authentication method
numbers described [here](#wi-fi-authentication-methods).

```
nvs-set test_wifi_ssid <SSID>
nvs-set test_wifi_pass <PASS>
nvs-set test_wifi_auth <AUTH>
enter-test-mode
```

Then, the test progress can be checked with the `prod-test` subcommand:

```powershell
.\link-tool.exe prod-test
```

## Miscellaneous

### Serial Number

The serial number (Ethernet MAC address) can be read from a development device
using the following command:

```powershell
.\link-tool.exe read-mac
```

This reads the MAC address directly from the hardware. Alternatively, the
following command (which read the address from software) can be used. This
command will also work in production mode.

```powershell
.\link-tool.exe cmd read-mac
```

### Firmware Image Version

To obtain the version of an unlabelled firmware image file, use the following
command:

```powershell
.\link-tool.exe read-version <FILE>
```

### Reset / Restart

To reset / restart the device, various options can be used:

- `.\link-tool.exe reset` performs a hard reset using the EN pin. Thus, EN must
  be connected for this to work.
- Pressing `Ctrl+R` in the monitor also performs a hard reset using the EN pin.
  Thus, EN must be connected for this to work.
- The `restart` command performs a software restart. Works without a connection
  to the EN pin.

## NVS

### Keys

The configuration of the WOLF Link is managed via NVS (non-volatile storage).
NVS is a key-value store. All available keys can be seen with the
`nvs-overview` command. The most relevant ones are the following:

- Wi-Fi
  - `wifi_authmethod`: Wi-Fi authentication method (see below)
  - `wifi_pass`: Wi-Fi passphrase
  - `wifi_ssid`: Wi-Fi SSID
  - `ap_enabled`: whether the access point is enabled (true/false)
- Network
  - `net_dhcp`: DHCP enabled (true/false)
  - `net_hostname`: hostname (DHCP)
  - `net_dns1`: primary DNS server (static IP)
  - `net_dns2`: secondary DNS server (static IP)
  - `net_gateway`: gateway (static IP)
  - `net_ip`: IP address (static IP)
  - `net_mask`: subnet mask (static IP) (0-31, not IP address!)
- Portal
  - `portal_enabled`: portal connection enabled (true/false)
  - `smartset_host`: Smartset server host as IP or domain
  - `smartset_port`: port (0-65535)
  - `system_name`: WOLF Link system name
- `factory_pass`: factory password (read-only)
- `password`: user password
- `ota_disabled`: disable updates via Smartset portal (OTW updates still work)

### Wi-Fi Authentication Methods

- 0: None (no encryption)
- 1: WEP
- 2: WPA
- 3: WPA2 Personal
- 4: WPA/WPA2 Personal
- 5: WPA2 Enterprise
- 6: WPA3 Personal
- 7: WPA2/WPA3 Personal
- 8: WAPI Personal

## Development

Install link-tool:
```sh
cd tools/link-tool
cargo install --path .
```

Bundle link-tool for production after creating a prod image (requires Windows):
```powershell
cd tools\link-tool
.\bundle.ps1 -Firmware ..\..\firmware\images\esp-firmware-<version>-prod.bin
```

Create dev image:
```sh
cd firmware
touch config/sdkconfig.defaults
cargo build
link-tool create
```

Create prod image:
```sh
cd firmware
touch config/sdkconfig.defaults
cargo build --release
link-tool create --prod
```

Initial deployment (dev):
```sh
cd firmware
touch config/sdkconfig.defaults
cargo build
link-tool create
link-tool deploy "images/esp-firmware-$(git describe --always)-dev.bin" --monitor
```

Flash update firmware (without redeploy) and start monitor:
```sh
cd firmware
cargo run
# or alternatively, manually:
cd firmware
cargo build
link-tool flash --monitor
```

Decode a stack trace:
```sh
# first, go back to the version that the device with the panic uses and compile it
# copy the part shown below from the stack trace
link-tool trace-decode "0x400892b3:0x3ffb94f0 0x401ab721:0x3ffb9510 0x4008cb0b:0x3ffb9530"
```

Erase flash:
```sh
link-tool erase                  # entire flash
link-tool erase [PARTITONS]...   # only selected partitions
```

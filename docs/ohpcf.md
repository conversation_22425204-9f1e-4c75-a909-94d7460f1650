# OHPCF

## State diagram

```mermaid
%%{init: { 'theme':'default', 'sequence': {'useMaxWidth':true} } }%%
stateDiagram-v2
    state "Idle" as idle
    state "Alternative Power Sequence" as alt
    state "Wait for capacity" as wait
    state "Charging" as charging
    state "Charging grace period" as grace
    state "Throttle" as throttle


    [*] --> idle
    idle --> alt : ready
    alt --> idle : not ready
    alt --> wait : EMS activates p. seq/<br>set HP elevated
    wait --> charging : capacity available
    charging --> wait : capacity full
    wait --> throttle : timeout 7 min/<br>set HP normal
    charging --> grace : not ready
    grace --> idle : timeout 10 min
    grace --> charging : ready
    throttle --> idle : timeout 30 min
    wait --> idle : EMS disables p. seq/<br>set HP normal
```

### Conditions

* ready - bit 0 of readiness bitmask (10997)
* capacity available - one of the bits (10988) is 0 => != 7
* capacity full - all bits (10988) are 1 => == 7
* timeout = we've been in this state for ... without interruption

### Actions

* set HP elevated: write 10998 := 3; write 10995 := 1;
* set HP normal: write 10998 := 0; write 10995 := 0;

## Unresolved questions

TODO: do we or how do we report end of the power sequence to the EMS?

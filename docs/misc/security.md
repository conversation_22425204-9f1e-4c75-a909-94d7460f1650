# Wolf Link Redesign Security

## Security Workflow

A host-based security workflow is used. Key generation and encryption are both
performed on the host, not on the device itself. This allows for greater
flexibility and reduced manufacturing time.

The initial steps are as follows:
1. Sign bootloader and application.
2. Generate a flash encryption key as well as an NVS encryption key.
3. Generate an NVS partition with device-specific TLS certificate, TLS private
   key and factory password. Encrypt the NVS partition with the NVS encryption
   key.
4. Encrypt signed bootloader, partition table, signed application and NVS key
   with the flash encryption key.
5. Flash all five partitions to the device.
6. Burn security-related eFuses, burn flash encryption key and secure boot key
   digest, and write protect relevant eFuses.
7. Remove device-specific keys (for flash encryption and NVS encryption).

Also see [Host-Based Security Workflows](https://docs.espressif.com/projects/esp-idf/en/latest/esp32/security/host-based-security-workflows.html).

## Components

### Secure Boot

The device is equipped with secure boot. For this, [Secure Boot V2](https://docs.espressif.com/projects/esp-idf/en/latest/esp32/security/secure-boot-v2.html)
is used. Secure boot protects the device from running unauthorized (i.e.,
unsigned) firmware by checking that each piece of software being booted is
signed. Unsigned software will be rejected. All firmware updates (OTA and OTW)
and the initially flashed firmware must be signed in order to be accepted by
the device.

The secure boot key is only ever stored on the build server, where it will be
used to sign the firmware images.

> **NOTE:** Ideally, the key would be stored in an external HSM. See the
> [documentation](https://docs.espressif.com/projects/esptool/en/latest/esp32/espsecure/index.html#remote-signing-using-an-external-hsm) for more details.

### Flash Encryption

In order to prevent users from reading the flash contents, [flash encryption](https://docs.espressif.com/projects/esp-idf/en/latest/esp32/security/flash-encryption.html)
is used. The partitions are encrypted on the host computer instead of the
device. Note that OTA / OTW update images do not need to be encrypted, as the
device takes care of that itself. Every device has its own flash encryption
key.

The flash encryption key will be generated on the host that also performs the
flashing of the devices. Immediately after the flashing completes, the key will
be deleted.

### NVS Encryption

Since the NVS also contains sensitive information, [NVS encryption](https://docs.espressif.com/projects/esp-idf/en/latest/esp32/api-reference/storage/nvs_encryption.html)
is activated. NVS encryption uses a separate key stored in the `nvs_key`
partition in order to encrypt the contents of the NVS. The `nvs_key` partition
is encrypted using regular flash encryption. Every device has its own NVS
encryption key.

The NVS encryption key will be generated on the host that also performs the
flashing of the devices. Immediately after the flashing completes, the key will
be deleted.

In order to generate the device-specific NVS partition, a factory password will
be generated on the fly. Certificate and private key will be taken from a pool
of available certificates and keys.

## Other Security Measures

### eFuses

The ESP32 chips supports various security-related eFuses:

- `FLASH_CRYPT_CNT = 127` and `FLASH_CRYPT_CONFIG = 0xF`: permanently enables
  flash encryption
- `ABS_DONE_1`: permanently enable secure boot
- `JTAG_DISABLE`: disable JTAG
- `DISABLE_DL_ENCRYPT`, `DISABLE_DL_DECRYPT`, `DISABLE_DL_CACHE`: disable UART
  bootloader encryption / decryption / flash cache access
- write_protect `MAC`
- write_protect `RD_DIS`: disable further read protection (flash encryption key
  will be read protected automatically after it was burned)

### OTW Updates

In order to strengthen the security of OTW updates, the device asks the update
tool to sign a challenge code and to send it back to the device. An update can
only be started when the signature is correct. This ensures that only users
with access to the signing key are able to perform OTW updates.

## Security Limitations

All devices need to have the same secure boot key. This drastically simplifies
the OTA update mechanism and avoids needing to persist all keys of all devices.

Between generation and deletion of both flash encryption key and NVS encryption
key, an attacker may be able to read the key either from disk, RAM or by
intercepting the serial connection to the device. Reading from disk can be
prevented by not relying on tools from ESP-IDF and instead implementing them
ourselves.

Similarly, the unencrypted firmware and TLS certificates / private keys can be
read from disk. The flashing host and build server should be equipped with disk
encryption to mitigate some external attacks.

Since flash encryption uses the same AES key for every pair of adjacent 16-byte
AES blocks it does not prevent an attacker from understanding the high-level
layout of the flash by comparing areas with identical content (e.g. empty or
padding areas). NVS encryption should however take care of this issue for the
NVS partition.

A key is only as good as its random number source. The system that generates
keys must be equipped with a quality entropy source.

## File Locations

| File                 | Location                      | Persistence          |
| -------------------- | ----------------------------- | -------------------- |
| Flash Encryption Key | `keys/flash-encryption.bin`   | generated, temporary |
| NVS Encryption Key   | `keys/nvs-key.bin`            | generated, temporary |
| Secure Boot Digest   | `keys/secure-boot-digest.bin` | persistent           |
| TLS Certificates     | `certs/link-<uuid>-cert.der`  | delete after use     |
| TLS Private Keys     | `certs/link-<uuid>-key.der`   | delete after use     |

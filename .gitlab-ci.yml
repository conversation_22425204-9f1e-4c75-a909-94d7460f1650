image: "rustlang/rust:nightly"

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_TAG
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_<PERSON><PERSON><PERSON>


build link-tool win64:
  tags:
    - saas-windows-medium-amd64
  before_script:
    - curl.exe -sSf -o rustup-init.exe https://win.rustup.rs
    - ./rustup-init.exe -y --profile minimal --default-toolchain nightly --default-host x86_64-pc-windows-msvc
  script:
    - powershell -Command "$env:USERPROFILE\.cargo\bin\cargo.exe b -r -p link-tool --bin link-tool"
  artifacts:
    paths:
      - target/release/link-tool.exe

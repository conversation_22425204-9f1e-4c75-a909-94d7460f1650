# Wolf Link Redesign / 2

[![Pipeline status](https://gitlab.com/system-evo/wrs2/link-redesign/badges/main/pipeline.svg)](https://gitlab.com/system-evo/wrs2/link-redesign/-/pipelines/latest)

This repository holds all software and accompanying tools for the Wolf Link Redesign.

## Hardware

The module used in the Wolf Link is: Espressif ESP32-WROVER-32E-16N-2R

## Contents

| Directory          | Description                   |
| ------------------ | ----------------------------- |
| `/firmware`        | ESP32 firmware                |
| `/eebus`           | EEBUS stack                   |
| `/tools/link-tool` | Build/Flash/Test/Monitor tool |
| `/website`         | Wolf Link Website             |

## Versioning

Example software version string: 4.10.1-12-g5ab0c03

Some terms to describe the version:
`<major>.<minor>.<patch>[-<additional>-g<commit>]`

The major version starts out with 4 and will be increased at our discretion.
The minor version must use two digits.
The patch version may use any number of digits.
The additional number may use any number of digits and is optional.
The commit string may use any number of hex digits and only appears together with "additional".

For every internal release, the additional number is increased (by 1 or more).
If an internal release gets promoted for field test, the patch version is increased by 1 and the additional and commit parts are removed.
If a field test version is promoted to production, the minor version will be increased by 10 if < 90, else the major version is increased by 1 and minor reset to 0. The patch version reset to 0. 

## Tool Usage

Plese refer to the separate [Tool Usage Document](https://gitlab.com/system-evo/wrs2/link-redesign/-/blob/main/docs/sw/link-tool-usage.md).

## Development Setup

> **Note:** Building natively on Windows is not supported (might or might not work)

You can either use the development container (firmware/.devcontainer) or compile natively on your Linux host:

1. Install Rust (https://rustup.rs/)
2. Install Python (v3.7+)
3. `cargo install espup ldproxy`
4. `espup install --toolchain-version ********`
7. Run `export-esp.sh`

Now you should be able to build:

```
cd firmware
cargo build
```

#!/usr/bin/bash
set -xe

VERSION="$1"

if [ "$IDF_PATH" == "" ]; then
  echo "Please export.sh the ESP-IDF tools before releasing"
  exit 1
fi

git tag -a "$VERSION" -m "$VERSION"
touch config/sdkconfig.defaults
cargo build
cargo build --release
link-tool create
link-tool create --prod
cp target/xtensa-esp32-espidf/debug/esp-firmware "images/esp-firmware-$VERSION-dev.elf"
cp target/xtensa-esp32-espidf/release/esp-firmware "images/esp-firmware-$VERSION-prod.elf"
git push
git push -u origin "$VERSION"

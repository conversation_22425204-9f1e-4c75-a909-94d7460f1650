use core::ffi::CStr;
use std::sync::atomic::Ordering;

use defmt::Debug2Format;
use esp_idf_svc::hal::reset::ResetReason;

use crate::{
    debug::stdin::{OTW_VERIFYING_KEY_EXPONENT, OTW_VERIFYING_KEY_MODULUS},
    prelude::*,
    smartset::local::{LOCAL_CONN_CA, LOCAL_CONN_CERT, LOCAL_CONN_KEY},
    utils,
};

pub fn apply_own_patches() {
    debug!("Setting eventfd config");
    esp_idf_svc::sys::esp!(unsafe {
        sys::esp_vfs_eventfd_register(&sys::esp_vfs_eventfd_config_t { max_fds: 5 })
    })
    .expect("failed to set eventfd config");
}

pub fn init_logging() {
    crate::debug::log::init_log();
    crate::debug::log::init_esplog();
    #[cfg(debug_assertions)]
    crate::debug::log::enable_espidf_logging();
    info!("Initialized");
}

pub async fn perform_migrations() -> Result<()> {
    if !nvs::smartset_config::exists().await || nvs::smartset_config::get().await < 1 {
        let current_host = nvs::smartset_host::get().await;
        info!("Migrating Smartset URL: current={}", current_host.as_str());

        match current_host.as_str() {
            "www.wolf-smartset.de" => {
                nvs::smartset_host::set(String::from("devices.wolf-smartset.com")).await;
                nvs::smartset_port::set(56155_u16).await;
            }
            "beta.wolf-smartset.com" => {
                nvs::smartset_host::set(String::from("devices.beta.wolf-smartset.com")).await;
                nvs::smartset_port::set(56155_u16).await;
            }
            _ => (),
        }

        nvs::smartset_config::set(1).await;
    }

    if !nvs::eebus_cert::exists().await || !nvs::eebus_key::exists().await {
        info!("Generating new EEBus certificate and key");
        let (cert, key) = crate::eebus::certgen::generate_eebus_cert()?;
        nvs::eebus_cert::set(cert).await;
        nvs::eebus_key::set(key).await;
    }

    if !nvs::local_con_ca::exists().await {
        info!("Migrating local connection CA to NVS");
        nvs::local_con_ca::set_static(LOCAL_CONN_CA).await;
    }
    if !nvs::local_con_cert::exists().await {
        info!("Migrating local connection certificate to NVS");
        nvs::local_con_cert::set_static(LOCAL_CONN_CERT).await;
    }
    if !nvs::local_con_key::exists().await {
        info!("Migrating local connection key to NVS");
        nvs::local_con_key::set_static(LOCAL_CONN_KEY).await;
    }

    if !nvs::serial_key_mod::exists().await {
        info!("Migrating serial verification key modulus to NVS");
        nvs::serial_key_mod::set_static(OTW_VERIFYING_KEY_MODULUS).await;
    }
    if !nvs::serial_key_exp::exists().await {
        info!("Migrating serial verification key exponent to NVS");
        nvs::serial_key_exp::set_static(OTW_VERIFYING_KEY_EXPONENT).await;
    }

    Ok(())
}

pub fn output_startup_info() {
    info!(
        "Firmware version: version={}, commit={}, prod={}",
        crate::utils::version::FULL,
        crate::utils::version::HASH,
        utils::is_prod_mode(),
    );
    info!(
        "Compilation info: time={}, rustc={}, sha={}",
        env!("VERGEN_BUILD_TIMESTAMP"),
        env!("VERGEN_RUSTC_SEMVER"),
        env!("VERGEN_RUSTC_COMMIT_HASH")
    );
    info!("Last reset: reason={}", Debug2Format(&ResetReason::get()));
}

pub fn init_alloc_fail_handler() {
    #[no_mangle]
    #[rustfmt::skip]
    pub unsafe extern "C" fn heap_caps_alloc_failed_hook(
        requested_size: usize,
        caps: u32,
        _function_name: *const std::ffi::c_char,
    ) {
        let task_handle = sys::xTaskGetCurrentTaskHandle();
        let task_name = CStr::from_ptr(sys::pcTaskGetName(task_handle)).to_str().unwrap();

        error!(
            "Failed to allocate memory: task={}, req={}, free_caps={}, free_int={}, free_ext={}, free_blk={}, caps={}",
            task_name,
            requested_size,
            sys::heap_caps_get_free_size(caps),
            sys::heap_caps_get_free_size(sys::MALLOC_CAP_INTERNAL),
            sys::heap_caps_get_free_size(sys::MALLOC_CAP_SPIRAM),
            sys::heap_caps_get_largest_free_block(caps),
            caps,
        );

        sys::esp_backtrace_print(32);
    }

    unsafe {
        sys::heap_caps_register_failed_alloc_callback(Some(heap_caps_alloc_failed_hook));
    }
}

pub fn perform_tests() -> Result<()> {
    Ok(())
}

pub async fn detect_test_mode() -> bool {
    let test_mode = nvs::test_mode::get().await;
    if test_mode {
        info!("Setting up test mode");
        let _ = nvs::test_mode::remove().await;
    } else {
        let _ = nvs::test_mode_auth::remove().await;
        let _ = nvs::test_mode_pass::remove().await;
        let _ = nvs::test_mode_ssid::remove().await;
    }
    state().test_mode.store(test_mode, Ordering::SeqCst);
    test_mode
}

use core::fmt::{Display, Write};

use defmt::Format;
use nanoxml::derive::ser::SerXml;
use serde::{Deserialize, Serialize};

use crate::prelude::*;

#[derive(<PERSON><PERSON>, Copy)]
pub struct StoredClockTime {
    time: Option<ClockTime>,
    received_at: Instant,
}

impl StoredClockTime {
    pub fn current(&self) -> Option<ClockTime> {
        let time = self.time?;
        let elapsed = self.received_at.elapsed();
        let elapsed = elapsed.as_secs();

        Some(time.plus_secs(elapsed as u32))
    }
}

impl Default for StoredClockTime {
    fn default() -> Self {
        StoredClockTime {
            time: None,
            received_at: Instant::now(),
        }
    }
}

#[derive(C<PERSON>, Copy, Debug, Deserialize, Hash, PartialEq, Serialize)]
pub struct ClockTime {
    pub seconds_of_day: u32,
    pub day: u8,
    pub month: u8,
    pub year: u32,
}

impl ClockTime {
    pub fn plus_secs(&self, secs: u32) -> Self {
        const SECS_PER_DAY: u32 = 60 * 60 * 24;
        const DAYS_TABLE: [u8; 13] = [0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

        let mut res = *self;
        let secs = self.seconds_of_day + secs;
        let days = secs / SECS_PER_DAY;

        if days == 0 {
            res.seconds_of_day = secs;
            return res;
        }
        let mut day_of_month = self.day as u32 + days;
        let mut month = self.month;
        let mut year = self.year;

        loop {
            // RM-2 does not have any date information, only time. Thus, it sends out
            // "2165-165-165" as its date, which is invalid for obvious reasons. When
            // we receive this date, we simply do not process the date information.
            if self.month > 12 {
                break;
            }

            let mut max_days = DAYS_TABLE[self.month as usize];
            if month == 2 && is_leap_year(year) {
                max_days += 1;
            }

            if day_of_month <= max_days as u32 {
                break;
            }

            day_of_month -= max_days as u32;
            month += 1;

            if month > 12 {
                year += 1;
                month = 1;
            }
        }

        res.day = day_of_month as u8;
        res.month = month;
        res.year = year;
        res.seconds_of_day = secs % SECS_PER_DAY;

        res
    }
}

impl Display for ClockTime {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let s = self.seconds_of_day;
        let hour = s / 3600;
        let minute = (s % 3600) / 60;
        let second = s % 60;
        write!(
            f,
            "{}-{:02}-{:02}T{:02}:{:02}:{:02}",
            self.year, self.month, self.day, hour, minute, second
        )
    }
}

impl SerXml for ClockTime {
    fn ser_body<W: Write>(&self, xml: &mut nanoxml::ser::XmlBuilder<'_, W>) -> std::fmt::Result {
        write!(xml, "{}", self)
    }

    fn ser_attrs<W: Write>(&self, _xml: &mut nanoxml::ser::XmlBuilder<'_, W>) -> std::fmt::Result {
        Ok(())
    }
}

fn is_leap_year(year: u32) -> bool {
    year % 4 == 0 && (year % 100 != 0 || year % 400 == 0)
}

pub fn process_ebus_clock_time(bytes: &[u8]) {
    let &[_, _, sec, min, hour, day, month, dow, year] = bytes else {
        warn!("Got invalid bus clock time: bytes={}", bytes);
        return;
    };

    if sec == 0xFF {
        info!("Got unavailable bus time");
        return;
    }

    if !(1..=7).contains(&dow) {
        warn!("Got invalid day of week");
        return;
    }

    fn bcd_decode(bcd: u8) -> u8 {
        ((bcd >> 4) * 10) + (bcd & 0x0F)
    }

    let sec = bcd_decode(sec);
    let min = bcd_decode(min);
    let hour = bcd_decode(hour);
    let day = bcd_decode(day);
    let month = bcd_decode(month);
    let year = bcd_decode(year);

    let time = ClockTime {
        seconds_of_day: sec as u32 + min as u32 * 60 + hour as u32 * 60 * 60,
        day,
        month,
        year: year as u32 + 2000,
    };

    info!("Updating bus time: time={}", time);
    state().ebus.time.update(StoredClockTime {
        time: Some(time),
        received_at: Instant::now(),
    });
}

impl Format for ClockTime {
    fn format(&self, fmt: defmt::Formatter) {
        defmt::write!(
            fmt,
            "{}-{}-{}+{}s",
            self.year,
            self.month,
            self.day,
            self.seconds_of_day
        );
    }
}

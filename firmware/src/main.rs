#![feature(
    allocator_api,
    array_chunks,
    async_fn_traits,
    const_trait_impl,
    future_join,
    iter_array_chunks,
    iter_intersperse,
    let_chains,
    never_type,
    non_null_from_ref,
    round_char_boundary,
    str_split_remainder,
    str_split_whitespace_remainder,
    string_remove_matches
)]
#![allow(unexpected_cfgs, unused_labels, async_fn_in_trait)]
#![allow(clippy::missing_const_for_thread_local)] // allowed due to clippy bug lmao
#![allow(clippy::new_without_default)]
#![deny(clippy::large_futures)]

use std::future::pending;

use esp_idf_svc::{
    eth::{RmiiClockConfig, RmiiEthChipset},
    eventloop::EspSystemEventLoop,
    hal::{
        cpu::Core,
        gpio::{self, Input, PinDriver},
        modem::Modem,
        peripheral::Peripheral,
        peripherals::Peripherals,
        reset::ResetReason,
        task::{
            block_on,
            watchdog::{TWDTConfig, TWDTDriver},
        },
    },
    ota::{EspOta, SlotState},
};

use crate::{
    debug::stdin::stdin_listener_task,
    ebus::{
        api::{ebus_discovery_task, error_history_collection_task},
        task::create_ebus_task,
    },
    eebus::{eebus_state_machines_task, eebus_websocket_task},
    flash::{
        eventlog::{events::Event, task::create_eventlog_task},
        ota::task::create_ota_task,
    },
    init::*,
    led_btn::Leds,
    modbus::{api::modbus_discovery_task, task::create_modbus_task},
    net::{
        broadcast::broadcast_task,
        dns::dns_server_task,
        eth::{reset_eth, EthPins},
        mdns::setup_mdns,
        task::NetworkTask,
    },
    prelude::*,
    server::start_web_server,
    smartset::{local::local_connect_task, portal::portal_connect_task},
    utils::{error::AnyError, hwversion, task},
};

pub mod debug;
pub mod ebus;
pub mod eebus;
pub mod env;
pub mod flash;
pub mod init;
pub mod itc;
pub mod led_btn;
pub mod modbus;
pub mod net;
pub mod prelude;
pub mod server;
pub mod smartset;
pub mod state;
pub mod sync;
pub mod time;
pub mod utils;

fn main() {
    sys::link_patches();
    init_logging();
    apply_own_patches();
    output_startup_info();
    init_alloc_fail_handler();

    let mut ota = EspOta::new().unwrap();
    if matches!(ota.get_running_slot().unwrap().state, SlotState::Unverified) {
        match perform_tests() {
            Ok(()) => ota.mark_running_slot_valid().unwrap(),
            Err(e) => {
                error!("Failed to run application tests: err={}", e);
                info!("Rolling back to previous version");
                ota.mark_running_slot_invalid_and_reboot();
                error!("Continuing due to failed rollback");
            }
        }
    }

    task::spawn_external("coordinator", 100_000, 5, Some(Core::Core0), || app());
}

fn app() -> ! {
    let err = block_on(coordinator()).unwrap_err();
    error!("Failed to start application: err={}", err);

    block_for(Duration::from_secs(1));
    hal::reset::restart();
}

async fn coordinator() -> Result<()> {
    let peripherals = Peripherals::take().unwrap();
    let pins = peripherals.pins;

    create_ebus_task(peripherals.uart1, pins.gpio32, pins.gpio35)?;
    create_nvs_task();

    create_eventlog_task();
    itc().eventlog.write(Event::Start(ResetReason::get())).await;
    itc().eventlog.flush(&ReturnSlot::new()).await;

    perform_migrations().await?;
    env::init_active_params().await;

    detect_test_mode().await;
    hwversion::init(peripherals.adc1, pins.gpio36).await?;

    env::init_shared();

    info!("Configuring peripherals");

    let sysloop = EspSystemEventLoop::take().unwrap();

    let btn_pin = PinDriver::input(pins.gpio34).unwrap();
    let mut leds = Leds::new(pins.gpio2, pins.gpio12, pins.gpio13);
    leds.bootup_sequence().await?;

    reset_eth(pins.gpio5, pins.gpio4).await?;

    let eth_pins = EthPins {
        mac: peripherals.mac,
        rmii_rdx0: pins.gpio25,
        rmii_rdx1: pins.gpio26,
        rmii_crs_dv: pins.gpio27,
        rmii_mdc: pins.gpio23,
        rmii_txd1: pins.gpio22,
        rmii_tx_en: pins.gpio21,
        rmii_txd0: pins.gpio19,
        rmii_mdio: pins.gpio18,
        rmii_ref_clk_config: RmiiClockConfig::Input(pins.gpio0),
    };

    create_net_task(
        peripherals.modem,
        eth_pins,
        Some(pins.gpio33),
        RmiiEthChipset::LAN87XX,
        None,
        sysloop.clone(),
    );

    setup_mdns().await;
    create_modbus_task(peripherals.uart2, pins.gpio15, pins.gpio39, pins.gpio14);
    create_led_btn_task(btn_pin, leds);
    create_stdin_task(peripherals.uart0, pins.gpio3);
    create_ota_task();

    info!("Running application");

    let leds = LedBits::EbusDiscovery | LedBits::ModbusDiscovery;
    state().led.enable(leds);

    if let Err(e) = start_web_server().await {
        error!("Failed to start webserver: err={}", e);
    }

    env::spawn(modbus_discovery_task());
    env::spawn(ebus_discovery_task());
    env::spawn(error_history_collection_task());
    env::spawn(stdin_listener_task());
    env::spawn(dns_server_task());
    env::spawn(broadcast_task());
    env::spawn(portal_connect_task());
    env::spawn(local_connect_task());
    env::spawn(eebus_state_machines_task());
    env::spawn(eebus_websocket_task());
    // start_ebus_activity_handler();
    // start_bundle_handler();

    let mut wdt_config = TWDTConfig::new();
    wdt_config.duration =
        core::time::Duration::from_secs(sys::CONFIG_ESP_TASK_WDT_TIMEOUT_S as u64);
    let mut wdt_driver = TWDTDriver::new(peripherals.twdt, &wdt_config)?;
    let mut wd_sub = wdt_driver.watch_current_task()?;

    env::executor()
        .run(async {
            let mut timer = Ticker::every(secs(1));
            let mut i = 0;
            loop {
                i += 1;
                timer.next().await;
                wd_sub.feed().unwrap();
                // Sleep for two tick every 5 seconds to allow low prio tasks to run
                if i == 2 {
                    i = 0;
                    block_for(Duration::from_millis(
                        1000 / esp_idf_svc::hal::delay::TICK_RATE_HZ as u64 + 1,
                    ));
                }
            }
        })
        .await;

    Ok(())
}

fn create_led_btn_task(btn: PinDriver<'static, gpio::Gpio34, Input>, leds: Leds) {
    info!("Starting led_btn task");
    task::spawn_external("led_btn", 4000, 8, Some(Core::Core1), move || {
        let res = if state().test_mode() {
            block_on(pending())
        } else {
            block_on(led_btn::led_btn_task(btn, leds))
        };
        error!("Failed to run button task: err={}", res.unwrap_err());
        std::process::abort();
    });
}

fn create_stdin_task(uart: esp_idf_svc::hal::uart::UART0, uart0_rx: gpio::Gpio3) {
    info!("Starting stdin task");
    task::spawn_external("stdio", 5500, 6, None, move || {
        block_on(debug::stdin_task::stdin_task(uart, uart0_rx));
    });
}

fn create_nvs_task() {
    info!("Started nvs task");
    task::spawn_pinned("nvs", 4000, 6, None, move || block_on(nvs::task::task()));
}

fn create_net_task(
    wifi_modem: impl Peripheral<P = Modem> + Send + 'static,
    eth_pins: EthPins,
    eth_rst: Option<impl Peripheral<P = impl gpio::OutputPin> + Send + 'static>,
    eth_chipset: RmiiEthChipset,
    eth_phy_addr: Option<u32>,
    sysloop: EspSystemEventLoop,
) {
    info!("Started net task");
    task::spawn_pinned("net", 12000, 6, None, move || {
        let Err(err): Result<!, AnyError> = block_on(async move {
            let task = NetworkTask::new(
                wifi_modem,
                eth_pins,
                eth_rst,
                eth_chipset,
                eth_phy_addr,
                sysloop,
            )
            .await?;
            block_on(task.run());
        });
        error!("Failed to start net task: err={}", err);
    });
}

#[used]
#[no_mangle]
#[allow(non_upper_case_globals)]
#[link_section = ".rodata_desc"]
pub static esp_app_desc: sys::esp_app_desc_t = {
    const fn str_to_cstr_array<const C: usize>(s: &str) -> [::core::ffi::c_char; C] {
        let mut ret: [::core::ffi::c_char; C] = [0; C];
        let mut i = 0;
        while i < s.len() {
            ret[i] = s.as_bytes()[i] as _;
            i += 1;
        }
        ret
    }

    sys::esp_app_desc_t {
        magic_word: sys::ESP_APP_DESC_MAGIC_WORD,
        secure_version: sys::CONFIG_BOOTLOADER_APP_SECURE_VERSION,
        reserv1: [0; 2],
        version: str_to_cstr_array(utils::version::FULL),
        project_name: str_to_cstr_array("link-redesign"),
        time: str_to_cstr_array(sys::build_time::build_time_utc!("%Y-%m-%d")),
        date: str_to_cstr_array(sys::build_time::build_time_utc!("%H:%M:%S")),
        idf_ver: str_to_cstr_array(sys::const_format::formatcp!(
            "{}.{}.{}",
            sys::ESP_IDF_VERSION_MAJOR,
            sys::ESP_IDF_VERSION_MINOR,
            sys::ESP_IDF_VERSION_PATCH
        )),
        app_elf_sha256: [0; 32],

        #[cfg(not(any(
            esp_idf_version_major = "4",
            esp_idf_version = "5.0",
            esp_idf_version = "5.1",
            esp_idf_version_full = "5.2.0",
            esp_idf_version_full = "5.2.1",
            esp_idf_version_full = "5.2.2",
            esp_idf_version_full = "5.3.0",
            esp_idf_version_full = "5.3.1"
        )))]
        max_efuse_blk_rev_full: 0,

        #[cfg(not(any(
            esp_idf_version_major = "4",
            esp_idf_version = "5.0",
            esp_idf_version = "5.1",
            esp_idf_version_full = "5.2.0",
            esp_idf_version_full = "5.2.1",
            esp_idf_version_full = "5.2.2",
            esp_idf_version_full = "5.3.0",
            esp_idf_version_full = "5.3.1"
        )))]
        min_efuse_blk_rev_full: 0,

        #[cfg(not(any(
            esp_idf_version_major = "4",
            esp_idf_version = "5.0",
            esp_idf_version = "5.1",
            esp_idf_version = "5.2",
            esp_idf_version = "5.3"
        )))]
        reserv2: [0; 18],
        #[cfg(any(esp_idf_version_full = "5.2.3", esp_idf_version_full = "5.3.2"))]
        reserv2: [0; 19],
        #[cfg(any(
            esp_idf_version_major = "4",
            esp_idf_version = "5.0",
            esp_idf_version = "5.1",
            esp_idf_version_full = "5.2.0",
            esp_idf_version_full = "5.2.1",
            esp_idf_version_full = "5.2.2",
            esp_idf_version_full = "5.3.0",
            esp_idf_version_full = "5.3.1"
        ))]
        reserv2: [0; 20],
    }
};

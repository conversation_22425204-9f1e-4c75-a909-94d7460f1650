use std::fmt::Debug;

use application::msg::*;
use pretty_assertions::assert_eq;
use regex::Regex;
use serde::{de::DeserializeOwned, Serialize};

fn check_ser<T: Serialize>(v: T, expected: &str) {
    let actual = quick_xml::se::to_string(&v).unwrap();
    let expected = Regex::new(">\\s+").unwrap().replace_all(expected, ">");

    assert_eq!(expected, actual);
}

fn check_de<T: Debug + DeserializeOwned + PartialEq>(source: &str, expected: T) {
    let actual: T = quick_xml::de::from_str(source).unwrap();

    assert_eq!(expected, actual);
}

#[test]
fn portal_logon_request() {
    check_ser(
        PortalLogonRequest {
            rand: 107447910,
            system_name: "ISM7iHRO",
            password: "1234",
            serial: "001ec0e4c30d",
            date_time: "2015-04-07T14:11:27",
            ism_software_version: 168,
            ism_software_number: 1,
            ism_hardware_version: "1.0",
            wlan_connected: false,
            ty: "ISM7i",
            v2_version: "4.10.7",
            v2_commit_hash: "a1f5bb7c",
            v2_test: None,
        },
        include_str!("xml/portal-logon-request.xml"),
    );
}

#[test]
fn portal_logon_response() {
    check_de(
        include_str!("xml/portal-logon-response1.xml"),
        PortalLogonResponse {
            state: PortalState::Ok,
            session_id: 625,
            next_connect_params: None,
        },
    );
    check_de(
        include_str!("xml/portal-logon-response2.xml"),
        PortalLogonResponse {
            state: PortalState::Busy,
            session_id: 625,
            next_connect_params: Some(NextConnectParams {
                dest_server: "example.com".to_owned(),
                dest_port: 42,
                delay_sec: 3,
                max_try: 5,
            }),
        },
    );
}

#[test]
fn tbreq() {
    check_de(
        include_str!("xml/tbreq1.xml"),
        BundleRequest {
            bundle_id: 1,
            gateway_id: 625,
            abort_on_error: false,
            ty: BundleType::Pull,
            info_reads: vec![
                InfoRead {
                    sequence: "A;0".to_owned().into(),
                    bus_address: 8,
                    numbers: Numbers::from_single(10017),
                    interval: 60,
                    service_number: 0x5022,
                    hysteresis: None,
                    last_sent_values: None,
                },
                InfoRead {
                    sequence: "A;1".to_owned().into(),
                    bus_address: 8,
                    numbers: Numbers::from_single(10033),
                    interval: 60,
                    service_number: 0x5022,
                    hysteresis: None,
                    last_sent_values: None,
                },
                InfoRead {
                    sequence: "A;2".to_owned().into(),
                    bus_address: 8,
                    numbers: Numbers::from_single(10075),
                    interval: 60,
                    service_number: 0x5022,
                    hysteresis: None,
                    last_sent_values: None,
                },
                InfoRead {
                    sequence: "A;3".to_owned().into(),
                    bus_address: 8,
                    numbers: Numbers::from_single(2751),
                    interval: 60,
                    service_number: 0x5022,
                    hysteresis: None,
                    last_sent_values: None,
                },
                InfoRead {
                    sequence: "A;4".to_owned().into(),
                    bus_address: 53,
                    numbers: Numbers::from_single(10163),
                    interval: 60,
                    service_number: 0x5022,
                    hysteresis: None,
                    last_sent_values: None,
                },
            ],
            info_writes: vec![],
            info_writes_multiple: vec![],
            svc03f1_reads: vec![],
            svc03f3_writes: vec![],
            svc_reads: vec![],
            error_reads: vec![],
            modbus_reads: vec![],
            modbus_writes: vec![],
        },
    );

    check_de(
        include_str!("xml/tbreq2.xml"),
        BundleRequest {
            bundle_id: 100,
            gateway_id: 625,
            abort_on_error: true,
            ty: BundleType::Push,
            info_reads: vec![
                InfoRead {
                    sequence: "A;27".to_owned().into(),
                    bus_address: 8,
                    numbers: Numbers::from_single(370),
                    interval: 60,
                    service_number: 0x5022,
                    hysteresis: None,
                    last_sent_values: None,
                },
                InfoRead {
                    sequence: "B;28;41".to_owned().into(),
                    bus_address: 8,
                    numbers: Numbers::from_single(371),
                    interval: 60,
                    service_number: 0x5022,
                    hysteresis: None,
                    last_sent_values: None,
                },
                InfoRead {
                    sequence: "A;23".to_owned().into(),
                    bus_address: 8,
                    numbers: Numbers::from_single(328),
                    interval: 60,
                    service_number: 0x5022,
                    hysteresis: None,
                    last_sent_values: None,
                },
                InfoRead {
                    sequence: "C;30;553;552".to_owned().into(),
                    bus_address: 8,
                    numbers: Numbers::from_single(553),
                    interval: 60,
                    service_number: 0x5022,
                    hysteresis: None,
                    last_sent_values: None,
                },
                InfoRead {
                    sequence: "C;30;553;552".to_owned().into(),
                    bus_address: 8,
                    numbers: Numbers::from_single(552),
                    interval: 60,
                    service_number: 0x5022,
                    hysteresis: None,
                    last_sent_values: None,
                },
                InfoRead {
                    sequence: "A;84".to_owned().into(),
                    bus_address: 53,
                    numbers: Numbers::from_single(10150),
                    interval: 60,
                    service_number: 0x5022,
                    hysteresis: None,
                    last_sent_values: None,
                },
                InfoRead {
                    sequence: "A;85".to_owned().into(),
                    bus_address: 53,
                    numbers: Numbers::from_single(10145),
                    interval: 60,
                    service_number: 0x5022,
                    hysteresis: None,
                    last_sent_values: None,
                },
            ],
            info_writes: vec![],
            info_writes_multiple: vec![],
            svc03f1_reads: vec![],
            svc03f3_writes: vec![],
            svc_reads: vec![],
            error_reads: vec![],
            modbus_reads: vec![],
            modbus_writes: vec![],
        },
    );

    check_de(
        include_str!("xml/tbreq9.xml"),
        BundleRequest {
            bundle_id: 603,
            gateway_id: 579,
            abort_on_error: true,
            ty: BundleType::Push,
            info_reads: vec![],
            info_writes: vec![],
            info_writes_multiple: vec![],
            svc03f1_reads: vec![],
            svc03f3_writes: vec![],
            svc_reads: vec![],
            error_reads: vec![],
            modbus_reads: vec![
                ModbusRead {
                    sequence: "F;660000;0".into(),
                    bus_address: 1,
                    function_code: 1,
                    start_address: 2,
                    count: 149,
                    interval: 60,
                    last_sent_response_hash: None,
                },
                ModbusRead {
                    sequence: "F;660000;0".into(),
                    bus_address: 1,
                    function_code: 1,
                    start_address: 165,
                    count: 30,
                    interval: 60,
                    last_sent_response_hash: None,
                },
                ModbusRead {
                    sequence: "F;660000;0".into(),
                    bus_address: 1,
                    function_code: 3,
                    start_address: 1053,
                    count: 2,
                    interval: 60,
                    last_sent_response_hash: None,
                },
                ModbusRead {
                    sequence: "F;660000;0".into(),
                    bus_address: 1,
                    function_code: 3,
                    start_address: 5003,
                    count: 121,
                    interval: 60,
                    last_sent_response_hash: None,
                },
            ],
            modbus_writes: vec![],
        },
    );

    check_de(
        include_str!("xml/tbreq10.xml"),
        BundleRequest {
            bundle_id: 1099,
            gateway_id: 579,
            abort_on_error: true,
            ty: BundleType::Write,
            info_reads: vec![],
            info_writes: vec![],
            info_writes_multiple: vec![],
            svc03f1_reads: vec![],
            svc03f3_writes: vec![],
            svc_reads: vec![],
            error_reads: vec![],
            modbus_reads: vec![],
            modbus_writes: vec![
                ModbusWrite {
                    sequence: "F;660000;0".into(),
                    bus_address: 1,
                    function_code: 5,
                    start_address: 625,
                    count: 1,
                    data_bytes: vec![0xFF, 0x00],
                },
                ModbusWrite {
                    sequence: "F;660000;0".into(),
                    bus_address: 1,
                    function_code: 5,
                    start_address: 625,
                    count: 1,
                    data_bytes: vec![0x00, 0x00],
                },
                ModbusWrite {
                    sequence: "F;660000;0".into(),
                    bus_address: 1,
                    function_code: 16,
                    start_address: 5504,
                    count: 2,
                    data_bytes: vec![0x01, 0xA4, 0x03, 0x84],
                },
                ModbusWrite {
                    sequence: "F;660000;0".into(),
                    bus_address: 1,
                    function_code: 16,
                    start_address: 5544,
                    count: 1,
                    data_bytes: vec![0x00, 0x02],
                },
            ],
        },
    );
}

#[test]
fn tbres() {
    check_ser(
        BundleResponse {
            status: BundleStatus::Ok,
            error_msg: None,
            bundle_id: 100,
            gateway_id: 625,
            time_stamp: Some("2015-04-06T14:35:39".to_owned()),
            info_responses: vec![
                InfoResponse {
                    sequence: "A;44".to_owned().into(),
                    bus_address: 0x3C,
                    service_number: 0x4022,
                    number: 7,
                    data_low: 0xD3,
                    data_high: 0x0,
                    status: DataPointStatus::Ok,
                    max: None,
                    min: None,
                },
                InfoResponse {
                    sequence: "A;45".to_owned().into(),
                    bus_address: 0x3C,
                    service_number: 0x4022,
                    number: 8,
                    data_low: 0xD3,
                    data_high: 0x0,
                    status: DataPointStatus::Ok,
                    max: None,
                    min: None,
                },
            ],
            info_acks: vec![],
            svc03f2_responses: vec![],
            svc03f3_acks: vec![],
            error_responses: vec![],
            service_responses: vec![],
            modbus_responses: vec![],
            modbus_acks: vec![],
        },
        include_str!("xml/tbres1.xml"),
    );
}

use std::{
    collections::{hash_map::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>hMap},
    hash::{<PERSON>h, <PERSON><PERSON>},
};

use defmt::Debug2Format;

use crate::{
    ebus::api::EbusReply,
    prelude::*,
    smartset::msg::{
        helpers::SeqNr, BundleRequest, DataPointStatus, ErrorResponse, InfoAck, InfoResponse,
        Service03f2Response, Service03f3Ack, ServiceResponse,
    },
};

fn substitute_blocked(bundle_id: u16, whitelisted: u8, n: usize, i: usize, x: u16) -> u16 {
    if whitelisted & (1 << (n - 1 - i)) != 0 {
        x
    } else {
        info!(
            "Blocking bundle due to read on non-whitelisted info num, bundle={}, info={}, index={}",
            bundle_id, x, i
        );
        349
    }
}

pub async fn process_info_reads(
    msg: &mut BundleRequest,
    mut wl_check: impl FnMut(u8, u16) -> bool,
    slot: &ReturnSlot<EbusReply>,
) -> Result<Vec<InfoResponse>> {
    let bundle_id = msg.bundle_id;
    let mut info_responses = vec![];

    let mut c_groups = HashMap::<SeqNr, Vec<(InfoResponse, bool)>>::default();

    for info in &mut msg.info_reads {
        let whitelisted = info
            .numbers
            .iter()
            .map(|nr| wl_check(info.bus_address.0, nr))
            .fold(0, |acc, i| (acc << 1) | i as u8);

        let n = info.numbers.0.len();

        let max_age = (info.interval as u64 / 2).max(1) - 1;

        let data = if whitelisted == 0 {
            info!(
                "Blocking bundle due to lack of whitelisted info nums: bundle={}, ird={}",
                bundle_id,
                info.sequence.0.as_str()
            );
            None
        } else {
            let numbers: heapless::Vec<u16, 5> = info
                .numbers
                .0
                .iter()
                .enumerate()
                .map(|(i, &x)| substitute_blocked(bundle_id, whitelisted, n, i, x))
                .collect();
            ebus()
                .read_info_num(
                    info.service_number.0,
                    &numbers,
                    info.bus_address.0,
                    slot,
                    secs(max_age),
                )
                .await
                .ok()
        };

        let Some(values) = data else {
            info.last_sent_values = None;
            continue;
        };

        let first_time = info.last_sent_values.is_none();
        if first_time {
            info.last_sent_values = Some([0; 5]);
        }
        let last_sent_values = info.last_sent_values.as_mut().unwrap();

        for (ind, ((number, data), seq)) in info
            .numbers
            .iter()
            .zip(values)
            .zip(info.sequence.iter())
            .enumerate()
        {
            let is_whitelisted = whitelisted & (1 << (n - 1 - ind)) != 0;

            let info = InfoResponse {
                sequence: seq.clone().into_owned(),
                bus_address: info.bus_address,
                service_number: match service_nr_is_default(info.service_number.0) {
                    true => None,
                    false => Some(info.service_number),
                },
                number,
                data_low: data.data_low.into(),
                data_high: data.data_high.into(),
                status: if is_whitelisted {
                    DataPointStatus::Ok
                } else {
                    DataPointStatus::NotWhitelisted
                },
                max: data.max.map(|x| x.into()),
                min: data.min.map(|x| x.into()),
            };

            let combined = data.data_low as u16 | ((data.data_high as u16) << 8);

            if seq.is_combined() {
                c_groups.entry(seq.into_owned()).or_default().push((
                    info,
                    first_time || (is_whitelisted && last_sent_values[ind] != combined),
                ));
                last_sent_values[ind] = combined;
                continue;
            }

            if !first_time && last_sent_values[ind] == combined {
                continue;
            }
            last_sent_values[ind] = combined;

            info_responses.push(info);
        }
    }

    // work through C groups to send all or none
    for (_seq, responses) in c_groups {
        if responses
            .iter()
            .any(|(_info, prompts_sending)| *prompts_sending)
        {
            info_responses.extend(responses.into_iter().map(|x| x.0));
        }
    }

    Ok(info_responses)
}

pub async fn process_info_writes(
    msg: &BundleRequest,
    mut wl_check: impl FnMut(u8, u16) -> bool,
    slot: &ReturnSlot<EbusReply>,
) -> Result<Vec<InfoAck>> {
    let mut info_acks = vec![];

    let ebus = ebus();

    for info in &msg.info_writes {
        let whitelisted = wl_check(info.bus_address.0, info.number);

        if !whitelisted {
            info!(
                "Blocking bundle due to read on non-whitelisted info num, bundle={}, info={}",
                msg.bundle_id, info.number
            );
            info_acks.push(InfoAck {
                sequence: info.sequence.clone(),
                bus_address: info.bus_address,
                number: info.number,
                data_low: 0.into(),
                data_high: 0.into(),
                status: DataPointStatus::NotWhitelisted,
                service_number: info.service_number,
            });
            continue;
        }
        let reply = ebus
            .write_info_nums(
                info.service_number.0,
                &[
                    info.number,
                    ((info.data_high.0 as u16) << 8) | info.data_low.0 as u16,
                ],
                info.bus_address.0,
                slot,
            )
            .await?;

        let status = if reply.is_okay() {
            let readback_svc = match info.service_number.0 {
                0x4080 => 0x4050,
                _ => 0x5022,
            };

            match ebus
                .readback_info_num(readback_svc, &[info.number], info.bus_address.0, slot)
                .await
                .ok()
                .and_then(|x| x.first().cloned())
            {
                Some(readback)
                    if readback.data_low == info.data_low.0
                        && readback.data_high == info.data_high.0 =>
                {
                    DataPointStatus::Ok
                }
                _ => {
                    info!("Got negative readback: infowrite={}", Debug2Format(&info));
                    DataPointStatus::Timeout
                }
            }
        } else {
            info!(
                "Got timeout: infowrite={}, reply={}",
                Debug2Format(&info),
                Debug2Format(&reply)
            );
            DataPointStatus::Timeout
        };
        info_acks.push(InfoAck {
            sequence: info.sequence.clone(),
            bus_address: info.bus_address,
            number: info.number,
            data_low: info.data_low,
            data_high: info.data_high,
            status,
            service_number: info.service_number,
        });
    }

    for info in &msg.info_writes_multiple {
        let data: heapless::Vec<u16, 10> = info
            .numbers
            .iter()
            .zip(info.data_bytes.0.array_chunks::<2>())
            .flat_map(|(num, &[high, low])| {
                if wl_check(info.bus_address.0, num) {
                    [num, ((high as u16) << 8) | low as u16]
                } else {
                    info!(
                        "Blocking bundle due to iwrx on non-whitelisted info num, bundle={}, info={}",
                        msg.bundle_id, num
                    );
                    // if not whitelisted, write dummy
                    [349, 0]
                }
            })
            .collect();
        let reply = if data.iter().step_by(2).any(|n| *n != 349) {
            ebus.write_info_nums(info.service_number.0, &data, info.bus_address.0, slot)
                .await?
        } else {
            // all info nums are writing dummy info
            EbusReply::Ok
        };
        if !reply.is_okay() {
            info!(
                "Got timeout: iwrx={}, reply={}",
                Debug2Format(&info),
                Debug2Format(&reply)
            );
        }

        for (i, (seq, number)) in info.sequence.iter().zip(info.numbers.iter()).enumerate() {
            let status = if wl_check(info.bus_address.0, number) {
                if reply.is_okay() {
                    match ebus
                        .readback_info_num(0x5022, &[number], info.bus_address.0, slot)
                        .await
                        .ok()
                        .and_then(|x| x.first().cloned())
                    {
                        Some(readback)
                            if readback.data_low == info.data_bytes.0[i * 2 + 1]
                                && readback.data_high == info.data_bytes.0[i * 2] =>
                        {
                            DataPointStatus::Ok
                        }
                        _ => {
                            info!(
                                "Got negative readback: iwrx={}, index={}",
                                Debug2Format(&info),
                                i
                            );
                            DataPointStatus::Timeout
                        }
                    }
                } else {
                    DataPointStatus::Timeout
                }
            } else {
                DataPointStatus::NotWhitelisted
            };
            info_acks.push(InfoAck {
                sequence: seq.into_owned(),
                bus_address: info.bus_address,
                number,
                data_low: info.data_bytes.0[2 * i + 1].into(),
                data_high: info.data_bytes.0[2 * i].into(),
                status,
                service_number: info.service_number,
            });
        }
    }

    Ok(info_acks)
}

pub async fn process_svc03f2_reads(
    msg: &mut BundleRequest,
    slot: &ReturnSlot<EbusReply>,
) -> Result<Vec<Service03f2Response>> {
    let mut svc03f2_responses = vec![];

    for timeprog in &mut msg.svc03f1_reads {
        // The mapping between ebus and smartset naming is a bit confusing here.
        // The number = heating program (hp) and the instance is heating_circuit (hk)
        let reply = ebus()
            .read_timeprog(
                timeprog.bus_address.0,
                timeprog.day_of_week,
                timeprog.heating_program,
                timeprog.heating_program_ty,
                timeprog.heating_circuit,
                slot,
            )
            .await;
        // TODO: this is inconsistent with the other error handling
        // errors will always lead to the 2 second timeout
        let status = if reply.is_ok() {
            DataPointStatus::Ok
        } else {
            DataPointStatus::Timeout
        };
        let data_bytes = reply.map(|tp| tp.bytes).unwrap_or([0u8; 20]);
        let mut hasher = DefaultHasher::default();
        data_bytes.hash(&mut hasher);
        let hash = hasher.finish();

        if let Some(last_hash) = timeprog.last_sent_response_hash
            && last_hash == hash
        {
            continue;
        }
        timeprog.last_sent_response_hash = Some(hash);

        let response = Service03f2Response {
            sequence: timeprog.sequence.clone(),
            bus_address: timeprog.bus_address,
            heating_program: timeprog.heating_program,
            heating_program_ty: timeprog.heating_program_ty,
            heating_circuit: timeprog.heating_circuit,
            day_of_week: timeprog.day_of_week,
            status,
            data_bytes: data_bytes.into(),
        };
        svc03f2_responses.push(response);
    }

    Ok(svc03f2_responses)
}

pub async fn process_svc03f2_writes(
    msg: &BundleRequest,
    slot: &ReturnSlot<EbusReply>,
) -> Result<Vec<Service03f3Ack>> {
    let mut svc03f3_acks = vec![];

    for timeprog in &msg.svc03f3_writes {
        let reply = ebus()
            .write_timeprog(
                timeprog.bus_address.0,
                timeprog.day_of_week,
                timeprog.heating_program,
                timeprog.heating_program_ty,
                timeprog.heating_circuit,
                timeprog.data_bytes.0,
                slot,
            )
            .await?;
        let status = if reply.is_okay() {
            DataPointStatus::Ok
        } else {
            DataPointStatus::Timeout
        };
        svc03f3_acks.push(Service03f3Ack {
            sequence: timeprog.sequence.clone(),
            bus_address: timeprog.bus_address,
            status,
            heating_program: timeprog.heating_program,
            heating_program_ty: timeprog.heating_program_ty,
            heating_circuit: timeprog.heating_circuit,
            day_of_week: timeprog.day_of_week,
        });
    }

    Ok(svc03f3_acks)
}

pub async fn process_service_reads(_msg: &BundleRequest) -> Result<Vec<ServiceResponse>> {
    // TODO: handle srd
    Ok(vec![])
}

pub async fn process_error_reads(msg: &mut BundleRequest) -> Result<Option<Vec<ErrorResponse>>> {
    let Some(erd) = msg.error_reads.last_mut() else {
        return Ok(None);
    };

    let mut error_responses = vec![];
    let histories = env::shared().ebus_history().histories.lock().await;

    let mut hasher = DefaultHasher::default();
    histories.hash(&mut hasher);
    let hash = hasher.finish();

    if let Some(last) = erd.last_sent_response_hash
        && last == hash
    {
        return Ok(None);
    }

    for (index, entry) in histories.entries() {
        error_responses.push(ErrorResponse {
            sequence: erd.sequence,
            bus_address: entry.addr.into(),
            index: index as u8,
            code: entry.code,
            active: entry.active,
            start: entry.start.as_ref().map(ToString::to_string),
            stop: entry.end.as_ref().map(ToString::to_string),
        });
    }

    erd.last_sent_response_hash = Some(hash);

    Ok(Some(error_responses))
}

fn service_nr_is_default(snr: u16) -> bool {
    snr == 0x5022
}

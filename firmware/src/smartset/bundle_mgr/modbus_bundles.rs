use std::{
    collections::hash_map::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    hash::{<PERSON>h, <PERSON><PERSON>},
};

use crate::{
    modbus::api::*,
    prelude::*,
    smartset::msg::{BundleRequest, ModbusAck, ModbusResponse, ModbusStatus},
};

pub async fn process_modbus_reads(
    msg: &mut BundleRequest,
    slot: &ReturnSlot<ModbusReply>,
) -> Result<Vec<ModbusResponse>> {
    let mut modbus_responses = Vec::new();

    for modbus_read in &mut msg.modbus_reads {
        let function = match modbus_read.function_code {
            READ_COILS => ModbusFunction::ReadCoils {
                count: modbus_read.count,
            },
            READ_REGISTERS => ModbusFunction::ReadRegisters {
                count: modbus_read.count,
            },
            other_fc => {
                error!(
                    "Received unexpected function code for ModbusRead, fc={}",
                    other_fc
                );
                continue;
            }
        };

        let req = ModbusRequest {
            bus_address: modbus_read.bus_address.0,
            data_address: (modbus_read.start_address - 1) as u16,
            function,
        };

        let resp = request(req, slot).await;

        let (data, status) = match resp {
            Ok(ModbusData {
                data: ModbusDataResponse::ReadCoils { data },
                ..
            }) => (Some(data), ModbusStatus::Ok),
            Ok(ModbusData {
                data: ModbusDataResponse::ReadRegisters { data },
                ..
            }) => (Some(data), ModbusStatus::Ok),
            Ok(_) => defmt::unreachable!("modbus::request should filter out mismatches"),
            Err(ModbusError::Exception) => (None, ModbusStatus::Exception),
            Err(_) => (None, ModbusStatus::CommunicationError),
        };

        if let Some(ref data) = data {
            let mut hasher = DefaultHasher::default();
            data.hash(&mut hasher);
            let hash = hasher.finish();
            if let Some(last_hash) = modbus_read.last_sent_response_hash
                && last_hash == hash
            {
                continue;
            }
            modbus_read.last_sent_response_hash = Some(hash);
        }

        modbus_responses.push(ModbusResponse {
            sequence: modbus_read.sequence.clone(),
            bus_address: modbus_read.bus_address,
            function_code: modbus_read.function_code,
            start_address: modbus_read.start_address,
            count: data.as_ref().map(|x| x.len() as u16),
            data_bytes: data.map(|x| x.into()),
            status,
        });
    }

    Ok(modbus_responses)
}

pub async fn process_modbus_writes(
    msg: &BundleRequest,
    slot: &ReturnSlot<ModbusReply>,
) -> Result<Vec<ModbusAck>> {
    let mut modbus_acks = Vec::new();

    for modbus_write in &msg.modbus_writes {
        let function = match modbus_write.function_code {
            WRITE_SINGLE_COIL => {
                let value = match &modbus_write.data_bytes.0[..] {
                    [0x00, 0x00] => [0x00, 0x00],
                    [0x00, 0xFF] => [0x00, 0xFF],
                    _ => {
                        error!("Got invalid coil write data");
                        continue;
                    }
                };
                ModbusFunction::WriteSingleCoil { value }
            }
            WRITE_SINGLE_REGISTER => {
                if modbus_write.data_bytes.0.len() != 2 {
                    error!("Got incorrect register write data");
                    continue;
                }
                let value = modbus_write.data_bytes.0[..2].try_into().unwrap();
                ModbusFunction::WriteSingleRegister { value }
            }
            WRITE_MULTIPLE_COILS => ModbusFunction::WriteMultipleCoils {
                values: modbus_write.data_bytes.0.clone(),
            },
            WRITE_MULTIPLE_REGISTERS => ModbusFunction::WriteMultipleRegisters {
                values: modbus_write.data_bytes.0.clone(),
            },
            other_fc => {
                error!(
                    "Received unexpected function code for ModbusWrite, fc={}",
                    other_fc
                );
                continue;
            }
        };

        let req = ModbusRequest {
            bus_address: modbus_write.bus_address.0,
            data_address: (modbus_write.start_address - 1) as u16,
            function,
        };

        let resp = request(req, slot).await;

        let status = match resp {
            Ok(_) => ModbusStatus::Ok,
            Err(ModbusError::Exception) => ModbusStatus::Exception,
            Err(_) => ModbusStatus::CommunicationError,
        };

        modbus_acks.push(ModbusAck {
            sequence: modbus_write.sequence.clone(),
            bus_address: modbus_write.bus_address,
            function_code: modbus_write.function_code,
            start_address: modbus_write.start_address,
            status,
        })
    }

    Ok(modbus_acks)
}

use std::future::pending;

use defmt::Format;
use enumset::EnumSetType;

use crate::{
    ebus::api::EbusReply,
    modbus::api::ModbusReply,
    prelude::*,
    smartset::msg::{BundleRequest, BundleResponse, BundleStatus, BundleType},
};

mod ebus_bundles;
mod modbus_bundles;

pub struct PushBundle {
    pub last: Option<Instant>,
    pub interval: u16,
    pub bundle: BundleRequest,
}

#[derive(Default)]
pub struct ScheduledBundles {
    // TODO: limit to 50
    active_bundles: Vec<PushBundle>,
    bundle_iter: usize,
}

impl ScheduledBundles {
    /// Find the first bundle which is ready to be processed (as per its interval).
    /// If multiple bundles are ready the one next in the list relative
    /// to the previously processed bundle is returned to achieve fairness.
    pub async fn next(&mut self) -> Option<&mut PushBundle> {
        if self.active_bundles.is_empty() {
            return None;
        }

        if self.bundle_iter >= self.active_bundles.len() {
            self.bundle_iter = 0;
        }

        let mut now = Instant::now();
        let start_index = self.bundle_iter;
        let mut soonest = now + Duration::from_secs(6000);

        loop {
            let cur = &mut self.active_bundles[self.bundle_iter];
            let last = match cur.last {
                Some(last) => last,
                None => {
                    info!(
                        "[bundlemgr] Scheduling bundle for the first time, id={}",
                        cur.bundle.bundle_id
                    );
                    // not processed yet, yield it
                    cur.last = Some(Instant::now());
                    return Some(&mut self.active_bundles[self.bundle_iter]);
                }
            };
            let at = last + Duration::from_secs(cur.interval as u64);

            if at <= now {
                cur.last = Some(Instant::now());
                return Some(&mut self.active_bundles[self.bundle_iter]);
            }

            soonest = soonest.min(at);

            self.bundle_iter = (self.bundle_iter + 1) % self.active_bundles.len();

            if self.bundle_iter == start_index {
                // we have looped through all bundles and none
                // was ready.
                Timer::at(soonest).await;
                now = Instant::now();
            }
        }
    }

    pub async fn next_always(&mut self) -> &mut PushBundle {
        match self.next().await {
            Some(x) => x,
            None => pending().await,
        }
    }

    /// Add new bundles so they are processed ASAP
    pub fn add(&mut self, bundle: PushBundle) {
        if let Some(b_pos) = self
            .active_bundles
            .iter()
            .position(|b| b.bundle.bundle_id == bundle.bundle.bundle_id)
        {
            warn!(
                "[bundlemgr] Tried to add bundle with non unique ID: id={}",
                bundle.bundle.bundle_id
            );
            self.active_bundles.remove(b_pos);
        } else {
            info!(
                "[bundlemgr] Adding new bundle: id={}",
                bundle.bundle.bundle_id
            );
        }

        self.active_bundles.insert(self.bundle_iter, bundle);
    }

    pub fn remove(&mut self, bundle_id: u16) {
        // The removal rules are a bit funny:
        // bundles % 100 == 0 => remove all bundles (bundle_id..bundle_id+100)
        // bundles % 100 != 0 => removes a single bundle

        let is_bundle_group = bundle_id % 100 == 0 && bundle_id != 0;

        let bundle_range = if is_bundle_group {
            bundle_id..bundle_id + 100
        } else {
            bundle_id..bundle_id + 1
        };

        self.active_bundles
            .retain(|b| !bundle_range.contains(&b.bundle.bundle_id))
    }
}

fn whitelist_check(channel: BundleChannel, addr: u8, info: u16) -> bool {
    match channel {
        BundleChannel::Local if state().smart_home.is_active() => {
            whitelist::is_whitelisted_ism9(addr, info)
        }
        _ => whitelist::is_whitelisted_smartset(addr, info),
    }
}

// TODO: reimplement modbus consecutive timeout check!
// ===================================================

pub async fn handle_bundle(
    scheduled: &mut ScheduledBundles,
    mut msg: BundleRequest,
    channel: BundleChannel,
    ebus_slot: &ReturnSlot<EbusReply>,
    modbus_slot: &ReturnSlot<ModbusReply>,
) -> Option<BundleResponse> {
    match msg.ty {
        BundleType::Push => {
            scheduled.add(PushBundle {
                last: None,
                interval: {
                    let ir_interval = msg.info_reads.first().map(|ir| ir.interval);
                    let mr_interval = msg.modbus_reads.first().map(|mr| mr.interval);
                    ir_interval.or(mr_interval).unwrap_or(60)
                },
                bundle: msg.clone(),
            });
            None
        }
        BundleType::Remove => {
            scheduled.remove(msg.bundle_id);
            Some(BundleResponse {
                bundle_id: msg.bundle_id,
                gateway_id: msg.gateway_id,
                status: BundleStatus::Ok,
                time_stamp: state().ebus.time_stamp(),
                error_msg: None,
                info_responses: vec![],
                info_acks: vec![],
                svc03f2_responses: vec![],
                svc03f3_acks: vec![],
                error_responses: None,
                service_responses: vec![],
                modbus_acks: vec![],
                modbus_responses: vec![],
            })
        }
        _ => execute_msg(&mut msg, channel, ebus_slot, modbus_slot).await,
    }
}

/// Performs whatever the bundle asks us to do
pub async fn execute_msg(
    msg: &mut BundleRequest,
    channel: BundleChannel,
    ebus_slot: &ReturnSlot<EbusReply>,
    modbus_slot: &ReturnSlot<ModbusReply>,
) -> Option<BundleResponse> {
    match execute_msg_failible(msg, channel, ebus_slot, modbus_slot).await {
        Ok(res) => res,
        Err(e) => {
            error!("[bundlemgr] Could not execute msg: err={}", e);
            Some(BundleResponse {
                bundle_id: msg.bundle_id,
                gateway_id: msg.gateway_id,
                time_stamp: state().ebus.time_stamp(),
                status: BundleStatus::Error,
                error_msg: Some(format!("{:?}", e)),
                ..Default::default()
            })
        }
    }
}

/// Performs whatever the bundle asks us to do
async fn execute_msg_failible(
    msg: &mut BundleRequest,
    channel: BundleChannel,
    ebus_slot: &ReturnSlot<EbusReply>,
    modbus_slot: &ReturnSlot<ModbusReply>,
) -> Result<Option<BundleResponse>> {
    with_timeout(secs(30), async move {
        let wl_check = |addr, info| whitelist_check(channel, addr, info);
        let info_responses = ebus_bundles::process_info_reads(msg, wl_check, ebus_slot).await?;
        let info_acks = ebus_bundles::process_info_writes(msg, wl_check, ebus_slot).await?;
        let svc03f2_responses = ebus_bundles::process_svc03f2_reads(msg, ebus_slot).await?;
        let svc03f3_acks = ebus_bundles::process_svc03f2_writes(msg, ebus_slot).await?;
        let error_responses = ebus_bundles::process_error_reads(msg).await?;
        let service_responses = ebus_bundles::process_service_reads(msg).await?;
        let modbus_responses = modbus_bundles::process_modbus_reads(msg, modbus_slot).await?;
        let modbus_acks = modbus_bundles::process_modbus_writes(msg, modbus_slot).await?;

        // Check if all responses are empty (and we are not replying to an error read bundle, because the response to that can be empty)
        if info_responses.is_empty()
            && info_acks.is_empty()
            && svc03f2_responses.is_empty()
            && svc03f3_acks.is_empty()
            && error_responses.is_none()
            && service_responses.is_empty()
            && modbus_responses.is_empty()
            && modbus_acks.is_empty()
        {
            return Ok(None);
        }

        Ok(Some(BundleResponse {
            bundle_id: msg.bundle_id,
            gateway_id: msg.gateway_id,
            status: BundleStatus::Ok,
            time_stamp: state().ebus.time_stamp(),
            error_msg: None,
            info_responses,
            info_acks,
            svc03f2_responses,
            svc03f3_acks,
            error_responses,
            service_responses,
            modbus_responses,
            modbus_acks,
        }))
    })
    .await
    .context(intern!("execute bundle within 30 seconds"))?
}

#[derive(Debug, EnumSetType, Format)]
#[enumset(repr = "u8")]
pub enum BundleChannel {
    Local = 0,
    Smartset = 1,
}

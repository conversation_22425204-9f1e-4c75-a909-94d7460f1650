use std::{
    fmt::Write,
    net::{Ipv4Addr, SocketAddr, TcpStream, ToSocketAddrs},
    pin::Pin,
};

use async_io_mini::Async;
use defmt::Debug2Format;
use embassy_futures::select::{select, Either};
use futures_lite::{AsyncRead, AsyncWrite};

use crate::{
    flash::{
        eventlog::{
            events::{
                DnsResolutionResult, Event, PortalConnectResult, PortalDisconnectReason,
                ResetTrigger, TlsConnectResult,
            },
            utils::ResultEventLogAsync,
        },
        ota,
    },
    net::tls::upgrade_to_tls,
    prelude::*,
    server::schema::PortalState,
    smartset::{bundle_mgr::BundleChannel, connection, wait_until_connected},
    utils::{
        cod::call_on_drop,
        retry::{exponential_backoff, retry},
        task,
    },
};

const PORTAL_RETRY_LIMIT: usize = 5;
const PORTAL_RETRY_MIN_TIME: Duration = Duration::from_secs(60 * 15);

pub async fn portal_connect_task() {
    let portal_enabled = env::active_params().portal_enabled;
    if !portal_enabled && !state().test_mode() {
        warn!("Disabling portal connection due to user setting");
        return;
    }

    let start = Instant::now();
    let mut error_counter = 0;
    loop {
        if let Err(e) = Box::pin(try_connect()).await {
            error!("Finished portal connection: err={}", e);
            error_counter += 1;
        }

        // Make sure to clean up portal connection state
        ota::net::cancel_network().await;
        env::acquire_website_state().await.portal_state = PortalState::NotConnected;
        state().led.disable(LedBits::PortalConnected);

        if !state().net.temp_ip()
            && error_counter >= PORTAL_RETRY_LIMIT
            && start.elapsed() > PORTAL_RETRY_MIN_TIME
        {
            break;
        }

        Timer::after(Duration::from_secs(2)).await;
    }

    error!("Exceeded retry threshold");
    crate::utils::restart(ResetTrigger::PortalTimeout).await;
}

async fn try_connect() -> Result<()> {
    info!("Waiting for network");
    wait_until_connected(true).await;
    info!("Got network, ready for connection");

    match select(wait_until_connected(false), connect_and_handle()).await {
        Either::First(_) => {
            info!("Cancelled portal connection availability due to network unavailability");
        }
        Either::Second(res) => {
            info!("Closed smartset connection");
            res?;
        }
    }

    Ok(())
}

async fn connect_and_handle() -> Result<()> {
    let (reader, writer) = Box::pin(setup_smartset_connection()).await?;

    let ret = Box::pin(connection::run(reader, writer, BundleChannel::Smartset))
        .await
        .context(intern!("handle portal connection"));

    Event::PortalDisconnect(PortalDisconnectReason::Unknown)
        .log()
        .await;

    ret
}

async fn setup_smartset_connection() -> Result<(
    Pin<Box<dyn AsyncRead + Unpin>>,
    Pin<Box<dyn AsyncWrite + Unpin>>,
)> {
    state().led.enable(LedBits::PortalConnecting);

    let _guard = call_on_drop(|| state().led.disable(LedBits::PortalConnecting));

    let stream = Box::pin(with_timeout(
        mins(10),
        retry(
            connect_with_server,
            "smartset connection",
            exponential_backoff(),
        ),
    ))
    .await
    .context(intern!("connect to Smartset within 10 minutes"))?;

    let stream = match stream {
        Ok(stream) => stream,
        Err(e) => {
            if !state().net.temp_ip() {
                error!(
                    "Failed to connect to Smartset for 10 minutes, resetting due to dead network"
                );
                crate::utils::restart(ResetTrigger::PortalTimeout).await;
            }
            return Err(e);
        }
    };

    let reader = Box::pin(stream.0);
    let writer = Box::pin(stream.1);

    Ok((reader, writer))
}

type AsyncStream = (Box<dyn AsyncRead + Unpin>, Box<dyn AsyncWrite + Unpin>);

async fn connect_with_server() -> Result<AsyncStream> {
    use std::io::ErrorKind;

    env::acquire_website_state().await.portal_state = PortalState::SetupConnection;

    let skip_cn = nvs::tls_skip_cn::get().await;

    let params = env::active_params();
    let mut address = heapless::String::<64>::new();
    write!(address, "{}:{}", params.portal_host, params.portal_port)
        .context(intern!("format smartset address"))?;

    info!("Trying to connect to Smartset: addr={}", address.as_str());
    if skip_cn {
        warn!("Skipping certificate common name check");
    }

    let address = dns_query(address).await?;
    let ip = match address {
        SocketAddr::V4(ip) => *ip.ip(),
        SocketAddr::V6(_) => Ipv4Addr::BROADCAST,
    };
    Event::DnsResolution(DnsResolutionResult::Ok(ip))
        .log()
        .await;

    env::acquire_website_state().await.portal_state = PortalState::DelayedLogon;

    static mut LAST_CONNECTION_ATTEMPT: Option<Instant> = None;
    let seconds_since = unsafe { LAST_CONNECTION_ATTEMPT }
        .map(|x| x.elapsed().as_secs())
        .unwrap_or(60);
    if seconds_since < 60 {
        let secs_to_wait = 60 - seconds_since;
        info!(
            "Delaying due to recent connection attempt: delay={}s",
            seconds_since
        );
        Timer::after(Duration::from_secs(secs_to_wait)).await;
    }

    let random_delay = fastrand::u64(0..30);
    info!("Delaying connection attempt: delay={}s", random_delay);
    Timer::after(Duration::from_secs(random_delay)).await;

    unsafe {
        LAST_CONNECTION_ATTEMPT = Some(Instant::now());
    }

    env::acquire_website_state().await.portal_state = PortalState::Logon;

    let stream = Async::<TcpStream>::connect(address)
        .ok_event(Event::PortalConnect(PortalConnectResult::Ok))
        .err_event_map(|e| {
            Event::PortalConnect(match e.kind() {
                ErrorKind::ConnectionRefused => PortalConnectResult::ErrConnRefused,
                ErrorKind::ConnectionReset => PortalConnectResult::ErrConnReset,
                ErrorKind::ConnectionAborted => PortalConnectResult::ErrConnAbort,
                ErrorKind::NotConnected => PortalConnectResult::ErrNotConnected,
                ErrorKind::TimedOut => PortalConnectResult::ErrTimeout,
                ErrorKind::Interrupted => PortalConnectResult::ErrInterrupted,
                ErrorKind::UnexpectedEof => PortalConnectResult::ErrUnexpectedEof,
                ErrorKind::OutOfMemory => PortalConnectResult::ErrOutOfMemory,
                _ => PortalConnectResult::ErrOther,
            })
        })
        .await
        .context(intern!("connect to portal"))?;

    info!("Connected to Smartset (TCP)");

    env::acquire_website_state().await.portal_state = PortalState::SslConnecting;

    let ca_cert = nvs::ca_cert::get().await;
    let client_cert = nvs::client_cert::get().await;
    let client_key = nvs::client_key::get().await;
    let stream = Box::pin(
        upgrade_to_tls(
            stream,
            &params.portal_host,
            skip_cn,
            &ca_cert,
            &client_cert,
            &client_key,
        )
        .ok_event(Event::PortalTlsConnect(TlsConnectResult::Ok))
        .err_event_map(|e| Event::PortalTlsConnect(*e)),
    )
    .await
    .map_err(|e| anyhow!("failed to upgrade tcp to tls: {}", e))?;

    info!("Connected to Smartset (TLS)");

    Ok(stream)
}

async fn dns_query(address: heapless::String<64>) -> Result<SocketAddr> {
    use std::io::ErrorKind;

    static_slot!(SLOT, Option<SocketAddr>);

    task::spawn_external("dns_resolver", 4096, 5, None, move || {
        let addrs = address.to_socket_addrs();
        let event = match addrs {
            Ok(_) => DnsResolutionResult::Ok(Ipv4Addr::BROADCAST),
            Err(ref e) => match e.kind() {
                ErrorKind::ConnectionRefused => DnsResolutionResult::ErrConnRefused,
                ErrorKind::ConnectionReset => DnsResolutionResult::ErrConnReset,
                ErrorKind::ConnectionAborted => DnsResolutionResult::ErrConnAbort,
                ErrorKind::NotConnected => DnsResolutionResult::ErrNotConnected,
                ErrorKind::TimedOut => DnsResolutionResult::ErrTimeout,
                ErrorKind::Interrupted => DnsResolutionResult::ErrInterrupted,
                ErrorKind::UnexpectedEof => DnsResolutionResult::ErrUnexpectedEof,
                ErrorKind::OutOfMemory => DnsResolutionResult::ErrOutOfMemory,
                _ => DnsResolutionResult::ErrOther,
            },
        };
        let addrs = addrs
            .context(intern!("resolve portal domain"))
            .map_err(|e| (event, e));
        if let Ok(mut addrs) = addrs {
            let num_addresses = addrs.len();
            let address = addrs.nth(fastrand::usize(0..num_addresses)).unwrap();
            SLOT.clone().ret(Some(address));
        } else {
            SLOT.clone().ret(None);
        }
    });

    let address = with_timeout(mins(1), SLOT.wait())
        .err_evcontext(
            Event::DnsResolution(DnsResolutionResult::ErrTimeout),
            intern!("resolve portal domain within timeout"),
        )
        .await?
        .ok_or_else(|| anyhow!("resolve portal domain"))?;

    info!("Resolved server domain: addr={}", Debug2Format(&address));

    Ok(address)
}

use std::fmt::{Result as FmtResult, Write};

use nanoxml::{
    de::{XmlError, XmlStr},
    derive::{
        de::DeXmlAttr,
        ser::{SerXml, SerXmlAsAttr},
    },
    ser::XmlBuilder,
};

#[derive(Co<PERSON>, Clone, Debug, Eq, Ord, PartialEq, PartialOrd)]
pub struct HexNr<T: Copy + Into<u32> + TryFrom<u32>>(pub T);

impl<T: Copy + Into<u32> + TryFrom<u32>> From<T> for HexNr<T> {
    fn from(value: T) -> Self {
        Self(value)
    }
}

impl<T: Copy + Into<u32> + TryFrom<u32>> SerXml for HexNr<T> {
    fn ser_body<W: Write>(&self, xml: &mut XmlBuilder<'_, W>) -> FmtResult {
        let value: u32 = self.0.into();
        write!(xml, "0x{:X}", value)
    }

    fn ser_attrs<W: Write>(&self, _xml: &mut XmlBuilder<'_, W>) -> FmtResult {
        Ok(())
    }
}

impl<T: Copy + Into<u32> + TryFrom<u32>> SerXmlAsAttr for HexNr<T> {}

impl<'a, T: Copy + Into<u32> + TryFrom<u32> + 'a> DeXmlAttr<'a> for HexNr<T> {
    fn de_xml_attr(s: XmlStr<'a>) -> Result<Self, XmlError> {
        let s = s.raw().strip_prefix("0x").ok_or(XmlError::InvalidValue)?;
        let x = u32::from_str_radix(s, 16).map_err(|_| XmlError::InvalidValue)?;
        let x = x.try_into().map_err(|_| XmlError::InvalidValue)?;
        Ok(Self(x))
    }
}

#[derive(Copy, Clone, Debug, Eq, Ord, PartialEq, PartialOrd)]
pub struct HexNrNoPrefix<T: Copy + Into<u32> + TryFrom<u32>>(pub T);

impl<T: Copy + Into<u32> + TryFrom<u32>> From<T> for HexNrNoPrefix<T> {
    fn from(value: T) -> Self {
        Self(value)
    }
}

impl<T: Copy + Into<u32> + TryFrom<u32>> SerXml for HexNrNoPrefix<T> {
    fn ser_body<W: Write>(&self, xml: &mut XmlBuilder<'_, W>) -> FmtResult {
        let value: u32 = self.0.into();
        write!(xml, "{:X}", value)
    }

    fn ser_attrs<W: Write>(&self, _xml: &mut XmlBuilder<'_, W>) -> FmtResult {
        Ok(())
    }
}

impl<T: Copy + Into<u32> + TryFrom<u32>> SerXmlAsAttr for HexNrNoPrefix<T> {}

impl<'a, T: Copy + Into<u32> + TryFrom<u32> + 'a> DeXmlAttr<'a> for HexNrNoPrefix<T> {
    fn de_xml_attr(s: XmlStr<'a>) -> Result<Self, XmlError> {
        let x = u32::from_str_radix(s.raw(), 16).map_err(|_| XmlError::InvalidValue)?;
        let x = x.try_into().map_err(|_| XmlError::InvalidValue)?;
        Ok(Self(x))
    }
}

#[derive(Clone, Debug, PartialEq)]
pub struct HexBytes<T>(pub T);

impl<T> From<T> for HexBytes<T> {
    fn from(value: T) -> Self {
        Self(value)
    }
}

impl<const N: usize> SerXml for HexBytes<[u8; N]> {
    fn ser_body<W: Write>(&self, xml: &mut XmlBuilder<'_, W>) -> FmtResult {
        for value in self.0 {
            write!(xml, "{:02X}", value)?;
        }
        Ok(())
    }

    fn ser_attrs<W: Write>(&self, _xml: &mut XmlBuilder<'_, W>) -> FmtResult {
        Ok(())
    }
}

impl SerXml for HexBytes<Vec<u8>> {
    fn ser_body<W: Write>(&self, xml: &mut XmlBuilder<'_, W>) -> FmtResult {
        for value in &self.0 {
            write!(xml, "{:02X}", value)?;
        }
        Ok(())
    }

    fn ser_attrs<W: Write>(&self, _xml: &mut XmlBuilder<'_, W>) -> FmtResult {
        Ok(())
    }
}

impl<const N: usize> SerXmlAsAttr for HexBytes<[u8; N]> {}
impl SerXmlAsAttr for HexBytes<Vec<u8>> {}

impl<'a, const N: usize> DeXmlAttr<'a> for HexBytes<[u8; N]> {
    fn de_xml_attr(s: XmlStr<'a>) -> Result<Self, XmlError> {
        let s = s.raw().as_bytes();
        if s.len() != N * 2 {
            return Err(XmlError::InvalidValue);
        }

        let mut bytes = [0; N];

        #[allow(clippy::needless_range_loop)]
        for i in 0..N {
            let current = &s
                .get((2 * i)..(2 * (i + 1)))
                .ok_or(XmlError::InvalidValue)?;
            let high = hex_to_bits(current[0]).ok_or(XmlError::InvalidValue)?;
            let low = hex_to_bits(current[1]).ok_or(XmlError::InvalidValue)?;
            bytes[i] = (high << 4) | low;
        }

        Ok(Self(bytes))
    }
}

impl<'a> DeXmlAttr<'a> for HexBytes<Vec<u8>> {
    fn de_xml_attr(s: XmlStr<'a>) -> Result<Self, XmlError> {
        let s = s.raw().as_bytes();
        if s.len() % 2 != 0 {
            return Err(XmlError::InvalidValue);
        }

        let mut bytes = Vec::new();

        for chunk in s.chunks(2) {
            let high = hex_to_bits(chunk[0]).ok_or(XmlError::InvalidValue)?;
            let low = hex_to_bits(chunk[1]).ok_or(XmlError::InvalidValue)?;
            bytes.push((high << 4) | low);
        }

        Ok(Self(bytes))
    }
}

#[inline(always)]
fn hex_to_bits(hex: u8) -> Option<u8> {
    match hex {
        b'0'..=b'9' => Some(hex - b'0'),
        b'A'..=b'F' => Some(10 + hex - b'A'),
        _ => None,
    }
}

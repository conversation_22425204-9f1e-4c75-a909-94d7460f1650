use std::{
    borrow::Cow,
    fmt::{Result as FmtR<PERSON><PERSON>, Write},
    num::ParseIntError,
    str::FromStr,
};

use nanoxml::{
    de::{XmlError, XmlStr},
    derive::{
        de::DeXmlAttr,
        ser::{SerXml, SerXmlAsAttr},
    },
    ser::XmlBuilder,
};

use crate::prelude::*;

#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON>q, <PERSON>h, PartialEq)]
pub struct SeqNr(pub String);

impl SeqNr {
    pub fn is_single_data_point(&self) -> bool {
        let first = self.0.as_bytes()[0];

        match first {
            b'A'..=b'D' => true,
            b'E' => false,
            _ => {
                warn!("Got unknown seq nr type: seqnr={}", self.0.as_str());
                false
            }
        }
    }

    pub fn is_combined(&self) -> bool {
        let first = self.0.as_bytes()[0];

        first == b'C'
    }

    pub fn iter(&self) -> Box<dyn Iterator<Item = Cow<SeqNr>> + '_> {
        if self.0.starts_with('E') {
            Box::new(self.iter_extended().into_iter().flatten().map(Cow::Owned))
        } else {
            Box::new(std::iter::once(Cow::Borrowed(self)))
        }
    }

    pub fn iter_extended(&self) -> Option<impl Iterator<Item = SeqNr> + '_> {
        let mut iter = self.0.split(';');

        let first = iter.next()?;
        defmt::assert_eq!(first, "E");

        Some(iter.map(|nr| SeqNr(format!("A;{nr}"))))
    }
}

impl From<String> for SeqNr {
    fn from(value: String) -> Self {
        SeqNr(value)
    }
}

impl From<SeqNr> for String {
    fn from(val: SeqNr) -> Self {
        val.0
    }
}

impl SerXml for SeqNr {
    fn ser_body<W: Write>(&self, xml: &mut XmlBuilder<'_, W>) -> FmtResult {
        xml.text(&self.0)
    }

    fn ser_attrs<W: Write>(&self, _xml: &mut XmlBuilder<'_, W>) -> FmtResult {
        Ok(())
    }
}

impl SerXmlAsAttr for SeqNr {}

impl<'a> DeXmlAttr<'a> for SeqNr {
    fn de_xml_attr(s: XmlStr<'a>) -> Result<Self, XmlError> {
        Ok(Self(s.owned()))
    }
}

#[derive(Clone, Debug, PartialEq)]
pub struct Numbers(pub heapless::Vec<u16, 5>);

impl Numbers {
    #[allow(dead_code)]
    pub fn from_single(n: u16) -> Self {
        let mut v = heapless::Vec::new();
        v.push(n).unwrap();

        Numbers(v)
    }

    pub fn iter(&self) -> impl Iterator<Item = u16> + '_ {
        self.0.iter().cloned()
    }
}

impl FromStr for Numbers {
    type Err = ParseIntError;
    fn from_str(s: &str) -> Result<Self, ParseIntError> {
        Ok(Numbers(
            s.split(' ')
                .map(|s| s.parse::<u16>())
                .collect::<Result<heapless::Vec<u16, 5>, _>>()?,
        ))
    }
}

impl<'a> DeXmlAttr<'a> for Numbers {
    fn de_xml_attr(s: XmlStr<'a>) -> Result<Self, XmlError> {
        Self::from_str(s.raw()).map_err(|_| XmlError::InvalidValue)
    }
}

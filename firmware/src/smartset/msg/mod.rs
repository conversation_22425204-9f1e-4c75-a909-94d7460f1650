use std::{fmt, io, iter::repeat_n};

use defmt::{Debug2Format, Format};
use futures_lite::{AsyncRead, AsyncReadExt, AsyncWrite, AsyncWriteExt};
use nanoxml::derive::{de::DeXmlTopLevel, ser::SerXmlTopLevel};

use crate::{prelude::*, smartset::bundle_mgr::BundleChannel, utils::aes};

pub mod helpers;
pub mod hex_nr;
mod xml;

pub use self::xml::*;

const MAX_TELEGRAM_SIZE: usize = 8192;

#[derive(Debug)]
pub enum Telegram {
    #[allow(dead_code)]
    PortalLogonRequest(PortalLogonRequest),
    PortalLogonResponse(PortalLogonResponse),
    SystemConfigRequest(SystemConfigRequest),
    SystemConfigResponse(SystemConfigResponse),
    BundleRequest(BundleRequest),
    BundleResponse(BundleResponse),
    GwConfigRequest(GwConfigRequest),
    GwConfigResponse(GwConfigResponse),
    DirectLogonRequest(DirectLogonRequest),
    DirectLogonResponse(DirectLogonResponse),
    #[allow(dead_code)]
    GwProtocolConfigRequest(GwProtocolConfigRequest),
    #[allow(dead_code)]
    GwProtocolConfigResponse(GwProtocolConfigResponse),
    #[allow(dead_code)]
    GwProtocolValueRequest(GwProtocolValueRequest),
    #[allow(dead_code)]
    GwProtocolValueResponse(GwProtocolValueResponse),
    BinaryData(BinaryDataWrapper),
    KeepAlive(KeepAlive),
    PortalLogonRequestEncrypted(PortalLogonRequest),
    StartSequence(StartSequence),
    OtaUpdateRequest(OtaUpdateRequest),
    OtaUpdateResponse(OtaUpdateResponse),
    OtaUpdateCompletionStatus(OtaUpdateCompletionStatus),
    Restart(Restart),
    EebusStatus(EebusStatus),
    EebusControl(EebusControl),
}

impl Telegram {
    pub fn parse(ty: TelegramType, data: &[u8]) -> Option<Self> {
        let value = match ty {
            TelegramType::PortalLogonRequest => return None,
            TelegramType::PortalLogonResponse => Telegram::PortalLogonResponse(from_xml(data)?),
            TelegramType::SystemConfigRequest => Telegram::SystemConfigRequest(from_xml(data)?),
            TelegramType::SystemConfigResponse => return None,
            TelegramType::BundleRequest => Telegram::BundleRequest(from_xml(data)?),
            TelegramType::BundleResponse => return None,
            TelegramType::GwConfigRequest => Telegram::GwConfigRequest(from_xml(data)?),
            TelegramType::GwConfigResponse => return None,
            TelegramType::DirectLogonRequest => Telegram::DirectLogonRequest(from_xml(data)?),
            TelegramType::DirectLogonResponse => return None,
            TelegramType::GwProtocolConfigRequest => defmt::todo!(),
            TelegramType::GwProtocolConfigResponse => defmt::todo!(),
            TelegramType::GwProtocolValueRequest => defmt::todo!(),
            TelegramType::GwProtocolValueResponse => defmt::todo!(),
            TelegramType::BinaryData => Telegram::BinaryData(BinaryDataWrapper(Vec::from(data))), // TODO: change to borrowed?
            TelegramType::KeepAlive => Telegram::KeepAlive(KeepAlive::parse(data)?),
            TelegramType::PortalLogonRequestEncrypted => return None,
            TelegramType::StartSequence => Telegram::StartSequence(from_xml(data)?),
            TelegramType::OtaUpdateRequest => Telegram::OtaUpdateRequest(from_xml(data)?),
            TelegramType::OtaUpdateResponse => return None,
            TelegramType::OtaUpdateCompletionStatus => return None,
            TelegramType::Restart => Telegram::Restart(from_xml(data)?),
            TelegramType::EebusStatus => return None,
            TelegramType::EebusControl => Telegram::EebusControl(from_xml(data)?),
        };

        Some(value)
    }

    /// Returns `true` if this telegram supports serializing
    pub fn to_xml(&self, output: &mut String) -> bool {
        match self {
            Telegram::PortalLogonRequest(x) => to_xml(x, output),
            Telegram::PortalLogonRequestEncrypted(x) => to_xml(x, output),
            Telegram::SystemConfigResponse(x) => to_xml(x, output),
            Telegram::BundleResponse(x) => to_xml(x, output),
            Telegram::GwConfigResponse(x) => to_xml(x, output),
            Telegram::DirectLogonResponse(x) => to_xml(x, output),
            Telegram::GwProtocolConfigResponse(x) => to_xml(x, output),
            Telegram::GwProtocolValueResponse(x) => to_xml(x, output),
            Telegram::OtaUpdateResponse(x) => to_xml(x, output),
            Telegram::OtaUpdateCompletionStatus(x) => to_xml(x, output),
            Telegram::EebusStatus(x) => to_xml(x, output),
            _ => return false,
        }

        true
    }

    pub fn ty(&self) -> TelegramType {
        match self {
            Telegram::PortalLogonRequest(_) => TelegramType::PortalLogonRequest,
            Telegram::PortalLogonRequestEncrypted(_) => TelegramType::PortalLogonRequestEncrypted,
            Telegram::StartSequence(_) => TelegramType::StartSequence,
            Telegram::PortalLogonResponse(_) => TelegramType::PortalLogonResponse,
            Telegram::KeepAlive(_) => TelegramType::KeepAlive,
            Telegram::SystemConfigRequest(_) => TelegramType::SystemConfigRequest,
            Telegram::SystemConfigResponse(_) => TelegramType::SystemConfigResponse,
            Telegram::BundleRequest(_) => TelegramType::BundleRequest,
            Telegram::BundleResponse(_) => TelegramType::BundleResponse,
            Telegram::GwConfigRequest(_) => TelegramType::GwConfigRequest,
            Telegram::GwConfigResponse(_) => TelegramType::GwConfigResponse,
            Telegram::DirectLogonRequest(_) => TelegramType::DirectLogonRequest,
            Telegram::DirectLogonResponse(_) => TelegramType::DirectLogonResponse,
            Telegram::GwProtocolConfigRequest(_) => TelegramType::GwProtocolConfigRequest,
            Telegram::GwProtocolConfigResponse(_) => TelegramType::GwProtocolConfigResponse,
            Telegram::GwProtocolValueRequest(_) => TelegramType::GwProtocolValueRequest,
            Telegram::GwProtocolValueResponse(_) => TelegramType::GwProtocolValueResponse,
            Telegram::BinaryData(_) => TelegramType::BinaryData,
            Telegram::OtaUpdateRequest(_) => TelegramType::OtaUpdateRequest,
            Telegram::OtaUpdateResponse(_) => TelegramType::OtaUpdateResponse,
            Telegram::OtaUpdateCompletionStatus(_) => TelegramType::OtaUpdateCompletionStatus,
            Telegram::Restart(_) => TelegramType::Restart,
            Telegram::EebusStatus(_) => TelegramType::EebusStatus,
            Telegram::EebusControl(_) => TelegramType::EebusControl,
        }
    }
}

pub struct BinaryDataWrapper(pub Vec<u8>);

impl fmt::Debug for BinaryDataWrapper {
    fn fmt(&self, f: &mut fmt::Formatter) -> Result<(), fmt::Error> {
        let mut s = String::new();
        for i in 0..self.0.len().min(8) {
            s.push_str(&format!("{},", self.0[i]))
        }
        if self.0.len() > 8 {
            s.push_str("...");
        }
        write!(f, "[{}]", s)
    }
}

fn from_xml<'a, T: DeXmlTopLevel<'a>>(data: &'a [u8]) -> Option<T> {
    let s = std::str::from_utf8(data)
        .inspect_err(|_| error!("Got invalid UTF-8 message"))
        .ok()?;

    T::deserialize_str(s)
        .inspect_err(|err| error!("Failed to parse XML data: err={}", err))
        .ok()
}

fn to_xml<T: SerXmlTopLevel>(value: &T, buf: &mut String) {
    buf.clear();
    value.serialize(buf).unwrap();
}

#[derive(Clone, Copy, Debug, Format)]
#[repr(u16)]
pub enum TelegramType {
    PortalLogonRequest = 0,
    PortalLogonResponse = 1,
    SystemConfigRequest = 2,
    SystemConfigResponse = 3,
    BundleRequest = 4,
    BundleResponse = 5,
    GwConfigRequest = 6,
    GwConfigResponse = 7,
    DirectLogonRequest = 8,
    DirectLogonResponse = 9,
    GwProtocolConfigRequest = 10,
    GwProtocolConfigResponse = 11,
    GwProtocolValueRequest = 12,
    GwProtocolValueResponse = 13,
    BinaryData = 14,
    KeepAlive = 15,
    PortalLogonRequestEncrypted = 16,
    StartSequence = 17,
    OtaUpdateRequest = 18,
    OtaUpdateResponse = 19,
    OtaUpdateCompletionStatus = 20,
    Restart = 21,
    EebusStatus = 22,
    EebusControl = 23,
}

impl TryFrom<u16> for TelegramType {
    type Error = ();

    fn try_from(value: u16) -> Result<Self, Self::Error> {
        use TelegramType::*;

        Ok(match value {
            0 => PortalLogonRequest,
            1 => PortalLogonResponse,
            2 => SystemConfigRequest,
            3 => SystemConfigResponse,
            4 => BundleRequest,
            5 => BundleResponse,
            6 => GwConfigRequest,
            7 => GwConfigResponse,
            8 => DirectLogonRequest,
            9 => DirectLogonResponse,
            10 => GwProtocolConfigRequest,
            11 => GwProtocolConfigResponse,
            12 => GwProtocolValueRequest,
            13 => GwProtocolValueResponse,
            14 => BinaryData,
            15 => KeepAlive,
            16 => PortalLogonRequestEncrypted,
            17 => StartSequence,
            18 => OtaUpdateRequest,
            19 => OtaUpdateResponse,
            20 => OtaUpdateCompletionStatus,
            21 => Restart,
            22 => EebusStatus,
            23 => EebusControl,
            _ => return Err(()),
        })
    }
}

pub async fn read_telegram<R: AsyncRead + Unpin>(
    reader: &mut R,
    buf: &mut Vec<u8>,
    channel: BundleChannel, // only used for logging
) -> std::io::Result<Telegram> {
    let mut header_buf = [0u8; 6];

    reader.read_exact(&mut header_buf).await?;
    let len = u32::from_be_bytes(header_buf[0..4].try_into().unwrap()) as usize;
    let ty = u16::from_be_bytes(header_buf[4..6].try_into().unwrap());

    debug!("Received telegram: type={}, length={}", ty, len);

    if len > MAX_TELEGRAM_SIZE {
        return Err(io::Error::new(
            io::ErrorKind::InvalidData,
            format!("telegram size {len} is too large"),
        ));
    }

    // ensure buf is large enough
    if len > buf.len() {
        let missing = len - buf.len();
        buf.extend(repeat_n(0, missing));
    }

    reader.read_exact(&mut buf[..len]).await?;

    let ty = TelegramType::try_from(ty).map_err(|_| {
        io::Error::new(
            io::ErrorKind::Other,
            format!("got {ty} for telegram type, which is not known"),
        )
    })?;

    if matches!(ty, TelegramType::KeepAlive | TelegramType::BinaryData) {
        info!(
            "Received telegram: channel={}, ty={}",
            Debug2Format(&ty),
            channel
        );
    } else {
        info!(
            "Received telegram: channel={}, ty={}, xml={}",
            channel,
            Debug2Format(&ty),
            std::str::from_utf8(&buf[..len]).unwrap_or("non-utf8")
        );
    }
    Telegram::parse(ty, &buf[..len]).ok_or_else(|| {
        warn!("Failed to parse XML content: ty={}", Debug2Format(&ty),);

        io::Error::new(
            io::ErrorKind::Other,
            format!("telegram of type {ty:?} could not be parsed"),
        )
    })
}

// CANCEL SAFETY: this function is not cancel safe!
pub async fn write_telegram<W: AsyncWrite + Unpin>(
    writer: &mut W,
    tel: &Telegram,
    buf: &mut String,
    channel: BundleChannel, // only used for logging
) -> Result<()> {
    write_telegram_inner(writer, tel, buf, channel)
        .await
        .context(intern!("write telegram"))
}

#[allow(clippy::type_complexity)]
async fn write_telegram_inner<W: AsyncWrite + Unpin>(
    writer: &mut W,
    tel: &Telegram,
    buf: &mut String,
    channel: BundleChannel, // only used for logging
) -> std::io::Result<()> {
    let ty = tel.ty() as u16;

    // handle non-xml telegrams
    match tel {
        Telegram::KeepAlive(ka) => {
            info!("Sending telegram KeepAlive: channel={}", channel);
            return write_telegram_raw(writer, 2, ty, &ka.counter.to_be_bytes()).await;
        }
        Telegram::BinaryData(data) => {
            info!("sending telegram BinaryData: channel={}", channel);
            return write_telegram_raw(writer, data.0.len() as u32, ty, &data.0).await;
        }
        _ => (),
    }

    if !tel.to_xml(buf) {
        defmt::panic!("Tried to serialize recv-only telegram");
    }

    info!(
        "Sending telegram: channel={}, ty={}, xml={}",
        channel,
        tel.ty(),
        buf.as_str(),
    );

    let encrypt_buf;
    let buf = match tel {
        Telegram::PortalLogonRequestEncrypted(_) => {
            const AES_KEY: [u8; 16] = [
                207, 79, 66, 88, 131, 205, 241, 166, 52, 139, 89, 30, 132, 127, 246, 141,
            ]; // 128-bit key
            const AES_IV: [u8; 16] = [
                37, 47, 173, 210, 79, 45, 222, 198, 179, 47, 78, 183, 252, 161, 12, 213,
            ]; // 128-bit IV
            encrypt_buf = aes::encrypt128(buf.as_bytes().to_vec(), &AES_KEY, &AES_IV); // TODO: remove unecessary extra allocation
            &encrypt_buf
        }
        _ => buf.as_bytes(),
    };

    write_telegram_raw(writer, buf.len() as u32, ty, buf).await
}

pub async fn write_telegram_raw<W: AsyncWrite + Unpin>(
    writer: &mut W,
    len: u32,
    ty: u16,
    data: &[u8],
) -> std::io::Result<()> {
    let mut buf = [0u8; 6];
    buf[0..4].clone_from_slice(&len.to_be_bytes());
    buf[4..6].clone_from_slice(&ty.to_be_bytes());

    writer.write_all(&buf).await?;

    writer.write_all(data).await
}

#![allow(dead_code)]

use std::{
    io::Cursor,
    net::{IpAddr, Ipv4Addr},
};

use byteorder::{ReadBytesExt, BE};
use defmt::Format;
use nanoxml::derive::{de::DeXml, ser::SerXml};

use super::{
    helpers::{Numbers, SeqNr},
    hex_nr::{HexBytes, HexNr, HexNrNoPrefix},
};
use crate::time::ClockTime;

#[derive(Clone, Debug, SerXml, PartialEq)]
#[nanoxml(rename = "portal-logon-request")]
pub struct PortalLogonRequest {
    #[nanoxml(attr, rename = "rnd")]
    pub rand: u32,
    #[nanoxml(rename = "installationname")]
    pub system_name: &'static str,
    #[nanoxml(rename = "passwd")]
    pub password: &'static str,
    #[nanoxml(rename = "serialnumber")]
    pub serial: &'static str,
    #[nanoxml(rename = "date-time")]
    pub date_time: Option<ClockTime>,
    #[nanoxml(rename = "ism-softwareversion")]
    pub ism_software_version: u16,
    #[nanoxml(rename = "ism-softwarenumber")]
    pub ism_software_number: u16,
    #[nanoxml(rename = "ism-hardwareversion")]
    pub ism_hardware_version: &'static str,
    #[nanoxml(rename = "wlan-connected")]
    pub wlan_connected: bool,
    #[nanoxml(rename = "type")]
    pub ty: &'static str,
    #[nanoxml(rename = "v2-version")]
    pub v2_version: &'static str,
    #[nanoxml(rename = "v2-commit-hash")]
    pub v2_commit_hash: &'static str,
    #[nanoxml(rename = "v2-test")]
    pub v2_test: Option<bool>,
    #[nanoxml(rename = "v2-dev")]
    pub v2_dev: Option<bool>,
    #[nanoxml(rename = "v2-disconnect-reason")]
    pub v2_disconnect_reason: String,
}

#[derive(Clone, Debug, DeXml, PartialEq)]
#[nanoxml(rename = "portal-logon-response")]
pub struct PortalLogonResponse {
    #[nanoxml(attr, rename = "state")]
    pub state: PortalState,
    #[nanoxml(attr, rename = "sid")]
    pub session_id: u32,
    #[nanoxml(rename = "next-connect-params")]
    pub next_connect_params: Option<NextConnectParams>,
}

#[derive(Clone, Debug, DeXml, PartialEq)]
pub struct NextConnectParams {
    #[nanoxml(rename = "dest-server")]
    pub dest_server: String,
    #[nanoxml(rename = "dest-port")]
    pub dest_port: u16,
    #[nanoxml(rename = "delay_sec")]
    pub delay_sec: i16,
    #[nanoxml(rename = "max_try")]
    pub max_try: u16,
}

#[derive(Clone, Debug, DeXml, Format, PartialEq)]
pub enum PortalState {
    #[nanoxml(rename = "ok")]
    Ok,
    #[nanoxml(rename = "busy")]
    Busy,
    #[nanoxml(rename = "error")]
    Error,
    #[nanoxml(rename = "UnknownRegistrationData")]
    UnknownRegistrationData,
}

#[derive(Clone, Debug, DeXml, PartialEq)]
#[nanoxml(rename = "read-systemconfig-request")]
pub struct SystemConfigRequest {
    #[nanoxml(attr, rename = "sid")]
    pub session_id: u32,
    #[nanoxml(rename = "system-config-addresses")]
    pub addresses: Option<String>,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
#[nanoxml(rename = "read-systemconfig-response")]
pub struct SystemConfigResponse {
    #[nanoxml(attr, rename = "sid")]
    pub session_id: u32,
    #[nanoxml(rename = "gateway")]
    pub gateway: SystemConfigResponseGateway,
    #[nanoxml(rename = "errormsg")]
    pub error_msg: Option<String>,
    #[nanoxml(seq, rename = "busconfig")]
    pub bus_configs: Vec<EbusConfig>,
    #[nanoxml(rename = "modbusconfig")]
    pub modbus_config: Option<ModbusConfig>,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub struct EbusConfig {
    #[nanoxml(attr, rename = "type")]
    pub ty: BusType,
    #[nanoxml(rename = "busDevices")]
    pub bus_devices: EbusDevices,
}

#[derive(Clone, Debug, Default, SerXml, PartialEq)]
pub struct EbusDevices {
    #[nanoxml(seq, rename = "busDevice")]
    pub bus_devices: Vec<EbusDevice>,
}

impl EbusDevices {
    pub fn get_by_address(&self, bus_address: u8) -> Option<&EbusDevice> {
        self.bus_devices
            .iter()
            .find(|d| d.bus_address.0 == bus_address)
    }

    pub fn get_mut_by_address(&mut self, bus_address: u8) -> Option<&mut EbusDevice> {
        self.bus_devices
            .iter_mut()
            .find(|d| d.bus_address.0 == bus_address)
    }

    pub fn insert(&mut self, device: EbusDevice) {
        self.bus_devices.push(device);
        self.bus_devices.sort_unstable_by_key(|x| x.bus_address);
    }

    pub fn remove_by_address(&mut self, bus_address: u8) -> Option<EbusDevice> {
        let index = self
            .bus_devices
            .iter()
            .position(|d| d.bus_address.0 == bus_address)?;
        Some(self.bus_devices.remove(index))
    }

    pub fn has_address(&self, bus_address: u8) -> bool {
        self.get_by_address(bus_address).is_some()
    }
}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub struct EbusDevice {
    #[nanoxml(attr, rename = "ba")]
    pub bus_address: HexNr<u8>,
    #[nanoxml(attr, rename = "sv")]
    pub software_version: HexNr<u8>,
    #[nanoxml(attr, rename = "sr")]
    pub software_revision: HexNr<u8>,
    #[nanoxml(attr, rename = "cfg")]
    pub config: HexNr<u16>,
    #[nanoxml(attr, rename = "did")]
    pub device_id: HexNr<u8>,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub struct ModbusConfig {
    #[nanoxml(rename = "busDevices")]
    pub bus_devices: ModbusDevices,
}

#[derive(Clone, Default, Debug, SerXml, PartialEq)]
pub struct ModbusDevices {
    #[nanoxml(seq, rename = "modBusDevice")]
    pub bus_devices: Vec<ModbusDevice>,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub struct ModbusDevice {
    /*  <modBusDevice ba="0x11" sv="0x88" ty="0x1" />  */
    #[nanoxml(attr, rename = "ba")]
    pub bus_address: HexNr<u8>,
    #[nanoxml(attr, rename = "sv")]
    pub software_version: Option<HexNr<u16>>,
    #[nanoxml(attr, rename = "ty")]
    pub ty: Option<HexNr<u16>>,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub enum BusType {
    #[nanoxml(rename = "ebus")]
    Ebus,
    #[nanoxml(rename = "wbus")]
    Wbus,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub struct SystemConfigResponseGateway {
    #[nanoxml(attr, rename = "type")]
    pub ty: &'static str,
    #[nanoxml(attr, rename = "softwareNumber")]
    pub software_number: u16,
    #[nanoxml(attr, rename = "softwareVersion")]
    pub software_version: u16,
    #[nanoxml(attr, rename = "wlan")]
    pub wlan: bool,
    #[nanoxml(attr, rename = "g3")]
    pub mobile_data: bool,
}

#[derive(Clone, Debug, DeXml, PartialEq)]
#[nanoxml(rename = "tbreq")]
pub struct BundleRequest {
    #[nanoxml(attr, rename = "bn")]
    pub bundle_id: u16,
    #[nanoxml(attr, rename = "gw")]
    pub gateway_id: u32,
    #[nanoxml(attr, rename = "ty")]
    pub ty: BundleType,
    #[nanoxml(attr, rename = "ae")]
    pub abort_on_error: Option<bool>,
    #[nanoxml(seq, rename = "ird")]
    pub info_reads: Vec<InfoRead>,
    #[nanoxml(seq, rename = "iwr")]
    pub info_writes: Vec<InfoWrite>,
    #[nanoxml(seq, rename = "iwrx")]
    pub info_writes_multiple: Vec<InfoWriteMultiple>,
    #[nanoxml(seq, rename = "s03f1rd")]
    pub svc03f1_reads: Vec<Service03f1Read>,
    #[nanoxml(seq, rename = "s03f3wr")]
    pub svc03f3_writes: Vec<Service03f1Write>,
    #[nanoxml(seq, rename = "srd")]
    pub svc_reads: Vec<ServiceRead>,
    #[nanoxml(seq, rename = "erd")]
    pub error_reads: Vec<ErrorRead>,
    #[nanoxml(seq, rename = "mrd")]
    pub modbus_reads: Vec<ModbusRead>,
    #[nanoxml(seq, rename = "mwr")]
    pub modbus_writes: Vec<ModbusWrite>,
}

#[derive(Clone, Debug, DeXml, PartialEq)]
pub struct InfoRead {
    #[nanoxml(attr, rename = "se")]
    pub sequence: SeqNr,
    #[nanoxml(attr, rename = "ba")]
    pub bus_address: HexNr<u8>,
    #[nanoxml(attr, rename = "in")]
    pub numbers: Numbers,
    #[nanoxml(seq)]
    pub last_sent_values: Option<[u16; 5]>,
    #[nanoxml(attr, rename = "is", default_de = "default_interval")]
    pub interval: u16,
    #[nanoxml(attr, rename = "snr", default_de = "default_service_number_read")]
    pub service_number: HexNrNoPrefix<u16>,
    #[nanoxml(attr, rename = "hy")]
    pub hysteresis: Option<u16>, // TODO: type?
}

fn default_interval() -> u16 {
    60
}

fn default_service_number_read() -> HexNrNoPrefix<u16> {
    HexNrNoPrefix(0x5022)
}

#[derive(Clone, Debug, DeXml, PartialEq)]
pub struct InfoWrite {
    #[nanoxml(attr, rename = "se")]
    pub sequence: SeqNr,
    #[nanoxml(attr, rename = "ba")]
    pub bus_address: HexNr<u8>,
    #[nanoxml(attr, rename = "in")]
    pub number: u16,
    #[nanoxml(attr, rename = "dl")]
    pub data_low: HexNr<u8>,
    #[nanoxml(attr, rename = "dh")]
    pub data_high: HexNr<u8>,
    #[nanoxml(attr, rename = "snr", default_de = "default_service_number_write")]
    pub service_number: HexNrNoPrefix<u16>,
}

#[derive(Clone, Debug, DeXml, PartialEq)]
pub struct InfoWriteMultiple {
    #[nanoxml(attr, rename = "se")]
    pub sequence: SeqNr,
    #[nanoxml(attr, rename = "ba")]
    pub bus_address: HexNr<u8>,
    #[nanoxml(attr, rename = "in")]
    pub numbers: Numbers,
    #[nanoxml(attr, rename = "db")]
    pub data_bytes: HexBytes<[u8; 10]>,
    #[nanoxml(attr, rename = "snr", default_de = "default_service_number_write")]
    pub service_number: HexNrNoPrefix<u16>,
}

fn default_service_number_write() -> HexNrNoPrefix<u16> {
    HexNrNoPrefix(0x5023)
}

/*
 <s03f1rd se="D;86" ba="0x35" hp="1" ty="0" hk="0" d="1" />
*/

#[derive(Clone, Debug, DeXml, PartialEq)]
pub struct Service03f1Read {
    #[nanoxml(attr, rename = "se")]
    pub sequence: SeqNr,
    #[nanoxml(attr, rename = "ba")]
    pub bus_address: HexNr<u8>,
    #[nanoxml(attr, rename = "hp")]
    pub heating_program: u8,
    #[nanoxml(attr, rename = "ty")]
    pub heating_program_ty: u8,
    #[nanoxml(attr, rename = "hk")]
    pub heating_circuit: u8,
    #[nanoxml(attr, rename = "d")]
    pub day_of_week: u8,
    #[nanoxml(skip_ser)]
    pub last_sent_response_hash: Option<u64>,
}

#[derive(Clone, Debug, DeXml, PartialEq)]
pub struct Service03f1Write {
    #[nanoxml(attr, rename = "se")]
    pub sequence: SeqNr,
    #[nanoxml(attr, rename = "ba")]
    pub bus_address: HexNr<u8>,
    #[nanoxml(attr, rename = "hp")]
    pub heating_program: u8,
    #[nanoxml(attr, rename = "ty")]
    pub heating_program_ty: u8,
    #[nanoxml(attr, rename = "hk")]
    pub heating_circuit: u8,
    #[nanoxml(attr, rename = "d")]
    pub day_of_week: u8,
    #[nanoxml(attr, rename = "db")]
    pub data_bytes: HexBytes<[u8; 20]>,
}

#[derive(Clone, Debug, DeXml, PartialEq)]
pub struct ErrorRead {
    #[nanoxml(attr, rename = "se")]
    pub sequence: u32,
    #[nanoxml(attr, rename = "li")]
    pub numbers: String,
    #[nanoxml(skip_ser)]
    pub last_sent_response_hash: Option<u64>,
}

#[derive(Clone, Debug, DeXml, PartialEq)]
pub struct ServiceRead {
    #[nanoxml(attr, rename = "ba")]
    pub bus_address: HexNr<u8>,
    #[nanoxml(attr, rename = "se")]
    pub sequence: String,
    #[nanoxml(attr, rename = "snr")]
    pub service_number: HexNr<u16>,
    #[nanoxml(attr, rename = "tbi")]
    pub telegram_byte_index: u16,
    #[nanoxml(attr, rename = "hy")]
    pub hysteresis: Option<u16>, // TODO: type?
    #[nanoxml(attr, rename = "ty")]
    pub ty: Option<u16>, // TODO: type?
}

#[derive(Clone, Debug, DeXml, PartialEq)]
pub struct ModbusRead {
    #[nanoxml(attr, rename = "se")]
    pub sequence: String,
    #[nanoxml(attr, rename = "ba")]
    pub bus_address: HexNr<u8>,
    #[nanoxml(attr, rename = "fc")]
    pub function_code: u8,
    /// 1..65536
    #[nanoxml(attr, rename = "adr")]
    pub start_address: u32,
    #[nanoxml(attr, rename = "count")]
    pub count: u16,
    #[nanoxml(attr, rename = "is", default_de = "default_interval")]
    pub interval: u16,
    #[nanoxml(skip_ser)]
    pub last_sent_response_hash: Option<u64>,
}

#[derive(Clone, Debug, DeXml, PartialEq)]
pub struct ModbusWrite {
    #[nanoxml(attr, rename = "se")]
    pub sequence: String,
    #[nanoxml(attr, rename = "ba")]
    pub bus_address: HexNr<u8>,
    #[nanoxml(attr, rename = "fc")]
    pub function_code: u8,
    /// 1..65536
    #[nanoxml(attr, rename = "adr")]
    pub start_address: u32,
    #[nanoxml(attr, rename = "count")]
    pub count: u16,
    #[nanoxml(attr, rename = "db")]
    pub data_bytes: HexBytes<Vec<u8>>,
}

#[derive(Clone, Debug, DeXml, PartialEq)]
pub enum BundleType {
    #[nanoxml(rename = "pull")]
    Pull,
    #[nanoxml(rename = "push")]
    Push,
    #[nanoxml(rename = "write")]
    Write,
    #[nanoxml(rename = "remove")]
    Remove,
}

#[derive(Clone, Debug, Default, SerXml, PartialEq)]
#[nanoxml(rename = "tbres")]
pub struct BundleResponse {
    #[nanoxml(attr, rename = "bn")]
    pub bundle_id: u16,
    #[nanoxml(attr, rename = "gw")]
    pub gateway_id: u32,
    #[nanoxml(attr, rename = "st")]
    pub status: BundleStatus,
    #[nanoxml(attr, rename = "ts")]
    pub time_stamp: Option<String>,
    #[nanoxml(attr, rename = "emsg")]
    pub error_msg: Option<String>,
    #[nanoxml(seq, rename = "irs")]
    pub info_responses: Vec<InfoResponse>,
    #[nanoxml(seq, rename = "iac")]
    pub info_acks: Vec<InfoAck>,
    #[nanoxml(seq, rename = "s03f2rs")]
    pub svc03f2_responses: Vec<Service03f2Response>,
    #[nanoxml(seq, rename = "s03f3ac")]
    pub svc03f3_acks: Vec<Service03f3Ack>,
    #[nanoxml(seq, rename = "ers")]
    pub error_responses: Option<Vec<ErrorResponse>>,
    #[nanoxml(seq, rename = "srs")]
    pub service_responses: Vec<ServiceResponse>,
    #[nanoxml(seq, rename = "mrs")]
    pub modbus_responses: Vec<ModbusResponse>,
    #[nanoxml(seq, rename = "mac")]
    pub modbus_acks: Vec<ModbusAck>,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub struct InfoResponse {
    #[nanoxml(attr, rename = "se")]
    pub sequence: SeqNr,
    #[nanoxml(attr, rename = "ba")]
    pub bus_address: HexNr<u8>,
    #[nanoxml(attr, rename = "snr")]
    pub service_number: Option<HexNrNoPrefix<u16>>,
    #[nanoxml(attr, rename = "in")]
    pub number: u16, // TODO: replace w custom data structure
    #[nanoxml(attr, rename = "dl")]
    //#[serde(skip_serializing_if = "is_zero")]
    pub data_low: HexNr<u8>,
    #[nanoxml(attr, rename = "dh")]
    //#[serde(skip_serializing_if = "is_zero")]
    pub data_high: HexNr<u8>,
    #[nanoxml(attr, rename = "st")]
    pub status: DataPointStatus,
    #[nanoxml(attr, rename = "max")]
    pub max: Option<HexNr<u16>>,
    #[nanoxml(attr, rename = "min")]
    pub min: Option<HexNr<u16>>,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub struct InfoAck {
    #[nanoxml(attr, rename = "se")]
    pub sequence: SeqNr,
    #[nanoxml(attr, rename = "ba")]
    pub bus_address: HexNr<u8>,
    #[nanoxml(attr, rename = "in")]
    pub number: u16, // TODO: hex?
    #[nanoxml(attr, rename = "dl")]
    pub data_low: HexNr<u8>,
    #[nanoxml(attr, rename = "dh")]
    pub data_high: HexNr<u8>,
    #[nanoxml(attr, rename = "st")]
    pub status: DataPointStatus,
    #[nanoxml(attr, rename = "snr")]
    pub service_number: HexNrNoPrefix<u16>,
}

// TODO: could reuse *Write
#[derive(Clone, Debug, SerXml, PartialEq)]
pub struct Service03f2Response {
    #[nanoxml(attr, rename = "se")]
    pub sequence: SeqNr,
    #[nanoxml(attr, rename = "ba")]
    pub bus_address: HexNr<u8>,
    #[nanoxml(attr, rename = "hp")]
    pub heating_program: u8,
    #[nanoxml(attr, rename = "ty")]
    pub heating_program_ty: u8,
    #[nanoxml(attr, rename = "hk")]
    pub heating_circuit: u8,
    #[nanoxml(attr, rename = "d")]
    pub day_of_week: u8,
    #[nanoxml(attr, rename = "db")]
    pub data_bytes: HexBytes<[u8; 20]>,
    #[nanoxml(attr, rename = "st")]
    pub status: DataPointStatus,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub struct Service03f3Ack {
    #[nanoxml(attr, rename = "se")]
    pub sequence: SeqNr,
    #[nanoxml(attr, rename = "ba")]
    pub bus_address: HexNr<u8>,
    #[nanoxml(attr, rename = "st")]
    pub status: DataPointStatus,
    #[nanoxml(attr, rename = "hp")]
    pub heating_program: u8,
    #[nanoxml(attr, rename = "ty")]
    pub heating_program_ty: u8,
    #[nanoxml(attr, rename = "hk")]
    pub heating_circuit: u8,
    #[nanoxml(attr, rename = "d")]
    pub day_of_week: u8,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub struct ErrorResponse {
    #[nanoxml(attr, rename = "se")]
    pub sequence: u32,
    #[nanoxml(attr, rename = "ba")]
    pub bus_address: HexNr<u8>,
    #[nanoxml(attr, rename = "index")]
    pub index: u8,
    #[nanoxml(attr, rename = "code")]
    pub code: u16,
    #[nanoxml(attr, rename = "active")]
    pub active: bool,
    #[nanoxml(attr, rename = "start")]
    pub start: Option<String>,
    #[nanoxml(attr, rename = "stop")]
    pub stop: Option<String>,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub struct ServiceResponse {
    #[nanoxml(attr, rename = "ba")]
    pub bus_address: HexNr<u16>,
    #[nanoxml(attr, rename = "se")]
    pub sequence: String,
    #[nanoxml(attr, rename = "snr")]
    pub service_number: HexNrNoPrefix<u16>,
    #[nanoxml(attr, rename = "tbi")]
    pub telegram_byte_index: u16,
    #[nanoxml(attr, rename = "db")]
    pub data_bytes: HexBytes<Vec<u8>>, // TODO: could use slice
    #[nanoxml(attr, rename = "st")]
    pub status: DataPointStatus,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub struct ModbusResponse {
    #[nanoxml(attr, rename = "se")]
    pub sequence: String,
    #[nanoxml(attr, rename = "ba")]
    pub bus_address: HexNr<u8>,
    #[nanoxml(attr, rename = "fc")]
    pub function_code: u8,
    /// 1..65536
    #[nanoxml(attr, rename = "adr")]
    pub start_address: u32,
    #[nanoxml(attr, rename = "count")]
    pub count: Option<u16>,
    #[nanoxml(attr, rename = "db")]
    pub data_bytes: Option<HexBytes<Vec<u8>>>,
    #[nanoxml(attr, rename = "st")]
    pub status: ModbusStatus,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub struct ModbusAck {
    #[nanoxml(attr, rename = "se")]
    pub sequence: String,
    #[nanoxml(attr, rename = "ba")]
    pub bus_address: HexNr<u8>,
    #[nanoxml(attr, rename = "fc")]
    pub function_code: u8,
    /// 1..65536
    #[nanoxml(attr, rename = "adr")]
    pub start_address: u32,
    #[nanoxml(attr, rename = "st")]
    pub status: ModbusStatus,
}

#[derive(Clone, Debug, Default, SerXml, PartialEq)]
pub enum BundleStatus {
    #[default]
    #[nanoxml(rename = "OK")]
    Ok,
    #[nanoxml(rename = "BU")]
    Busy,
    #[nanoxml(rename = "ER")]
    Error,
}

#[derive(Clone, Copy, Debug, SerXml, PartialEq)]
pub enum DataPointStatus {
    #[nanoxml(rename = "OK")]
    Ok,
    #[nanoxml(rename = "ER")]
    NotWhitelisted,
    #[nanoxml(rename = "TO")]
    Timeout,
}

#[derive(Clone, Copy, Debug, SerXml, PartialEq)]
pub enum ModbusStatus {
    #[nanoxml(rename = "OK")]
    Ok,
    #[nanoxml(rename = "ER")]
    Exception,
    #[nanoxml(rename = "TO")]
    CommunicationError,
}

/// refers to ISMConfigRequest
///
#[derive(Clone, Debug, DeXml, PartialEq)]
#[nanoxml(rename = "ism-config-request")]
pub struct GwConfigRequest {
    #[nanoxml(attr, rename = "sid")]
    pub session_id: u32,
    #[nanoxml(rename = "dhcp-active")]
    pub dhcp_active: Option<bool>,
    #[nanoxml(rename = "ip-address")]
    pub ip_address: Option<IpAddr>,
    #[nanoxml(rename = "subnetmask")]
    pub subnet_mask: Option<bool>,
    #[nanoxml(rename = "default-gateway")]
    pub default_gateway: Option<IpAddr>,
    #[nanoxml(rename = "dns-name-server")]
    pub dns_name_server: Option<IpAddr>,
    #[nanoxml(rename = "dest-server")]
    pub dest_server: Option<String>,
    #[nanoxml(rename = "dest-port")]
    pub dest_port: Option<bool>,
    #[nanoxml(rename = "installationname")]
    pub system_name: Option<String>,
    #[nanoxml(rename = "connect-portal")]
    pub connect_portal: Option<bool>,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
#[nanoxml(rename = "ism-config-response")]
pub struct GwConfigResponse {
    #[nanoxml(attr, rename = "sid")]
    pub session_id: u32,
    #[nanoxml(rename = "dhcp-active")]
    pub dhcp_active: bool,
    #[nanoxml(rename = "ip-address")]
    pub ip_address: IpAddr,
    #[nanoxml(rename = "subnetmask")]
    pub subnet_mask: Ipv4Addr,
    #[nanoxml(rename = "default-gateway")]
    pub default_gateway: IpAddr,
    #[nanoxml(rename = "dns-nameserver")]
    pub dns_name_server: IpAddr,
    #[nanoxml(rename = "dest-server")]
    pub dest_server: &'static str,
    #[nanoxml(rename = "dest-port")]
    pub dest_port: u16,
    #[nanoxml(rename = "installationname")]
    pub system_name: &'static str,
    #[nanoxml(rename = "connect-portal")]
    pub connect_portal: bool,
    #[nanoxml(rename = "date-time")]
    pub date_time: Option<ClockTime>,
    #[nanoxml(rename = "ism-serialnumber")]
    pub ism_serial_number: &'static str,
    #[nanoxml(rename = "ism-softwareversion")]
    pub ism_software_version: &'static str,
    #[nanoxml(rename = "ism-hardwareversion")]
    pub ism_hardware_version: &'static str,
    #[nanoxml(rename = "wlan-active")]
    pub wlan_active: bool, // TODO: difference wlan_connected and wlan_active?
    #[nanoxml(rename = "wlan-signal-strength-mW")]
    pub wlan_signal_strength: u8,
    #[nanoxml(rename = "mac-address-lan")]
    pub mac_address_lan: &'static str,
    #[nanoxml(rename = "mac-address-wlan")]
    pub mac_address_wlan: &'static str,
    #[nanoxml(rename = "v2-version")]
    pub v2_version: &'static str,
    #[nanoxml(rename = "v2-commit-hash")]
    pub v2_commit_hash: &'static str,
}

#[derive(Clone, Debug, DeXml, PartialEq)]
#[nanoxml(rename = "direct-logon-request")]
pub struct DirectLogonRequest {
    #[nanoxml(attr, rename = "Type")]
    pub ty: Option<u16>,
    #[nanoxml(attr, rename = "Version")]
    pub version: Option<u16>,
    pub password: String,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
#[nanoxml(rename = "direct-logon-response")]
pub struct DirectLogonResponse {
    #[nanoxml(attr, rename = "state")]
    pub state: GwState,
    #[nanoxml(attr, rename = "sid")]
    pub session_id: Option<u32>,
    #[nanoxml(rename = "installationname")]
    pub system_name: Option<&'static str>,
    #[nanoxml(rename = "serialnumber")]
    pub serial: Option<&'static str>,
    #[nanoxml(rename = "date-time")]
    pub date_time: Option<ClockTime>,
    #[nanoxml(rename = "ism-softwareversion")]
    pub ism_software_version: Option<&'static str>,
    #[nanoxml(rename = "ism-hardwareversion")]
    pub ism_hardware_version: Option<&'static str>,
    #[nanoxml(rename = "wlan-connected")]
    pub wlan_connected: Option<bool>,
    #[nanoxml(rename = "type")]
    pub ty: Option<&'static str>,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub enum GwState {
    #[nanoxml(rename = "pending")]
    Pending,
    #[nanoxml(rename = "invalidCredentials")]
    InvalidCredentials,
    #[nanoxml(rename = "ok")]
    Ok,
}

#[derive(Clone, Debug, DeXml, PartialEq)]
pub struct GwProtocolConfigRequest {}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub struct GwProtocolConfigResponse {}

#[derive(Clone, Debug, DeXml, PartialEq)]
pub struct GwProtocolValueRequest {}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub struct GwProtocolValueResponse {}

#[derive(Clone, Copy, Debug)]
pub struct KeepAlive {
    pub counter: u16,
}

impl KeepAlive {
    pub fn parse(bytes: &[u8]) -> Option<Self> {
        let counter = Cursor::new(bytes).read_u16::<BE>().ok()?;

        Some(KeepAlive { counter })
    }
}

#[derive(Clone, Debug, DeXml, PartialEq)]
#[nanoxml(rename = "startSession")]
pub struct StartSequence {}

#[derive(Clone, Debug, DeXml, PartialEq)]
#[nanoxml(rename = "firmware-update-request")]
pub struct OtaUpdateRequest {
    pub version: String,
    #[nanoxml(rename = "security-version")]
    pub security_version: String,
    #[nanoxml(rename = "size")]
    pub total_size: usize,
    #[nanoxml(rename = "stream")]
    pub stream_id: u8,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
#[nanoxml(rename = "firmware-update-response")]
pub struct OtaUpdateResponse {
    #[nanoxml(rename = "intent")]
    pub intent: OtaUpdateResponseIntent,
    #[nanoxml(rename = "chunk-size")]
    pub chunk_size: Option<usize>,
    #[nanoxml(rename = "reject-reason")]
    pub reject_reason: Option<OtaUpdateResponseRejectReason>,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub enum OtaUpdateResponseIntent {
    #[nanoxml(rename = "ACCEPT")]
    Accept,
    #[nanoxml(rename = "REJECT")]
    Reject,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub enum OtaUpdateResponseRejectReason {
    #[nanoxml(rename = "SECURITY")]
    Security,
    #[nanoxml(rename = "BUSY")]
    Busy,
    #[nanoxml(rename = "UPDATES_DISABLED")]
    UpdatesDisabled,
    #[nanoxml(rename = "BAD_STREAM_ID")]
    BadStreamId,
    #[nanoxml(rename = "INTERNAL_ERROR")]
    InternalError,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
#[nanoxml(rename = "firmware-update-completion-status")]
pub struct OtaUpdateCompletionStatus {
    #[nanoxml(rename = "status")]
    pub status: OtaUpdateCompletionEnum,
    #[nanoxml(rename = "message")]
    pub message: Option<String>,
}

#[derive(Clone, Debug, SerXml, PartialEq)]
pub enum OtaUpdateCompletionEnum {
    #[nanoxml(rename = "SUCCESS")]
    Success,
    #[nanoxml(rename = "FAILURE")]
    Failure,
    #[nanoxml(rename = "ABORT")]
    Abort,
}

#[derive(Clone, Debug, DeXml, PartialEq)]
#[nanoxml(rename = "restart")]
pub struct Restart {}

#[derive(Clone, Debug, DeXml, SerXml, PartialEq)]
#[nanoxml(rename = "eebus-status")]
pub struct EebusStatus {
    #[nanoxml(attr, rename = "state")]
    pub state: EebusState,
    #[nanoxml(seq, rename = "device")]
    pub devices: Vec<EebusDevice>,
}

#[derive(Clone, Debug, DeXml, SerXml, PartialEq)]
pub enum EebusState {
    #[nanoxml(rename = "on")]
    On,
    #[nanoxml(rename = "off")]
    Off,
}

#[derive(Clone, Debug, DeXml, SerXml, PartialEq)]
pub struct EebusDevice {
    #[nanoxml(attr, rename = "ski")]
    pub ski: String,
    #[nanoxml(attr, rename = "ip")]
    pub ip: Option<String>,
    #[nanoxml(attr, rename = "status")]
    pub status: EebusDeviceStatus,
}

#[derive(Clone, Debug, DeXml, SerXml, PartialEq)]
pub enum EebusDeviceStatus {
    #[nanoxml(rename = "TRUSTED")]
    Trusted,
    #[nanoxml(rename = "DISTRUSTED")]
    Distrusted,
    #[nanoxml(rename = "PENDING")]
    Pending,
    #[nanoxml(rename = "CONNECTED")]
    Connected,
    #[nanoxml(rename = "PIN_REQUIRED")]
    PinRequired,
}

#[derive(Clone, Debug, DeXml, PartialEq)]
#[nanoxml(rename = "eebus-control")]
pub struct EebusControl {
    #[nanoxml(rename = "set-trust")]
    pub set_trust: Option<EebusSetTrust>,
    #[nanoxml(rename = "set-pin")]
    pub set_pin: Option<EebusSetPin>,
    #[nanoxml(rename = "state")]
    pub state: Option<EebusState>,
}

#[derive(Clone, Debug, DeXml, PartialEq)]
pub struct EebusSetTrust {
    #[nanoxml(attr, rename = "ski")]
    pub ski: String,
    #[nanoxml(attr, rename = "status")]
    pub status: EebusSetTrustStatus,
}

#[derive(Clone, Debug, DeXml, PartialEq)]
pub enum EebusSetTrustStatus {
    #[nanoxml(rename = "TRUSTED")]
    Trusted,
    #[nanoxml(rename = "DISTRUSTED")]
    Distrusted,
    #[nanoxml(rename = "PENDING")]
    Pending,
}

#[derive(Clone, Debug, DeXml, PartialEq)]
pub struct EebusSetPin {
    #[nanoxml(attr, rename = "ski")]
    pub ski: String,
    #[nanoxml(attr, rename = "pin")]
    pub pin: String,
}

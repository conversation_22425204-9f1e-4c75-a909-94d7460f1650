use std::{
    cell::Cell,
    future::Future,
    net::{IpAddr, Ipv4Addr},
    pin::Pin,
    str::FromStr,
};

use defmt::Debug2Format;
use eebus::ship::{Ski, TrustLevel};
use embassy_futures::{
    join::join,
    select::{select, select3, Either3},
};
use esp_idf_svc::hal::reset::ResetReason;
use futures_lite::{AsyncRead, AsyncWrite};

use super::bundle_mgr::{execute_msg, handle_bundle, ScheduledBundles};
use crate::{
    ebus::api::EbusReply,
    flash::{
        eventlog::{
            events::{Event, PortalLogonResponseResult, ResetTrigger},
            utils::ResultEventLogAsync,
        },
        ota,
    },
    modbus::api::ModbusReply,
    net::task::default_ip_info,
    prelude::*,
    server::schema::{LocalConnectionState, PortalState},
    smartset::{
        bundle_mgr::BundleChannel,
        msg::{
            self, read_telegram, write_telegram, DirectLogonResponse, EbusConfig,
            EebusSetTrustStatus, EebusState, GwState, ModbusConfig, PortalLogonRequest,
            SystemConfigResponse, SystemConfigResponseGateway, Telegram,
        },
    },
    state::net::RssiLevel,
    utils::{calc_subnet_mask, cod::call_on_drop},
};

thread_local! {
    // signifies whether the device has been connected to smartset after the boot. used in order to
    // detect whether a disconnect from the portal was caused by software or (hardware) reset.
    static CONNECTED_TO_PORTAL_AFTER_BOOT: Cell<bool> = const { Cell::new(false) };
}

const TIMEOUT: Duration = mins(4);

pub async fn run(
    stream_reader: impl AsyncRead + Unpin,
    stream_writer: impl AsyncWrite + Unpin,
    channel: BundleChannel,
) -> Result<()> {
    match channel {
        BundleChannel::Smartset => {
            run_inner(stream_reader, stream_writer, channel, &mut |i, o| {
                Box::pin(handle_server(i, o))
            })
            .await
        }
        BundleChannel::Local => {
            run_inner(stream_reader, stream_writer, channel, &mut |i, o| {
                Box::pin(handle_local(i, o))
            })
            .await
        }
    }
}

#[allow(clippy::type_complexity)]
async fn run_inner(
    stream_reader: impl AsyncRead + Unpin,
    stream_writer: impl AsyncWrite + Unpin,
    channel: BundleChannel,
    handle_con: &mut dyn for<'a> FnMut(
        &'a Channel<Telegram, 1>,
        &'a Channel<Telegram, 3>,
    ) -> Pin<Box<dyn Future<Output = Result<()>> + 'a>>,
) -> Result<()> {
    let tel_in = Channel::<Telegram, 1>::new();
    let tel_out = Channel::<Telegram, 3>::new();
    let (r1, r2) = join(
        handle_con(&tel_in, &tel_out),
        handle_socket(stream_reader, stream_writer, &tel_in, &tel_out, channel),
    )
    .await;

    r1.and(r2)
}

#[allow(clippy::large_futures)] // this function is only used in places where it is is already boxed
async fn handle_server(
    tel_in: &Channel<Telegram, 1>,
    tel_out: &Channel<Telegram, 3>,
) -> Result<()> {
    let sid = init_server(tel_in, tel_out).await?;
    handle_connection(Some(sid), tel_in, tel_out, BundleChannel::Smartset).await
}

async fn init_server(tel_in: &Channel<Telegram, 1>, tel_out: &Channel<Telegram, 3>) -> Result<u32> {
    env::acquire_website_state().await.portal_state = PortalState::Logon;

    let telegram = with_timeout(TIMEOUT, tel_in.receive())
        .await
        .context(intern!("read StartSequence before timeout"))?;

    if !matches!(telegram, Telegram::StartSequence(_)) {
        bail!("first telegram is expected to be StartSequence");
    }

    let test_mode = state().test_mode();
    let prod_mode = crate::utils::is_prod_mode();
    let system_name = &env::active_params().system_name;
    let pass = &env::active_params().password;

    // whether the disconnect was caused by a reboot or by software (e.g. Smartset kicked us)
    // NOTE: this is not currently parsed by Smartset, only used for debugging
    // TODO: SoftwareDisconnect could be more specific
    let disconnect_reason = match CONNECTED_TO_PORTAL_AFTER_BOOT.get() {
        true => "SoftwareDisconnect".into(),
        false => format!("Reboot:{:?}", ResetReason::get()),
    };

    let portal_logon_request = Telegram::PortalLogonRequestEncrypted(PortalLogonRequest {
        rand: fastrand::u32(..),
        system_name,
        password: pass,
        serial: crate::utils::serial(),
        date_time: state().ebus.current_time(),
        ism_software_version: crate::utils::version::SHORT_NUM,
        ism_software_number: crate::utils::version::SOFTWARE_NUM,
        ism_hardware_version: crate::utils::hwversion::get_str(),
        wlan_connected: state().net.state.get().is_wifi_connected(),
        ty: "Link",
        v2_version: crate::utils::version::FULL,
        v2_commit_hash: crate::utils::version::HASH,
        v2_test: Some(true).filter(|_| test_mode),
        v2_dev: Some(true).filter(|_| !prod_mode),
        v2_disconnect_reason: disconnect_reason,
    });

    with_timeout(TIMEOUT, tel_out.send(portal_logon_request))
        .err_evcontext(
            Event::PortalLogonResponse(PortalLogonResponseResult::ErrTimeout),
            intern!("write PortalLogonRequest before timeout"),
        )
        .await?;

    let telegram = with_timeout(TIMEOUT, tel_in.receive())
        .err_evcontext(
            Event::PortalLogonResponse(PortalLogonResponseResult::ErrTimeout),
            intern!("read PortalLogonResponse before timeout"),
        )
        .await?;

    let Telegram::PortalLogonResponse(portal_logon_response) = telegram else {
        Event::PortalLogonResponse(PortalLogonResponseResult::ErrInvalidResponse)
            .log()
            .await;
        bail!("expected portal logon response")
    };

    if test_mode {
        info!("Authenticated");
        loop {
            info!("PRODUCTION_TEST_SUCCESS");
            Timer::after(Duration::from_secs(1)).await;
        }
    }

    env::acquire_website_state().await.portal_state = PortalState::Connected;

    state().led.disable(LedBits::PortalConnecting);

    // TODO: If the device was updated via smartset but cannot connect after the reboot (after a
    // certain amount of retries etc), we should rollback to the previous version.

    match portal_logon_response.state {
        msg::PortalState::Ok => {
            info!("Connected");
            state().led.enable(LedBits::PortalConnected);
        }
        e => {
            warn!("Failed to connect: err={}", e);
            // TODO: next_connect_params
            bail!("portal requires retry");
        }
    }

    let session_id = portal_logon_response.session_id;
    info!("Got session ID: id={}", session_id);

    CONNECTED_TO_PORTAL_AFTER_BOOT.set(true);

    // Send EEBUS status after portal login
    // TODO: send unconditionally, even if eebus is disabled
    if env::active_params().eebus_enabled {
        let eebus_status = crate::smartset::eebus_status::create_eebus_status_telegram().await;
        tel_out.send(eebus_status).await;
    }

    Ok(session_id)
}

async fn init_local(
    tel_in: &Channel<Telegram, 1>,
    tel_out: &Channel<Telegram, 3>,
) -> Result<Option<(u16, u16)>> {
    env::acquire_website_state().await.local_connection_state = LocalConnectionState::Logon;

    let system_name = &env::active_params().system_name;
    let pass = &env::active_params().password;

    let telegram = tel_in.receive().await;
    let smart_home;
    let password_correct;
    match telegram {
        Telegram::DirectLogonRequest(req) => {
            info!("Got password from client: pw={}", req.password);
            smart_home = req
                .version
                .and_then(|version| req.ty.map(|ty| (version, ty)));

            let expect_password = match smart_home {
                Some(_) => "SmartHomeManager@ISM9",
                None => pass.as_str(),
            };

            password_correct = req.password.as_str() == expect_password;
        }
        _ => bail!("first telegram is expected to be DirectLogonRequest"),
    }

    let tel = match password_correct {
        true => {
            let num_devices = state().ebus.devices.get().bus_devices.len()
                + state().modbus.devices.get().bus_devices.len();
            let logon_state = match num_devices {
                0 => GwState::Pending,
                _ => GwState::Ok,
            };

            Telegram::DirectLogonResponse(DirectLogonResponse {
                state: logon_state,
                session_id: None,
                system_name: Some(system_name),
                serial: Some(crate::utils::serial()),
                date_time: state().ebus.current_time(),
                ism_software_version: Some(crate::utils::version::SHORT),
                ism_hardware_version: Some(crate::utils::hwversion::get_str()),
                wlan_connected: Some(state().net.state.get().is_wifi_connected()),
                ty: Some("Link"),
            })
        }
        false => Telegram::DirectLogonResponse(DirectLogonResponse {
            state: GwState::InvalidCredentials,
            session_id: None,
            system_name: None,
            serial: None,
            date_time: None,
            ism_software_version: None,
            ism_hardware_version: None,
            wlan_connected: None,
            ty: None,
        }),
    };

    tel_out.send(tel).await;
    ensure!(password_correct);

    Ok(smart_home)
}

#[allow(clippy::large_futures)] // this function is only used in places where it is is already boxed
async fn handle_local(tel_in: &Channel<Telegram, 1>, tel_out: &Channel<Telegram, 3>) -> Result<()> {
    let smart_home = init_local(tel_in, tel_out).await?;

    // TODO: handle website states more consistently
    env::acquire_website_state().await.local_connection_state =
        LocalConnectionState::ConnectedLocally;

    // Send EEBUS status after local login
    // let eebus_status = crate::application::eebus_status::create_eebus_status_telegram().await;
    // write_telegram(
    //     &mut stream_writer,
    //     &eebus_status,
    //     &mut xml_buf,
    //     Channel::Local,
    // )
    // .await?;

    let _smart_home_guard;
    if let Some((version, ty)) = smart_home {
        _smart_home_guard = call_on_drop(|| {
            state().smart_home.disable();
        });
        state().smart_home.enable(version, ty);
    }

    handle_connection(None, tel_in, tel_out, BundleChannel::Local).await
}

async fn handle_socket(
    mut stream_reader: impl AsyncRead + Unpin,
    mut stream_writer: impl AsyncWrite + Unpin,
    tel_in: &Channel<Telegram, 1>,
    tel_out: &Channel<Telegram, 3>,
    channel: BundleChannel,
) -> Result<()> {
    let mut read_buf = Vec::new();
    let mut xml_buf = String::new();
    loop {
        let read_tel = read_telegram(&mut stream_reader, &mut read_buf, channel);
        futures_lite::pin!(read_tel);
        let mut connection_timeout = Timer::after(TIMEOUT);
        loop {
            if channel == BundleChannel::Local {
                // local connection does not send keep-alive if we write to the socket
                // thus we need to reset the timeout even thogh we did not receive anything
                connection_timeout = Timer::after(TIMEOUT);
            }

            match select3(&mut read_tel, tel_out.receive(), &mut connection_timeout).await {
                Either3::First(tel) => {
                    let tel = tel.context(intern!("read telegram"))?;
                    debug!(
                        "Received parsed telegram: channel={}, tel={}",
                        channel,
                        Debug2Format(&tel)
                    );
                    tel_in.send(tel).await;
                    break;
                }
                Either3::Second(tel) => {
                    write_telegram(&mut stream_writer, &tel, &mut xml_buf, channel).await?;
                }
                Either3::Third(_) => {
                    error!(
                        "Got layer 7 timeout: channel={}, timeout={}",
                        channel, TIMEOUT
                    );
                    bail!("layer7 timeout");
                }
            };
        }
    }
}

async fn handle_connection(
    mut session_id: Option<u32>,
    tel_in: &Channel<Telegram, 1>,
    tel_out: &Channel<Telegram, 3>,
    channel: BundleChannel,
) -> Result<()> {
    let mut system_config_sent = None;

    // let trust_db = env::shared().trust_db();
    let mut scheduled = ScheduledBundles::default();

    let ebus_slot = ReturnSlot::new();
    let modbus_slot = ReturnSlot::new();

    let mut ebus_sub = state().ebus.devices.subscribe();
    let mut modbus_sub = state().modbus.devices.subscribe();

    // _ = trust_db.wait_update().fuse() => {
    //     // Send EEBUS status after trust update

    //     if env::active_params().eebus_enabled {
    //         let eebus_status = crate::application::eebus_status::create_eebus_status_telegram().await;
    //         write_telegram(&mut stream_writer, &eebus_status, &mut xml_buf, channel).await?;
    //     }
    // }

    loop {
        let ret_tel = match select3(
            tel_in.receive(),
            select(ebus_sub.changed(), modbus_sub.changed()),
            scheduled.next_always(),
        )
        .await
        {
            Either3::First(tel) => {
                process_telegram(
                    tel,
                    &mut session_id,
                    channel,
                    &mut scheduled,
                    &mut system_config_sent,
                    &ebus_slot,
                    &modbus_slot,
                )
                .await?
            }
            Either3::Second(_) => {
                // only send it if we already replied to a SystemConfigRequest at least two minutes ago
                if let Some(sent) = system_config_sent
                    && sent.elapsed().as_secs() > 120
                {
                    system_config_sent = Some(Instant::now());
                    Some(build_system_config(session_id.unwrap()))
                } else {
                    info!("Ignoring updated system config due to delay hack");
                    None
                }
            }
            Either3::Third(bundle) => {
                execute_msg(&mut bundle.bundle, channel, &ebus_slot, &modbus_slot)
                    .await
                    .map(Telegram::BundleResponse)
            }
        };

        if let Some(ret_tel) = ret_tel {
            tel_out.send(ret_tel).await;
        }
    }
}

async fn process_telegram(
    tel: Telegram,
    session_id: &mut Option<u32>,
    channel: BundleChannel,
    scheduled: &mut ScheduledBundles,
    system_config_sent: &mut Option<Instant>,
    ebus_slot: &ReturnSlot<EbusReply>,
    modbus_slot: &ReturnSlot<ModbusReply>,
) -> Result<Option<Telegram>> {
    let ret = match tel {
        Telegram::SystemConfigRequest(scr) => {
            if session_id.is_none() {
                *session_id = Some(scr.session_id);
            }
            if channel == BundleChannel::Smartset {
                Timer::after(secs(10)).await;
            }
            *system_config_sent = Some(Instant::now());
            Some(build_system_config(session_id.unwrap()))
        }
        Telegram::BundleRequest(br) => {
            handle_bundle(scheduled, br, channel, ebus_slot, modbus_slot)
                .await
                .map(Telegram::BundleResponse)
        }
        Telegram::EebusControl(control) => {
            info!(
                "Received EEBus control message: msg={}",
                Debug2Format(&control)
            );

            // Handle set-trust command
            if let Some(set_trust) = control.set_trust {
                let Ok(ski) = Ski::from_str(&set_trust.ski) else {
                    error!("Invalid SKI in EEBus control message");
                    return Ok(None);
                };

                let trust_level = match set_trust.status {
                    EebusSetTrustStatus::Trusted => TrustLevel::Trust,
                    EebusSetTrustStatus::Distrusted => TrustLevel::Distrust,
                    EebusSetTrustStatus::Pending => TrustLevel::Pending,
                };

                env::shared().trust_db().set_trust(ski, trust_level).await;
            }

            // Handle set-pin command
            if let Some(set_pin) = control.set_pin {
                info!("Set PIN for device: ski={}", set_pin.ski);
                // TODO: Implement PIN handling when PIN authentication is implemented
            }

            // Handle state command
            if let Some(state) = control.state {
                let enabled = matches!(state, EebusState::On);
                nvs::eebus_enabled::set(enabled).await;
                info!("Wrote EEBus parameter to NVS: enabled={}", enabled);
            }

            None
        }
        Telegram::GwConfigRequest(_req) => {
            const DEFAULT_IP: Ipv4Addr = Ipv4Addr::new(0, 0, 0, 0);

            let net_state = state().net.state.get();
            let ip_info = net_state.ip_info().copied().unwrap_or_else(default_ip_info);

            let dhcp_active = env::active_params().dhcp && !state().net.temp_ip();

            let params = env::active_params();
            Some(Telegram::GwConfigResponse(msg::GwConfigResponse {
                session_id: session_id.unwrap_or(1),
                dhcp_active,
                ip_address: IpAddr::V4(ip_info.ip),
                subnet_mask: calc_subnet_mask(ip_info.subnet.mask.0),
                default_gateway: IpAddr::V4(ip_info.subnet.gateway),
                dns_name_server: IpAddr::V4(ip_info.dns.unwrap_or(DEFAULT_IP)),
                dest_server: &params.portal_host,
                dest_port: params.portal_port,
                system_name: &params.system_name,
                connect_portal: params.portal_enabled,
                date_time: None,
                ism_serial_number: crate::utils::serial(),
                ism_software_version: crate::utils::version::SHORT,
                ism_hardware_version: crate::utils::hwversion::get_str(),
                wlan_active: net_state.is_wifi_connected(),
                wlan_signal_strength: net_state.rssi().unwrap_or(RssiLevel(0)).0,
                mac_address_lan: crate::utils::mac::eth(),
                mac_address_wlan: crate::utils::mac::wifi(),
                v2_version: crate::utils::version::FULL,
                v2_commit_hash: crate::utils::version::HASH,
            }))
        }
        Telegram::KeepAlive(_) => Some(tel),
        Telegram::OtaUpdateRequest(r) => {
            if channel == BundleChannel::Local {
                bail!("unauthorized");
            }
            let resp = ota::net::start(&r).await;
            Some(Telegram::OtaUpdateResponse(resp))
        }
        Telegram::BinaryData(data) => {
            if channel == BundleChannel::Local {
                bail!("unauthorized");
            }
            let resp = ota::net::write(&data.0).await;
            Some(resp)
        }
        Telegram::Restart(_) => crate::utils::restart(ResetTrigger::Smartset).await,
        _ => {
            warn!("Got unexpected telegram: tel={}", Debug2Format(&tel));
            None
        }
    };

    Ok(ret)
}

fn build_system_config(session_id: u32) -> Telegram {
    Telegram::SystemConfigResponse(SystemConfigResponse {
        session_id,
        gateway: SystemConfigResponseGateway {
            ty: "Link",
            software_number: crate::utils::version::SOFTWARE_NUM,
            software_version: crate::utils::version::SHORT_NUM,
            wlan: state().net.state.get().is_wifi_connected(),
            mobile_data: false,
        },
        error_msg: None,
        bus_configs: vec![EbusConfig {
            ty: msg::BusType::Ebus,
            bus_devices: state().ebus.devices.try_get().unwrap_or_default(),
        }],
        modbus_config: Some(ModbusConfig {
            bus_devices: state().modbus.devices.try_get().unwrap_or_default(),
        }),
    })
}

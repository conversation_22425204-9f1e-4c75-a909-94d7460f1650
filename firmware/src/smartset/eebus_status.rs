use std::net::IpAddr;

use eebus::ship::TrustLevel;

use crate::{
    prelude::*,
    smartset::msg::{EebusDevice, EebusDeviceStatus, EebusState, EebusStatus, Telegram},
};

/// Get the current EEBUS status
pub async fn get_eebus_status() -> EebusStatus {
    // Check if EEBUS functionality is enabled
    let eebus_enabled = env::active_params().eebus_enabled;

    if !eebus_enabled {
        return EebusStatus {
            state: EebusState::Off,
            devices: Vec::new(),
        };
    }

    // Get the list of EEBUS devices from the trust database
    let trust_db = env::shared().trust_db();
    let devices = trust_db.get_all_devices().await;

    let mut eebus_devices = Vec::new();
    for device in devices {
        let status = match device.level {
            TrustLevel::Trust => EebusDeviceStatus::Trusted,
            TrustLevel::Distrust => EebusDeviceStatus::Distrusted,
            TrustLevel::Pending => EebusDeviceStatus::Pending,
        };

        let ip = if device.ip == IpAddr::V4(std::net::Ipv4Addr::UNSPECIFIED) {
            None
        } else {
            Some(device.ip.to_string())
        };

        eebus_devices.push(EebusDevice {
            ski: device.ski.to_string(),
            ip,
            status,
        });
    }

    EebusStatus {
        state: EebusState::On,
        devices: eebus_devices,
    }
}

/// Create an EEBUS status telegram
pub async fn create_eebus_status_telegram() -> Telegram {
    let status = get_eebus_status().await;
    Telegram::EebusStatus(status)
}

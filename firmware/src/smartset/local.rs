use std::net::TcpListener;

use async_io_mini::Async;
use defmt::{Debug2Format, Display2Format};

use crate::{
    flash::eventlog::{
        events::{Event, LocalConnectResult, PortalDisconnectReason, TlsConnectResult},
        utils::ResultEventLogAsync,
    },
    net::tls::upgrade_to_tls_server,
    prelude::*,
    server::schema::LocalConnectionState,
    smartset::{bundle_mgr::BundleChannel, connection},
    utils::retry::{linear_delay, retry},
};

pub const LOCAL_CONN_CA: &[u8] = include_bytes!("../../sec/certs/server/ca-direct.der");
pub const LOCAL_CONN_CERT: &[u8] = include_bytes!("../../sec/certs/server/server-cert.der");
pub const LOCAL_CONN_KEY: &[u8] = include_bytes!("../../sec/certs/server/server-key.der");

pub async fn local_connect_task() {
    loop {
        if let Err(e) = Box::pin(retry(
            listen_local_connection,
            "local connection",
            linear_delay(1),
        ))
        .await
        {
            error!("Failed to connect: err={}", e);
        }
    }
}

async fn listen_local_connection() -> Result<()> {
    info!("Listening for local connection");

    let listener =
        Async::<TcpListener>::bind(([0, 0, 0, 0], 9092)).context(intern!("bind tcp listener"))?;
    loop {
        Timer::after(Duration::from_secs(1)).await;

        let (stream, addr) = listener
            .accept()
            .ok_event(Event::LocalConnect(LocalConnectResult::Ok))
            .err_event(Event::LocalConnect(LocalConnectResult::Err))
            .await
            .context(intern!("accept local client"))?;

        stream
            .get_ref()
            .set_read_timeout(Some(std::time::Duration::from_secs(10)))
            .unwrap();
        info!("Accepted local client: addr={}", Display2Format(&addr));

        let stream = upgrade_to_tls_server(stream, LOCAL_CONN_CA, LOCAL_CONN_CERT, LOCAL_CONN_KEY)
            .ok_event(Event::LocalTlsConnect(TlsConnectResult::Ok))
            .err_event_map(|e| Event::LocalTlsConnect(*e))
            .await;
        let stream = match stream
            .map_err(|e| anyhow!("failed to upgrade client to tls: {}", Debug2Format(&e)))
        {
            Ok(stream) => stream,
            Err(e) => {
                error!("{}", e);
                continue;
            }
        };

        let err = Box::pin(connection::run(stream.0, stream.1, BundleChannel::Local)).await;

        Event::LocalDisconnect(PortalDisconnectReason::Unknown)
            .log()
            .await;
        env::acquire_website_state().await.local_connection_state =
            LocalConnectionState::NotConnected;

        if let Err(err) = err {
            error!("Failed to connect: err={}", err);
        }
    }
}

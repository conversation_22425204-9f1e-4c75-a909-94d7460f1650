use defmt::Debug2Format;

use crate::prelude::*;

pub mod eebus_status;
pub mod local;
pub mod msg;
pub mod portal;

mod bundle_mgr;
mod connection;

pub async fn wait_until_connected(connected: bool) {
    debug!("Waiting for: connected={}", connected);
    let mut listener = state().net.state.subscribe();
    let state = listener
        .changed_and(|s| s.is_connected() == connected)
        .await;
    debug!("Updated network: state={}", Debug2Format(&state));
}

#![allow(unused_imports)]

pub use defmt::{
    assert, assert_eq, assert_ne, debug, error, info, intern, panic, todo, trace, unimplemented,
    unreachable, warn,
};
pub use embassy_time::{with_timeout, Duration, Instant, Ticker, Timer};
pub use esp_idf_svc::{
    self as svc, hal,
    sys::{self, EspError},
};

pub use crate::{
    anyhow, bail,
    debug::defmt_compat::DefmtCompat,
    ebus::api::ebus,
    ensure, env,
    flash::nvs,
    itc::{itc, ReturnSlot},
    led_btn::LedBits,
    state::{state, Observable},
    static_slot,
    sync::{Channel, Mutex, MutexGuard, Signal},
    utils::{
        block_for,
        error::{ErrorContext, Result},
        millis, mins, secs,
    },
};

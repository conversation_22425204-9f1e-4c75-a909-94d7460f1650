#![allow(dead_code)]

use super::{const_unwrap, mem::const_byte_slice};

pub const FULL: &str = env!("VERGEN_GIT_DESCRIBE"); // 4.20.9[-7-g7917480]
pub const HASH: &str = env!("VERGEN_GIT_SHA"); // 7917480
pub const TAG: &str = PARSED_VERSION_DATA.0; // 4.20.9
pub const SHORT: &str = PARSED_VERSION_DATA.1; // 4.20
pub const SHORT_NUM: u16 = PARSED_VERSION_DATA.2; // 420
pub const SOFTWARE_NUM: u16 = PARSED_VERSION_DATA.3; // 9

const PARSED_VERSION_DATA: (&str, &str, u16, u16) = {
    // cannot use str::parse() at comptime, so this will have to do
    const fn parse(mut bytes: &[u8]) -> u16 {
        let mut val = 0;
        while let [byte, rest @ ..] = bytes {
            assert!(b'0' <= *byte && *byte <= b'9', "invalid digit");
            val = val * 10 + (*byte - b'0') as u16;
            bytes = rest;
        }
        val
    }

    let slice = FULL.as_bytes();
    let mut i = 0;
    let mut first_dot = 0;
    let mut second_dot = 0;
    let mut dash = slice.len();
    let mut num_dots = 0;

    while i < slice.len() {
        if slice[i] == b'.' {
            num_dots += 1;
            if num_dots == 1 {
                first_dot = i;
            } else if num_dots == 2 {
                second_dot = i;
            }
        } else if slice[i] == b'-' {
            dash = i;
            break;
        }
        i += 1;
    }

    let major = const_byte_slice(slice, 0, first_dot);
    let minor = const_byte_slice(slice, first_dot + 1, second_dot);
    let patch = const_byte_slice(slice, second_dot + 1, dash);
    (
        const_unwrap(std::str::from_utf8(const_byte_slice(slice, 0, dash))),
        const_unwrap(std::str::from_utf8(const_byte_slice(slice, 0, second_dot))),
        parse(major) * 100 + parse(minor),
        parse(patch),
    )
};

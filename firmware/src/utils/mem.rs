use crate::prelude::*;

pub struct MemoryInfo {
    pub free_stack: usize,
    pub free_heap_internal: usize,
    pub free_heap_external: usize,
}

pub fn get_memory_info() -> MemoryInfo {
    unsafe {
        MemoryInfo {
            free_stack: sys::uxTaskGetStackHighWaterMark(core::ptr::null_mut()) as usize,
            free_heap_internal: sys::heap_caps_get_free_size(sys::MALLOC_CAP_INTERNAL),
            free_heap_external: sys::heap_caps_get_free_size(sys::MALLOC_CAP_SPIRAM),
        }
    }
}

#[allow(dead_code)]
pub fn print_stack_water_mark() {
    unsafe {
        let water_mark = sys::uxTaskGetStackHighWaterMark(core::ptr::null_mut());
        info!("Reporting free water mark: bytes={}", water_mark);
    }
}

// cannot slice into byte array at comptime, so this will have to do
pub const fn const_byte_slice(bytes: &[u8], from: usize, to: usize) -> &[u8] {
    bytes.split_at(from).1.split_at(to - from).0
}

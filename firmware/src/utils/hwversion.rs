use std::{fmt::Write, sync::OnceLock};

use esp_idf_svc::hal::{
    adc::{
        oneshot::{
            config::{AdcChannelConfig, Calibration},
            AdcChannelDriver, AdcDriver,
        },
        Resolution, ADC1,
    },
    gpio::Gpio36,
};

use crate::prelude::*;

static HARDWARE_VERSION: OnceLock<HardwareVersion> = OnceLock::new();

struct HardwareVersion {
    str: heapless::String<9>,
    num: u8,
}

pub fn get() -> u8 {
    HARDWARE_VERSION.get().map(|hwver| hwver.num).unwrap()
}

pub fn get_str() -> &'static str {
    HARDWARE_VERSION
        .get()
        .map(|hwver| hwver.str.as_str())
        .unwrap()
}

pub async fn init(adc: ADC1, pin: Gpio36) -> Result<()> {
    let mut hw_ver_channel = AdcChannelDriver::new(
        AdcDriver::new(adc)?,
        pin,
        &AdcChannelConfig {
            attenuation: sys::adc_atten_t_ADC_ATTEN_DB_11,
            resolution: Resolution::Resolution12Bit,
            calibration: Calibration::Line,
        },
    )
    .context(intern!("initialize adc driver"))?;

    Timer::after(Duration::from_millis(100)).await;

    let hw_ver_raw = hw_ver_channel.read_raw().unwrap();
    // let hw_ver = powered_adc1.read(&mut hw_ver_channel).unwrap();
    let hw_ver = match hw_ver_raw {
        1600..=2000 => 3,
        750..=900 => 4,
        650..=749 => 5,
        _ => 0,
    };

    info!("Detected: adc={}, version={})", hw_ver, hw_ver_raw);

    let mut hw_str = heapless::String::new();
    write!(hw_str, "linkr-{hw_ver}").unwrap();
    let hwver = HardwareVersion {
        str: hw_str,
        num: hw_ver,
    };
    if HARDWARE_VERSION.set(hwver).is_err() {
        defmt::panic!("hardware version already initialised");
    }

    Ok(())
}

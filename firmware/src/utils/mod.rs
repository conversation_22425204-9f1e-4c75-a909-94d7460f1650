use std::net::Ipv4Addr;

use crate::{
    flash::eventlog::events::{Event, ResetTrigger},
    prelude::*,
};

pub mod aes;
pub mod alloc;
pub mod base64;
pub mod cod;
pub mod error;
pub mod hwversion;
pub mod mac;
pub mod mem;
pub mod retry;
pub mod rsa;
pub mod sha1;
pub mod task;
pub mod version;

pub const fn mins(mins: u64) -> Duration {
    Duration::from_secs(mins * 60)
}

pub const fn secs(secs: u64) -> Duration {
    Duration::from_secs(secs)
}

pub const fn millis(millis: u64) -> Duration {
    Duration::from_millis(millis)
}

pub fn block_for(duration: Duration) {
    hal::delay::FreeRtos::delay_ms(duration.as_millis() as u32);
}

pub fn serial() -> &'static str {
    mac::eth()
}

pub fn is_prod_mode() -> bool {
    // see: https://github.com/espressif/esp-idf/blob/9be1c9f03ec890b76cfc875ba6adbd77aac8b73d/components/bootloader_support/include/esp_flash_encrypt.h
    extern "C" {
        fn esp_get_flash_encryption_mode() -> std::ffi::c_int;
    }
    const ESP_FLASH_ENC_MODE_RELEASE: std::ffi::c_int = 2;

    let res = unsafe { esp_get_flash_encryption_mode() };
    res == ESP_FLASH_ENC_MODE_RELEASE
}

pub fn set_log_level(target: &str, filter: log::LevelFilter) {
    svc::log::set_target_level(target, filter).unwrap();
}

pub async fn restart(trigger: ResetTrigger) -> ! {
    state().led.disable_all();
    block_for(Duration::from_millis(100));
    itc().eventlog.write(Event::ResetTrigger(trigger)).await;
    itc().eventlog.flush(&ReturnSlot::new()).await;
    info!("Restarting...");
    hal::reset::restart();
}

pub fn get_os_timestamp() -> u32 {
    unsafe { sys::esp_log_timestamp() }
}

pub fn calc_subnet_mask(prefix: u8) -> Ipv4Addr {
    let subnet_mask = !((1u32 << (32 - prefix)) - 1);
    Ipv4Addr::from(subnet_mask.to_le())
}

pub fn is_printable_ascii(s: &str) -> bool {
    for char in s.chars() {
        if !matches!(char, '\x20'..='\x7E') {
            return false;
        }
    }
    true
}

pub fn is_valid_wep_password(s: &str) -> bool {
    if !(s.len() == 10 || s.len() == 26) {
        return false;
    };
    for char in s.chars() {
        if !char.is_ascii_hexdigit() {
            return false;
        }
    }
    true
}

// cannot use Result::unwrap() at comptime, so this will have to do
const fn const_unwrap(result: Result<&str, std::str::Utf8Error>) -> &str {
    match result {
        Ok(x) => x,
        Err(_) => std::panic!("unwrap err"),
    }
}

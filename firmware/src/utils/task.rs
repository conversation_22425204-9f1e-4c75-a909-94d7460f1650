use std::ffi::{c_void, CString};

use esp_idf_svc::hal::cpu::Core;

use crate::prelude::*;

pub fn spawn(name: &str, stack: usize, f: impl FnOnce() + Send + 'static) {
    spawn_pinned(name, stack, 5, Some(Core::Core0), f)
}

pub fn spawn_pinned(
    name: &str,
    stack: usize,
    prio: u8,
    core: Option<Core>,
    f: impl FnOnce() + Send + 'static,
) {
    defmt::assert!(name.len() < 16);
    let name = CString::new(name).unwrap();
    let param: Box<Box<dyn FnOnce()>> = Box::new(Box::new(f));
    unsafe {
        hal::task::create(
            run,
            &name,
            stack,
            Box::into_raw(param) as *mut c_void,
            prio,
            core,
        )
        .unwrap();
    }
}

#[allow(dead_code)]
pub fn spawn_external(
    name: &str,
    stack: usize,
    prio: u8,
    core: Option<Core>,
    f: impl FnOnce() + Send + 'static,
) {
    defmt::assert!(name.len() < 16);
    let name = CString::new(name).unwrap();
    let param: Box<Box<dyn FnOnce()>> = Box::new(Box::new(f));
    let stack_depth = stack / std::mem::size_of::<sys::StackType_t>();
    let task_vars: *mut sys::StaticTask_t = unsafe {
        sys::heap_caps_malloc(
            std::mem::size_of::<sys::StaticTask_t>(),
            sys::MALLOC_CAP_8BIT,
        ) as *mut _
    };
    let stack: Vec<sys::StackType_t> = vec![0; stack_depth];
    unsafe {
        sys::xTaskCreateStaticPinnedToCore(
            Some(run),
            name.as_ptr(),
            stack_depth as u32,
            Box::into_raw(param) as *mut c_void,
            prio as u32,
            stack.leak().as_mut_ptr(),
            task_vars,
            core.map(Into::into)
                .unwrap_or(sys::CONFIG_FREERTOS_NO_AFFINITY as i32),
        );
    }
}

extern "C" fn run(param: *mut c_void) {
    let param: *mut Box<dyn FnOnce()> = param as _;
    let param: Box<Box<dyn FnOnce()>> = unsafe { Box::from_raw(param) };

    param();

    unsafe {
        hal::task::destroy(std::ptr::null_mut());
    }
}

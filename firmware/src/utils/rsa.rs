use std::ffi::{c_int, c_short, c_uchar, c_uint, c_ushort};

use crate::utils::cod::call_on_drop;

#[repr(C)]
#[derive(Default)]
#[allow(non_camel_case_types)]
struct mbedtls_rsa_context {
    _private_ver: c_int,
    _private_len: usize,
    _private_n: mbedtls_mpi,
    _private_e: mbedtls_mpi,
    _private_d: mbedtls_mpi,
    _private_p: mbedtls_mpi,
    _private_q: mbedtls_mpi,
    _private_dp: mbedtls_mpi,
    _private_dq: mbedtls_mpi,
    _private_qp: mbedtls_mpi,
    _private_rn: mbedtls_mpi,
    _private_rp: mbedtls_mpi,
    _private_rq: mbedtls_mpi,
    _private_vi: mbedtls_mpi,
    _private_vf: mbedtls_mpi,
    _private_padding: c_int,
    _private_hash_id: c_int,
}

#[repr(C)]
#[allow(non_camel_case_types)]
struct mbedtls_mpi {
    _private_p: *mut u32,
    _private_s: c_short,
    _private_n: c_ushort,
}

impl Default for mbedtls_mpi {
    fn default() -> Self {
        Self {
            _private_p: std::ptr::null_mut(),
            _private_s: 0,
            _private_n: 0,
        }
    }
}

#[repr(C)]
#[allow(non_camel_case_types)]
struct mbedtls_md_info_t {
    _unused: [u8; 0],
}

extern "C" {
    fn mbedtls_rsa_init(ctx: *mut mbedtls_rsa_context);
    fn mbedtls_rsa_free(ctx: *mut mbedtls_rsa_context);
    fn mbedtls_rsa_import_raw(
        ctx: *mut mbedtls_rsa_context,
        N: *const c_uchar,
        N_len: usize,
        P: *const c_uchar,
        P_len: usize,
        Q: *const c_uchar,
        Q_len: usize,
        D: *const c_uchar,
        D_len: usize,
        E: *const c_uchar,
        E_len: usize,
    ) -> c_int;
    fn mbedtls_rsa_complete(ctx: *mut mbedtls_rsa_context) -> c_int;
    fn mbedtls_rsa_get_len(ctx: *const mbedtls_rsa_context) -> usize;
    fn mbedtls_md_info_from_type(md_type: c_uint) -> *const mbedtls_md_info_t;
    fn mbedtls_md(
        md_info: *const mbedtls_md_info_t,
        input: *const c_uchar,
        ilen: usize,
        output: *mut c_uchar,
    ) -> c_int;
    fn mbedtls_rsa_pkcs1_verify(
        ctx: *mut mbedtls_rsa_context,
        md_alg: c_uint,
        hashlen: c_uint,
        hash: *const c_uchar,
        sig: *const c_uchar,
    ) -> c_int;
}

const MBEDTLS_MD_SHA256: c_uint = 9;

pub fn verify(
    data: &[u8],
    signature: &[u8],
    key_modulus: &[u8],
    key_exponent: &[u8],
) -> Result<bool, c_int> {
    fn check(code: c_int) -> Result<(), c_int> {
        match code {
            0 => Ok(()),
            e => Err(e),
        }
    }

    let mut rsa: mbedtls_rsa_context = Default::default();
    let mut hash = [0_u8; 32];

    unsafe {
        mbedtls_rsa_init(&mut rsa as *mut _);

        let _guard = call_on_drop(|| {
            mbedtls_rsa_free(&rsa as *const _ as *mut _); // please don't tell the borrow checker about this
        });

        // NOTE: i couldn't find a way to create an rsa context by simply parsing a .der file or
        //       something of that nature. thus, we need to pass modulus and exponent separately as
        //       raw bytes. there is an example from mbedtls where they read from only one file
        //       which is formatted as "N = <hex data>\nE = <hex data>", but i guess we can't
        //       really use files here, can we.
        check(mbedtls_rsa_import_raw(
            &rsa as *const _ as *mut _,
            key_modulus.as_ptr(),
            key_modulus.len(),
            std::ptr::null(),
            0,
            std::ptr::null(),
            0,
            std::ptr::null(),
            0,
            key_exponent.as_ptr(),
            key_exponent.len(),
        ))?;

        check(mbedtls_rsa_complete(&rsa as *const _ as *mut _))?;

        if signature.len() != mbedtls_rsa_get_len(&rsa as *const _) {
            return Err(-1);
        }

        let mdinfo = mbedtls_md_info_from_type(MBEDTLS_MD_SHA256);
        if mdinfo.is_null() {
            return Err(-1);
        }

        check(mbedtls_md(
            mdinfo,
            data.as_ptr(),
            data.len(),
            hash.as_mut_slice().as_mut_ptr(),
        ))?;

        match mbedtls_rsa_pkcs1_verify(
            &rsa as *const _ as *mut _,
            MBEDTLS_MD_SHA256,
            hash.len() as u32,
            hash.as_slice().as_ptr(),
            signature.as_ptr(),
        ) {
            0 => Ok(true),
            _ => Ok(false),
        }
    }
}

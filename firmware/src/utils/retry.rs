use std::{fmt::Debug, future::Future};

use defmt::Format;

use crate::prelude::*;

pub async fn retry<F, T, E>(
    mut f: impl FnMut() -> F,
    activity: &str,
    strategy: impl IntoIterator<Item = Duration>,
) -> Result<T, E>
where
    F: Future<Output = Result<T, E>>,
    E: Debug + Format,
{
    let sleeps = std::iter::once(secs(0)).chain(strategy.into_iter());

    let mut last_error = None;
    for sleep in sleeps {
        Timer::after(sleep).await;

        match f().await {
            Ok(x) => return Ok(x),
            Err(e) => {
                warn!("{} failed: {}", activity, e);
                last_error = Some(e);
            }
        }
    }

    Err(last_error.unwrap())
}

pub fn linear_delay(secs: u64) -> impl Iterator<Item = Duration> {
    std::iter::repeat(secs).map(Duration::from_secs)
}

pub fn exponential_backoff() -> impl Iterator<Item = Duration> {
    std::iter::successors(Some(1), |x| Some(x * 2))
        .map(Duration::from_secs)
        .take(9) // max: 4min 16sec
}

use std::{
    fmt::{Debug, Display},
    sync::atomic::AtomicU32,
};

use defmt::{Debug2Format, Format};
use esp_idf_svc::sys::EspError;

use crate::debug::defmt_compat::DefmtCompat;

#[macro_export]
macro_rules! anyhow {
    ($msg:literal $(,)?) => {
        {
            let error = $crate::utils::error::AnyError::new();
            defmt::error!("{} (tracking id #{})",
                $msg,
                error.id
            );

            error
        }
    };
    ($fmt:literal, $($arg:expr),*) => {
        {
            let error = $crate::utils::error::AnyError::new();
            //defmt::error!("{} (tracking id #{})", format_args!($fmt $(, $arg)*), error.id);
            //defmt::error!(concat!($fmt, " (tracking id #{})") $(, $arg)*, error.id);
            defmt::error!($fmt $(, $arg)*);
            defmt::error!("(^^^ tracking id #{})", error.id);
            error
        }
    };
}

#[macro_export]
macro_rules! bail {
    ($msg:literal $(,)?) => {{
        return Err(anyhow!($msg));
    }};
    ($fmt:literal, $($arg:expr),*) => {{
        return Err(anyhow!($fmt, $($arg),*));
    }};
}

#[macro_export]
macro_rules! ensure {
    ($e:expr) => {
        if !($e) {
            bail!("ensure failed: $e");
        }
    };
}

#[derive(Clone, Copy)]
#[non_exhaustive]
pub struct AnyError {
    pub id: u32,
}

impl AnyError {
    pub fn new() -> Self {
        static ID: AtomicU32 = AtomicU32::new(0);
        let id = ID.fetch_add(1, std::sync::atomic::Ordering::Relaxed);

        Self { id }
    }
}

impl From<EspError> for AnyError {
    fn from(e: EspError) -> Self {
        anyhow!("esp-idf error: {}", e.defmt())
    }
}

impl Debug for AnyError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "error #{}", self.id)
    }
}

impl Display for AnyError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "error #{}", self.id)
    }
}

impl Format for AnyError {
    fn format(&self, fmt: defmt::Formatter) {
        defmt::write!(fmt, "#{}", self.id);
    }
}

impl std::error::Error for AnyError {}

pub type Result<T, E = AnyError> = std::result::Result<T, E>;

pub trait ErrorContext<T> {
    fn context(self, s: defmt::Str) -> Result<T, AnyError>;
    // fn context2(self, s1: impl AsRef<str>, s2: impl AsRef<str>) -> Result<T, AnyError>;
    // fn context3(
    //     self,
    //     s1: impl AsRef<str>,
    //     s2: impl AsRef<str>,
    //     s3: impl AsRef<str>,
    // ) -> Result<T, AnyError>;
}

impl<T, E> ErrorContext<T> for std::result::Result<T, E>
where
    E: Debug + 'static,
{
    fn context(self, msg: defmt::Str) -> Result<T, AnyError> {
        self.map_err(|e| {
            let any_err = AnyError::new();
            defmt::error!(
                "Failed to {} caused by {} (tracking id #{})",
                msg,
                Debug2Format(&e),
                any_err.id
            );
            any_err
        })
    }

    // fn context2(self, s1: impl AsRef<str>, s2: impl AsRef<str>) -> Result<T, AnyError> {
    //     let s1 = s1.as_ref();
    //     let s2 = s2.as_ref();
    //     self.map_err(|e| anyhow!("failed to {} {} caused by {:?}", s1, s2, e))
    // }

    // fn context3(
    //     self,
    //     s1: impl AsRef<str>,
    //     s2: impl AsRef<str>,
    //     s3: impl AsRef<str>,
    // ) -> Result<T, AnyError> {
    //     let s1 = s1.as_ref();
    //     let s2 = s2.as_ref();
    //     let s3 = s3.as_ref();
    //     self.map_err(|e| anyhow!("failed to {} {} {} caused by {:?}", s1, s2, s3, e))
    // }
}

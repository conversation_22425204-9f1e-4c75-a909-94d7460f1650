use std::{
    alloc::{AllocError, Allocator, Layout},
    ffi::c_void,
    ptr::NonNull,
    slice,
};

use crate::prelude::*;

#[derive(Clone)]
pub struct InternalAllocator;

unsafe impl Sync for InternalAllocator {}

unsafe impl Allocator for InternalAllocator {
    fn allocate(&self, layout: Layout) -> Result<NonNull<[u8]>, AllocError> {
        let caps = sys::MALLOC_CAP_8BIT | sys::MALLOC_CAP_INTERNAL;
        let ptr = unsafe { sys::heap_caps_malloc(layout.size(), caps) };
        if ptr.is_null() {
            Err(AllocError)
        } else {
            let slice = unsafe { slice::from_raw_parts(ptr as *mut u8, layout.size()) };
            Ok(NonNull::from_ref(slice))
        }
    }

    unsafe fn deallocate(&self, ptr: NonNull<u8>, _layout: Layout) {
        unsafe { sys::heap_caps_free(ptr.as_ptr() as *mut c_void) };
    }

    unsafe fn grow(
        &self,
        ptr: NonNull<u8>,
        _old_layout: Layout,
        new_layout: Layout,
    ) -> Result<NonNull<[u8]>, AllocError> {
        let caps = sys::MALLOC_CAP_8BIT | sys::MALLOC_CAP_INTERNAL;
        let ptr = ptr.as_ptr() as *mut c_void;
        let ptr = unsafe { sys::heap_caps_realloc(ptr, new_layout.size(), caps) };
        if ptr.is_null() {
            Err(AllocError)
        } else {
            let slice = unsafe { slice::from_raw_parts(ptr as *mut _, new_layout.size()) };
            Ok(NonNull::from_ref(slice))
        }
    }

    unsafe fn shrink(
        &self,
        ptr: NonNull<u8>,
        old_layout: Layout,
        new_layout: Layout,
    ) -> Result<NonNull<[u8]>, AllocError> {
        self.grow(ptr, old_layout, new_layout)
    }
}

pub type InternalVec<T> = Vec<T, InternalAllocator>;

pub fn vec_zeroed<A: Allocator>(len: usize, allocator: A) -> Vec<u8, A> {
    unsafe {
        let layout = Layout::array::<u8>(len).unwrap();
        let mut ptr = allocator.allocate_zeroed(layout).unwrap();
        let ptr = ptr.as_mut().as_mut_ptr();
        Vec::from_raw_parts_in(ptr, len, len, allocator)
    }
}

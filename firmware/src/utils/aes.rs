use std::ffi::{c_int, c_uchar, c_uint};

#[repr(C)]
#[allow(non_camel_case_types)]
struct esp_aes_context {
    _key_bytes: u8,
    _key_in_hardware: u8,
    _key: [u8; 32],
}

extern "C" {
    fn esp_aes_init(ctx: *mut esp_aes_context);
    fn esp_aes_free(ctx: *mut esp_aes_context);
    fn esp_aes_setkey(ctx: *mut esp_aes_context, key: *const c_uchar, keybits: c_uint) -> c_int;
    fn esp_aes_crypt_cbc(
        ctx: *mut esp_aes_context,
        mode: c_int,
        length: usize,
        iv: *const c_uchar,
        input: *const c_uchar,
        output: *mut c_uchar,
    ) -> c_int;
}

pub fn encrypt128(mut input: Vec<u8>, key: &[u8; 16], iv: &[u8; 16]) -> Vec<u8> {
    let required = calculate_output_buffer_size(input.len());

    let pad_necessary = required - input.len();
    input.extend(std::iter::repeat_n(pad_necessary as u8, pad_necessary));
    let mut output = vec![0; required];

    let mut aes_ctx: esp_aes_context = unsafe { std::mem::zeroed() };

    // ensures that key and iv are on the stack, and not in some kind of const. because if they are in a const,
    // we get a nasty LoadStoreError for an unknown reason. mbedtls, ladies and gentlemen.
    let key: [u8; 16] = *key;
    let iv: [u8; 16] = *iv;

    unsafe {
        esp_aes_init(&mut aes_ctx);
        if esp_aes_setkey(&mut aes_ctx, key.as_ptr(), 128) != 0 {
            defmt::panic!("Key initialization error");
        }

        let ret = esp_aes_crypt_cbc(
            &mut aes_ctx,
            1, // MBEDTLS_AES_ENCRYPT
            input.len(),
            iv.as_ptr(),
            input.as_ptr(),
            output.as_mut_ptr(),
        );

        if ret != 0 {
            let ret = -ret;
            defmt::panic!("Encryption error 0x{:X}", ret);
        }

        esp_aes_free(&mut aes_ctx);
    }

    output
}

fn calculate_output_buffer_size(input_data_size: usize) -> usize {
    let block_size = 16;
    let required = (input_data_size as isize + block_size as isize) & -(block_size as isize);

    required as usize
}

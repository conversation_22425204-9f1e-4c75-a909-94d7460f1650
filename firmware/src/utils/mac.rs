use std::fmt::Write;

use embassy_sync::lazy_lock::LazyLock;

use crate::prelude::*;

/// Uses as serial number
pub fn eth() -> &'static str {
    static ETH_MAC: LazyLock<&'static str> = LazyLock::new(|| bytes_to_hex_leaked(eth_bytes()));

    ETH_MAC.get()
}

pub fn eth_bytes() -> [u8; 6] {
    read_mac(sys::esp_mac_type_t_ESP_MAC_ETH)
}

pub fn wifi() -> &'static str {
    static WIFI_MAC: LazyLock<&'static str> = LazyLock::new(|| bytes_to_hex_leaked(wifi_bytes()));

    WIFI_MAC.get()
}

pub fn wifi_bytes() -> [u8; 6] {
    read_mac(sys::esp_mac_type_t_ESP_MAC_WIFI_STA)
}

fn bytes_to_hex_leaked(mac: [u8; 6]) -> &'static str {
    let mut res = String::with_capacity(12);
    for i in &mac[0..6] {
        write!(res, "{:02x}", i).unwrap();
    }
    String::leak(res)
}

fn read_mac(ty: sys::esp_mac_type_t) -> [u8; 6] {
    let mut mac = [0u8; 6];
    unsafe {
        sys::esp_read_mac(&mut mac as *mut _, ty);
    }
    mac
}

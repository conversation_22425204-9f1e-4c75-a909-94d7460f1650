pub fn call_on_drop<F: FnOnce()>(f: F) -> CallOnDrop<F> {
    CallOnDrop { opt: Some(f) }
}

pub struct CallOnDrop<F: FnOnce()> {
    opt: Option<F>,
}

impl<F> CallOnDrop<F>
where
    F: FnOnce(),
{
    #[allow(dead_code)]
    pub fn cancel(mut self) {
        self.opt = None;
        std::mem::forget(self);
    }
}

impl<F> Drop for CallOnDrop<F>
where
    F: FnOnce(),
{
    fn drop(&mut self) {
        let f = self.opt.take().unwrap();
        f();
    }
}

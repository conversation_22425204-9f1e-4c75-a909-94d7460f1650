use std::ffi::{c_int, c_uchar};

use defmt::Format;

extern "C" {
    fn mbedtls_base64_encode(
        dst: *const c_uchar,
        dlen: usize,
        olen: *mut usize,
        src: *const c_uchar,
        slen: usize,
    ) -> c_int;

    fn mbedtls_base64_decode(
        dst: *const c_uchar,
        dlen: usize,
        olen: *mut usize,
        src: *const c_uchar,
        slen: usize,
    ) -> c_int;
}

const MBEDTLS_ERR_BASE64_BUFFER_TOO_SMALL: c_int = -0x002A;
const MBEDTLS_ERR_BASE64_INVALID_CHARACTER: c_int = -0x002C;

#[allow(dead_code)]
#[derive(Debug, Format)]
pub enum Error {
    BufferTooSmall { need: usize, have: usize },
    InvalidChar,
    Unknown,
}

pub fn encode<'a>(src: &[u8], out: &'a mut [u8]) -> Result<&'a [u8], Error> {
    let mut olen = 0;
    let res = unsafe {
        mbedtls_base64_encode(
            out.as_mut_ptr() as *mut _,
            out.len(),
            &mut olen as *mut _,
            src.as_ptr() as *const _,
            src.len(),
        )
    };
    match res {
        0 => Ok(&out[..olen]),
        MBEDTLS_ERR_BASE64_BUFFER_TOO_SMALL => Err(Error::BufferTooSmall {
            need: olen,
            have: out.len(),
        }),
        _ => Err(Error::Unknown),
    }
}

pub fn decode<'a>(src: &[u8], out: &'a mut [u8]) -> Result<&'a [u8], Error> {
    let mut olen = 0;
    let res = unsafe {
        mbedtls_base64_decode(
            out.as_mut_ptr() as *mut _,
            out.len(),
            &mut olen as *mut _,
            src.as_ptr() as *const _,
            src.len(),
        )
    };
    match res {
        0 => Ok(&out[..olen]),
        MBEDTLS_ERR_BASE64_BUFFER_TOO_SMALL => Err(Error::BufferTooSmall {
            need: olen,
            have: out.len(),
        }),
        MBEDTLS_ERR_BASE64_INVALID_CHARACTER => Err(Error::InvalidChar),
        _ => Err(Error::Unknown),
    }
}

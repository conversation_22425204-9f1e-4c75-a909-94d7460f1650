use crate::prelude::*;

pub struct SmartEnergyState {
    /// Is feature available? (only if heat pump is detected)
    pub available: Observable<bool>,
    // TODO: observable overkill for primitive types
    pub elevation: Observable<bool>,
    pub state: Observable<OhpcfState>,
}

impl SmartEnergyState {
    pub const fn new() -> Self {
        Self {
            available: Observable::new(),
            elevation: Observable::new(),
            state: Observable::new(),
        }
    }
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, Default, Eq, PartialEq)]
#[repr(u8)]
pub enum OhpcfState {
    #[default]
    Idle = 0,
    Alternative,
    WaitCapacity,
    Charging,
    ChargingGrace,
    Throttle,
}

impl OhpcfState {
    pub fn is_offering(self) -> bool {
        !matches!(self, Self::Idle | Self::Throttle)
    }

    pub fn is_running(self) -> bool {
        matches!(
            self,
            Self::Charging | Self::ChargingGrace | Self::WaitCapacity
        )
    }
}

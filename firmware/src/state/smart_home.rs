use std::sync::atomic::{AtomicU16, Ordering};

// use crate::prelude::*;

pub struct SmartHomeState {
    pub version: AtomicU16,
    pub ty: AtomicU16,
}

impl SmartHomeState {
    pub const fn new() -> Self {
        Self {
            version: AtomicU16::new(0),
            ty: AtomicU16::new(0),
        }
    }

    pub fn is_active(&self) -> bool {
        self.version.load(Ordering::Relaxed) != 0 && self.ty.load(Ordering::Relaxed) != 0
    }

    pub fn enable(&self, version: u16, ty: u16) {
        self.version.store(version, Ordering::Relaxed);
        self.ty.store(ty, Ordering::Relaxed);
    }

    pub fn disable(&self) {
        self.version.store(0, Ordering::Relaxed);
        self.ty.store(0, Ordering::Relaxed);
    }

    pub fn get_version(&self) -> u16 {
        self.version.load(Ordering::Relaxed)
    }

    pub fn get_type(&self) -> u16 {
        self.ty.load(Ordering::Relaxed)
    }
}

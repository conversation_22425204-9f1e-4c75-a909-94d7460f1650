use std::{
    any::type_name,
    sync::atomic::{AtomicBool, Ordering},
};

use ebus::EbusState;
use led::LedState;
use modbus::ModbusState;
use net::NetworkState;
use smart_energy::SmartEnergyState;
use smart_home::SmartHomeState;

use crate::{prelude::*, sync::Watch};

pub mod ebus;
pub mod led;
pub mod modbus;
pub mod net;
pub mod smart_energy;
pub mod smart_home;

pub fn state() -> &'static State {
    &STATE
}

static STATE: State = State::new();

pub struct State {
    pub ebus: EbusState,
    pub led: LedState,
    pub modbus: ModbusState,
    pub net: NetworkState,
    pub smart_energy: SmartEnergyState,
    pub smart_home: SmartHomeState,
    pub test_mode: AtomicBool,
}

impl State {
    const fn new() -> Self {
        Self {
            ebus: EbusState::new(),
            led: LedState::new(),
            modbus: ModbusState::new(),
            net: NetworkState::new(),
            smart_energy: SmartEnergyState::new(),
            smart_home: SmartHomeState::new(),
            test_mode: AtomicBool::new(false),
        }
    }

    pub fn test_mode(&self) -> bool {
        self.test_mode.load(Ordering::SeqCst)
    }
}

pub struct Observable<T: Clone, const N: usize = 1> {
    watch: Watch<T, N>,
}

impl<T: Clone, const N: usize> Observable<T, N> {
    pub const fn new() -> Self {
        Self {
            watch: Watch::new(),
        }
    }

    pub fn update(&self, value: T) {
        self.watch.sender().send(value);
    }

    pub fn update_if_changed(&self, value: T)
    where
        T: PartialEq,
    {
        if self.watch.try_get().map(|v| v != value).unwrap_or(true) {
            self.update(value);
        }
    }

    pub fn get(&self) -> T
    where
        T: Default,
    {
        self.watch.try_get().unwrap_or_default()
    }

    pub fn try_get(&self) -> Option<T> {
        self.watch.try_get()
    }

    pub fn subscribe(&'static self) -> ObservableSubscriber<T, N> {
        self.watch
            .receiver()
            .unwrap_or_else(|| defmt::panic!("not enough receivers for {}", type_name::<T>()))
    }
}

pub type ObservableSubscriber<T, const N: usize = 1> =
    embassy_sync::watch::Receiver<'static, hal::task::embassy_sync::EspRawMutex, T, N>;

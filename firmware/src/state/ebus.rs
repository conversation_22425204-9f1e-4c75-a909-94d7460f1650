use crate::{
    prelude::*,
    smartset::msg::EbusDevices,
    time::{ClockTime, StoredClockTime},
};

pub struct EbusState {
    pub devices: Observable<EbusDevices, 3>,
    pub last_syn: Observable<Instant>,
    pub time: Observable<StoredClockTime>,
}

impl EbusState {
    pub const fn new() -> Self {
        Self {
            devices: Observable::new(),
            last_syn: Observable::new(),
            time: Observable::new(),
        }
    }

    pub fn current_time(&self) -> Option<ClockTime> {
        self.time.try_get().and_then(|t| t.current())
    }

    pub fn time_stamp(&self) -> Option<String> {
        self.current_time().as_ref().map(ToString::to_string)
    }
}

use std::sync::atomic::{AtomicU16, Ordering};

use crate::prelude::*;

pub struct LedState {
    led_bits: AtomicU16,
}

impl LedState {
    pub const fn new() -> Self {
        Self {
            led_bits: AtomicU16::new(0),
        }
    }

    pub fn enable(&self, state: LedBits) {
        self.led_bits.fetch_or(state.bits(), Ordering::Relaxed);
    }

    pub fn disable(&self, state: LedBits) {
        self.led_bits.fetch_and(!state.bits(), Ordering::Relaxed);
    }

    pub fn disable_all(&self) {
        self.led_bits.store(0, Ordering::Relaxed);
    }

    pub fn snapshot(&self) -> LedBits {
        LedBits::from_bits_truncate(self.led_bits.load(Ordering::Relaxed))
    }
}

use std::{
    net::Ipv4Addr,
    sync::atomic::{AtomicBool, Ordering},
};

use embassy_time::WithTimeout;
use esp_idf_svc::{ipv4::IpInfo, wifi::AccessPointInfo};

use crate::{net::task::NetworkRequest, prelude::*};

pub struct NetworkState {
    pub state: Observable<NetworkStateValue>,
    pub ap: Observable<Option<ApState>>,
    pub scanned_access_points: Mutex<Vec<AccessPointInfo>>,
    pub temp_ip: AtomicBool,
}

impl NetworkState {
    pub const fn new() -> Self {
        Self {
            state: Observable::new(),
            ap: Observable::new(),
            scanned_access_points: Mutex::new(Vec::new()),
            temp_ip: AtomicBool::new(false),
        }
    }

    pub async fn set_scanned_access_points(&self, aps: Vec<AccessPointInfo>) {
        *self.scanned_access_points.lock().await = aps;
    }

    pub async fn request_ap_state_update(&'static self) -> Option<ApState> {
        let mut sub = self.ap.subscribe();
        itc().net.send(NetworkRequest::RefreshApInfo).await;
        match sub.changed().with_timeout(secs(1)).await {
            Ok(value) => value,
            Err(_) => self.ap.get(),
        }
    }

    pub fn temp_ip(&self) -> bool {
        self.temp_ip.load(Ordering::SeqCst)
    }
}

#[derive(Clone, Debug, Default)]
pub enum NetworkStateValue {
    #[default]
    NotConnected,
    Wifi(IpInfo, RssiLevel),
    Eth(IpInfo),
}

impl NetworkStateValue {
    pub fn ip_info(&self) -> Option<&IpInfo> {
        match self {
            Self::NotConnected => None,
            Self::Wifi(ip, _) | Self::Eth(ip) => Some(ip),
        }
    }

    pub fn rssi(&self) -> Option<RssiLevel> {
        match self {
            Self::Wifi(_, RssiLevel(rssi)) => Some(RssiLevel(*rssi)),
            _ => None,
        }
    }

    pub fn is_connected(&self) -> bool {
        matches!(self, Self::Wifi(_, _) | Self::Eth(_))
    }

    pub fn is_wifi_connected(&self) -> bool {
        matches!(self, Self::Wifi(_, _))
    }

    #[allow(dead_code)]
    pub fn is_eth_connected(&self) -> bool {
        matches!(self, Self::Eth(_))
    }
}

#[derive(Clone)]
pub struct ApState {
    pub ip_info: IpInfo,
    pub connected_clients: ClientList,
    pub ssid: heapless::String<32>,
}

pub type ClientList = heapless::Vec<Ipv4Addr, { sys::ESP_WIFI_MAX_CONN_NUM as usize }>;

/// accepts 0..4
#[derive(Clone, Copy, Debug)]
pub struct RssiLevel(pub u8);

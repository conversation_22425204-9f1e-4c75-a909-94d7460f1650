use std::sync::Arc;

use crate::{
    ebus::api::{EbusReply, EbusRequest},
    flash::{
        eventlog::{
            events::Event,
            task::{Entry, EventLogRange, EventLogRequest, EventLogResponse},
        },
        nvs::task::{NvsRequest, NvsResponse},
        ota::OtaMessage,
    },
    modbus::api::{ModbusReply, ModbusRequest},
    net::task::NetworkRequest,
    prelude::*,
};

pub fn itc() -> &'static Itc {
    &ITC
}

static ITC: Itc = Itc::new();

pub struct Itc {
    pub net: OneWay<NetworkRequest>, // application --> network task
    pub ebus: TwoWay<EbusRequest, EbusReply>,
    pub modbus: TwoWay<ModbusRequest, ModbusReply>,
    pub nvs: TwoWay<NvsRequest, NvsResponse>,
    pub ota: TwoWay<OtaMessage, Result<()>>,
    pub stdin: OneWay<Vec<u8>>, // stdin task --> application
    pub eventlog: TwoWay<EventLogRequest, EventLogResponse>,
}

impl Itc {
    pub const fn new() -> Self {
        Self {
            net: OneWay::new(),
            ebus: TwoWay::new(),
            modbus: TwoWay::new(),
            nvs: TwoWay::new(),
            ota: TwoWay::new(),
            stdin: OneWay::new(),
            eventlog: TwoWay::new(),
        }
    }
}

/// A one-way channel for sending messages to a single consumer task.
pub struct OneWay<A> {
    channel: Channel<A, 8>,
}

impl<A> OneWay<A> {
    pub const fn new() -> Self {
        Self {
            channel: Channel::new(),
        }
    }

    pub async fn send(&self, value: A) {
        self.channel.send(value).await;
    }

    pub async fn recv(&self) -> A {
        self.channel.receive().await
    }
}

/// A two-way channel for sending messages to a single consumer task
/// and receiving a reply.
pub struct TwoWay<A, R: 'static> {
    channel: Channel<(A, ReturnSlot<R>), 8>,
}

impl<A, R> TwoWay<A, R> {
    pub const fn new() -> Self {
        Self {
            channel: Channel::new(),
        }
    }

    pub async fn process(&self, value: A, slot: &ReturnSlot<R>) -> R {
        slot.get().reset();
        // TODO add timeout
        self.channel.send((value, slot.clone())).await;
        slot.wait().await
    }

    pub async fn recv(&self) -> (A, ReturnSlot<R>) {
        self.channel.receive().await
    }

    pub fn try_recv(&self) -> Option<(A, ReturnSlot<R>)> {
        self.channel.try_receive().ok()
    }
}

impl TwoWay<EventLogRequest, EventLogResponse> {
    pub async fn write(&self, event: Event) {
        static_slot!(IGNORED, EventLogResponse);
        self.channel
            .send((EventLogRequest::Write(event), IGNORED.clone()))
            .await;
    }

    pub async fn flush(&self, slot: &ReturnSlot<EventLogResponse>) {
        match self.process(EventLogRequest::Flush, slot).await {
            EventLogResponse::FlushAck => (),
            _ => defmt::panic!("event log out of sync"),
        }
    }

    pub async fn get(
        &self,
        range: EventLogRange,
        slot: &ReturnSlot<EventLogResponse>,
    ) -> Result<Vec<Entry>> {
        match self.process(EventLogRequest::Get(range), slot).await {
            EventLogResponse::Get(ret) => ret,
            _ => defmt::panic!("event log out of sync"),
        }
    }

    pub async fn get_page_entry_count(
        &self,
        page: u8,
        slot: &ReturnSlot<EventLogResponse>,
    ) -> Result<usize> {
        let value = EventLogRequest::GetPageEntryCount(page);
        match self.process(value, slot).await {
            EventLogResponse::GetPageEntryCount(ret) => ret,
            _ => defmt::panic!("event log out of sync"),
        }
    }
}

pub struct ReturnSlot<T: 'static> {
    inner: ReturnSlotInner<T>,
}

impl<T: 'static> Clone for ReturnSlot<T> {
    fn clone(&self) -> Self {
        Self {
            inner: self.inner.clone(),
        }
    }
}

impl<T: 'static> ReturnSlot<T> {
    pub fn new() -> Self {
        Self {
            inner: ReturnSlotInner::Dynamic(Arc::new(Signal::new())),
        }
    }

    #[doc(hidden)]
    pub const fn new_static(signal: &'static Signal<T>) -> Self {
        Self {
            inner: ReturnSlotInner::Static(signal),
        }
    }

    fn get(&self) -> &Signal<T> {
        match &self.inner {
            ReturnSlotInner::Dynamic(signal) => signal,
            ReturnSlotInner::Static(signal) => signal,
        }
    }

    pub async fn wait(&self) -> T {
        self.get().wait().await
    }

    pub fn ret(self, value: T) {
        self.get().signal(value)
    }
}

#[macro_export]
macro_rules! static_slot {
    ($id:ident, $ty:ty) => {
        static $id: ReturnSlot<$ty> = {
            static SIGNAL: Signal<$ty> = Signal::new();
            ReturnSlot::new_static(&SIGNAL)
        };
    };
}

enum ReturnSlotInner<T: 'static> {
    Dynamic(Arc<Signal<T>>),
    Static(&'static Signal<T>),
}

impl<T: 'static> Clone for ReturnSlotInner<T> {
    fn clone(&self) -> Self {
        match self {
            Self::Dynamic(signal) => Self::Dynamic(signal.clone()),
            Self::Static(signal) => Self::Static(signal),
        }
    }
}

type Raw = esp_idf_svc::hal::task::embassy_sync::EspRawMutex;

pub type Mutex<T> = embassy_sync::mutex::Mutex<Raw, T>;
pub type MutexGuard<'a, T> = embassy_sync::mutex::MutexGuard<'a, Raw, T>;
pub type Channel<T, const N: usize> = embassy_sync::channel::Channel<Raw, T, N>;
pub type Signal<T> = embassy_sync::signal::Signal<Raw, T>;
pub type Watch<T, const N: usize = 4> = embassy_sync::watch::Watch<Raw, T, N>; // let's hope 4 receivers are enough

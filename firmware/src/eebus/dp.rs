use std::{
    marker::PhantomData,
    sync::atomic::{AtomicU16, Ordering},
};

use crate::prelude::*;

pub trait CanRead {}
pub trait CanWrite {}

pub struct R;
impl CanRead for R {}
pub struct W;
impl CanWrite for W {}

#[allow(dead_code)]
pub struct RW;
impl CanRead for RW {}
impl CanWrite for RW {}

pub struct BusProxy<const ID: u16, T, A> {
    value: AtomicU16,
    _marker: PhantomData<fn(A) -> T>,
}

impl<const ID: u16, T, A> BusProxy<ID, T, A> {
    pub const fn new() -> Self {
        BusProxy {
            value: AtomicU16::new(0),
            _marker: PhantomData,
        }
    }
}

impl<const ID: u16, T, A> Default for BusProxy<ID, T, A> {
    fn default() -> Self {
        BusProxy {
            value: Default::default(),
            _marker: Default::default(),
        }
    }
}

impl<const ID: u16, T, A> BusProxy<ID, T, A>
where
    A: CanRead,
    T: From<u16>,
{
    // pub fn get(&self) -> T {
    //     self.value.load(Ordering::Relaxed).into()
    // }

    pub async fn read(&self) -> Result<T> {
        // TODO: actually use cache
        let value = read_single(ID).await?;
        self.value.store(value, Ordering::Relaxed);

        Ok(value.into())
    }

    pub async fn wait_until(&self, mut condition: impl FnMut(&T) -> bool, interval: Duration) {
        let mut timer = Ticker::every(interval);
        loop {
            let Ok(value) = self.read().await else {
                timer.next().await;
                continue;
            };
            if condition(&value) {
                break;
            }
            timer.next().await;
        }
    }
}

impl<const ID: u16, T, A> BusProxy<ID, T, A>
where
    A: CanWrite,
    T: Into<u16>,
{
    pub async fn write(&self, value: T) -> Result<()> {
        let value = value.into();
        write_single(ID, value).await?;
        self.value.store(value, Ordering::Relaxed);

        Ok(())
    }
}

async fn read_single(info: u16) -> Result<u16> {
    let slot = ReturnSlot::new();
    ebus()
        .read_info_num(0x5022, &[info], 0x8, &slot, secs(20))
        .await
        .map(|ns| ns[0].as_value())
}

async fn write_single(info: u16, value: u16) -> Result<()> {
    let slot = ReturnSlot::new();
    ebus()
        .write_info_nums(0x5023, &[info, value], 0x8, &slot)
        .await
        .map(|_| ())
}

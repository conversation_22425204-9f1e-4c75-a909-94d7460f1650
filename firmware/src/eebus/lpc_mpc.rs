use eebus::spine::{
    error::SpineError,
    loadctl::{ActiveLimit, LoadControl, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>},
    measurement::{Measurement, MeasurementHandler, MeasurementType, Measurements},
    types::*,
};

use super::dp::{BusProxy, R};
use crate::prelude::*;

const AC_POWER_TOTAL_MEASUREMENT_ID: u32 = 1;
const AC_POWER_PHASE_MEASUREMENT_ID: u32 = 2;
const POWER_LIMIT_DEVICE_CONFIG_KEY_ID: u32 = 1;
const MIN_DURATION_DEVICE_CONFIG_KEY_ID: u32 = 2;
const ELECTRICAL_CONNECTION_ID: u32 = 1;
const AC_POWER_TOTAL_ELECTRICAL_PARAMETER_ID: u32 = 1;
const AC_POWER_PHASE_ELECTRICAL_PARAMETER_ID: u32 = 2;
const LOAD_CONTROL_LIMIT_ID: u32 = 1;

pub struct LpcMpc {
    measurements: Measurements,
    measurement_handler: MeasurementHandlerImpl,
    load_control: LoadControl,
}

impl LpcMpc {
    pub fn new() -> Self {
        // these two measurements must be present in this exact configuration for MPC use case
        const MEASUREMENTS: &[Measurement] = &[
            Measurement::new(
                AC_POWER_TOTAL_MEASUREMENT_ID,
                MeasurementType::Power,
                Commodity::Electricity,
                Scope::AcPowerTotal,
                UnitOfMeasurement::Watt,
                None,
            ),
            Measurement::new(
                AC_POWER_PHASE_MEASUREMENT_ID,
                MeasurementType::Power,
                Commodity::Electricity,
                Scope::AcPower,
                UnitOfMeasurement::Watt,
                None,
            ),
        ];

        Self {
            measurements: Measurements::new(MEASUREMENTS),
            measurement_handler: MeasurementHandlerImpl {
                power_consumption: BusProxy::default(),
            },
            load_control: LoadControl::lpc_default(
                LOAD_CONTROL_LIMIT_ID,
                AC_POWER_TOTAL_MEASUREMENT_ID,
            ),
        }
    }

    pub async fn measurements(
        &mut self,
        partial_request: Option<&[MeasurementListDataSelectors]>,
    ) -> MeasurementListData {
        let (resp, res) = self
            .measurements
            .list_data_response(&self.measurement_handler, partial_request)
            .await;

        if let Err(e) = res {
            error!("Failed to perform measurement: {}", e);
        }

        resp
    }

    pub fn measurement_description(&self) -> MeasurementDescriptionListData {
        self.measurements.description_list_data_response()
    }

    pub fn measurement_constraints(&self) -> MeasurementConstraintsListData {
        self.measurements.constraints_list_data_response()
    }

    pub fn device_configuration_key_value_description(
        &self,
    ) -> DeviceConfigurationKeyValueDescriptionListData {
        DeviceConfigurationKeyValueDescriptionListData {
            device_configuration_key_value_description_data: vec![
                // LPC use case specifies two device configuration key value descriptions:
                DeviceConfigurationKeyValueDescriptionData {
                    // the key identifier
                    // "shall be set as primary identifier"
                    key_id: Some(POWER_LIMIT_DEVICE_CONFIG_KEY_ID),
                    // must be "failsafeConsumptionActivePowerLimit" for LPC
                    key_name: Some(DeviceConfigurationKeyName::FailsafeConsumptionActivePowerLimit),
                    // must be "scaledNumber" for LPC
                    value_type: Some(DeviceConfigurationKeyValue::ScaledNumber),
                    // must be "W" for LPC
                    unit: Some(UnitOfMeasurement::Watt),
                    label: None,
                    description: None,
                },
                DeviceConfigurationKeyValueDescriptionData {
                    key_id: Some(MIN_DURATION_DEVICE_CONFIG_KEY_ID),
                    // must be "failsafeDurationMinimum" for LPC
                    key_name: Some(DeviceConfigurationKeyName::FailsafeDurationMinimum),
                    // must be "duration" for LPC
                    value_type: Some(DeviceConfigurationKeyValue::Duration),
                    // TODO: unsure about this. this field is present in the xsd, but is not mentioned in the LPC use case.
                    unit: None,
                    label: None,
                    description: None,
                },
            ],
        }
    }

    pub fn device_configuration_key_value(&self) -> DeviceConfigurationKeyValueListData {
        DeviceConfigurationKeyValueListData {
            device_configuration_key_value_data: vec![
                // similar to DeviceConfigurationKeyValueDescriptionListData, we have two things here according to LPC.
                DeviceConfigurationKeyValueData {
                    // the key identifier
                    // "shall be set as primary identifier"
                    key_id: Some(POWER_LIMIT_DEVICE_CONFIG_KEY_ID),
                    // the actual value.
                    // LPC-010, LPC-021 (failsafe power limit always greater than zero)
                    value: Some(DeviceConfigurationKeyValueValue {
                        boolean: None,
                        date: None,
                        date_time: None,
                        duration: None,
                        string: None,
                        time: None,
                        // the *actual* actual value.
                        // has to be scaledNumber because DeviceConfigurationKeyValueDescriptionData::valueType is
                        // scaledNumber. DeviceConfigurationKeyValueValue should really be a choice, but it isn't...
                        // LPC-010, LPC-021 (power limit always greater than zero).
                        // scaled number rules (`effective_value = number.unwrap() * 10.pow(scale.unwrap())`)
                        //   (but not regular scaled number rules, since scale is mandatory for whatever reason...)
                        //   (╯°□°）╯︵ ┻━┻
                        scaled_number: Some(ScaledNumber {
                            number: Some(42), // mandatory
                            scale: Some(0), // MANDATORY (this is unlike regular scaled numbers!!!!!)
                        }),
                        integer: None,
                    }),
                    // must be true for LPC use case.
                    // if set to true, the server must accept changes of the element value by client.
                    is_value_changeable: Some(true),
                },
                DeviceConfigurationKeyValueData {
                    key_id: Some(MIN_DURATION_DEVICE_CONFIG_KEY_ID),
                    // LPC-022 (failsafe duration minimum)
                    value: Some(DeviceConfigurationKeyValueValue {
                        boolean: None,
                        date: None,
                        date_time: None,
                        // minimum value: 2 hours (inclusive, probably)
                        // maximum value: 24 hours (inclusive, probably)
                        // LPC-022/1 (2..24 hour range for failsafe duration minimum, shall be preconfigured by the
                        //            controllable system vendor)
                        // LPC-022/3 (maximum for failsafe duration minimum must be between failsafe duration minimum and 24
                        //            hours, energy guard shall choose value between 2 and 24 hours)
                        duration: Some(XsDuration::hms(2, 0, 0)),
                        string: None,
                        time: None,
                        scaled_number: None,
                        integer: None,
                    }),
                    // must be true for LPC use case.
                    // if set to true, the server must accept changes of the element value by client according to:
                    //  - LPC-022/2 (energy guard may change failsafe duration minimum)
                    //  - LPC-022/4 (failsafe duration minimum may be rejected if larger than maximum for failsafe duration
                    //               min value).
                    is_value_changeable: Some(true),
                },
            ],
        }
    }

    pub fn electrical_connection_description(&self) -> ElectricalConnectionDescriptionListData {
        ElectricalConnectionDescriptionListData {
            electrical_connection_description_data: vec![ElectricalConnectionDescriptionData {
                // the electrical connection this static information belongs to
                // "shall be set as primary identifier"
                electrical_connection_id: Some(ELECTRICAL_CONNECTION_ID),
                // must be "ac" for MPC use case
                power_supply_type: Some(ElectricalConnectionVoltage::Ac),
                // typically 1, 2 or 3
                // TODO: unsure about this. this field is present in the xsd, but it is not mentioned in the MPC use case.
                ac_connected_phases: None,
                // this relates to ElectricalConnectionParameterDescriptionData::ac_measurement_variant when it is set to
                // the value "rms". it states the duration the rms value is measured in.
                // TODO: unsure about this. this field is present in the xsd, but it is not mentioned in the MPC use case.
                ac_rms_period_duration: Some(XsDuration::hms(0, 0, 1)),
                // must be "consume" for MPC use case
                positive_energy_direction: Some(EnergyDirection::Consume),
                // TODO: unsure about this. this field is present in the xsd, but it is not mentioned in the MPC use case.
                scope_type: None,
                label: None,
                description: None,
            }],
        }
    }

    pub fn electrical_connection_parameter_description(
        &self,
    ) -> ElectricalConnectionParameterDescriptionListData {
        ElectricalConnectionParameterDescriptionListData {
            electrical_connection_parameter_description_data: vec![
                // MPC use case specifies between two and four ElectricalConnectionParameterDescriptionData elements.
                // one is fixed: the electrical connection parameter for the "acPowerTotal" measurement
                // the other three are for "acPower" measurements on the three different phases of the ac connection
                // however, if we only have one phase (or pretend that we only have one phase), it should be fine to just
                // have one of the phase-specific "acPower" thingies.
                ElectricalConnectionParameterDescriptionData {
                    // the electrical connection this static information belongs to
                    // "shall be set as primary identifier"
                    electrical_connection_id: Some(ELECTRICAL_CONNECTION_ID),
                    // one electrical connection can have multiple parameter combinations, which can be measured.
                    // this is used for distinguishing between different parameters within one electrical connection.
                    // "shall be set as sub identifier" (primary: electricalConnectionId, sub: parameterId)
                    parameter_id: Some(AC_POWER_TOTAL_ELECTRICAL_PARAMETER_ID),
                    // reference to the measurement which contains the actual measured values for the parameter combination
                    // "shall be set as foreign identifier" (must be same as id given in measurement responses)
                    measurement_id: Some(AC_POWER_TOTAL_MEASUREMENT_ID),
                    // must be "ac" for MPC use case
                    voltage_type: Some(ElectricalConnectionVoltage::Ac),
                    // let's pretend we only have one phase because it makes stuff easier
                    ac_measured_phases: Some(ElectricalConnectionPhaseName::A), // optional
                    // must be omitted or "neutral" for MPC use case
                    ac_measured_in_reference_to: None,
                    // must be "real" for MPC use case
                    ac_measurement_type: Some(ElectricalConnectionAcMeasurement::Real),
                    // must be omitted or "rms" for MPC use case
                    ac_measurement_variant: None,
                    // TODO: unsure about this. this field is present in the xsd, but is not mentioned in the MPC use case.
                    ac_measured_harmonic: None,
                    // TODO: unsure about this. this field is present in the xsd, but is not mentioned in the MPC use case.
                    scope_type: None,
                    label: None,
                    description: None,
                },
                ElectricalConnectionParameterDescriptionData {
                    electrical_connection_id: Some(ELECTRICAL_CONNECTION_ID),
                    parameter_id: Some(AC_POWER_PHASE_ELECTRICAL_PARAMETER_ID),
                    measurement_id: Some(AC_POWER_PHASE_MEASUREMENT_ID),
                    // must be "ac" for MPC use case
                    voltage_type: Some(ElectricalConnectionVoltage::Ac),
                    // must be "a", "b" or "c" for MPC use case
                    ac_measured_phases: Some(ElectricalConnectionPhaseName::A),
                    // must be "neutral" for MPC use case
                    ac_measured_in_reference_to: Some(ElectricalConnectionPhaseName::Neutral),
                    // must be "real" for MPC use case
                    ac_measurement_type: Some(ElectricalConnectionAcMeasurement::Real),
                    // must be "rms" for MPC use case
                    ac_measurement_variant: Some(ElectricalConnectionMeasurandVariant::Rms),
                    // TODO: unsure about this. this field is present in the xsd, but is not mentioned in the MPC use case.
                    ac_measured_harmonic: None,
                    // TODO: unsure about this. this field is present in the xsd, but is not mentioned in the MPC use case.
                    scope_type: None,
                    label: None,
                    description: None,
                },
            ],
        }
    }

    pub fn load_control_limit_description(&self) -> LoadControlLimitDescriptionListData {
        self.load_control.read_descriptions()
    }

    pub fn load_control_limit(&self) -> LoadControlLimitListData {
        self.load_control.read_data()
    }

    pub async fn load_control_update(
        &mut self,
        request: &LoadControlLimitListData,
    ) -> Result<(), SpineError> {
        self.load_control.update(request, &LpcHandlerImpl).await
    }
}

struct MeasurementHandlerImpl {
    power_consumption: BusProxy<12389, u16, R>,
}

impl MeasurementHandler for MeasurementHandlerImpl {
    type Error = ();

    async fn measure(&self, measurement_id: u32) -> Result<ScaledNumber, Self::Error> {
        let power = self.power_consumption.read().await.map_err(|_| ())?;

        // We send the same power measurement bc we only have one phase
        match measurement_id {
            AC_POWER_TOTAL_MEASUREMENT_ID => Ok(ScaledNumber {
                number: Some(power as i64),
                scale: Some(-1),
            }),
            AC_POWER_PHASE_MEASUREMENT_ID => Ok(ScaledNumber {
                number: Some(power as i64),
                scale: Some(-1),
            }),
            _ => Err(()),
        }
    }
}

struct LpcHandlerImpl;

impl LpcHandler for LpcHandlerImpl {
    type Error = ();

    async fn write_limit(&self, limit: &Option<ActiveLimit>) -> Result<(), Self::Error> {
        //match limit {
        //    Some(ActiveLimit { end_time, value }) => write_limit(),
        //    None => deactivate_limit(),
        //}
        _ = limit;
        Err(())
    }
}

use std::ffi::c_uchar;

use eebus::ship::Ski;
use esp_idf_svc::sys;

use crate::{prelude::*, utils::cod::call_on_drop};

#[allow(clippy::not_unsafe_ptr_arg_deref)] // clippy is too stupid to recognise any form of null checks on cert_ptr
pub fn get_ski_from_cert(
    cert_ptr: *const sys::mbedtls_x509_crt,
) -> Result<[std::ffi::c_uchar; 20]> {
    if cert_ptr.is_null() {
        bail!("cert null ptr")
    }

    unsafe {
        let mut extbuf = (*cert_ptr).v3_ext;
        let mut first_ext = sys::mbedtls_asn1_sequence::default();
        // get extension list
        let ret = sys::mbedtls_asn1_get_sequence_of(
            &mut extbuf.p,
            extbuf.p.add(extbuf.len),
            &mut first_ext,
            (sys::MBEDTLS_ASN1_CONSTRUCTED | sys::MBEDTLS_ASN1_SEQUENCE) as i32,
        );
        // extension list might be allocated even if get_sequence fails, so defer the free here already.
        let _seq_free = call_on_drop(|| {
            sys::mbedtls_asn1_sequence_free(first_ext.next);
        });
        if ret != 0 {
            bail!("failed to get cert extensions");
        }
        // iterate through entries in the sequence
        // uses `as *const _ as *mut _` to make the borrow checker shut up lmao
        let mut next: *mut sys::mbedtls_asn1_sequence = &first_ext as *const _ as *mut _;
        while !next.is_null() {
            let mut tag_len = 0;
            // get object identifier (oid) entry of extension
            let ret = sys::mbedtls_asn1_get_tag(
                &mut ((*next).buf.p),
                (*next).buf.p.add((*next).buf.len),
                &mut tag_len,
                sys::MBEDTLS_ASN1_OID as i32,
            );
            if ret != 0 {
                bail!("failed to get extension oid");
            }
            let oid = std::slice::from_raw_parts((*next).buf.p, tag_len);
            const SKI_OID: &[u8] = &[0x55, 0x1d, 0x0e];
            if oid == SKI_OID {
                let mut p = (*next).buf.p.add(tag_len);
                // idk why whe need to get the oid octet string two times here, i just copied this from somewhere
                let ret = sys::mbedtls_asn1_get_tag(
                    &mut p,
                    p.add((*next).buf.len - 2 - tag_len),
                    &mut tag_len,
                    sys::MBEDTLS_ASN1_OCTET_STRING as i32,
                );
                if ret != 0 {
                    bail!("failed to get ski string");
                }
                let ret = sys::mbedtls_asn1_get_tag(
                    &mut p,
                    p.add((*next).buf.len - 2),
                    &mut tag_len,
                    sys::MBEDTLS_ASN1_OCTET_STRING as i32,
                );
                if ret != 0 {
                    bail!("failed to get ski string");
                }
                if tag_len != 20 {
                    bail!("invalid ski string");
                }
                let mut ret = [0; 20];
                std::ptr::copy(p, ret.as_mut_ptr(), 20);
                return Ok(ret);
            }
            next = (*next).next;
        }
        bail!("no ski extension");
    }
}

/// Get SKI from X.509 certificate in DER format
fn get_ski_from_x509_der(cert_der: &[c_uchar]) -> Result<[std::ffi::c_uchar; 20]> {
    unsafe {
        let mut cert = sys::mbedtls_x509_crt::default();
        sys::mbedtls_x509_crt_init(&mut cert);
        let ret = sys::mbedtls_x509_crt_parse_der(&mut cert, cert_der.as_ptr(), cert_der.len());
        if ret < 0 {
            bail!("Failed to parse certificate: {ret:X}");
        };
        let ski = get_ski_from_cert(&cert);

        sys::mbedtls_x509_crt_free(&mut cert);

        ski
    }
}

pub async fn get_own_ski() -> Ski {
    let cert_full_der = nvs::eebus_cert::get().await;
    Ski::from(get_ski_from_x509_der(&cert_full_der).unwrap())
}

use std::{
    collections::{BTreeMap, BTreeSet},
    net::IpAddr,
};

use defmt::Debug2Format;
use eebus::ship::{DeviceInfo, Ski, Trust, TrustLevel};

use crate::prelude::*;

#[derive(Debug, <PERSON><PERSON>, Co<PERSON>)]
pub struct Device {
    pub ski: Ski,
    pub level: TrustLevel,
    pub ip: IpAddr,
}

impl PartialEq for Device {
    fn eq(&self, other: &Self) -> bool {
        self.ski == other.ski
    }
}
impl Eq for Device {}

impl PartialOrd for Device {
    fn partial_cmp(&self, other: &Self) -> Option<std::cmp::Ordering> {
        Some(self.cmp(other))
    }
}
impl Ord for Device {
    fn cmp(&self, other: &Self) -> std::cmp::Ordering {
        self.ski.cmp(&other.ski)
    }
}

pub struct TrustDb {
    ski_to_trust: Observable<BTreeMap<Ski, Device>>,
    interest: Observable<BTreeSet<Device>>,
}

impl TrustDb {
    /// Create trust db and interest receiver
    pub const fn new() -> Self {
        Self {
            ski_to_trust: Observable::new(),
            interest: Observable::new(),
        }
    }

    pub async fn register_trust_interest(&self, ski: Ski, ip: IpAddr) {
        let mut interest = self.interest.get();
        if interest.insert(Device {
            ski,
            level: TrustLevel::Pending,
            ip,
        }) {
            self.interest.update(interest);
        }
    }

    // pub async fn wait_update(&self) {
    //     self.changed.listen().await
    // }

    pub async fn set_trust(&self, ski: Ski, level: TrustLevel) {
        let ip = self
            .interest
            .get()
            .iter()
            .find(|entry| entry.ski == ski)
            .map(|entry| entry.ip)
            .unwrap_or(IpAddr::V4(std::net::Ipv4Addr::UNSPECIFIED));
        let mut trust = self.ski_to_trust.get();
        trust.insert(ski, Device { ski, level, ip });
        self.ski_to_trust.update(trust);
    }

    /// Get all devices in the trust database
    pub async fn get_all_devices(&self) -> Vec<Device> {
        let trust_db: Vec<_> = self.ski_to_trust.get().values().cloned().collect();

        self.interest
            .get()
            .iter()
            .cloned()
            .chain(trust_db)
            .collect()
    }
}

impl Trust for TrustDb {
    async fn wait_trust_update(&self, _info: &DeviceInfo, from: TrustLevel) -> TrustLevel {
        /*let current = self.trust_level().await;
        if current == from {
            self.1.changed.listen().await;
        }
        self.trust_level().await*/

        if from != TrustLevel::Trust {
            Timer::after(secs(10)).await;
            TrustLevel::Trust
        } else {
            std::future::pending().await
        }
    }

    async fn trust_level(&self, info: &DeviceInfo) -> TrustLevel {
        let level = self
            .ski_to_trust
            .get()
            .get(&info.ski)
            .map(|entry| entry.level)
            .unwrap_or(TrustLevel::Pending);

        info!(
            "Got trust level: ski={}, trust={}",
            info.ski.bytes,
            Debug2Format(&level)
        );

        if level == TrustLevel::Pending {
            self.register_trust_interest(info.ski, info.ip).await;
        }

        level
        //TrustLevel::Trust
    }
}

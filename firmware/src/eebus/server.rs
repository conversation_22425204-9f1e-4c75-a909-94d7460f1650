use std::sync::Arc;

use defmt::Debug2Format;
use eebus::{
    ship::{tungstenite::Connection, Ship},
    spine::{
        devdiag::{HeartbeatEvent, HeartbeatHandler},
        error::SpineError,
        nodemgmt::NodeManagement,
        types::*,
        <PERSON><PERSON>, <PERSON><PERSON>,
    },
};
use embassy_futures::select::{select3, Either3};
use esp_idf_svc::hal::task::embassy_sync::EspRawMutex;

use super::{
    lpc_mpc::LpcMpc,
    nodemgmt::{node_management, COMPRESSOR_ID},
    ohpcf::SmartEnergyManagementImpl,
    TrustDb,
};
use crate::{eebus::nodemgmt::DEVICE_DIAGNOSIS_FEATURE_ID, prelude::*};

struct Server {
    node_management: Mutex<NodeManagement>,
    smart_energy: SmartEnergyManagementImpl,
    lpc_mpc: LpcMpc,
    heartbeat: <PERSON><PERSON><PERSON><PERSON><PERSON>,
    spine: Arc<Spine<Connection<EspRawMutex>, TrustDb>>,
}

impl Handler for Server {
    async fn handle_detailed_discovery_read(
        &mut self,
        elements: &NodeManagementDetailedDiscoveryDataElements,
    ) -> Result<NodeManagementDetailedDiscoveryData, SpineError> {
        let mut res = self
            .node_management
            .lock()
            .await
            .discovery_details_response();

        elements.filter_elements(&mut res);

        Ok(res)
    }

    async fn handle_detailed_discovery_read_partial(
        &mut self,
        _elements: &NodeManagementDetailedDiscoveryDataElements,
        _one_of: &[NodeManagementDetailedDiscoveryDataSelectors],
    ) -> Result<NodeManagementDetailedDiscoveryData, SpineError> {
        Err(SpineError::Unsupported)
    }

    async fn handle_use_case_read(
        &mut self,
        elements: &NodeManagementUseCaseDataElements,
    ) -> Result<NodeManagementUseCaseData, SpineError> {
        let mut res = self.node_management.lock().await.use_case_response();
        elements.filter_elements(&mut res);
        Ok(res)
    }

    async fn handle_use_case_read_partial(
        &mut self,
        _elements: &NodeManagementUseCaseDataElements,
        _one_of: &[NodeManagementUseCaseDataSelectors],
    ) -> Result<NodeManagementUseCaseData, SpineError> {
        Err(SpineError::Unsupported)
    }

    async fn handle_smart_energy_management_read(
        &mut self,
        elements: &SmartEnergyManagementPsDataElements,
    ) -> Result<SmartEnergyManagementPsData, SpineError> {
        if !state().smart_energy.available.get() {
            return Err(SpineError::Unsupported);
        }
        let mut response = self
            .smart_energy
            .read()
            .await
            .map_err(|e| SpineError::Custom(e.to_string()))?;
        elements.filter_elements(&mut response);
        Ok(response)
    }

    async fn handle_smart_energy_management_read_partial(
        &mut self,
        _elements: &SmartEnergyManagementPsDataElements,
        _one_of: &[SmartEnergyManagementPsDataSelectors],
    ) -> Result<SmartEnergyManagementPsData, SpineError> {
        Err(SpineError::Unsupported)
    }

    async fn handle_smart_energy_management_upsert(
        &mut self,
        _request: &SmartEnergyManagementPsData,
    ) -> Result<(), SpineError> {
        Err(SpineError::Unsupported)
    }

    async fn handle_smart_energy_management_upsert_partial(
        &mut self,
        request: &SmartEnergyManagementPsData,
        one_of: &[SmartEnergyManagementPsDataSelectors],
    ) -> Result<(), SpineError> {
        if !state().smart_energy.available.get() {
            return Err(SpineError::Unsupported);
        }
        self.smart_energy.upsert(request, one_of).await
    }

    async fn handle_binding_request_call(
        &mut self,
        request: &NodeManagementBindingRequestCall,
    ) -> Result<(), SpineError> {
        self.node_management
            .lock()
            .await
            .device
            .add_binding(request)
    }

    async fn handle_subscription_request_call(
        &mut self,
        request: &NodeManagementSubscriptionRequestCall,
    ) -> Result<(), SpineError> {
        self.node_management
            .lock()
            .await
            .device
            .add_subscription(request)
    }

    async fn handle_device_config_description_read(
        &mut self,
        elements: &DeviceConfigurationKeyValueDescriptionDataElements,
    ) -> Result<DeviceConfigurationKeyValueDescriptionListData, SpineError> {
        if !state().smart_energy.available.get() {
            return Err(SpineError::Unsupported);
        }
        let mut resp = self.lpc_mpc.device_configuration_key_value_description();
        for entry in &mut resp.device_configuration_key_value_description_data {
            elements.filter_elements(entry);
        }
        Ok(resp)
    }

    async fn handle_device_config_description_read_partial(
        &mut self,
        _elements: &DeviceConfigurationKeyValueDescriptionDataElements,
        _one_of: &[DeviceConfigurationKeyValueDescriptionListDataSelectors],
    ) -> Result<DeviceConfigurationKeyValueDescriptionListData, SpineError> {
        Err(SpineError::Unsupported)
    }

    async fn handle_device_config_read(
        &mut self,
        elements: &DeviceConfigurationKeyValueDataElements,
    ) -> Result<DeviceConfigurationKeyValueListData, SpineError> {
        if !state().smart_energy.available.get() {
            return Err(SpineError::Unsupported);
        }
        let mut resp = self.lpc_mpc.device_configuration_key_value();
        for entry in &mut resp.device_configuration_key_value_data {
            elements.filter_elements(entry);
        }
        Ok(resp)
    }

    async fn handle_device_config_read_partial(
        &mut self,
        _elements: &DeviceConfigurationKeyValueDataElements,
        _one_of: &[DeviceConfigurationKeyValueListDataSelectors],
    ) -> Result<DeviceConfigurationKeyValueListData, SpineError> {
        Err(SpineError::Unsupported)
    }

    async fn handle_device_config_upsert(
        &mut self,
        _request: &DeviceConfigurationKeyValueListData,
    ) -> Result<(), SpineError> {
        defmt::todo!();
    }

    async fn handle_device_config_upsert_partial(
        &mut self,
        _request: &DeviceConfigurationKeyValueListData,
        _one_of: &[DeviceConfigurationKeyValueListDataSelectors],
    ) -> Result<(), SpineError> {
        defmt::todo!();
    }

    async fn handle_electrical_connection_description_read(
        &mut self,
        elements: &ElectricalConnectionDescriptionDataElements,
    ) -> Result<ElectricalConnectionDescriptionListData, SpineError> {
        if !state().smart_energy.available.get() {
            return Err(SpineError::Unsupported);
        }
        let mut resp = self.lpc_mpc.electrical_connection_description();
        for entry in &mut resp.electrical_connection_description_data {
            elements.filter_elements(entry);
        }
        Ok(resp)
    }

    async fn handle_electrical_connection_description_read_partial(
        &mut self,
        _elements: &ElectricalConnectionDescriptionDataElements,
        _one_of: &[ElectricalConnectionDescriptionListDataSelectors],
    ) -> Result<ElectricalConnectionDescriptionListData, SpineError> {
        Err(SpineError::Unsupported)
    }

    async fn handle_electrical_connection_parameter_description_read(
        &mut self,
        elements: &ElectricalConnectionParameterDescriptionDataElements,
    ) -> Result<ElectricalConnectionParameterDescriptionListData, SpineError> {
        if !state().smart_energy.available.get() {
            return Err(SpineError::Unsupported);
        }
        let mut resp = self.lpc_mpc.electrical_connection_parameter_description();
        for entry in &mut resp.electrical_connection_parameter_description_data {
            elements.filter_elements(entry);
        }
        Ok(resp)
    }

    async fn handle_electrical_connection_parameter_description_read_partial(
        &mut self,
        _elements: &ElectricalConnectionParameterDescriptionDataElements,
        _one_of: &[ElectricalConnectionParameterDescriptionListDataSelectors],
    ) -> Result<ElectricalConnectionParameterDescriptionListData, SpineError> {
        Err(SpineError::Unsupported)
    }

    async fn handle_load_control_limit_description_read(
        &mut self,
        elements: &LoadControlLimitDescriptionDataElements,
    ) -> Result<LoadControlLimitDescriptionListData, SpineError> {
        if !state().smart_energy.available.get() {
            return Err(SpineError::Unsupported);
        }
        let mut resp = self.lpc_mpc.load_control_limit_description();
        for entry in &mut resp.load_control_limit_description_data {
            elements.filter_elements(entry);
        }
        Ok(resp)
    }

    async fn handle_load_control_limit_read_partial(
        &mut self,
        _elements: &LoadControlLimitDataElements,
        _one_of: &[LoadControlLimitListDataSelectors],
    ) -> Result<LoadControlLimitListData, SpineError> {
        Err(SpineError::Unsupported)
    }

    async fn handle_load_control_limit_read(
        &mut self,
        elements: &LoadControlLimitDataElements,
    ) -> Result<LoadControlLimitListData, SpineError> {
        if !state().smart_energy.available.get() {
            return Err(SpineError::Unsupported);
        }
        let mut resp = self.lpc_mpc.load_control_limit();
        for entry in &mut resp.load_control_limit_data {
            elements.filter_elements(entry);
        }
        Ok(resp)
    }

    async fn handle_load_control_limit_description_read_partial(
        &mut self,
        _elements: &LoadControlLimitDescriptionDataElements,
        _one_of: &[LoadControlLimitDescriptionListDataSelectors],
    ) -> Result<LoadControlLimitDescriptionListData, SpineError> {
        Err(SpineError::Unsupported)
    }

    async fn handle_load_control_limit_upsert(
        &mut self,
        request: &LoadControlLimitListData,
    ) -> Result<(), SpineError> {
        if !state().smart_energy.available.get() {
            return Err(SpineError::Unsupported);
        }
        self.lpc_mpc.load_control_update(request).await
    }

    async fn handle_load_control_limit_upsert_partial(
        &mut self,
        _request: &LoadControlLimitListData,
        _one_of: &[LoadControlLimitListDataSelectors],
    ) -> Result<(), SpineError> {
        defmt::todo!();
    }

    async fn handle_measurement_read(
        &mut self,
        elements: &MeasurementDataElements,
    ) -> Result<MeasurementListData, SpineError> {
        if !state().smart_energy.available.get() {
            return Err(SpineError::Unsupported);
        }
        let mut resp = self.lpc_mpc.measurements(None).await;
        for entry in &mut resp.measurement_data {
            elements.filter_elements(entry);
        }
        Ok(resp)
    }

    async fn handle_measurement_read_partial(
        &mut self,
        elements: &MeasurementDataElements,
        one_of: &[MeasurementListDataSelectors],
    ) -> Result<MeasurementListData, SpineError> {
        if !state().smart_energy.available.get() {
            return Err(SpineError::Unsupported);
        }
        let mut resp = self.lpc_mpc.measurements(Some(one_of)).await;
        for entry in &mut resp.measurement_data {
            elements.filter_elements(entry);
        }
        Ok(resp)
    }

    async fn handle_measurement_constraints_read(
        &mut self,
        elements: &MeasurementConstraintsDataElements,
    ) -> Result<MeasurementConstraintsListData, SpineError> {
        if !state().smart_energy.available.get() {
            return Err(SpineError::Unsupported);
        }
        let mut resp = self.lpc_mpc.measurement_constraints();
        for entry in &mut resp.measurement_constraints_data {
            elements.filter_elements(entry);
        }
        Ok(resp)
    }

    async fn handle_measurement_constraints_read_partial(
        &mut self,
        _elements: &MeasurementConstraintsDataElements,
        _one_of: &[MeasurementConstraintsListDataSelectors],
    ) -> Result<MeasurementConstraintsListData, SpineError> {
        Err(SpineError::Unsupported)
    }

    async fn handle_measurement_description_read(
        &mut self,
        elements: &MeasurementDescriptionDataElements,
    ) -> Result<MeasurementDescriptionListData, SpineError> {
        if !state().smart_energy.available.get() {
            return Err(SpineError::Unsupported);
        }
        let mut resp = self.lpc_mpc.measurement_description();
        for entry in &mut resp.measurement_description_data {
            elements.filter_elements(entry);
        }
        Ok(resp)
    }

    async fn handle_measurement_description_read_partial(
        &mut self,
        _elements: &MeasurementDescriptionDataElements,
        _one_of: &[MeasurementDescriptionListDataSelectors],
    ) -> Result<MeasurementDescriptionListData, SpineError> {
        Err(SpineError::Unsupported)
    }

    async fn handle_heartbeat_read(
        &mut self,
        elements: &DeviceDiagnosisHeartbeatDataElements,
        new_request_destination: FeatureAddress,
    ) -> Result<DeviceDiagnosisHeartbeatData, SpineError> {
        let spine = Arc::clone(&self.spine);
        let heartbeat_read = self.heartbeat.get_data_read();
        env::spawn(async move {
            Timer::after(Duration::from_millis(500)).await;
            let send_fut = spine.send_request(
                FeatureAddress {
                    device: None,
                    entity: Vec::from(COMPRESSOR_ID),
                    feature: Some(DEVICE_DIAGNOSIS_FEATURE_ID),
                },
                new_request_destination,
                CmdClassifier::Read,
                Some(true),
                Box::new([Cmd {
                    function: None,
                    filter: Vec::new(),
                    last_update_at: None,
                    manufacturer_specific_extension: None,
                    data: Some(Data::DeviceDiagnosisHeartbeatData(heartbeat_read)),
                }]),
            );
            match with_timeout(secs(5), send_fut).await {
                Ok(Err(e)) => {
                    error!(
                        "Failed to send deviceDiagnosisHeartbeat read request: err={}",
                        Debug2Format(&e)
                    );
                }
                Err(_) => {
                    error!("Timed out while sending deviceDiagnosisHeartbeat read request");
                }
                _ => {}
            }
        });

        let mut res = self.heartbeat.read_data_response();
        elements.filter_elements(&mut res);
        Ok(res)
    }

    async fn handle_heartbeat_reply(
        &mut self,
        reply: &DeviceDiagnosisHeartbeatData,
    ) -> Result<(), SpineError> {
        self.heartbeat.process_reply(reply);
        Ok(())
    }

    async fn handle_heartbeat_notify(
        &mut self,
        notification: &DeviceDiagnosisHeartbeatData,
    ) -> Result<(), SpineError> {
        if self.heartbeat.process_notify(notification).is_err() {
            warn!("Received mismatching deviceDiagnosisHeartbeat counter");
        }
        Ok(())
    }
}

pub async fn run(ship: Ship<Connection<EspRawMutex>, TrustDb>) -> Result<()> {
    let spine = Spine::new(ship);
    let spine = Arc::new(spine);

    let mut server = Server {
        node_management: Mutex::new(node_management()),
        smart_energy: SmartEnergyManagementImpl::new(), // TODO: share between connections
        lpc_mpc: LpcMpc::new(),
        heartbeat: HeartbeatHandler::new(),
        spine: Arc::clone(&spine),
    };
    let mut smart_energy_state_subscription = state().smart_energy.state.subscribe();
    let mut notification = async || {
        // TODO: add other features as needed (use select)
        smart_energy_state_subscription.changed().await;
        Feature::SmartEnergyManagementPs
    };

    loop {
        match select3(
            spine.recv_request(),
            notification(),
            server.heartbeat.wait_notify_event(),
        )
        .await
        {
            Either3::First(request) => {
                let request = request.context(intern!("receive incoming request"))?;
                info!("Received request: req={}", Debug2Format(&request));
                Box::pin(server.handle(request))
                    .await
                    .context(intern!("handle spine request"))?;
            }
            Either3::Second(notify) => {
                info!("Sending notification for: notify={}", Debug2Format(&notify));
                for rel in server
                    .node_management
                    .lock()
                    .await
                    .device
                    .subscriptions_for_feature(notify)
                {
                    spine
                        .notify(rel)
                        .await
                        .context(intern!("notify subscribers"))?;
                }
            }
            Either3::Third(heartbeat_event) => {
                match heartbeat_event {
                    HeartbeatEvent::NotifyRequired => (), // TODO: notify(server.heartbeat.get_notify())
                    HeartbeatEvent::RemoteTimedOut => {
                        bail!("deviceDiagnosisHeartbeatData of remote timed out")
                    }
                }
            }
        }
    }
}

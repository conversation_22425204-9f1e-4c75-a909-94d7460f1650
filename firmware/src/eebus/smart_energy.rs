use defmt::Debug2Format;
use embassy_futures::select::{select, Either};

use crate::{prelude::*, state::smart_energy::OhpcfState};

pub async fn task() -> ! {
    Timer::after(secs(1)).await;

    let mut sub = state().ebus.devices.subscribe();
    let mut devices = sub.get().await;

    loop {
        if let Some(d) = devices.bus_devices.iter().find(|d| d.bus_address.0 == 0x8) {
            info!("Found heat pump device: device={}", Debug2Format(&d));
            state().smart_energy.available.update(true);
            match select(state_machine(), sub.changed_and(|d| !d.has_address(0x8))).await {
                Either::First(_) => defmt::unreachable!(),
                Either::Second(_d) => info!("heat pump device removed: {}", Debug2Format(&d)),
            }
        }

        info!("Stopping state machine until heat pump device is found");
        state().smart_energy.state.update(OhpcfState::Idle);
        state().smart_energy.available.update(false);
        devices = sub.changed_and(|d| d.has_address(0x8)).await;
    }
}

async fn state_machine() -> ! {
    use OhpcfState::*;

    let mut elevation = state().smart_energy.elevation.subscribe();
    let mut st = state().smart_energy.state.get();
    let ohpcf = &env::interfaces().ohpcf;

    loop {
        st = 'state: loop {
            match st {
                Idle => {
                    info!("Waiting for compressor to be ready");
                    ohpcf.wait_ready().await;
                    info!("Received compressor ready state");

                    // eebus subscribers will receive state change
                    break 'state Alternative;
                }
                Alternative => {
                    match select(elevation.changed_and(|s| *s), ohpcf.wait_not_ready()).await {
                        Either::First(_) => {
                            info!("Received elevation on");
                            if ohpcf.set_hp_elevated(true).await.is_ok() {
                                break 'state WaitCapacity;
                            }
                        }
                        Either::Second(_) => {
                            info!("Received compressor no longer ready state");
                            break 'state Idle;
                        }
                    }
                }
                WaitCapacity => match with_timeout(
                    mins(7),
                    select(elevation.changed_and(|s| !*s), ohpcf.wait_capacity_avail()),
                )
                .await
                {
                    Ok(Either::First(_)) => {
                        info!("Received elevation off while waiting for capacity");
                        break 'state Alternative;
                    }
                    Ok(Either::Second(_)) => {
                        info!("Received capacity available state");
                        break 'state Charging;
                    }
                    Err(_) => {
                        warn!("Timed out waiting for capacity");
                        let _ = ohpcf.set_hp_elevated(false).await;
                        break 'state Throttle;
                    }
                },
                Charging => {
                    match select(ohpcf.wait_capacity_full(), ohpcf.wait_not_ready()).await {
                        Either::First(_) => {
                            info!("Received capacity full state");
                            break 'state WaitCapacity;
                        }
                        Either::Second(_) => {
                            info!("Received compressor no longer ready state");
                            let _ = ohpcf.set_hp_elevated(false).await;
                            break 'state ChargingGrace;
                        }
                    }
                }
                ChargingGrace => match with_timeout(mins(10), ohpcf.wait_ready()).await {
                    Ok(_) => {
                        info!("Received compressor ready state");
                        break 'state Charging;
                    }
                    Err(_) => {
                        warn!("Timed out waiting for compressor to be ready");
                        let _ = ohpcf.set_hp_elevated(false).await;
                        break 'state Idle;
                    }
                },
                Throttle => {
                    info!("Throttling for 30 minutes");
                    Timer::after(mins(30)).await;
                    break 'state Idle;
                }
            }
        };
        info!("Changed state: {}", Debug2Format(&st));
        state().smart_energy.state.update(st);
    }
}

use eebus::spine::{
    nodemgmt::{
        EEBusDevice, EEBusEntity, EEBusFeature, EEBusFunction, EEBusOperations, EEBusUseCase,
        NodeManagement,
    },
    types::{Device, Entity, Feature, Function, Role},
};

use super::own_device_id;

//const DEVICE_NAME: &str = "d:_i:46922_TheMostAmazingWolfHeatPump";
pub const NODE_MANAGEMENT_ID: &[u32] = &[0];
pub const HEAT_PUMP_APPLIANCE_ID: &[u32] = &[1];
pub const COMPRESSOR_ID: &[u32] = &[1, 3];
pub const NODE_MANAGEMENT_FEATURE_ID: u32 = 0;
pub const SMART_ENERGY_MANAGEMENT_PS_FEATURE_ID: u32 = 1;
pub const LOAD_CONTROL_FEATURE_ID: u32 = 2;
pub const DEVICE_CONFIGURATION_FEATURE_ID: u32 = 3;
pub const DEVICE_DIAGNOSIS_FEATURE_ID: u32 = 4;
pub const ELECTRICAL_CONNECTION_FEATURE_ID: u32 = 5;
pub const MEASUREMENT_FEATURE_ID: u32 = 6;

pub fn node_management() -> NodeManagement {
    let device_information = device_information_entity();
    let (heat_pump_appliance, compressor) = heat_pump_entity();

    let mut device = EEBusDevice::new(own_device_id(), Device::HeatGenerationSystem);
    device.add_entity(device_information).unwrap();
    device.add_entity(heat_pump_appliance).unwrap();
    device.add_entity(compressor).unwrap();

    NodeManagement::new(device)
}

#[rustfmt::skip]
fn device_information_entity() -> EEBusEntity {
    const FUNCTIONS: &[EEBusFunction; 4] = &[
        EEBusFunction::new(Function::NodeManagementDetailedDiscoveryData, EEBusOperations::READ),
        EEBusFunction::new(Function::NodeManagementUseCaseData, EEBusOperations::READ),
        EEBusFunction::new(Function::NodeManagementSubscriptionRequestCall, EEBusOperations::empty()), // TODO: what does call count as? read? write? none?
        EEBusFunction::new(Function::NodeManagementBindingRequestCall, EEBusOperations::empty()), // TODO: what does call count as? read? write? none?
    ];

    const FEATURES: &[EEBusFeature] = &[
        EEBusFeature::new(NODE_MANAGEMENT_FEATURE_ID, Feature::NodeManagement, Role::Special, FUNCTIONS),
    ];

    EEBusEntity::new(NODE_MANAGEMENT_ID, Entity::DeviceInformation, FEATURES, &[])
}

#[rustfmt::skip]
fn heat_pump_entity() -> (EEBusEntity, EEBusEntity) {
    const SMART_ENERGY_MANAGEMENT_PS_FUNCTIONS: &[EEBusFunction] = &[
        EEBusFunction::new(Function::SmartEnergyManagementPsData, EEBusOperations::READ.union(EEBusOperations::WRITE_PARTIAL)),
    ];

    const LOAD_CONTROL_FUNCTIONS: &[EEBusFunction] = &[
        EEBusFunction::new(Function::LoadControlLimitDescriptionListData, EEBusOperations::READ),
        EEBusFunction::new(Function::LoadControlLimitListData, EEBusOperations::READ.union(EEBusOperations::WRITE_PARTIAL)),
    ];

    const DEVICE_CONFIGURATION_FUNCTIONS: &[EEBusFunction] = &[
        EEBusFunction::new(Function::DeviceConfigurationKeyValueDescriptionListData, EEBusOperations::READ),
        EEBusFunction::new(Function::DeviceConfigurationKeyValueListData, EEBusOperations::READ.union(EEBusOperations::WRITE_PARTIAL)),
    ];

    const DEVICE_DIAGNOSIS_FUNCTIONS: &[EEBusFunction] = &[
        EEBusFunction::new(Function::DeviceDiagnosisHeartbeatData, EEBusOperations::READ)
    ];

    const ELECTRICAL_CONNECTION_FUNCTIONS: &[EEBusFunction] = &[
        EEBusFunction::new(Function::ElectricalConnectionDescriptionListData, EEBusOperations::READ),
        EEBusFunction::new(Function::ElectricalConnectionParameterDescriptionListData, EEBusOperations::READ),
    ];

    const MEASUREMENT_FUNCTIONS: &[EEBusFunction] = &[
        EEBusFunction::new(Function::MeasurementDescriptionListData, EEBusOperations::READ),
        EEBusFunction::new(Function::MeasurementConstraintsListData, EEBusOperations::READ),
        EEBusFunction::new(Function::MeasurementListData, EEBusOperations::READ_PARTIAL),
    ];

    const FEATURES: &[EEBusFeature] = &[
        EEBusFeature::new(SMART_ENERGY_MANAGEMENT_PS_FEATURE_ID, Feature::SmartEnergyManagementPs, Role::Server, SMART_ENERGY_MANAGEMENT_PS_FUNCTIONS),
        EEBusFeature::new(LOAD_CONTROL_FEATURE_ID, Feature::LoadControl, Role::Server, LOAD_CONTROL_FUNCTIONS),
        EEBusFeature::new(DEVICE_CONFIGURATION_FEATURE_ID, Feature::DeviceConfiguration, Role::Server, DEVICE_CONFIGURATION_FUNCTIONS),
        EEBusFeature::new(DEVICE_DIAGNOSIS_FEATURE_ID, Feature::DeviceDiagnosis, Role::Server, DEVICE_DIAGNOSIS_FUNCTIONS),
        EEBusFeature::new(ELECTRICAL_CONNECTION_FEATURE_ID, Feature::ElectricalConnection, Role::Server, ELECTRICAL_CONNECTION_FUNCTIONS),
        EEBusFeature::new(MEASUREMENT_FEATURE_ID, Feature::Measurement, Role::Server, MEASUREMENT_FUNCTIONS),
    ];

    const USE_CASES: &[EEBusUseCase] = &[
        EEBusUseCase::OHPCF_SERVER,
        EEBusUseCase::LPC_SERVER,
        EEBusUseCase::MPC_SERVER,
    ];

    (
        EEBusEntity::new(HEAT_PUMP_APPLIANCE_ID, Entity::HeatPumpAppliance, &[], &[]),
        EEBusEntity::new(COMPRESSOR_ID, Entity::Compressor, FEATURES, USE_CASES),
    )
}

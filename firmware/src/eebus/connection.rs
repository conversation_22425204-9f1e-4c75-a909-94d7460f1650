use std::net::IpAddr;

use async_executor::LocalExecutor;
use eebus::ship::{tungstenite::Connection, Ski};
use esp_idf_svc::hal::task::embassy_sync::EspRawMutex;
use futures_lite::{AsyncRead, AsyncWrite};

use crate::prelude::*;

pub async fn spawn_connection<'a, S>(
    stream: S,
    ip: IpAddr,
    port: u16,
    ski: Ski,
    executor: &LocalExecutor<'a>,
) -> Result<Connection<EspRawMutex>>
where
    S: AsyncRead + AsyncWrite + Unpin + 'a,
{
    let (con, runner) = Connection::new(stream, ip, port, ski, |payload| {
        let mut sha = vec![0; 20];
        if let Err(e) = crate::utils::sha1::hash(payload.as_bytes(), &mut sha) {
            error!("Failed to hash with sha1: err={}", e);
        }
        let mut out = vec![0; 29];
        if let Err(e) = crate::utils::base64::encode(&sha[0..20], &mut out) {
            error!("Failed to encode with base64: err={}", e);
        }
        while out.last().copied() == Some(0) {
            out.pop();
        }

        String::from_utf8(out).unwrap_or_default()
    })
    .await
    .context(intern!("perform websocket handshake"))?;
    executor.spawn(runner).detach();

    Ok(con)
}

use std::{
    cell::Ref<PERSON>ell,
    ffi::c_uchar,
    net::{Ip<PERSON>dd<PERSON>, TcpListener},
    rc::Rc,
};

use async_io_mini::Async;
use connection::spawn_connection;
use defmt::Debug2Format;
use eebus::ship::{Ship, Ski};
use ski::get_own_ski;

use crate::{
    net::{
        async_tcp::{AsyncReadWriter, AsyncTcpStream},
        tls::{EspAsyncTls, PollableSocket, ServerConfig, X509},
    },
    prelude::*,
};

pub mod certgen;
mod connection;
pub mod dp;
mod lpc_mpc;
mod nodemgmt;
pub mod ohpcf;
mod server;
pub mod ski;
mod smart_energy;
mod trust;

pub use self::trust::TrustDb;

pub async fn eebus_state_machines_task() -> Result<()> {
    smart_energy::task().await
}

pub async fn eebus_websocket_task() -> Result<()> {
    let listener = TcpListener::bind("0.0.0.0:4711").context(intern!("bind to tcp address"))?;
    let listener = Async::new(listener).context(intern!("put socket into non-blocking mode"))?;

    info!("Bound TCP websocket");

    let cert = nvs::eebus_cert::get().await;
    let key = nvs::eebus_key::get().await;

    let ski_cell = RefCell::new([0 as c_uchar; 20]);
    let cert_callback = |cert| {
        if let Ok(ski) = ski::get_ski_from_cert(cert) {
            ski_cell.replace(ski);
        } else {
            error!("Failed to get SKI");
        }
    };
    let cfg = ServerConfig {
        alpn_protos: None,
        ca_cert: None,
        server_cert: Some(X509::der(&cert)),
        server_key: Some(X509::der(&key)),
        server_key_password: None,
        use_secure_element: false,
        cert_callback: Some(&cert_callback),
    };

    loop {
        info!(
            "Serving eebus websocket: own_ski={}",
            get_own_ski().await.bytes
        );
        let (stream, addr) = listener
            .accept()
            .await
            .context(intern!("accept eebus tcp connection"))?;
        info!("Accepted TCP client: addr={}", Debug2Format(&addr));
        let stream = AsyncTcpStream(Some(stream));
        let mut stream = EspAsyncTls::adopt(stream)?;
        stream.negotiate_server(&cfg).await?;
        let ski: Ski = ski_cell.take().into();
        info!("Accepted EEBus connection from client: ski={}", &ski.bytes);
        let stream = Rc::new(stream);
        let writer = AsyncReadWriter(stream);
        if let Err(e) = handle_connection(writer, addr.ip(), addr.port(), ski).await {
            error!("Failed to connect: err={}", e);
        }
    }
}

fn own_device_id() -> String {
    const WOLF_PEN: u32 = 46922;

    let serial = crate::utils::mac::eth();

    format!("d:_i:{WOLF_PEN}_LINK{serial}")
}

async fn handle_connection(
    stream: AsyncReadWriter<impl PollableSocket + 'static>,
    ip: IpAddr,
    port: u16,
    ski: Ski,
) -> Result<()> {
    let connection = spawn_connection(stream, ip, port, ski, env::executor().as_ref()).await?;
    let trust = env::shared().trust_db();
    let ship = Ship::init(
        connection,
        &*trust,
        Default::default(),
        own_device_id(),
        true,
    )
    .await
    .context(intern!("initiate ship connection"))?;
    let ship = match ship {
        Ok(ship) => ship,
        Err(_) => defmt::todo!("implement pin exchange"),
    };

    info!("serving eebus spine requests now");
    Box::pin(server::run(ship)).await
}

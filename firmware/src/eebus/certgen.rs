use std::ffi::c_void;

use esp_idf_svc::sys;

use crate::{
    prelude::*,
    utils::{
        alloc::{vec_zeroed, InternalAllocator, InternalVec},
        cod::call_on_drop,
    },
};

/// Generates a new EEBUS certificate and private key and returns it in DER format.
pub fn generate_eebus_cert() -> Result<(InternalVec<u8>, InternalVec<u8>)> {
    // Buffer sizes
    const CERT_BUF_SIZE: usize = 4096;
    const KEY_BUF_SIZE: usize = 4096;

    // Prepare output vectors
    let cert_der: InternalVec<u8>;
    let key_der: InternalVec<u8>;

    // Setup MbedTLS contexts
    let mut key = sys::mbedtls_pk_context::default();
    let mut cert = sys::mbedtls_x509write_cert::default();
    let mut entropy = sys::mbedtls_entropy_context::default();
    let mut ctr_drbg = sys::mbedtls_ctr_drbg_context::default();

    // Personalization string for the random generator
    let pers = b"rust_secp256r1_certgen";

    // build 64-bit serial number from mac + random bytes
    let mut serial_buf = [0u8; 8];
    unsafe {
        sys::esp_read_mac(&mut serial_buf as *mut _, 0);
    }
    // Replace last two bytes since the mac is usually only 6 bytes
    serial_buf[6] = fastrand::u8(..);
    serial_buf[7] = fastrand::u8(..);

    info!(
        "Generating EEBUS certificate: serial={}",
        hex::encode(serial_buf).as_str()
    );

    unsafe {
        // Initialize structures
        sys::mbedtls_pk_init(&mut key);
        sys::mbedtls_x509write_crt_init(&mut cert);
        sys::mbedtls_entropy_init(&mut entropy);
        sys::mbedtls_ctr_drbg_init(&mut ctr_drbg);

        // Make sure we clean up structures regardless of the result
        let _cleanup = {
            let key = &mut key as *mut _;
            let cert = &mut cert as *mut _;
            let entropy = &mut entropy as *mut _;
            let ctr_drbg = &mut ctr_drbg as *mut _;

            call_on_drop(move || {
                // Cleanup
                sys::mbedtls_pk_free(key);
                sys::mbedtls_x509write_crt_free(cert);
                sys::mbedtls_entropy_free(entropy);
                sys::mbedtls_ctr_drbg_free(ctr_drbg);
            })
        };

        // Seed the random number generator
        let result = sys::mbedtls_ctr_drbg_seed(
            &mut ctr_drbg,
            Some(sys::mbedtls_entropy_func),
            &mut entropy as *mut _ as *mut c_void,
            pers.as_ptr(),
            pers.len(),
        );
        if result != 0 {
            bail!("Failed to seed random generator: {}", result);
        }

        // Set up the key structure for an EC key
        let result = sys::mbedtls_pk_setup(
            &mut key,
            sys::mbedtls_pk_info_from_type(sys::mbedtls_pk_type_t_MBEDTLS_PK_ECKEY),
        );
        if result != 0 {
            bail!("Failed to setup PK context: {}", result);
        }

        // Generate the EC key (SECP256R1)
        let result = sys::mbedtls_ecp_gen_key(
            sys::mbedtls_ecp_group_id_MBEDTLS_ECP_DP_SECP256R1,
            sys::mbedtls_pk_ec_static(key),
            Some(sys::mbedtls_ctr_drbg_random),
            &mut ctr_drbg as *mut _ as *mut c_void,
        );
        if result != 0 {
            bail!("Key generation failed: {}", result);
        }

        // Set certificate details
        let subject = c"CN=Link,O=Wolf,C=DE".as_ptr();
        let issuer = c"CN=Link,O=Wolf,C=DE".as_ptr();
        let not_before = c"20240312000000".as_ptr();
        let not_after = c"20450312000000".as_ptr();

        let result = sys::mbedtls_x509write_crt_set_subject_name(&mut cert, subject);
        if result != 0 {
            bail!("Failed to set subject name: {}", result);
        }

        let result = sys::mbedtls_x509write_crt_set_issuer_name(&mut cert, issuer);
        if result != 0 {
            bail!("Failed to set issuer name: {}", result);
        }

        sys::mbedtls_x509write_crt_set_serial_raw(
            &mut cert,
            serial_buf.as_mut_ptr(),
            serial_buf.len(),
        );

        let result = sys::mbedtls_x509write_crt_set_validity(&mut cert, not_before, not_after);
        if result != 0 {
            bail!("Failed to set validity period: {}", result);
        }

        sys::mbedtls_x509write_crt_set_subject_key(&mut cert, &mut key);
        sys::mbedtls_x509write_crt_set_issuer_key(&mut cert, &mut key);
        sys::mbedtls_x509write_crt_set_md_alg(&mut cert, sys::mbedtls_md_type_t_MBEDTLS_MD_SHA256);
        sys::mbedtls_x509write_crt_set_version(&mut cert, sys::MBEDTLS_X509_CRT_VERSION_3 as i32);

        let ret = sys::mbedtls_x509write_crt_set_basic_constraints(
            &mut cert, // certificate
            1,         // 1 = is_ca and critical, 0 = neither
            -1,        // max_pathlen, -1 to omit
        );
        if ret != 0 {
            bail!("Failed to add Basic Constraints extension: {}", ret);
        }

        let san_oid: &[u8] = &[0x55, 0x1d, 0x11, 0]; // Subject Alternative Name OID

        // ASN.1 encoding of: SEQUENCE { [2] dNSName "eebus-device" }
        // Breakdown:
        // 0x30 LEN  - SEQUENCE
        //   0x82 LEN - [2] dNSName
        //   <hostname>
        let hostname = b"eebus-device";
        let san_len = 2 + hostname.len(); // tag + len + string
        let mut san_der = InternalVec::with_capacity_in(2 + san_len, InternalAllocator);
        san_der.push(0x30); // SEQUENCE
        san_der.push(san_len as u8); // Length
        san_der.push(0x82); // [2] dNSName
        san_der.push(hostname.len() as u8); // dNSName length
        san_der.extend_from_slice(hostname); // "eebus-device"

        let ret = sys::mbedtls_x509write_crt_set_extension(
            &mut cert,
            san_oid.as_ptr(),
            san_oid.len() - 1,
            0, // non-critical
            san_der.as_ptr(),
            san_der.len(),
        );
        if ret != 0 {
            bail!("Failed to add SAN extension: {}", ret);
        }

        // Add Subject Key Identifier extension
        let ret = sys::mbedtls_x509write_crt_set_subject_key_identifier(&mut cert);
        if ret != 0 {
            bail!("Failed to add SKI extension: {}", result);
        }

        // Generate the certificate in DER format
        let mut cert_buffer = vec_zeroed(CERT_BUF_SIZE, InternalAllocator);
        let result = sys::mbedtls_x509write_crt_der(
            &mut cert,
            cert_buffer.as_mut_ptr(),
            cert_buffer.len(),
            Some(sys::mbedtls_ctr_drbg_random),
            &mut ctr_drbg as *mut _ as *mut c_void,
        );

        if result < 0 {
            bail!("Failed to generate certificate: {}", result);
        } else {
            let length = result as usize;
            let offset = cert_buffer.len() - length;
            cert_der = cert_buffer[offset..].to_vec_in(InternalAllocator);
        }

        // Extract the private key in DER format
        let mut key_buffer = vec![0u8; KEY_BUF_SIZE];
        let result = sys::mbedtls_pk_write_key_der(&key, key_buffer.as_mut_ptr(), key_buffer.len());

        if result < 0 {
            bail!("Failed to extract private key: {}", result);
        } else {
            let length = result as usize;
            let offset = key_buffer.len() - length;
            key_der = key_buffer[offset..].to_vec_in(InternalAllocator);
        }

        info!("Generated EEBUS certificate");
    }

    Ok((cert_der, key_der))
}

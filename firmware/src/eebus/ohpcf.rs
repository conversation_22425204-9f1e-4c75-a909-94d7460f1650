use std::sync::Arc;

use bitflags::bitflags;
use eebus::spine::{
    self,
    semps::Ohp<PERSON><PERSON><PERSON><PERSON><PERSON>,
    types::{SmartEnergyManagementPsData, SmartEnergyManagementPsDataSelectors},
};

use super::dp::{BusProxy, R, W};
use crate::{prelude::*, utils::error::AnyError};

#[repr(u16)]
pub enum SmartOperationMode {
    Normal = 0,
    PowerLimit = 1,
    RecommendOn = 2,
    ForceOn = 3,
}

impl From<SmartOperationMode> for u16 {
    fn from(val: SmartOperationMode) -> Self {
        val as u16
    }
}

bitflags! {
    pub struct Components: u16 {
        const COMPRESSOR = 0b01;
        const E_HEATER = 0b10;

        const _ = !0;
    }
}

impl From<u16> for Components {
    fn from(value: u16) -> Self {
        Self::from_bits_retain(value)
    }
}

impl From<Components> for u16 {
    fn from(val: Components) -> Self {
        val.bits()
    }
}

bitflags! {
    /// Is it currently possible to store surplus energy?
    /// bit 0 - is hot water storage "full" => if 1, no more energy can be stored
    /// bit 1 - is heating storage "full" => if 1, no more energy can be stored
    /// bit 2 - cooling storage => if 1, no more energy can be stored
    ///
    /// In conclusion, if one bit is unset, we can store energy.
    pub struct CapacityFlags : u16 {
        const HOT_WATER = 0b0001;
        const HEATING = 0b0010;
        const COOLING = 0b0100;

        const FULL = Self::HOT_WATER.bits() | Self::HEATING.bits() | Self::COOLING.bits();

        const _ = !0;
    }
}

impl From<u16> for CapacityFlags {
    fn from(value: u16) -> Self {
        Self::from_bits_retain(value)
    }
}

pub struct Ohpcf {
    /// Minimum operation time of the compressor in minutes
    pub min_operation_time_compressor: BusProxy<10996, u16, R>,
    /// Bits that signal which storage is full - see `CapacityFlags`.
    ///
    /// This is monitored after the fact because it is only available when we already switched to ForceOn.
    pub capacity_energy_mode: BusProxy<10988, CapacityFlags, R>,

    /// Which components are available?
    #[allow(unused)]
    pub available: BusProxy<10984, Components, R>,
    /// Are the components ready, that is, not in an error state?
    pub ready: BusProxy<10997, Components, R>,

    /// Operation mode
    /// TODO: how does this interact with LPC?
    pub smart_operation_mode: BusProxy<10998, SmartOperationMode, W>,
    /// Write-only to turn on components (we only turn on the compressor)
    pub command_turn_on: BusProxy<10995, Components, W>,
}

impl Ohpcf {
    pub const fn new() -> Self {
        Self {
            min_operation_time_compressor: BusProxy::new(),
            capacity_energy_mode: BusProxy::new(),
            available: BusProxy::new(),
            ready: BusProxy::new(),
            smart_operation_mode: BusProxy::new(),
            command_turn_on: BusProxy::new(),
        }
    }
}

impl Ohpcf {
    pub async fn set_hp_elevated(&self, elevated: bool) -> Result<()> {
        async fn inner(ohpcf: &Ohpcf, elevated: bool) -> Result<()> {
            if elevated {
                ohpcf
                    .smart_operation_mode
                    .write(SmartOperationMode::ForceOn)
                    .await?;
                ohpcf.command_turn_on.write(Components::COMPRESSOR).await?;
            } else {
                ohpcf
                    .smart_operation_mode
                    .write(SmartOperationMode::Normal)
                    .await?;
                ohpcf.command_turn_on.write(Components::empty()).await?;
            }
            Ok(())
        }

        inner(self, elevated)
            .await
            .context(intern!("set hp (un)elevated"))
    }

    pub async fn wait_ready(&self) {
        self.ready
            .wait_until(|r| r.contains(Components::COMPRESSOR), mins(1))
            .await;
    }

    pub async fn wait_not_ready(&self) {
        self.ready
            .wait_until(|r| !r.contains(Components::COMPRESSOR), mins(1))
            .await;
    }

    pub async fn wait_capacity_full(&self) {
        self.capacity_energy_mode
            .wait_until(|r| r.contains(CapacityFlags::FULL), mins(1))
            .await;
    }

    pub async fn wait_capacity_avail(&self) {
        self.capacity_energy_mode
            .wait_until(|r| !r.contains(CapacityFlags::FULL), mins(1))
            .await;
    }
}

impl OhpcfHandler for Ohpcf {
    type Error = AnyError;

    async fn send_elevation_cmd(&self, cmd: bool) {
        info!("Sending elevation command: cmd={}", cmd);
        state().smart_energy.elevation.update(cmd);
    }

    async fn read_min_operation_time_compressor_minutes(&self) -> Result<u16> {
        self.min_operation_time_compressor.read().await
    }
}

pub struct SmartEnergyManagementImpl {
    semps: Arc<Mutex<spine::semps::SmartEnergyManagement>>,
}

impl SmartEnergyManagementImpl {
    pub fn new() -> Self {
        Self {
            semps: Arc::new(Mutex::new(spine::semps::SmartEnergyManagement::new())),
        }
    }

    pub async fn read(&self) -> Result<SmartEnergyManagementPsData> {
        let state = state().smart_energy.state.get();
        self.semps
            .lock()
            .await
            .read(
                state.is_offering(),
                state.is_running(),
                &env::interfaces().ohpcf,
            )
            .await
    }

    pub async fn upsert(
        &self,
        request: &SmartEnergyManagementPsData,
        selectors: &[SmartEnergyManagementPsDataSelectors],
    ) -> spine::Result<()> {
        let state = state().smart_energy.state.get();
        self.semps
            .lock()
            .await
            .upsert(
                request,
                selectors,
                state.is_offering(),
                &env::interfaces().ohpcf,
            )
            .await
    }
}

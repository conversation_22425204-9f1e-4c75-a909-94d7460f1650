use std::time::Duration;

use async_io_mini::Timer;

use crate::{
    flash::eventlog::events::{Bus, BusDevice, Event},
    prelude::*,
    server::schema::BusState,
    smartset::msg::{ModbusDevice, ModbusDevices},
};

#[derive(Clone)]
pub struct ModbusRequest {
    pub bus_address: u8,
    pub data_address: u16, // address as 0..65535
    pub function: ModbusFunction,
}

#[derive(Clone)]
pub enum ModbusFunction {
    ReadCoils { count: u16 },
    ReadRegisters { count: u16 },
    WriteSingleCoil { value: [u8; 2] },
    WriteSingleRegister { value: [u8; 2] },
    WriteMultipleCoils { values: Vec<u8> }, // max 246 bytes
    WriteMultipleRegisters { values: Vec<u8> }, // max 246 bytes
}

pub type ModbusReply = Result<ModbusData, ModbusError>;

pub struct ModbusData {
    pub bus_address: u8,
    pub data: ModbusDataResponse,
}

pub enum ModbusDataResponse {
    ReadCoils { data: Vec<u8> },
    ReadRegisters { data: Vec<u8> },
    WriteSingleCoil { address: u16 },        // address as 0..65535
    WriteSingleRegister { address: u16 },    // address as 0..65535
    WriteMultipleCoils { address: u16 },     // address as 0..65535
    WriteMultipleRegisters { address: u16 }, // address as 0..65535
}

pub enum ModbusError {
    Timeout,
    /// excepction / "NACK" returned by device
    Exception,
    InvalidMessage,
    IncorrectCrc,
}

pub async fn request(req: ModbusRequest, slot: &ReturnSlot<ModbusReply>) -> ModbusReply {
    let mut retries_left = 3;
    loop {
        let resp = itc().modbus.process(req.clone(), slot).await;
        match resp {
            Ok(data) if req_resp_match(&req, &data) => return Ok(data),
            Ok(_) => continue,
            Err(e) if retries_left == 0 => return Err(e),
            Err(_) => retries_left -= 1,
        }
    }
}

#[rustfmt::skip]
fn req_resp_match(req: &ModbusRequest, resp: &ModbusData) -> bool {
    if req.bus_address != resp.bus_address {
        return false;
    }

    match (&req.function, &resp.data) {
        (ModbusFunction::ReadCoils { .. }, ModbusDataResponse::ReadCoils { .. }) => true,
        (ModbusFunction::ReadRegisters { .. }, ModbusDataResponse::ReadRegisters { .. }) => true,
        (ModbusFunction::WriteSingleCoil { .. }, ModbusDataResponse::WriteSingleCoil { address })
        | (ModbusFunction::WriteSingleRegister { .. }, ModbusDataResponse::WriteSingleRegister { address })
        | (ModbusFunction::WriteMultipleCoils { .. }, ModbusDataResponse::WriteMultipleCoils { address })
        | (ModbusFunction::WriteMultipleRegisters { .. }, ModbusDataResponse::WriteMultipleRegisters { address }) => {
            req.data_address == *address
        }
        _ => false,
    }
}

pub async fn discover_devices(slot: &ReturnSlot<ModbusReply>) -> usize {
    // (device address, whether the response yields device information, register address, register count)
    const DEVICES_TO_CHECK: [(u8, bool, u16, u16); 5] = [
        // wolf devices (4..9 are reserved but not used yet)
        (1, true, 5857, 2),
        (2, true, 5857, 2),
        (3, true, 5857, 2),
        // bhkw (kuntschar + schlüter)
        (10, false, 8239, 1),
        // ckl pool (pro klima)
        (20, false, 19, 1),
    ];

    let mut devices = Vec::new();

    for device in DEVICES_TO_CHECK {
        let (bus_address, type_available, data_address, count) = device;

        let req = ModbusRequest {
            bus_address,
            data_address,
            function: ModbusFunction::ReadRegisters { count },
        };

        let response = request(req, slot).await;

        if let Ok(resp) = response
            && let ModbusDataResponse::ReadRegisters { data } = resp.data
            && data.len() == (count * 2) as usize
        {
            let (software_version, device_type) = match type_available {
                true => {
                    let software_version = u16::from_be_bytes([data[0], data[1]]);
                    let device_type = u16::from_be_bytes([data[2], data[3]]);
                    (Some(software_version), Some(device_type))
                }
                false => (None, None),
            };

            devices.push(ModbusDevice {
                bus_address: bus_address.into(),
                software_version: software_version.map(|x| x.into()),
                ty: device_type.map(|x| x.into()),
            });
        }
    }

    let devices_len = devices.len();
    info!(
        "Finished modbus device discovery: device_count={}",
        devices_len
    );

    let devices = ModbusDevices {
        bus_devices: devices,
    };

    state().modbus.devices.update_if_changed(devices);

    devices_len
}

pub async fn modbus_discovery_task() {
    async fn discover(slot: &ReturnSlot<ModbusReply>, secs: u64, initial_discovery: bool) {
        let bus_devices = state().modbus.devices.get();
        let old_num = bus_devices.bus_devices.len();
        Timer::after(Duration::from_secs(secs)).await;

        let num_devices = discover_devices(slot).await;

        let mut guard = env::acquire_website_state().await;
        let modbus_state = match num_devices {
            0 => BusState::NotConnected,
            _ => BusState::Connected,
        };
        guard.modbus_state = modbus_state;
        drop(guard);

        if initial_discovery {
            state().led.disable(LedBits::ModbusDiscovery);
        }
        match num_devices {
            0 => state().led.disable(LedBits::ModbusConnected),
            _ => state().led.enable(LedBits::ModbusConnected),
        };

        if num_devices > 0 && old_num == 0 {
            let bus_devices = state().modbus.devices.get();
            for i in &bus_devices.bus_devices {
                let dev = BusDevice {
                    device_id: i.bus_address.0 as u16,
                    bus: Bus::Modbus,
                };
                Event::BusDiscover(dev).log().await;
            }
        }
        if num_devices == 0 && old_num > 0 {
            let dev = BusDevice {
                device_id: 0xFFFF, // TODO
                bus: Bus::Modbus,
            };
            Event::BusDisconnect(dev).log().await;
        }
    }

    let slot = ReturnSlot::new();
    discover(&slot, 0, true).await;
    discover(&slot, 30, true).await;
    discover(&slot, 60, false).await;
    discover(&slot, 60, false).await;
    loop {
        discover(&slot, 600, false).await;
    }
}

pub(super) fn modbus_request_to_message(req: &ModbusRequest, buf: &mut [u8; 256]) -> usize {
    let mut idx = 0;
    let (mut crc_lo, mut crc_hi) = (0xFF, 0xFF);

    let mut add_to_data = |byte: u8| {
        buf[idx] = byte;
        idx += 1;

        let crc_idx = crc_lo ^ byte;
        crc_lo = crc_hi ^ CRC_HI_LUT[crc_idx as usize];
        crc_hi = CRC_LO_LUT[crc_idx as usize];
    };

    add_to_data(req.bus_address);
    add_to_data(match req.function {
        ModbusFunction::ReadCoils { .. } => READ_COILS,
        ModbusFunction::ReadRegisters { .. } => READ_REGISTERS,
        ModbusFunction::WriteSingleCoil { .. } => WRITE_SINGLE_COIL,
        ModbusFunction::WriteSingleRegister { .. } => WRITE_SINGLE_REGISTER,
        ModbusFunction::WriteMultipleCoils { .. } => WRITE_MULTIPLE_COILS,
        ModbusFunction::WriteMultipleRegisters { .. } => WRITE_MULTIPLE_REGISTERS,
    });
    add_to_data((req.data_address >> 8) as u8);
    add_to_data(req.data_address as u8);

    match &req.function {
        ModbusFunction::ReadCoils { count } | ModbusFunction::ReadRegisters { count } => {
            add_to_data((*count >> 8) as u8);
            add_to_data(*count as u8);
        }
        ModbusFunction::WriteSingleCoil { value }
        | ModbusFunction::WriteSingleRegister { value } => {
            add_to_data(value[0]);
            add_to_data(value[1]);
        }
        ModbusFunction::WriteMultipleCoils { values }
        | ModbusFunction::WriteMultipleRegisters { values } => {
            for byte in values {
                add_to_data(*byte);
            }
        }
    }

    buf[idx] = crc_hi;
    buf[idx] = crc_lo;

    idx + 2
}

pub(super) fn parse_response(bytes: &[u8]) -> ModbusReply {
    if bytes.len() < 4 {
        return Err(ModbusError::InvalidMessage);
    }

    let (mut crc_lo, mut crc_hi) = (0xFF, 0xFF);
    for byte in &bytes[..(bytes.len() - 2)] {
        let crc_idx = crc_lo ^ byte;
        crc_lo = crc_hi ^ CRC_HI_LUT[crc_idx as usize];
        crc_hi = CRC_LO_LUT[crc_idx as usize];
    }
    let crc_lo_ok = crc_lo == bytes[bytes.len() - 2];
    let crc_hi_ok = crc_hi == bytes[bytes.len() - 1];

    if !(crc_lo_ok && crc_hi_ok) {
        return Err(ModbusError::IncorrectCrc);
    }

    let bytes = &bytes[..(bytes.len() - 2)];

    let bus_address = bytes[0];
    let function = bytes[1];

    if (function & 0x80) != 0 {
        return Err(ModbusError::Exception);
    }

    let data = match function {
        READ_COILS | READ_REGISTERS => {
            let expected_byte_count = *bytes.get(2).ok_or(ModbusError::InvalidMessage)?;
            let got_byte_count = bytes.len() - 3;
            if expected_byte_count as usize != got_byte_count {
                return Err(ModbusError::InvalidMessage);
            }
            let data = Vec::from(&bytes[3..]);
            match function {
                READ_COILS => ModbusDataResponse::ReadCoils { data },
                READ_REGISTERS => ModbusDataResponse::ReadRegisters { data },
                _ => defmt::unreachable!(),
            }
        }
        WRITE_SINGLE_COIL | WRITE_SINGLE_REGISTER => {
            if bytes.len() != 6 {
                return Err(ModbusError::InvalidMessage);
            }
            let address = ((bytes[3] as u16) << 8) | (bytes[4] as u16);
            match function {
                WRITE_SINGLE_COIL => ModbusDataResponse::WriteSingleCoil { address },
                WRITE_SINGLE_REGISTER => ModbusDataResponse::WriteSingleRegister { address },
                _ => defmt::unreachable!(),
            }
        }
        WRITE_MULTIPLE_COILS | WRITE_MULTIPLE_REGISTERS => {
            if bytes.len() != 6 {
                return Err(ModbusError::InvalidMessage);
            }
            let address = ((bytes[3] as u16) << 8) | (bytes[4] as u16);
            //let count = ((bytes[5] as u16) << 8) | (bytes[6] as u16);
            match function {
                WRITE_MULTIPLE_COILS => ModbusDataResponse::WriteMultipleCoils { address },
                WRITE_MULTIPLE_REGISTERS => ModbusDataResponse::WriteMultipleRegisters { address },
                _ => defmt::unreachable!(),
            }
        }
        _ => return Err(ModbusError::InvalidMessage),
    };

    Ok(ModbusData { bus_address, data })
}

pub const READ_COILS: u8 = 1;
pub const READ_REGISTERS: u8 = 3;
pub const WRITE_SINGLE_COIL: u8 = 5;
pub const WRITE_SINGLE_REGISTER: u8 = 6;
pub const WRITE_MULTIPLE_COILS: u8 = 15;
pub const WRITE_MULTIPLE_REGISTERS: u8 = 16;

const CRC_HI_LUT: [u8; 256] = [
    0x00, 0xc1, 0x81, 0x40, 0x01, 0xc0, 0x80, 0x41, 0x01, 0xc0, 0x80, 0x41, 0x00, 0xc1, 0x81, 0x40,
    0x01, 0xc0, 0x80, 0x41, 0x00, 0xc1, 0x81, 0x40, 0x00, 0xc1, 0x81, 0x40, 0x01, 0xc0, 0x80, 0x41,
    0x01, 0xc0, 0x80, 0x41, 0x00, 0xc1, 0x81, 0x40, 0x00, 0xc1, 0x81, 0x40, 0x01, 0xc0, 0x80, 0x41,
    0x00, 0xc1, 0x81, 0x40, 0x01, 0xc0, 0x80, 0x41, 0x01, 0xc0, 0x80, 0x41, 0x00, 0xc1, 0x81, 0x40,
    0x01, 0xc0, 0x80, 0x41, 0x00, 0xc1, 0x81, 0x40, 0x00, 0xc1, 0x81, 0x40, 0x01, 0xc0, 0x80, 0x41,
    0x00, 0xc1, 0x81, 0x40, 0x01, 0xc0, 0x80, 0x41, 0x01, 0xc0, 0x80, 0x41, 0x00, 0xc1, 0x81, 0x40,
    0x00, 0xc1, 0x81, 0x40, 0x01, 0xc0, 0x80, 0x41, 0x01, 0xc0, 0x80, 0x41, 0x00, 0xc1, 0x81, 0x40,
    0x01, 0xc0, 0x80, 0x41, 0x00, 0xc1, 0x81, 0x40, 0x00, 0xc1, 0x81, 0x40, 0x01, 0xc0, 0x80, 0x41,
    0x01, 0xc0, 0x80, 0x41, 0x00, 0xc1, 0x81, 0x40, 0x00, 0xc1, 0x81, 0x40, 0x01, 0xc0, 0x80, 0x41,
    0x00, 0xc1, 0x81, 0x40, 0x01, 0xc0, 0x80, 0x41, 0x01, 0xc0, 0x80, 0x41, 0x00, 0xc1, 0x81, 0x40,
    0x00, 0xc1, 0x81, 0x40, 0x01, 0xc0, 0x80, 0x41, 0x01, 0xc0, 0x80, 0x41, 0x00, 0xc1, 0x81, 0x40,
    0x01, 0xc0, 0x80, 0x41, 0x00, 0xc1, 0x81, 0x40, 0x00, 0xc1, 0x81, 0x40, 0x01, 0xc0, 0x80, 0x41,
    0x00, 0xc1, 0x81, 0x40, 0x01, 0xc0, 0x80, 0x41, 0x01, 0xc0, 0x80, 0x41, 0x00, 0xc1, 0x81, 0x40,
    0x01, 0xc0, 0x80, 0x41, 0x00, 0xc1, 0x81, 0x40, 0x00, 0xc1, 0x81, 0x40, 0x01, 0xc0, 0x80, 0x41,
    0x01, 0xc0, 0x80, 0x41, 0x00, 0xc1, 0x81, 0x40, 0x00, 0xc1, 0x81, 0x40, 0x01, 0xc0, 0x80, 0x41,
    0x00, 0xc1, 0x81, 0x40, 0x01, 0xc0, 0x80, 0x41, 0x01, 0xc0, 0x80, 0x41, 0x00, 0xc1, 0x81, 0x40,
];

const CRC_LO_LUT: [u8; 256] = [
    0x00, 0xc0, 0xc1, 0x01, 0xc3, 0x03, 0x02, 0xc2, 0xc6, 0x06, 0x07, 0xc7, 0x05, 0xc5, 0xc4, 0x04,
    0xcc, 0x0c, 0x0d, 0xcd, 0x0f, 0xcf, 0xce, 0x0e, 0x0a, 0xca, 0xcb, 0x0b, 0xc9, 0x09, 0x08, 0xc8,
    0xd8, 0x18, 0x19, 0xd9, 0x1b, 0xdb, 0xda, 0x1a, 0x1e, 0xde, 0xdf, 0x1f, 0xdd, 0x1d, 0x1c, 0xdc,
    0x14, 0xd4, 0xd5, 0x15, 0xd7, 0x17, 0x16, 0xd6, 0xd2, 0x12, 0x13, 0xd3, 0x11, 0xd1, 0xd0, 0x10,
    0xf0, 0x30, 0x31, 0xf1, 0x33, 0xf3, 0xf2, 0x32, 0x36, 0xf6, 0xf7, 0x37, 0xf5, 0x35, 0x34, 0xf4,
    0x3c, 0xfc, 0xfd, 0x3d, 0xff, 0x3f, 0x3e, 0xfe, 0xfa, 0x3a, 0x3b, 0xfb, 0x39, 0xf9, 0xf8, 0x38,
    0x28, 0xe8, 0xe9, 0x29, 0xeb, 0x2b, 0x2a, 0xea, 0xee, 0x2e, 0x2f, 0xef, 0x2d, 0xed, 0xec, 0x2c,
    0xe4, 0x24, 0x25, 0xe5, 0x27, 0xe7, 0xe6, 0x26, 0x22, 0xe2, 0xe3, 0x23, 0xe1, 0x21, 0x20, 0xe0,
    0xa0, 0x60, 0x61, 0xa1, 0x63, 0xa3, 0xa2, 0x62, 0x66, 0xa6, 0xa7, 0x67, 0xa5, 0x65, 0x64, 0xa4,
    0x6c, 0xac, 0xad, 0x6d, 0xaf, 0x6f, 0x6e, 0xae, 0xaa, 0x6a, 0x6b, 0xab, 0x69, 0xa9, 0xa8, 0x68,
    0x78, 0xb8, 0xb9, 0x79, 0xbb, 0x7b, 0x7a, 0xba, 0xbe, 0x7e, 0x7f, 0xbf, 0x7d, 0xbd, 0xbc, 0x7c,
    0xb4, 0x74, 0x75, 0xb5, 0x77, 0xb7, 0xb6, 0x76, 0x72, 0xb2, 0xb3, 0x73, 0xb1, 0x71, 0x70, 0xb0,
    0x50, 0x90, 0x91, 0x51, 0x93, 0x53, 0x52, 0x92, 0x96, 0x56, 0x57, 0x97, 0x55, 0x95, 0x94, 0x54,
    0x9c, 0x5c, 0x5d, 0x9d, 0x5f, 0x9f, 0x9e, 0x5e, 0x5a, 0x9a, 0x9b, 0x5b, 0x99, 0x59, 0x58, 0x98,
    0x88, 0x48, 0x49, 0x89, 0x4b, 0x8b, 0x8a, 0x4a, 0x4e, 0x8e, 0x8f, 0x4f, 0x8d, 0x4d, 0x4c, 0x8c,
    0x44, 0x84, 0x85, 0x45, 0x87, 0x47, 0x46, 0x86, 0x82, 0x42, 0x43, 0x83, 0x41, 0x81, 0x80, 0x40,
];

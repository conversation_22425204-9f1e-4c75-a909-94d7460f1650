use esp_idf_svc::hal::{
    cpu::Core,
    delay::TICK_RATE_HZ,
    gpio::{self, InputPin, OutputPin},
    peripheral::Peripheral,
    uart::{
        config::{Config, DataBits, FlowControl, Parity, SourceClock, StopBits},
        AsyncUartDriver, Uart, UartDriver, UartRxDriver,
    },
    units::Hertz,
};

use super::api::{modbus_request_to_message, parse_response, ModbusError};
use crate::{
    flash::eventlog::events::{Event, ResetTrigger},
    prelude::*,
    utils::task,
};

const MODBUS_UART_CONFIG: Config = Config {
    baudrate: Hertz(9600),
    data_bits: DataBits::DataBits8,
    parity: Parity::ParityNone,
    stop_bits: StopBits::STOP1,
    flow_control: FlowControl::None,
    source_clock: SourceClock::default(),
    flow_control_rts_threshold: 122,
    rx_fifo_size: 256,
    ..Config::new()
};

/// timeout for when a modbus response is considered to be timed out.
/*                               msec / (conversion.........) */
const MODBUS_READ_TIMEOUT: u32 = 1000 / (1000 / TICK_RATE_HZ);

/// timeout for detecting when a response ended. this should be about 5ms. we would only need
/// roughly 0.36ms, but 5ms is the smallest we can do.
const MODBUS_CHAR_TIMEOUT: u32 = 1;

pub fn create_modbus_task<UART: Uart>(
    uart: impl Peripheral<P = UART> + Send + 'static,
    tx: impl Peripheral<P = impl OutputPin> + Send + 'static,
    rx: impl Peripheral<P = impl InputPin> + Send + 'static,
    enable: impl Peripheral<P = impl OutputPin> + Send + 'static,
) {
    info!("Spawning modbus task");

    task::spawn_pinned("modbus", 6_000, 10, Some(Core::Core1), move || {
        let mut driver = AsyncUartDriver::new(
            uart,
            tx,
            rx,
            Option::<gpio::Gpio0>::None,
            Some(enable),
            &MODBUS_UART_CONFIG,
        )
        .unwrap();

        sys::esp!(unsafe { sys::uart_set_rx_timeout(2, 4) }).unwrap();
        sys::esp!(unsafe { sys::uart_set_mode(2, sys::uart_mode_t_UART_MODE_RS485_HALF_DUPLEX) })
            .unwrap();

        for _ in 0..10 {
            let Err(err) = modbus_task(&mut driver);
            error!("Failed to run task: err={}", err);
            info!("Restarting modbus task");
        }
        error!("Rebooting due to unrecoverable task");

        hal::task::block_on(Event::ResetTrigger(ResetTrigger::BusFailure).log());
        esp_idf_svc::hal::reset::restart();
    });
}

pub fn modbus_task(driver: &mut AsyncUartDriver<'static, UartDriver<'static>>) -> Result<!> {
    let (tx, rx) = driver.split();

    hal::task::block_on(async {
        loop {
            let mut buf = [0; 256];

            let (req, slot) = itc().modbus.recv().await;
            let msg_len = modbus_request_to_message(&req, &mut buf);
            rx.driver().clear()?;
            tx.write(&buf[..msg_len]).await?;

            let msg_len = recv_response(rx.driver(), &mut buf)?;
            let resp = match msg_len {
                0 => Err(ModbusError::Timeout),
                _ => parse_response(&buf[..msg_len]),
            };
            slot.ret(resp);
        }
    })
}

fn recv_response(rx: &UartRxDriver<'_>, buf: &mut [u8; 256]) -> Result<usize> {
    let mut num_bytes_read = 0;
    let mut timeout = MODBUS_READ_TIMEOUT;
    loop {
        match rx.read(&mut buf[num_bytes_read..], timeout) {
            Ok(0) => break,
            Ok(n) => num_bytes_read += n,
            Err(e) if e.code() == sys::ESP_ERR_TIMEOUT => break,
            Err(e) => Err(e)?,
        }
        timeout = MODBUS_CHAR_TIMEOUT;
    }
    Ok(num_bytes_read)
}

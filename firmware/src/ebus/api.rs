use defmt::Debug2Format;
use ebus::{<PERSON><PERSON><PERSON>, MasterTelegram, Telegram, TelegramFlag, TelegramFlags};
use serde::{ser::SerializeTuple, Deserialize, Serialize};

use super::cache::{EbusDatabase, EbusIdent};
use crate::{
    flash::eventlog::events::{Bus, BusDevice, Event},
    prelude::*,
    server::schema::BusState,
    smartset::msg::EbusDevice,
    sync::Watch,
    time::{process_ebus_clock_time, ClockTime},
};

pub struct EbusRequest {
    pub telegram: MasterTelegram,
    pub retries: u8,
}

#[derive(Debug)]
pub enum EbusReply {
    /// None = ACK_ERR or Timeout
    AckError,
    Timeout,
    Ok,
    Reply(Vec<u8>),
    ReplyUnclean(Vec<u8>),
}

impl EbusReply {
    pub fn is_okay(&self) -> bool {
        matches!(self, EbusReply::Ok | EbusReply::Reply(_))
    }

    pub fn reply(&self, allow_dirty: bool) -> Result<&[u8], ReplyError> {
        match self {
            EbusReply::Reply(bytes) => Ok(bytes),
            EbusReply::ReplyUnclean(bytes) => {
                if allow_dirty {
                    Ok(bytes)
                } else {
                    info!(
                        "Discarding reply because it is dirty, bytes={}",
                        bytes.as_slice()
                    );
                    Err(ReplyError::Dirty)
                }
            }
            _ => Err(ReplyError::NoData),
        }
    }

    #[allow(dead_code)]
    pub fn into_reply(self) -> Option<Vec<u8>> {
        match self {
            EbusReply::Reply(bytes) => Some(bytes),
            _ => None,
        }
    }
}

#[derive(Clone, Copy, Debug)]
pub enum ReplyError {
    Dirty,
    NoData,
}

#[derive(Clone, Copy, Debug, Default, PartialEq)]
pub struct DeviceIdent {
    pub software_version: u8,
    pub software_revision: u8,
    pub config: u16,
    pub device_id: u8,
    /// How many times the device was not detected
    pub undetected: u8,
}

impl DeviceIdent {
    pub fn parse(bytes: &[u8]) -> Result<Self> {
        ensure!(bytes.len() == 10);

        let device_id = bytes[2];
        let config = u16::from_le_bytes([bytes[4], bytes[5]]);

        let software_version = bytes[6];
        let software_revision = bytes[7];

        Ok(DeviceIdent {
            software_version,
            software_revision,
            config,
            device_id,
            undetected: 0,
        })
    }
}

#[derive(Clone, Copy, Debug, Eq, PartialEq)]
pub struct InfoNum {
    pub data_low: u8,
    pub data_high: u8,
    pub min: Option<u16>,
    pub max: Option<u16>,
}

impl InfoNum {
    pub fn as_value(&self) -> u16 {
        self.data_low as u16 | ((self.data_high as u16) << 8)
    }

    pub fn parse(bytes: &[u8], svc: u16) -> Result<heapless::Vec<Self, 5>> {
        match svc {
            0x5022 => {
                ensure!(bytes.len() % 2 == 0);
                ensure!(!bytes.is_empty());
                ensure!(bytes.len() <= 10);

                Ok(bytes
                    .array_chunks::<2>()
                    .map(|value| InfoNum {
                        data_low: value[0],
                        data_high: value[1],
                        min: None,
                        max: None,
                    })
                    .collect())
            }
            0x4022 => {
                ensure!(bytes.len() == 2);

                // big endian because why not
                let data_high = bytes[0];
                let data_low = bytes[1];

                let mut v = heapless::Vec::new();
                let _ = v.push(InfoNum {
                    data_low,
                    data_high,
                    min: None,
                    max: None,
                });
                Ok(v)
            }
            0x4050 => {
                ensure!(bytes.len() == 10);

                // big endian because why not
                let data_high = bytes[0];
                let data_low = bytes[1];
                let min = Some(u16::from_be_bytes([bytes[2], bytes[3]]));
                let max = Some(u16::from_be_bytes([bytes[4], bytes[5]]));

                let mut v = heapless::Vec::new();
                let _ = v.push(InfoNum {
                    data_low,
                    data_high,
                    min,
                    max,
                });
                Ok(v)
            }
            _ => defmt::unreachable!(),
        }
    }
}

#[derive(Clone, Copy, Debug)]
pub struct TimeProg {
    pub bytes: [u8; 20],
}

impl TimeProg {
    pub fn parse(telegram: &Telegram) -> Result<Self> {
        Ok(TimeProg {
            bytes: telegram.data.as_bytes()[0..20]
                .try_into()
                .context(intern!("parse timeprogram that has not 20 bytes"))?,
        })
    }
}

pub fn ebus() -> &'static EbusInterface {
    EbusInterface::get()
}

pub struct EbusInterface {
    cache: Mutex<EbusDatabase>,
    own_address: u8,
    /// Synchronize timeprog receival
    // TODO: might be able to increase timeprog read throughput
    // by allowing multiple bus participants concurrently.
    timeprog: Watch<TimeProg, 1>,
}

impl EbusInterface {
    pub fn get() -> &'static Self {
        static EBUS: EbusInterface = EbusInterface {
            cache: Mutex::new(EbusDatabase::new()),
            own_address: 0xFF,
            timeprog: Watch::new(),
        };
        &EBUS
    }

    pub fn process_incoming(&self, tel: Telegram) -> Option<EbusReply> {
        //const BROADCAST_ADDRESS: u8 = 0xFE;

        if tel.dest == self.own_address {
            if tel.service == 0x03f2 {
                info!("Received timeprog: src=0x{:X}", tel.src);

                let Ok(time_prog) = TimeProg::parse(&tel) else {
                    // early return (cannot indicate error on ebus)
                    return Some(EbusReply::Ok);
                };
                self.timeprog.sender().send(time_prog);

                return Some(EbusReply::Ok);
            }

            if tel.service == 0x5022 {
                let bytes = tel.data.as_bytes();
                if bytes.len() != 3 {
                    warn!(
                        "Received 5022 with more than 3 byte payload: bytes={}",
                        bytes
                    );
                    return None;
                }
                let info_num = u16::from_le_bytes(bytes[1..3].try_into().unwrap());

                let value = match info_num {
                    10920 => state().smart_home.get_type(),
                    10921 => state().smart_home.get_version(),
                    _ => 0x8000,
                };

                info!(
                    "Replying to 5022: info_num={}, value={}, value_hex=0x{:X}",
                    info_num, value, value
                );

                return Some(EbusReply::Reply(value.to_le_bytes().to_vec()));
            }

            warn!("Received unknown telegram: svc=0x{:X}", tel.service)
        } else {
            // info!("got broadcast svc {:X}", tel.service);
            if tel.service == 0x0700 {
                if tel.src != 0x30 {
                    warn!("Ignoring clock time: src=0x{:X}", tel.src);
                } else {
                    let bytes = tel.data.as_bytes();
                    process_ebus_clock_time(bytes);
                }
            }
        }

        None
    }

    pub async fn request(
        &self,
        telegram: MasterTelegram,
        retries: u8,
        slot: &ReturnSlot<EbusReply>,
    ) -> EbusReply {
        itc()
            .ebus
            .process(EbusRequest { telegram, retries }, slot)
            .await
    }

    pub async fn master_slave(
        &self,
        service: u16,
        data: &[u8],
        dest: u8,
        flags: impl Into<TelegramFlags>,
        slot: &ReturnSlot<EbusReply>,
    ) -> EbusReply {
        let buf = Buffer::from_slice(data);

        self.request(
            MasterTelegram {
                telegram: Telegram {
                    src: self.own_address,
                    dest,
                    service,
                    data: buf,
                },
                flags: flags.into(),
            },
            3,
            slot,
        )
        .await
    }

    pub async fn identify(&self, dest: u8, slot: &ReturnSlot<EbusReply>) -> Option<DeviceIdent> {
        let buf = Buffer::from_slice(&[]);

        self.request(
            MasterTelegram {
                telegram: Telegram {
                    src: self.own_address,
                    dest,
                    service: 0x0704,
                    data: buf,
                },
                flags: TelegramFlags::none() | TelegramFlag::ExpectReply,
            },
            0,
            slot,
        )
        .await
        .reply(true)
        .ok()
        .and_then(|bytes| {
            DeviceIdent::parse(bytes)
                .context(intern!("parse device identification"))
                .ok()
        })
    }

    pub async fn read_info_num(
        &self,
        service: u16,
        nums: &[u16],
        dest: u8,
        slot: &ReturnSlot<EbusReply>,
        max_age: Duration,
    ) -> Result<heapless::Vec<InfoNum, 5>> {
        let cached = self
            .cache
            .lock()
            .await
            .get_all(dest, nums, service, max_age);

        if !cached.is_empty() {
            return Ok(cached);
        }

        let reply = match service {
            0x4050 => {
                ensure!(nums.len() == 1);
                let param_index = u8::try_from(nums[0]).context(intern!("parse 4050 param num"))?;
                let flags = TelegramFlags::none() | TelegramFlag::ExpectReply;

                self.master_slave(service, &[param_index], dest, flags, slot)
                    .await
            }
            0x4022 => {
                ensure!(matches!(nums.len(), 1));
                let flags = TelegramFlags::none() | TelegramFlag::ExpectReply;

                self.master_slave(service, &[nums[0] as u8], dest, flags, slot)
                    .await
            }
            0x5022 => {
                ensure!(matches!(nums.len(), 1 | 5));
                let mut data = [0u8; 10];
                for (i, &num) in nums.iter().enumerate() {
                    data[i * 2..(i + 1) * 2].copy_from_slice(&num.to_le_bytes());
                }
                self.master_slave(
                    service,
                    &data[..nums.len() * 2],
                    dest,
                    TelegramFlag::ExpectReply | TelegramFlag::NeedsDataCrc,
                    slot,
                )
                .await
            }
            _ => bail!("unknown service 0x{service:X}"),
        };

        let reply = reply.reply(false).context(intern!("read info num"))?;

        let info_nums = InfoNum::parse(reply, service)?;
        for (i, &num) in nums.iter().enumerate() {
            let ident = EbusIdent::new(dest, num, service);
            self.cache.lock().await.set(ident, info_nums[i]);
        }

        Ok(info_nums)
    }

    pub async fn readback_info_num(
        &self,
        service: u16,
        nums: &[u16],
        write_address: u8,
        slot: &ReturnSlot<EbusReply>,
    ) -> Result<heapless::Vec<InfoNum, 5>> {
        fn is_lsb_filled(num: u8) -> bool {
            num & num.wrapping_add(1) == 0
        }

        fn is_master_addr(num: u8) -> bool {
            is_lsb_filled(num & 0xF) && is_lsb_filled(num >> 4)
        }

        let dest = if is_master_addr(write_address) {
            write_address.wrapping_add(5)
        } else {
            write_address
        };

        self.read_info_num(service, nums, dest, slot, Duration::MIN)
            .await
    }

    pub async fn write_info_nums(
        &self,
        service: u16,
        nums_and_data: &[u16],
        dest: u8,
        slot: &ReturnSlot<EbusReply>,
    ) -> Result<EbusReply> {
        for &num in nums_and_data.iter().step_by(2) {
            self.cache
                .lock()
                .await
                .erase(EbusIdent::new(dest, num, service));
        }
        match service {
            0x4080 => {
                if nums_and_data.len() != 2 {
                    bail!("4080 supports exactly 1 info num write");
                }
                let param_index =
                    u8::try_from(nums_and_data[0]).context(intern!("parse 4080 param index"))?;
                let be_bytes = nums_and_data[1].to_be_bytes();
                let buf = [param_index, be_bytes[0], be_bytes[1]];
                Ok(self
                    .master_slave(
                        service,
                        &buf,
                        dest,
                        TelegramFlags::none() | TelegramFlag::ExpectReply,
                        slot,
                    )
                    .await)
            }
            0x5023 => {
                if nums_and_data.len() % 2 != 0 {
                    bail!("write payload ill formatted");
                }
                let n = nums_and_data.len() / 2;
                if !matches!(n, 1..=5) {
                    bail!("can only write between 1 and 5 info nums");
                }
                let mut buf = [0u8; 5 * 2 * 2];
                for (i, elem) in nums_and_data.iter().enumerate() {
                    buf[i * 2..(i + 1) * 2].copy_from_slice(&elem.to_le_bytes());
                }

                // CRY FOR HELP: some devices do not support writing a single data point, so we need to use a second as dummy
                if n == 1 {
                    const DUMMY_INFO_NUM: u16 = 349;
                    buf[4..6].copy_from_slice(&DUMMY_INFO_NUM.to_le_bytes());
                    buf[6] = 0;
                    buf[7] = 0;
                }

                Ok(self
                    .master_slave(
                        service,
                        &buf[..n.max(2) * 4],
                        dest,
                        TelegramFlags::none() | TelegramFlag::NeedsDataCrc,
                        slot,
                    )
                    .await)
            }
            _ => bail!("unknown service 0x{service:X}"),
        }
    }

    /// Cannot be used concurrently. Could use Mutex to prevent concurrency,
    /// but current code uses loop / select! to ensure non-concurrency of timeprog
    /// reads.
    pub async fn read_timeprog(
        &self,
        dest: u8,
        day: u8,
        number: u8,
        ty: u8,
        instance: u8,
        slot: &ReturnSlot<EbusReply>,
    ) -> Result<TimeProg> {
        //ensure!(day <= 6);
        let telegram = MasterTelegram {
            telegram: Telegram {
                src: self.own_address,
                dest,
                service: 0x03F1,
                data: Buffer::from_slice(&[day, number, ty, instance]),
            },
            flags: TelegramFlags::none(),
        };

        let mut recv = self.timeprog.receiver().unwrap();
        recv.get().await; // mark old timeprog as seen
        for _ in 1..=3 {
            ensure!(self.request(telegram.clone(), 3, slot).await.is_okay());

            info!("Waiting for timeprog to arrive: addr=0x{:X}, day={}, number={}, ty={}, instance={}", dest, day, number, ty, instance);

            match with_timeout(secs(2), recv.changed()).await {
                Ok(tp) => return Ok(tp),
                Err(_) => {
                    info!("Timed out waiting for timeprog, retrying");
                }
            }
        }

        Err(anyhow!("too many timeouts waiting for timeprog response"))
    }

    #[allow(clippy::too_many_arguments)]
    pub async fn write_timeprog(
        &self,
        dest: u8,
        day: u8,
        number: u8,
        ty: u8,
        instance: u8,
        payload: [u8; 20],
        slot: &ReturnSlot<EbusReply>,
    ) -> Result<EbusReply> {
        ensure!(day <= 6);

        let mut buf = heapless::Vec::<u8, 24>::new();
        buf.extend_from_slice(&[day, number, ty, instance]).unwrap();
        buf.extend_from_slice(&payload).unwrap();

        let telegram = MasterTelegram {
            telegram: Telegram {
                src: self.own_address,
                dest,
                service: 0x03F3,
                data: Buffer::from_slice(buf.as_slice()),
            },
            flags: TelegramFlags::none(),
        };

        Ok(self.request(telegram, 20, slot).await)
    }

    // requests
    // 0704 discovery
    // 5023 -> info num write 1/5
    // 03f1 timeprog read master-master
    // 03f3 timeprog write master-slave
    // 4080 info num write parameter air handling
    //
    // receive
    // 03f2 master master
}

// pub fn start_ebus_activity_handler() {
//     env::spawn(ebus_activity_handler());
// }

// pub async fn ebus_activity_handler() {
//     let mut ebus_active = false;
//     loop {
//         select! {
//             _ = ebus().activity_recv.listen().fuse() => {
//                 Timer::after(Duration::from_secs(1)).await;
//                 if ebus_active {
//                     continue;
//                 }
//                 info!("ebus was connected");
//                 // ebus activity includes messages that were sent from our
//                 // device, so when we try to turn on the led on activity, it
//                 // will turn on even if we send a message instead of activity
//                 // coming from connected devices.
//                 //env::interfaces().led().enable(LedState::EbusConnected).await;
//                 ebus_active = true;
//             },
//             _ = Timer::after(Duration::from_secs(10)).fuse() => {
//                 if ebus_active {
//                     info!("ebus was disconnected");
//                     env::interfaces().led().disable(LedState::EbusConnected).await;
//                 }
//                 loop {
//                     select_biased! {
//                         _ = ebus().activity_recv.listen().fuse() => {
//                             break;
//                         }
//                         res = ebus().receiver.recv().fuse() => {
//                             if let Ok(req) = res {
//                                 let _ = req.sender.try_send(EbusReply::Timeout);
//                             }
//                         }
//                     }
//                 }
//                 ebus_active = false;
//             },
//         }
//     }
// }

const EBUS_ADDRESSES: &[u8] = &[
    0x8, 0x18, 0x38, 0x78, 0xF8, 0x35, 0x75, 0xF5, 0x1C, 0x3C, 0x7C, 0xFC, 0x24, 0x51, 0x52, 0x53,
    0x54, 0x55, 0x56, 0x57, 0x76, 0x15,
];

pub async fn ebus_discovery_task() {
    handle_system_identification().await;
}

pub struct Undetected([u32; 16]);

impl Undetected {
    pub fn new() -> Self {
        // all bits = 1 ⇒ each two-bit field = 0b11 (no longer detected)
        Undetected([u32::MAX; 16])
    }

    pub fn get(&self, addr: u8) -> u32 {
        // addr is 0..=255 so idx = addr/16 = addr>>4 in 0..=15
        let idx = (addr >> 4) as usize;
        // low 4 bits * 2 ⇒ bit offset within that u32
        let shift = ((addr & 0x0F) as u32) * 2;
        (self.0[idx] >> shift) & 0b11
    }

    pub fn set(&mut self, addr: u8, value: u32) {
        debug_assert!(value < 4, "value must be 0..=3");

        let idx = (addr >> 4) as usize;
        let shift = ((addr & 0x0F) as u32) * 2;

        // build a mask that has 0s in our target bits, 1s everywhere else
        let clear_mask = !(0b11 << shift);
        // zero out those two bits, then OR in the new 2-bit value
        self.0[idx] = (self.0[idx] & clear_mask) | ((value & 0b11) << shift);
    }
}

async fn handle_system_identification() {
    let mut undetected = Undetected::new();

    let start = Instant::now();
    // run first identification immediately
    identify_all(&mut undetected, true).await;

    if state().test_mode() {
        loop {
            identify_all(&mut undetected, false).await;
        }
    }

    // next one after 150 seconds
    Timer::at(start + secs(150)).await;

    let mut interval = Ticker::every(mins(10));
    loop {
        identify_all(&mut undetected, false).await;
        // finally, do it every 10 minutes
        interval.next().await;
    }
}

async fn identify_all(undetected: &mut Undetected, initial_discovery: bool) {
    // TODO should we run identification at all if we don't see SYN?
    // select_biased! {
    //     _ = ebus().activity_recv.listen().fuse() => (),
    //     _ = Timer::after(Duration::from_secs(30)).fuse() => (),
    // }

    info!("Running ebus identification");

    // increase all previously detected devices' undetected count
    for &addr in EBUS_ADDRESSES {
        let st = undetected.get(addr);
        if st != 0b11 {
            undetected.set(addr, st + 1);
        }
    }

    if initial_discovery {
        state().led.enable(LedBits::EbusDiscovery);
    }

    let mut devices = state().ebus.devices.get();
    let mut changed = initial_discovery;

    let slot = ReturnSlot::new();
    for &addr in EBUS_ADDRESSES {
        if let Some(ident) = ebus().identify(addr, &slot).await {
            undetected.set(addr, 0);
            let ndev = EbusDevice {
                bus_address: addr.into(),
                software_version: ident.software_version.into(),
                software_revision: ident.software_revision.into(),
                config: ident.config.into(),
                device_id: ident.device_id.into(),
            };
            if let Some(dev) = devices.get_mut_by_address(addr) {
                changed |= *dev != ndev;
                *dev = ndev;
            } else {
                info!(
                    "Found ebus device: addr=0x{:X}, ident={}",
                    addr,
                    Debug2Format(&ident)
                );
                devices.insert(ndev);
                changed = true;
                Event::BusDiscover(BusDevice {
                    device_id: addr as u16,
                    bus: Bus::Ebus,
                })
                .log()
                .await;
            }
        }
    }

    for &addr in EBUS_ADDRESSES {
        let st = undetected.get(addr);
        if matches!(st, 0b01 | 0b10) {
            warn!(
                "Got no response for 0704 from previously detected ebus device: addr=0x{:X}",
                addr
            );
            info!("Performing second chance ident");
            if ebus().identify(addr, &slot).await.is_some() {
                undetected.set(addr, 0);
            }
        }
        if undetected.get(addr) > 1 {
            warn!("Removing address from serviced addresses: addr={:X}", addr);
            devices.remove_by_address(addr);
            changed = true;
            //Event::BusDisconnect(BusDevice {
            //    device_id: addr as u16,
            //    bus: Bus::Ebus,
            //})
            //.log()
            //.await;
        }
    }

    let num_devices = devices.bus_devices.len();
    info!("Discovered ebus devices: num_devices={}", num_devices);

    // TODO: this state probably needs rethinking: state "connected" should be renamed to "devices
    // found", new state "connected" specifically for when ebus did not complete the scan yet but
    // knows that connection exists
    let mut guard = env::acquire_website_state().await;
    let ebus_state = match num_devices {
        0 => BusState::NotConnected,
        _ => BusState::Connected,
    };
    guard.ebus_state = ebus_state;
    drop(guard);

    if initial_discovery {
        state().led.disable(LedBits::EbusDiscovery);
    }

    match num_devices {
        0 => state().led.disable(LedBits::EbusConnected),
        _ => state().led.enable(LedBits::EbusConnected),
    }

    if changed {
        state().ebus.devices.update(devices);
    }
}

// const ERROR_HISTORY_ADDRESSES: &[u8] = &[
//     0x8, 0x18, 0x38, 0x78, 0xF8, 0x35, 0x75, 0xF5, 0x1C, 0x3C, 0x7C, 0xFC, 0x24, 0x51, 0x52, 0x53,
//     0x54, 0x56, 0x57, 0x76, 0x15, /* workaround for CWL */
//     0x8B,
// ];

pub struct HistoryData {
    pub histories: Mutex<ErrorHistory>,
}

impl HistoryData {
    pub fn new() -> Self {
        HistoryData {
            histories: Mutex::new(Default::default()),
        }
    }
}

impl Default for HistoryData {
    fn default() -> Self {
        Self::new()
    }
}

const NUM_HISTORY_ENTRIES: usize = 10;

#[derive(Clone, Copy, Default, Deserialize, Hash, Serialize)]
pub struct ErrorHistory {
    pub tag: VersionTag,
    /// entries with code 0 are empty
    pub entries: [ErrorEntry; NUM_HISTORY_ENTRIES],
    /// last written entry
    pub cursor: usize,
}

impl ErrorHistory {
    /// Iterate entries from youngest to oldest
    pub fn entries(&self) -> impl Iterator<Item = (usize, &ErrorEntry)> {
        (0..NUM_HISTORY_ENTRIES)
            .map(|i| (self.cursor + i + 1) % NUM_HISTORY_ENTRIES)
            .map(|i| &self.entries[i])
            .filter(|e| e.code != 0)
            .rev()
            .enumerate()
    }
}

#[derive(Clone, Copy, Default, Hash)]
pub struct VersionTag;

const MAGIC_PREFIX: u64 = 0xac66639e4a38ddc5;
const HISTORY_SCHEMA_VERSION: u64 = 2;

impl Serialize for VersionTag {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        let mut tuple = serializer.serialize_tuple(2)?;
        tuple.serialize_element(&MAGIC_PREFIX)?;
        tuple.serialize_element(&HISTORY_SCHEMA_VERSION)?;
        tuple.end()
    }
}

impl<'de> Deserialize<'de> for VersionTag {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let (magic, version) = <(u64, u64)>::deserialize(deserializer)?;

        if magic != MAGIC_PREFIX {
            return Err(<D::Error as serde::de::Error>::custom(
                "expected magic prefix",
            ));
        }
        if version != HISTORY_SCHEMA_VERSION {
            return Err(<D::Error as serde::de::Error>::custom(
                "expected current version",
            ));
        }

        Ok(VersionTag)
    }
}

#[derive(Clone, Copy, Default, Deserialize, Hash, Serialize)]
pub struct ErrorEntry {
    pub addr: u8,
    pub code: u16,
    pub start: Option<ClockTime>,
    pub end: Option<ClockTime>,
    pub active: bool,
}

pub async fn error_history_collection_task() {
    let bytes = nvs::error_history::get().await;
    if !bytes.is_empty() {
        if let Ok(histories) = postcard::from_bytes::<ErrorHistory>(&bytes) {
            *env::shared().ebus_history().histories.lock().await = histories;
            info!("Restored fault history from NVS");
        }
    }

    let mut ticker = Ticker::every(secs(60));
    loop {
        ticker.next().await;
        let mut histories = env::shared().ebus_history().histories.lock().await;
        if let Err(e) = collect_error_history(&mut histories).await {
            warn!("Failed to collect error history: err={}", e);
        }
    }
}

pub async fn collect_error_history(history: &mut ErrorHistory) -> Result<()> {
    static_slot!(COLLECT_ERROR_HISTORY, EbusReply);

    let current_time = state().ebus.current_time();
    let devices = state().ebus.devices.try_get().unwrap_or_default();
    let devices = &devices.bus_devices;

    let mut changed = false;
    for device in devices.iter() {
        // Detect CWL based on config index
        let addr = if matches!(device.bus_address.0, 0x3C | 0x7C)
            && matches!(device.config.0 & 0xFF, 0x21 | 0x22)
        {
            // special address provided by BM for CWL errors
            0x8B
        } else {
            device.bus_address.0
        };

        let Ok(active_error) = ebus()
            .read_info_num(0x5022, &[1], addr, &COLLECT_ERROR_HISTORY, Duration::MIN)
            .await
        else {
            error!("Failed to read active error: addr={:X}", addr);
            continue;
        };
        let Some(active_error) = active_error.first() else {
            continue;
        };
        let active_error = active_error.as_value();

        // we assume that every error we've seen is active
        // until no errors are active
        if active_error == 0 {
            // clear active errors
            for entry in &mut history.entries {
                if entry.addr == addr && entry.active {
                    entry.end = current_time;
                    entry.active = false;
                    changed = true;
                }
            }
        } else if !history
            .entries
            .iter()
            .filter(|entry| entry.addr == addr)
            .any(|entry| entry.active && entry.code == active_error)
        {
            // new error
            history.cursor = (history.cursor + 1) % history.entries.len();
            history.entries[history.cursor] = ErrorEntry {
                addr,
                code: active_error,
                start: current_time,
                end: None,
                active: true,
            };
            changed = true;
        }
    }

    if changed {
        info!("Storing changed fault history");
        let bytes = vec![];
        let bytes =
            postcard::to_extend(history, bytes).context(intern!("serialize error history"))?;
        nvs::error_history::set(bytes).await;
    }

    Ok(())
}

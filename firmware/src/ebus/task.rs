use ebus::{EbusDriver, ProcessResult, Transmit};
use enumset::enum_set;
use esp_idf_svc::{
    hal::{
        cpu::Core,
        delay::{self, Ets},
        gpio::{self, InputPin, OutputPin},
        interrupt::IntrFlags,
        peripheral::Peripheral,
        sys::EspError,
        task::block_on,
        uart::{
            self,
            config::{DataBits, EventConfig, EventFlags, FlowControl, Parity, StopBits},
            Uart, UartDriver, UartEventPayload, UartRxDriver, UartTxDriver,
        },
        units::Hertz,
    },
    sys::TickType_t,
};

use super::api::{EbusInterface, EbusReply, EbusRequest};
use crate::{
    flash::eventlog::events::{Event, ResetTrigger},
    prelude::*,
    utils::task,
};

pub const ARBITRATION_DELAY: std::time::Duration = std::time::Duration::from_micros(272);
pub const TELEGRAM_CRC: u8 = 0x9B;
pub const DATA_CRC: u8 = 0x5C;

pub const EBUS_UART_CONFIG: uart::config::Config = uart::config::Config {
    baudrate: Hertz(2400),
    data_bits: DataBits::DataBits8,
    parity: Parity::ParityNone,
    stop_bits: StopBits::STOP1,
    flow_control: FlowControl::None,
    intr_flags: enum_set!(IntrFlags::Level2),
    rx_fifo_size: 200,
    tx_fifo_size: 0,
    queue_size: 32,
    event_config: EventConfig {
        receive_timeout: None,
        rx_fifo_full: Some(1),
        tx_fifo_empty: Some(1),
        flags: enum_set!(EventFlags::RxFifoOverflow | EventFlags::TxDone),
        _non_exhaustive: (),
    },
    ..uart::config::Config::new()
};

pub fn create_ebus_task<UART: Uart>(
    uart: impl Peripheral<P = UART> + Send + 'static,
    tx: impl Peripheral<P = impl OutputPin> + Send + 'static,
    rx: impl Peripheral<P = impl InputPin> + Send + 'static,
) -> Result<()> {
    info!("Spawning ebus task");

    task::spawn_pinned("ebus", 7_000, 10, Some(Core::Core1), move || {
        let mut driver = UartDriver::new(
            uart,
            tx,
            rx,
            Option::<gpio::Gpio0>::None,
            Option::<gpio::Gpio1>::None,
            &EBUS_UART_CONFIG,
        )
        .unwrap();

        for _ in 0..10 {
            let Err(err) = block_on(ebus_task(&mut driver));
            error!("Failed to run ebus task: err={}", err);
            info!("Restarting ebus task");
        }
        error!("Rebooting due to unrecoverable ebus task");
        let ev = Event::ResetTrigger(ResetTrigger::BusFailure);
        hal::task::block_on(ev.log());
        esp_idf_svc::hal::reset::restart();
    });

    Ok(())
}

struct RxWrapper<'a> {
    rx: UartRxDriver<'a>,
    buf: Vec<u8>,
}

impl RxWrapper<'_> {
    fn read(&mut self, buf: &mut [u8], delay: TickType_t) -> Result<usize, EspError> {
        let overlap = self.buf.len().min(buf.len());
        buf[..overlap].copy_from_slice(&self.buf[..overlap]);
        self.buf.drain(..overlap);
        let n = if overlap < buf.len() {
            self.rx.read(&mut buf[overlap..], delay).or_else(|e| {
                if e.code() == sys::ESP_ERR_TIMEOUT {
                    Ok(0)
                } else {
                    Err(e)
                }
            })?
        } else {
            0
        };

        Ok(n + overlap)
    }

    fn peek(&mut self, buf: &mut [u8], delay: TickType_t) -> Result<usize, EspError> {
        let overlap = self.buf.len().min(buf.len());
        buf[..overlap].copy_from_slice(&self.buf[..overlap]);
        let n = if overlap < buf.len() {
            let n = self.rx.read(&mut buf[overlap..], delay).or_else(|e| {
                if e.code() == sys::ESP_ERR_TIMEOUT {
                    Ok(0)
                } else {
                    Err(e)
                }
            })?;
            self.buf.extend_from_slice(&buf[overlap..overlap + n]);
            n
        } else {
            0
        };

        Ok(n + overlap)
    }
}

// prevent inlining for backtraces
#[inline(never)]
pub async fn ebus_task(driver: &mut UartDriver<'static>) -> Result<!> {
    let mut tx_reply = |next_msg: &mut NextMsg, reply| {
        if next_msg.is_none() {
            warn!("potential bug in ebus state machine, got process result but no active ebus request");
            return;
        }

        if matches!(reply, EbusReply::AckError | EbusReply::Timeout) {
            let retries = &mut next_msg.as_mut().unwrap().0.retries;
            if *retries > 0 {
                *retries -= 1;
                // Retry, do not clear message
                return;
            }
        }

        let (request, slot) = next_msg.take().unwrap();
        let dst = request.telegram.telegram.dest;
        slot.ret(reply);
        debug!("Reported ebus reply: dest=0x{:02X}", dst);
    };

    driver.clear_rx()?;
    let (mut tx, rx) = driver.split();
    let mut rx = RxWrapper {
        rx,
        buf: Vec::default(),
    };

    let mut ebus = EbusDriver::new(ARBITRATION_DELAY, TELEGRAM_CRC, DATA_CRC, 0);
    let mut buf = [0; 32];

    let mut next_msg = None;
    let mut last_syn = Instant::now();

    loop {
        next_msg = next_msg.or_else(|| itc().ebus.try_recv());

        let event = rx
            .rx
            .event_queue()
            .unwrap()
            .recv_front(delay::BLOCK)
            .unwrap();
        let payload = event.0.payload();

        match payload {
            UartEventPayload::Data { .. } => {}
            UartEventPayload::RxFifoOverflow => {
                warn!("Detected overflow of UART fifo");
                rx.rx.clear()?;
                ebus.reset_wait_syn();
                continue;
            }
            _ => {
                continue;
            }
        }

        let n = match rx.read(&mut buf, hal::delay::NON_BLOCK) {
            Ok(0) | Err(_) => {
                continue;
            }
            Ok(n) => n,
        };

        if n > 1 {
            info!(
                "Got unexpected lag, found elements in rx fifo: n_elements={}",
                n
            );
        }

        let is_low_latency = n == 1;

        for &byte in &buf[..n] {
            handle_byte(
                byte,
                &mut next_msg,
                &mut rx,
                &mut tx,
                &mut ebus,
                is_low_latency,
                &mut tx_reply,
            )
            .await?;
        }

        if buf[..n].contains(&0xAA) && last_syn.elapsed().as_secs() > 0 {
            last_syn = Instant::now();
            state().ebus.last_syn.update(last_syn);
        }
    }
}

type NextMsg = Option<(EbusRequest, ReturnSlot<EbusReply>)>;

#[allow(clippy::too_many_arguments)]
async fn handle_byte(
    byte: u8,
    next_msg: &mut NextMsg,
    rx: &mut RxWrapper<'_>,
    tx: &mut UartTxDriver<'_>,
    ebus: &mut EbusDriver,
    is_low_latency: bool,
    mut tx_reply: impl FnMut(&mut NextMsg, EbusReply),
) -> Result<(), EspError> {
    let msg = next_msg.as_ref().map(|m| &m.0.telegram);
    let result = ebus.process(byte, &mut UartTransmit(tx), sleep, msg, is_low_latency)?;

    match result {
        ProcessResult::None => {}
        ProcessResult::SlaveAckOk => {
            debug!("Got acknowledgment from master for our reply");
        }
        ProcessResult::SlaveAckErr => {
            debug!("Did not get acknowledgement of master for our reply");
        }
        ProcessResult::MasterAckOk => {
            debug!("Got acknowledgment from slave for our reply");
            // this means there is not gonna be reply
            tx_reply(next_msg, EbusReply::Ok);
        }
        ProcessResult::MasterAckErr => {
            debug!("Did not get acknowledgement of slave for our reply");
            // this means there is not gonna be reply
            tx_reply(next_msg, EbusReply::AckError);
        }
        ProcessResult::Timeout => {
            // got SYN before response
            tx_reply(next_msg, EbusReply::Timeout);
        }
        ProcessResult::TelegramCrcError => {
            // TODO: send ACK_ERR?
        }
        ProcessResult::ReplyCrcError => {
            // TODO: send ACK_ERR?
            warn!("Failed to check crc of a reply of a slave");
            // we keep the message retry
            tx_reply(next_msg, EbusReply::AckError);
        }
        ProcessResult::Request { telegram, token } => {
            debug!(
                "Read message: svc=0x{:X}, from=0x{:X}, to=0x{:X}",
                telegram.service, telegram.src, telegram.dest,
            );

            let reply = EbusInterface::get().process_incoming(telegram);
            let Some(reply) = reply else {
                return Ok(());
            };

            debug!("Replying to ebus request");

            match reply {
                EbusReply::Ok => ebus.reply_ack(&mut UartTransmit(tx), token)?,
                EbusReply::Reply(bytes) => {
                    ebus.reply_as_slave(&bytes, &mut UartTransmit(tx), token)?
                }
                _ => defmt::panic!("bad ebus reply"),
            }
        }
        ProcessResult::VetReply { timeout_ms } => {
            if rx.peek(
                &mut [0],
                hal::delay::TickType::new_millis(timeout_ms as u64).ticks(),
            )? == 0
            {
                ebus.vet_timeout(&mut UartTransmit(tx))?;
            }
        }
        ProcessResult::Reply { data, clean } => {
            debug!("Received reply: bytes={}", data.as_bytes());
            let con = if clean {
                EbusReply::Reply
            } else {
                EbusReply::ReplyUnclean
            };
            tx_reply(next_msg, con(Vec::from(data.as_bytes())));
        }
    }

    Ok(())
}

struct UartTransmit<'a, 'd>(&'a mut UartTxDriver<'d>);

impl Transmit for UartTransmit<'_, '_> {
    type Error = EspError;

    fn clear_buffer(&mut self) -> Result<(), Self::Error> {
        Ok(())
    }

    fn transmit_raw(&mut self, mut bytes: &[u8]) -> Result<(), Self::Error> {
        loop {
            if bytes.is_empty() {
                break;
            }
            let written = self.0.write(bytes)?;
            bytes = &bytes[written..];
        }
        //info!("Transmitted bytes: bytes={:X}", bytes_cpy);

        Ok(())
    }
}

fn sleep(dur: std::time::Duration) {
    let micros = dur.subsec_micros();
    Ets::delay_us(micros);
}

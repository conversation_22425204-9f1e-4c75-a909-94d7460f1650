use std::collections::BTreeMap;

use crate::{ebus::api::InfoNum, prelude::*};

#[derive(Default)]
pub struct EbusDatabase {
    info_nums: BTreeMap<EbusIdent, EbusValue>,
}

impl EbusDatabase {
    pub const fn new() -> Self {
        EbusDatabase {
            info_nums: BTreeMap::new(),
        }
    }

    // pub fn get(&self, ident: EbusIdent, max_age: Duration) -> Option<InfoNum> {
    //     self.info_nums
    //         .get(&ident)
    //         .filter(|v| v.age.elapsed().as_secs() <= max_age.as_secs())
    //         .map(|v| v.value)
    // }

    pub fn get_all<'s: 'a, 'a>(
        &'s self,
        ba: u8,
        nums: &'a [u16],
        svc: u16,
        max_age: Duration,
    ) -> heapless::Vec<InfoNum, 5> {
        if max_age == Duration::MIN
            || !nums.iter().all(|num| {
                self.info_nums
                    .get(&EbusIdent::new(ba, *num, svc))
                    .map(|v| v.age.elapsed().as_secs() <= max_age.as_secs())
                    .unwrap_or(false)
            })
        {
            return heapless::Vec::new();
        }

        nums.iter()
            .flat_map(move |num| self.info_nums.get(&EbusIdent::new(ba, *num, svc)))
            .map(|v| v.value)
            .collect()
    }

    pub fn set(&mut self, ident: EbusIdent, value: InfoNum) {
        if self.info_nums.len() > 1024 {
            warn!("Clearing ebus cache that has more than 1024 datapoints");
            self.clear();
        }

        match self.info_nums.entry(ident) {
            std::collections::btree_map::Entry::Vacant(v) => {
                v.insert(EbusValue {
                    value,
                    age: Instant::now(),
                });
            }
            std::collections::btree_map::Entry::Occupied(mut o) => {
                let entry = o.get_mut();
                entry.age = Instant::now();
                entry.value = value;
            }
        }
    }

    pub fn erase(&mut self, ident: EbusIdent) {
        self.info_nums.remove(&ident);
    }

    pub fn clear(&mut self) {
        self.info_nums.clear();
    }
}

#[derive(Clone, Copy, Debug, Eq, PartialEq, PartialOrd, Ord)]
pub struct EbusIdent {
    pub address: u8,
    pub info_num: u16,
    #[doc(hidden)]
    pub _private: (),
}

impl EbusIdent {
    pub fn new(address: u8, info_num: u16, service: u16) -> Self {
        match service {
            0x5022 | 0x5023 => EbusIdent {
                address,
                info_num,
                _private: (),
            },
            0x4022 => EbusIdent {
                address,
                info_num,
                _private: (),
            },
            0x4050 | 0x4080 => EbusIdent {
                address,
                info_num: info_num | 0x100,
                _private: (),
            },
            _ => defmt::unimplemented!(),
        }
    }
}

#[derive(Clone, Copy, Debug)]
pub struct EbusValue {
    pub value: InfoNum,
    pub age: Instant,
}

use bitflags::bitflags;
use defmt::Format;
use embassy_futures::select::{select, Either};
use esp_idf_svc::hal::{
    gpio::{self, Input, Level, Output, PinDriver},
    peripheral::Peripheral,
};
use futures_lite::future::FutureExt;

use crate::{
    flash::eventlog::events::{Event, ResetTrigger},
    net::task::NetworkRequest,
    prelude::*,
};

pub struct Leds {
    bus: PinDriver<'static, gpio::Gpio2, Output>,
    wlan: PinDriver<'static, gpio::Gpio12, Output>,
    portal: PinDriver<'static, gpio::Gpio13, Output>,
}

impl Leds {
    pub fn new(
        bus: impl Peripheral<P = gpio::Gpio2> + 'static,
        wlan: impl Peripheral<P = gpio::Gpio12> + 'static,
        portal: impl Peripheral<P = gpio::Gpio13> + 'static,
    ) -> Self {
        Self {
            bus: PinDriver::output(bus).unwrap(),
            wlan: PinDriver::output(wlan).unwrap(),
            portal: PinDriver::output(portal).unwrap(),
        }
    }

    pub async fn bootup_sequence(&mut self) -> Result<()> {
        self.set_all(true)?;
        if state().test_mode() {
            info!("Keeping LEDs lit due to test mode");
        } else {
            Timer::after(millis(100)).await;
            self.set_all(false)?;
        }
        Ok(())
    }

    pub fn set(&mut self, led: Led, level: bool) -> Result<()> {
        match led {
            Led::Bus => Ok(self.bus.set_level(level.into())?),
            Led::Wlan => Ok(self.wlan.set_level(level.into())?),
            Led::Portal => Ok(self.portal.set_level(level.into())?),
        }
    }

    pub fn set_all(&mut self, level: bool) -> Result<()> {
        self.bus.set_level(level.into())?;
        self.wlan.set_level(level.into())?;
        self.portal.set_level(level.into())?;
        Ok(())
    }
}

pub async fn led_btn_task(
    mut btn: PinDriver<'static, gpio::Gpio34, Input>,
    mut leds: Leds,
) -> Result<!> {
    loop {
        match btn.get_level() {
            Level::High => {
                async {
                    btn.wait_for_low()
                        .await
                        .context(intern!("wait for btn low"))
                }
                .or(show_normal_leds(&mut leds))
                .await?
            }
            Level::Low => show_btn_leds(&mut leds, &mut btn).await?,
        }
    }
}

async fn show_normal_leds(leds: &mut Leds) -> Result<()> {
    let state = state().led.snapshot();
    for time in 0..LED_MAPPING[0].0.len() {
        let mut values = [false; 3];
        for flag in state.iter() {
            let item = &LED_MAPPING[flag.bits().trailing_zeros() as usize];
            values[item.1 as usize] = item.0[time] != 0;
        }

        leds.set(Led::Bus, values[Led::Bus as usize])?;
        leds.set(Led::Wlan, values[Led::Wlan as usize])?;
        leds.set(Led::Portal, values[Led::Portal as usize])?;

        delay(LED_TIMING_MS).await;
    }

    Ok(())
}

async fn show_btn_leds(
    leds: &mut Leds,
    btn: &mut PinDriver<'static, gpio::Gpio34, Input>,
) -> Result<()> {
    let mut action = ButtonAction::AccessPoint;

    for entry in &BTN_MAPPING {
        leds.set(Led::Bus, entry.2[Led::Bus as usize])?;
        leds.set(Led::Wlan, entry.2[Led::Wlan as usize])?;
        leds.set(Led::Portal, entry.2[Led::Portal as usize])?;

        match select(btn.wait_for_high(), delay(entry.1)).await {
            Either::First(_) => {
                action = entry.0;
                break;
            }
            Either::Second(_) => (),
        }
    }

    info!("Pressed action={}", action);
    match action {
        ButtonAction::AccessPoint => {
            nvs::ap_enabled::set(true).await;
            itc().net.send(NetworkRequest::ReloadAccessPoint).await
        }
        ButtonAction::StaticIP => itc().net.send(NetworkRequest::StaticIP).await,
        ButtonAction::Wps => itc().net.send(NetworkRequest::Wps).await,
        ButtonAction::Restart => {
            let _ = leds.set_all(false);
            Event::ResetTrigger(ResetTrigger::Button).log().await;
            itc().eventlog.flush(&ReturnSlot::new()).await;
            hal::reset::restart();
        }
        ButtonAction::FactoryReset => nvs::api::factory_reset().await,
    }

    // keep leds lit after release
    delay(BTN_LED_RELEASE_MS).await;

    Ok(())
}

async fn delay(msec: u32) {
    Timer::after(millis(msec as u64)).await;
}

// ===== normal mapping =====
//
//  action              | red        | yellow | green
// ---------------------+------------+--------+-------
//  portal connected    | -          | -      | on
//  portal connecting   | -          | -      | blink
//  bus connected       | -          | on     | -
//  first discovery     | -          | blink  | -
//  wifi connected      | on         | -      | -
//  establishing wifi   | blink      | -      | -
//  wps active          | fast blink | -      | -
//
//
// ===== timing =====
//
// time [ms]:    0    160   320   480   640   800   960
//               |     |     |     |     |     |     |
// blink:        |XXXXXXXXXXXXXXXXX|     |     |     |    (0.48s on, 0.48s off)   <- original wolf link: 0.50s on, 0.50s off
// fast blink:   |XXXXX|     |XXXXX|     |XXXXX|     |    (0.16s on, 0.16s off)   <- original wolf link: 0.20s on, 0.20s off

bitflags! {
    // XXX: Do not change the values of this without changing LED_MAPPING! The number of trailing
    //      zeroes is used as the index for LED_MAPPING. Constants with higher values take priority
    //      (that is, they determine what state the LED should have) over ones with smaller values.
    pub struct LedBits: u16 {
        const PortalConnected = 1 << 0;
        const PortalConnecting = 1 << 1;
        const EbusConnected = 1 << 2;
        const ModbusConnected = 1 << 3;
        const EbusDiscovery = 1 << 4;
        const ModbusDiscovery = 1 << 5;
        const WifiConnected = 1 << 6;
        const WpsActive = 1 << 7;
        const WifiConnecting = 1 << 8;
        const _OtwUnderWayYellow = 1 << 9;
        const _OtwUnderWayRed = 1 << 10;
        const _OtwUnderWayGreen = 1 << 11;
        const OtwUnderWay = (1 << 9) | (1 << 10) | (1 << 11);
    }
}

const LED_TIMING_MS: u32 = 80;

#[rustfmt::skip]
const LED_MAPPING: [([u8; 12], Led); 12] = [
    /* PortalConnected   */ ([1,1,1,1,1,1,1,1,1,1,1,1], Led::Portal),
    /* PortalConnecting  */ ([1,1,1,1,1,1,0,0,0,0,0,0], Led::Portal),
    /* EbusConnected     */ ([1,1,1,1,1,1,1,1,1,1,1,1], Led::Bus),
    /* ModbusConnected   */ ([1,1,1,1,1,1,1,1,1,1,1,1], Led::Bus),
    /* EbusDiscovery     */ ([1,1,1,1,1,1,0,0,0,0,0,0], Led::Bus),
    /* ModbusDiscovery   */ ([1,1,1,1,1,1,0,0,0,0,0,0], Led::Bus),
    /* WifiConnected     */ ([1,1,1,1,1,1,1,1,1,1,1,1], Led::Wlan),
    /* WpsActive         */ ([1,1,0,0,1,1,0,0,1,1,0,0], Led::Wlan),
    /* WifiConnecting    */ ([1,1,1,1,1,1,0,0,0,0,0,0], Led::Wlan),
    /* OtwUnderWay (Y)   */ ([0,1,1,1,0,0,0,0,0,1,1,1], Led::Bus),
    /* OtwUnderWay (R)   */ ([0,0,1,1,1,0,0,0,1,1,1,0], Led::Wlan),
    /* OtwUnderWay (G)   */ ([0,0,0,1,1,1,0,1,1,1,0,0], Led::Portal),
];

const _: () = {
    std::assert!(
        LED_MAPPING.len() as u32 == LedBits::all().bits().trailing_ones(),
        "LED Mapping and LED bits have mismatching number of elements",
    );
};

#[derive(Copy, Clone, Debug, Format)]
enum ButtonAction {
    AccessPoint,
    StaticIP,
    Wps,
    Restart,
    FactoryReset,
}

// ===== button mapping =====
//
//  action            | red | yellow | green
// -------------------+-----+--------+-------
//  access point      | off | off    | off
//  static ip address | off | off    | on
//  start wps         | off | on     | on
//  restart           | on  | on     | off
//  factory reset     | on  | on     | on

const BTN_LED_RELEASE_MS: u32 = 1500;

#[rustfmt::skip]
const BTN_MAPPING: [(ButtonAction, u32, [bool; 3]); 5] = [
    /* action,                   msec_active, yellow, red,   green */
    (ButtonAction::AccessPoint,  1000,        [false, false, false]),
    (ButtonAction::StaticIP,     2000,        [false, false, true ]),
    (ButtonAction::Wps,          2000,        [true,  false, true ]),
    (ButtonAction::Restart,      5000,        [true,  true,  false]),
    (ButtonAction::FactoryReset, u32::MAX,    [true,  true,  true ]),
];

#[derive(Copy, Clone)]
pub enum Led {
    Bus = 0,
    Wlan = 1,
    Portal = 2,
}

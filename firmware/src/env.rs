use std::{
    future::Future,
    rc::Rc,
    sync::{Arc, OnceLock},
};

use async_executor::LocalExecutor;
use embedded_svc::wifi::AuthMethod;

use crate::{
    ebus::api::HistoryData,
    eebus::{ohpcf::Ohpcf, TrustDb},
    prelude::*,
    server::schema::{BusState, LocalConnectionState, PortalState},
};

static INTERFACES: Interfaces = Interfaces::new();
static SHARED: OnceLock<Shared> = OnceLock::new();
static WEBSITE_STATE: Mutex<WebsiteState> = Mutex::new(WebsiteState::const_default());
static ACTIVE_PARAMS: OnceLock<&'static ActiveParams> = OnceLock::new();

thread_local! {
    static EXECUTOR: Rc<LocalExecutor<'static>> = Rc::new(LocalExecutor::new());
}

pub struct Interfaces {
    pub ohpcf: Ohpcf,
}

impl Interfaces {
    pub const fn new() -> Self {
        Self {
            ohpcf: Ohpcf::new(),
        }
    }
}

pub struct Shared {
    ebus_history: HistoryData,
    trust_db: Arc<TrustDb>,
}

impl Shared {
    fn new() -> Self {
        Self {
            ebus_history: HistoryData::new(),
            trust_db: Arc::new(TrustDb::new()),
        }
    }

    pub fn ebus_history(&self) -> &HistoryData {
        &self.ebus_history
    }

    pub fn trust_db(&self) -> Arc<TrustDb> {
        self.trust_db.clone()
    }
}

#[derive(Default)]
pub struct WebsiteState {
    pub portal_state: PortalState,
    pub local_connection_state: LocalConnectionState,
    pub modbus_state: BusState,
    pub ebus_state: BusState,
}

impl WebsiteState {
    // required because const traits don't work amazingly well yet
    pub const fn const_default() -> Self {
        Self {
            portal_state: PortalState::NotConnected,
            local_connection_state: LocalConnectionState::NotConnected,
            modbus_state: BusState::NotConnected,
            ebus_state: BusState::NotConnected,
        }
    }
}

pub struct ActiveParams {
    pub password: heapless::String<64>,
    pub portal_enabled: bool,
    pub portal_host: heapless::String<58>,
    pub portal_port: u16,
    pub system_name: heapless::String<32>,
    pub ap_ssid: heapless::String<32>,
    pub hostname: heapless::String<30>,
    pub wifi_data: Mutex<WifiData>,
    pub dhcp: bool, // must be stored somewhere since esp_netif_dhcpc_get_status() is not particularly reliable
    pub eebus_enabled: bool,
}

pub struct WifiData {
    pub ssid: heapless::String<32>,
    pub pass: heapless::String<64>,
    pub auth_method: AuthMethod,
}

pub fn init_shared() {
    SHARED.set(Shared::new()).unwrap_or_else(|_| {
        defmt::panic!("Shared already initialized");
    });
}

pub async fn init_active_params() {
    let active_params = Box::leak(Box::new(ActiveParams {
        password: nvs::password::get_heapless().await,
        portal_enabled: nvs::portal_enabled::get().await,
        portal_host: nvs::smartset_host::get_heapless().await,
        portal_port: nvs::smartset_port::get().await,
        system_name: nvs::system_name::get_heapless().await,
        ap_ssid: nvs::ap_ssid::get_heapless().await,
        hostname: nvs::net_hostname::get_heapless().await,
        wifi_data: Mutex::new(WifiData {
            ssid: nvs::wifi_ssid::get_heapless().await,
            pass: nvs::wifi_pass::get_heapless().await,
            auth_method: nvs::wifi_authmethod::get().await,
        }),
        dhcp: nvs::net_dhcp::get().await,
        eebus_enabled: nvs::eebus_enabled::get().await,
    }));

    ACTIVE_PARAMS.set(active_params).unwrap_or_else(|_| {
        defmt::panic!("Active params already initialized");
    });
}

pub fn interfaces() -> &'static Interfaces {
    &INTERFACES
}

pub fn shared() -> &'static Shared {
    SHARED.get().unwrap()
}

pub fn executor() -> Rc<LocalExecutor<'static>> {
    EXECUTOR.with(Rc::clone)
}

pub fn spawn<T: 'static>(future: impl Future<Output = T> + 'static) {
    executor().spawn(future).detach();
}

pub async fn acquire_website_state() -> MutexGuard<'static, WebsiteState> {
    WEBSITE_STATE.lock().await
}

pub fn active_params() -> &'static ActiveParams {
    ACTIVE_PARAMS.get().unwrap()
}

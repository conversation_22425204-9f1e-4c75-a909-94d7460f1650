use core::fmt;

use nanoxml::derive::ser::SerXmlTopLevel;
use picoserve::{
    io::{Read, Write},
    response::{Body, Connection, Content, IntoResponse, Response, ResponseWriter},
    ResponseSent,
};

pub(crate) enum FormatBufferWriteError<T> {
    FormatError,
    OutOfSpace(T),
}

pub(crate) struct FormatBuffer {
    pub data: heapless::Vec<u8, 128>,
    pub ignore_count: usize,
    pub error_state: FormatBufferWriteError<()>,
}

impl fmt::Write for FormatBuffer {
    fn write_str(&mut self, s: &str) -> fmt::Result {
        for &b in s.as_bytes() {
            match self.ignore_count.checked_sub(1) {
                Some(ignore_count) => self.ignore_count = ignore_count,
                None => {
                    if self.data.push(b).is_err() {
                        self.error_state = FormatBufferWriteError::OutOfSpace(());
                        return Err(fmt::Error);
                    }
                }
            }
        }

        Ok(())
    }
}

impl FormatBuffer {
    pub fn new(ignore_count: usize) -> Self {
        Self {
            data: heapless::Vec::new(),
            ignore_count,
            error_state: FormatBufferWriteError::FormatError,
        }
    }
}

enum XmlStream<T> {
    Short { buffer: FormatBuffer },
    Long { buffer: FormatBuffer, value: T },
}

impl<T: SerXmlTopLevel> XmlStream<T> {
    fn new(value: T) -> Self {
        let mut buffer = FormatBuffer::new(0);
        match value.serialize(&mut buffer) {
            Ok(_) => XmlStream::Short { buffer },
            Err(_) => match buffer.error_state {
                FormatBufferWriteError::FormatError => XmlStream::Long {
                    buffer: FormatBuffer::new(0),
                    value,
                },
                FormatBufferWriteError::OutOfSpace(()) => XmlStream::Long { buffer, value },
            },
        }
    }
}

impl<T: SerXmlTopLevel> XmlStream<T> {
    async fn write_xml_value<W: Write>(self, writer: &mut W) -> Result<(), W::Error> {
        match self {
            XmlStream::Short { buffer } => writer.write_all(&buffer.data).await,
            XmlStream::Long { mut buffer, value } => {
                writer.write_all(&buffer.data).await?;

                let mut ignore_count = buffer.data.len();

                loop {
                    buffer.data.clear();
                    buffer.ignore_count = ignore_count;
                    buffer.error_state = FormatBufferWriteError::FormatError;

                    match value.serialize(&mut buffer) {
                        Ok(_) => return writer.write_all(&buffer.data).await,
                        Err(_) => match buffer.error_state {
                            FormatBufferWriteError::FormatError => {
                                return writer.write_all(b"\r\n\r\nFailed to serialize xml").await
                            }
                            FormatBufferWriteError::OutOfSpace(()) => {
                                writer.write_all(&buffer.data).await?;
                                ignore_count += buffer.data.len();
                            }
                        },
                    }
                }
            }
        }
    }
}

struct MeasureFormatSize(pub usize);

impl fmt::Write for MeasureFormatSize {
    fn write_str(&mut self, s: &str) -> fmt::Result {
        self.0 += s.len();

        Ok(())
    }
}

struct XmlBody<T>(XmlStream<T>);

impl<T: SerXmlTopLevel> Content for XmlBody<T> {
    fn content_type(&self) -> &'static str {
        "application/xml"
    }

    fn content_length(&self) -> usize {
        match &self.0 {
            XmlStream::Short { buffer } => buffer.data.len(),
            XmlStream::Long { buffer: _, value } => {
                let mut content_length = MeasureFormatSize(0);
                value
                    .serialize(&mut content_length)
                    .map_or(0, |_| content_length.0)
            }
        }
    }

    async fn write_content<W: Write>(self, mut writer: W) -> Result<(), W::Error> {
        self.0.write_xml_value(&mut writer).await
    }
}

/// Serializes the value in xml form. The value might be serialized several times during sending, so the value must be serialized in the same way each time.
pub struct Xml<T>(pub T);

impl<T: SerXmlTopLevel> Xml<T> {
    /// Convert xml payload into a [super::Response] with a status code of "OK"
    pub fn into_response(self) -> Response<impl picoserve::response::HeadersIter, impl Body> {
        Response::ok(XmlBody(XmlStream::new(self.0)))
    }
}

impl<T: SerXmlTopLevel> IntoResponse for Xml<T> {
    async fn write_to<R: Read, W: ResponseWriter<Error = R::Error>>(
        self,
        connection: Connection<'_, R>,
        response_writer: W,
    ) -> Result<ResponseSent, W::Error> {
        response_writer
            .write_response(connection, self.into_response())
            .await
    }
}

impl<T: SerXmlTopLevel> core::future::IntoFuture for Xml<T> {
    type Output = Self;
    type IntoFuture = core::future::Ready<Self>;

    fn into_future(self) -> Self::IntoFuture {
        core::future::ready(self)
    }
}

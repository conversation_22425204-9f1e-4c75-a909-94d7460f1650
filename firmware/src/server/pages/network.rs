use embedded_svc::{ipv4::Ipv4Addr, wifi::AuthMethod};
use esp_idf_svc::ipv4::Mask;
use num_enum::TryFromPrimitive;
use picoserve::extract::Form;

use crate::{
    flash::eventlog::events::{ConfigAction, Event},
    net::task::NetworkRequest,
    prelude::*,
    server::util::{CheckedBool, Redirect},
    utils::{is_printable_ascii, is_valid_wep_password},
};

#[derive(serde::Deserialize)]
pub struct NetworkForm {
    name: String,
    ssid: Option<heapless::String<32>>,
    #[serde(rename = "sec")]
    auth_method: Option<u8>,
    #[serde(rename = "key")]
    pass: Option<heapless::String<63>>,
    dhcp: Option<CheckedBool>,
    ip: Option<Ipv4Addr>,
    #[serde(rename = "sub")]
    subnet: Option<Ipv4Addr>,
    #[serde(rename = "gw")]
    gateway: Option<Ipv4Addr>,
    dns1: Option<Ipv4Addr>,
    dns2: Option<Ipv4Addr>,
    #[serde(rename = "host")]
    hostname: Option<heapless::String<30>>,
    #[serde(rename = "wpip")]
    smartset_host: Option<heapless::String<58>>,
    #[serde(rename = "port")]
    smartset_port: Option<u16>,
    #[serde(rename = "ap")]
    ap_active: Option<CheckedBool>,
    #[serde(rename = "apssid")]
    ap_ssid: Option<heapless::String<32>>,
}

#[derive(serde::Deserialize)]
pub struct NetworkOptions {}

pub async fn post(form: Form<NetworkForm>) -> Redirect {
    match form.0.name.as_str() {
        "wlanKickoff" => post_wifi(form.0).await,
        "lanKickoff" => post_network(form.0).await,
        "apKickoff" => post_access_point(form.0).await,
        "wpsKickoff" => post_wps(form.0).await,
        _ => Redirect::to("/error.htm"),
    }
}

async fn post_wifi(form: NetworkForm) -> Redirect {
    let Some((ssid, auth_method)) = form.ssid.zip(form.auth_method) else {
        return Redirect::to("/error.htm");
    };

    let wep_pass_required = auth_method == AuthMethod::WEP as u8;
    let wpa_pass_required =
        !(auth_method == AuthMethod::None as u8 || auth_method == AuthMethod::WEP as u8);

    let Ok(auth_method) = AuthMethod::try_from_primitive(auth_method) else {
        return Redirect::to("/error.htm");
    };

    if ssid.is_empty()
        || ssid.len() > 32
        || !is_printable_ascii(&ssid)
        || ((wpa_pass_required || wep_pass_required) && form.pass.is_none())
        || (wpa_pass_required && form.pass.as_ref().unwrap().len() < 8)
        || (wpa_pass_required && form.pass.as_ref().unwrap().len() > 63)
        || (wpa_pass_required && !is_printable_ascii(form.pass.as_ref().unwrap()))
        || (wep_pass_required && !is_valid_wep_password(form.pass.as_ref().unwrap()))
    {
        return Redirect::to("/error.htm");
    }

    nvs::wifi_ssid::set(ssid.to_string()).await;
    nvs::wifi_authmethod::set(auth_method).await;
    nvs::wifi_pass::set(match auth_method {
        AuthMethod::None => String::new(),
        _ => form.pass.unwrap().to_string(),
    })
    .await;

    Event::Config(ConfigAction::ChangeWifi).log().await;

    Redirect::to("/protect/reboot.htm")
}

async fn post_network(form: NetworkForm) -> Redirect {
    let Some((dhcp, ip, subnet, gateway, dns1, dns2, hostname, smartset_host, smartset_port)) =
        (|| {
            Some((
                form.dhcp?,
                form.ip?,
                form.subnet?,
                form.gateway?,
                form.dns1?,
                form.dns2?,
                form.hostname?,
                form.smartset_host?,
                form.smartset_port?,
            ))
        })()
    else {
        return Redirect::to("/error.htm");
    };

    let subnet = subnet.to_bits();

    if smartset_host.is_empty()
        || dns1 == Ipv4Addr::new(0, 0, 0, 0)
        || dns2 == Ipv4Addr::new(0, 0, 0, 0)
        || !ip.is_private()
        || subnet.leading_ones() + subnet.trailing_zeros() != 32
        || hostname.is_empty()
        || hostname
            .chars()
            .any(|c| !(c.is_ascii_alphanumeric() || c == '-'))
    {
        return Redirect::to("/error.htm");
    }

    nvs::smartset_host::set(smartset_host.to_string()).await;
    nvs::smartset_port::set(smartset_port).await;
    nvs::net_ip::set(ip).await;
    nvs::net_mask::set(Mask(subnet.leading_zeros() as u8)).await;
    nvs::net_gateway::set(gateway).await;
    nvs::net_dns1::set(dns1).await;
    nvs::net_dns2::set(dns2).await;
    nvs::net_dhcp::set(dhcp.0).await;
    nvs::net_hostname::set(hostname.to_string()).await;

    let ev = match dhcp.0 {
        true => ConfigAction::EnableDhcp,
        false => ConfigAction::DisableDhcp,
    };
    Event::Config(ev).log().await;
    Redirect::to("/protect/reboot.htm")
}

async fn post_access_point(form: NetworkForm) -> Redirect {
    let Some((ssid, enabled)) = form.ap_ssid.zip(form.ap_active.map(|x| x.0)) else {
        return Redirect::to("/error.htm");
    };
    if ssid.is_empty() || ssid.len() > 32 || !is_printable_ascii(&ssid) {
        return Redirect::to("/error.htm");
    }

    let prev_ssid = nvs::ap_ssid::get().await;

    let reboot_required = prev_ssid.as_str() != ssid;
    nvs::ap_enabled::set(enabled).await;
    nvs::ap_ssid::set(ssid.to_string()).await;

    env::spawn(async {
        // wait a bit so that users don't get disconnected immediately
        Timer::after(secs(5)).await;
        itc().net.send(NetworkRequest::ReloadAccessPoint).await;
    });

    let ev = match enabled {
        true => ConfigAction::EnableAp,
        false => ConfigAction::DisableAp,
    };
    Event::Config(ev).log().await;

    match reboot_required {
        true => Redirect::to("/protect/reboot.htm"),
        false => Redirect::to("/protect/network.htm"),
    }
}

async fn post_wps(_form: NetworkForm) -> Redirect {
    info!("WPS enabled from website");
    itc().net.send(NetworkRequest::Wps).await;
    Redirect::to("/protect/network.htm")
}

use embedded_svc::ipv4::Ipv4Addr;
use esp_idf_svc::ipv4::IpInfo;

use crate::{
    prelude::*,
    server::{schema::*, util::CheckedBool, xml::Xml},
    state::net::{ApState, NetworkStateValue},
    utils::calc_subnet_mask,
};

pub async fn get(protect: bool) -> Xml<Status> {
    Xml(get_status(protect).await)
}

pub async fn get_status(protect: bool) -> Status {
    let net_state = state().net.state.get();
    let ap_state = state().net.request_ap_state_update().await;
    let ip_info = match net_state.ip_info() {
        Some(ip_info) => Some(*ip_info),
        None => ap_state.as_ref().map(|s| s.ip_info),
    };

    let mut network = get_active_network(&ip_info).await;
    if protect {
        get_config_network(&mut network).await;
    }

    Status {
        wlan: get_wlan(protect, &net_state).await,
        network,
        info: get_info(protect, net_state, ap_state).await,
        portal: get_portal(protect).await,
    }
}

async fn get_wlan(protect: bool, net_state: &NetworkStateValue) -> StatusWlan {
    let ssid = match protect {
        true => Some(nvs::wifi_ssid::get().await),
        false => None,
    };
    let rssi = net_state.rssi().map(|r| r.0);
    let wifi_data = env::active_params().wifi_data.lock().await;

    const BACKUP_SSID: &str = "";
    StatusWlan {
        ssid,
        active_ssid: wifi_data.ssid.clone(),
        active_pass: protect.then_some(wifi_data.pass.clone()),
        active_auth_method: protect.then_some(wifi_data.auth_method as u8),
        backup_ssid: BACKUP_SSID,
        rssi,
    }
}

const DEFAULT_IP: Ipv4Addr = Ipv4Addr::new(0, 0, 0, 0);

async fn get_active_network(ip_info: &Option<IpInfo>) -> StatusNetwork {
    let dhcp_active = env::active_params().dhcp && !state().net.temp_ip();

    StatusNetwork {
        active_dhcp: CheckedBool(dhcp_active),
        active_ip: ip_info.map(|i| i.ip).unwrap_or(DEFAULT_IP),
        active_subnet: ip_info
            .map(|i| calc_subnet_mask(i.subnet.mask.0))
            .unwrap_or(DEFAULT_IP),
        active_gateway: ip_info.map(|i| i.subnet.gateway).unwrap_or(DEFAULT_IP),
        active_dns1: ip_info.and_then(|i| i.dns).unwrap_or(DEFAULT_IP),
        active_dns2: ip_info.and_then(|i| i.secondary_dns).unwrap_or(DEFAULT_IP),
        active_hostname: env::active_params().hostname.clone(),
        config_dhcp: None,
        config_ip: None,
        config_subnet: None,
        config_gateway: None,
        config_dns1: None,
        config_dns2: None,
        config_hostname: None,
    }
}

async fn get_config_network(status: &mut StatusNetwork) {
    status.config_dhcp = Some(CheckedBool(nvs::net_dhcp::get().await));
    status.config_ip = Some(nvs::net_ip::get().await);
    status.config_subnet = Some(calc_subnet_mask(nvs::net_mask::get().await.0));
    status.config_gateway = Some(nvs::net_gateway::get().await);
    status.config_dns1 = Some(nvs::net_dns1::get().await);
    status.config_dns2 = Some(nvs::net_dns2::get().await);
    status.config_hostname = Some(nvs::net_hostname::get().await);
}

async fn get_info(
    protect: bool,
    net_state: NetworkStateValue,
    ap_state: Option<ApState>,
) -> StatusInfo {
    let mode = match net_state {
        NetworkStateValue::NotConnected => "",
        NetworkStateValue::Wifi(_, _) => "wlan",
        NetworkStateValue::Eth(_) => "lan",
    };

    let portal_enabled = nvs::portal_enabled::get().await;
    let ota_enabled = match protect {
        true => Some(CheckedBool(!nvs::ota_disabled::get().await)),
        false => None,
    };
    let ap_enabled = nvs::ap_enabled::get().await;
    let ap_ssid = match protect {
        true => Some(nvs::ap_ssid::get().await),
        false => None,
    };

    let (ap_active, ap_connected, active_ap_ssid) = match ap_state {
        Some(ap) => (true, !ap.connected_clients.is_empty(), Some(ap.ssid)),
        None => (false, false, None),
    };

    let state = env::acquire_website_state().await;

    StatusInfo {
        mode,
        ap_mode: CheckedBool(ap_enabled),
        ap_ssid,
        active_ap_ssid,
        ap_active: CheckedBool(ap_active),
        ap_connected: CheckedBool(ap_connected),
        portal_enabled: CheckedBool(portal_enabled),
        active_portal_enabled: CheckedBool(env::active_params().portal_enabled),
        ota_enabled,
        state_ebus: state.ebus_state as u8,
        state_modbus: state.modbus_state as u8,
        state_portal: state.portal_state as u8,
        state_directlink: state.local_connection_state as u8,
        serial: crate::utils::mac::eth(),
        firmware_version: crate::utils::version::FULL,
        mac_wlan: crate::utils::mac::wifi(),
        version: "7.31",
    }
}

async fn get_portal(protect: bool) -> StatusPortal {
    let (pass, system_name, host, port) = match protect {
        true => (
            Some(nvs::password::get().await),
            Some(nvs::system_name::get().await),
            Some(nvs::smartset_host::get().await),
            Some(nvs::smartset_port::get().await),
        ),
        false => (None, None, None, None),
    };

    let params = env::active_params();

    StatusPortal {
        active_host: &params.portal_host,
        active_port: params.portal_port,
        active_system_name: &params.system_name,
        active_pass: protect.then_some(&params.password),
        host,
        port,
        system_name,
        pass,
    }
}

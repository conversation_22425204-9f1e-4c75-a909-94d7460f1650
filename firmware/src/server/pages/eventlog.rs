use picoserve::{
    extract::Query,
    io::Write,
    response::{
        chunked::{<PERSON><PERSON><PERSON><PERSON><PERSON>, ChunkedResponse, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>},
        Body, HeadersIter, Response,
    },
};

use crate::{
    flash::eventlog::{
        events::Event,
        task::{EventLogRange, FILE_COUNT},
    },
    prelude::*,
};

#[derive(serde::Deserialize)]
pub struct EventLogQuery {
    page: Option<u8>,
    offset: Option<isize>,
    limit: Option<usize>,
}

struct EventLogPage(EventLogQuery);

impl Chunks for EventLogPage {
    fn content_type(&self) -> &'static str {
        "text/html"
    }

    async fn write_chunks<W: Write>(
        self,
        mut chunk_writer: ChunkWriter<W>,
    ) -> Result<ChunksWritten, W::Error> {
        let slot = ReturnSlot::new();

        chunk_writer.write_chunk(HEADER).await?;
        chunk_writer.write_chunk(b"<table>").await?;
        itc().eventlog.flush(&slot).await;

        let page = self.0.page.unwrap_or(0);
        let limit = self.0.limit.unwrap_or(64);
        let mut effective_limit = limit;
        let mut offset = self.0.offset.unwrap_or(0);
        if offset == isize::MAX {
            let entry_count = itc()
                .eventlog
                .get_page_entry_count(page, &slot)
                .await
                .unwrap_or(0);
            offset = entry_count.saturating_sub(limit) as isize;
        }
        let effective_offset = if offset < 0 {
            effective_limit = effective_limit.saturating_sub((-offset) as usize);
            0
        } else {
            offset as usize
        };

        let range = EventLogRange {
            page,
            offset: effective_offset,
            limit: effective_limit,
        };
        match itc().eventlog.get(range, &slot).await {
            Ok(entries) => {
                if entries.is_empty() {
                    chunk_writer
                        .write_chunk(b"<tr><td>No entries</td><tr>")
                        .await?;
                } else {
                    for entry in &entries {
                        let (time_s, time_ms) = (entry.timestamp / 1000, entry.timestamp % 1000);
                        let event = entry.event();
                        let classarg = match event {
                            Some(Event::Start(_)) => " class=\"border-top\"",
                            _ => "",
                        };
                        match event {
                            Some(e) => {
                                write!(
                                    chunk_writer,
                                    "<tr{classarg}><td>{time_s}.{time_ms}</td><td>{e:?}</td></tr>"
                                )
                                .await?
                            }
                            None => {
                                write!(
                                    chunk_writer,
                                    "<tr><td>{time_s}.{time_ms}</td><td>?</td></tr>"
                                )
                                .await?
                            }
                        }
                    }
                }
            }
            Err(e) => write!(chunk_writer, "<tr><td>Error: {e:?}</td><tr>").await?,
        }

        #[rustfmt::skip]
        write!(chunk_writer,
"
</table><br>
<a href=\"?page={}&offset={}&limit={}\">prev file</a> | \
<a href=\"?page={}&offset={}&limit={}\">prev page</a> | \
<a href=\"?page={}&offset={}&limit={}\">next page</a> | \
<a href=\"?page={}&offset={}&limit={}\">next file</a>
",
            (page + 1) % FILE_COUNT, offset, limit,
            page, offset + (limit as isize), limit,
            page, offset - (limit as isize), limit,
            (page + FILE_COUNT - 1) % FILE_COUNT, isize::MAX, limit,
        ).await?;

        chunk_writer.write_chunk(FOOTER).await?;
        chunk_writer.finalize().await
    }
}

pub async fn get(query: Query<EventLogQuery>) -> Response<impl HeadersIter, impl Body> {
    ChunkedResponse::new(EventLogPage(query.0)).into_response()
}

const HEADER: &[u8] = b"
<!DOCTYPE html>
<html>
<head>
  <style>
    .border-top td { border-top: 1px solid #000; }
  </style>
</head>
<body>
";

const FOOTER: &[u8] = b"
</body>
</html>
";

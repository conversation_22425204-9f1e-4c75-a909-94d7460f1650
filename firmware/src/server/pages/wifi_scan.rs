use nanoxml::derive::ser::SerXml;

use crate::{prelude::*, server::xml::Xml};

#[derive(SerXml)]
#[nanoxml(rename = "wifi-scan")]
pub struct WifiScan {
    #[nanoxml(seq)]
    pub data: Vec<AccessPointInfo>,
}

#[derive(SerXml)]
pub struct AccessPointInfo {
    pub ssid: heapless::String<32>,
    pub rssi: i8,
    #[nanoxml(rename = "auth-method")]
    pub auth_method: Option<u8>,
}

pub async fn get() -> Xml<WifiScan> {
    let data = state()
        .net
        .scanned_access_points
        .lock()
        .await
        .iter()
        .map(|x| AccessPointInfo {
            ssid: x.ssid.clone(),
            rssi: x.signal_strength,
            auth_method: x.auth_method.map(|x| x as u8),
        })
        .collect::<Vec<_>>();

    Xml(WifiScan { data })
}

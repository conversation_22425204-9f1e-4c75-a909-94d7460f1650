use picoserve::extract::Form;

use crate::{
    flash::eventlog::events::{ConfigAction, Event},
    prelude::*,
    server::util::{CheckedBool, Redirect},
};

#[derive(serde::Deserialize)]
pub struct SettingsForm {
    name: heapless::String<16>,
    pass: Option<heapless::String<63>>,
    #[serde(rename = "acon")]
    portal_enabled: Option<CheckedBool>,
    #[serde(rename = "ota")]
    ota_enabled: Option<CheckedBool>,
    #[serde(rename = "sysname")]
    system_name: Option<String>,
}

pub async fn post(form: Form<SettingsForm>) -> Redirect {
    match form.0.name.as_str() {
        "setKickoff" => {
            let mut res = Redirect::to("/error.htm");
            if let Some(ref pass) = form.0.pass {
                res = handle_password_change(pass).await;
            };
            if let Some(ref portal_enabled) = form.0.portal_enabled {
                res = handle_portal_enabled(portal_enabled.0).await;
            };
            if let Some(ref ota_enabled) = form.0.ota_enabled {
                res = handle_ota_enabled(ota_enabled.0).await;
            };
            return res;
        }
        "sysnameKickoff" => {
            if let Some(ref system_name) = form.0.system_name {
                return handle_system_name(system_name).await;
            }
        }
        _ => (),
    }
    Redirect::to("/error.htm")
}

async fn handle_portal_enabled(portal_enabled: bool) -> Redirect {
    nvs::portal_enabled::set(portal_enabled).await;
    let ev = match portal_enabled {
        true => ConfigAction::EnablePortal,
        false => ConfigAction::DisablePortal,
    };
    Event::Config(ev).log().await;
    Redirect::to("/protect/reboot.htm")
}

async fn handle_ota_enabled(ota_enabled: bool) -> Redirect {
    nvs::ota_disabled::set(!ota_enabled).await;
    Redirect::to("/protect/settings.htm")
}

async fn handle_password_change(pass: &str) -> Redirect {
    if is_valid_password(pass) {
        nvs::password::set(pass.to_string()).await;
        Event::Config(ConfigAction::ChangePassword).log().await;
        info!("Changed device password");
        Redirect::to("/protect/reboot.htm")
    } else {
        info!("Got invalid password submission");
        Redirect::to("/errorpw.htm")
    }
}

fn is_valid_password(pass: &str) -> bool {
    if pass.len() < 8 || pass.len() > 63 {
        return false;
    }

    let (mut lower, mut upper, mut special) = (false, false, false);
    for i in pass.chars() {
        match i {
            'a'..='z' => lower = true,
            'A'..='Z' => upper = true,
            ' '..='@' | '['..='`' | '{'..='~' => special = true, // includes '0'..='9'
            _ => return false,
        }
    }

    lower && upper && special
}

async fn handle_system_name(system_name: &str) -> Redirect {
    if system_name.is_empty() || system_name.len() > 32 {
        return Redirect::to("/error.htm");
    }
    nvs::system_name::set(system_name.to_string()).await;
    Redirect::to("/protect/reboot.htm")
}

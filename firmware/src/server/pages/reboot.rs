use picoserve::extract::Form;

use crate::{flash::eventlog::events::ResetTrigger, server::util::Redirect};

#[derive(serde::Deserialize)]
pub struct RebootForm {
    name: String,
    rebo: String,
}

pub async fn post(form: Form<RebootForm>) -> Redirect {
    if form.0.name == "reboKickoff" && form.0.rebo == "init" {
        crate::utils::restart(ResetTrigger::Website).await;
    }
    Redirect::to("/protect/reboot.htm")
}

use std::{
    net::{Ipv4Addr, SocketAddr, SocketAddrV4},
    rc::Rc,
};

use async_net_mini::TcpListener;
use defmt::Debug2Format;
use picoserve::{
    response::File,
    routing::{get, get_service, PathRouter, Router},
    Config, Timeouts,
};
use util::{IoWrapper, Redirect, TranslatableHtml};

use crate::prelude::*;

pub mod pages;
pub mod schema;

mod util;
mod xml;

pub async fn start_web_server() -> Result<()> {
    info!("Starting web server");

    let timeouts = Timeouts {
        start_read_request: Some(secs(5)),
        read_request: Some(secs(1)),
        write: Some(secs(1)),
    };
    let config = Config::new(timeouts).keep_connection_alive();

    #[rustfmt::skip]
    let router = Router::new()
        .route("/", get_service(&INDEX_HTML))
        .route("/index.htm", get_service(&INDEX_HTML))
        .route("/error.htm", get_service(&ERROR_HTML))
        .route("/errorpw.htm", get_service(&ERRORPW_HTML))
        .route("/privacypolicy.htm", get_service(&PRIVACY_HTML))
        .route("/conditionsofuse.htm", get_service(&TERMS_HTML))
        .route("/protect/settings.htm", get_service(&SETTINGS_HTML).post(pages::settings::post))
        .route("/protect/network.htm", get_service(&NETWORK_HTML).post(pages::network::post))
        .route("/protect/reboot.htm", get_service(&REBOOT_HTML).post(pages::reboot::post))
        .route("/protect/events.htm", get(pages::eventlog::get))
        .route("/events.htm", get(|| async { Redirect::to("/protect/events.htm") }))
        .route("/lang.htm", get(pages::switch_lang::get))
        .route("/style.css", get_service(STYLESHEET))
        .route("/wolf.svg", get_service(WOLF_LOGO))
        .route("/favicon.ico", get_service(FAVICON))
        .route("/status.xml", get(|| pages::status::get(false)))
        .route("/protect/status.xml", get(|| pages::status::get(true)))
        .route("/protect/wifi-scan.xml", get(pages::wifi_scan::get))
        .layer(util::AuthLayer);

    let router = Rc::new(router);

    let listener = TcpListener::bind(SocketAddr::V4(SocketAddrV4::new(Ipv4Addr::UNSPECIFIED, 80)))
        .await
        .context(intern!("bind to tcp address"))?;
    info!("Started web server");

    for _ in 0..2 {
        let listener = listener.clone();
        let router = router.clone();
        let config = config.clone();
        env::spawn(serve(listener, router, config));
    }

    Ok(())
}

async fn serve<T: PathRouter>(
    listener: TcpListener,
    router: Rc<Router<T>>,
    config: Config<Duration>,
) {
    let mut buf = vec![0; 2048];

    loop {
        let Ok((stream, addr)) = listener.accept().await else {
            warn!("failed to accept web server client");
            continue;
        };
        info!("Accepted web server client: addr={}", Debug2Format(&addr));

        let socket = IoWrapper(stream);
        _ = picoserve::serve(&router, util::TimerImpl, &config, &mut buf, socket)
            .await
            .inspect(|n| {
                info!(
                    "Handled requests from: addr={}, count={}",
                    Debug2Format(&addr),
                    n
                )
            })
            .context(intern!("handle website requests"));
    }
}

const INDEX_HTML: TranslatableHtml = TranslatableHtml {
    de: File::html(website::INDEX_HTML_DE),
    en: File::html(website::INDEX_HTML_EN),
};
const NETWORK_HTML: TranslatableHtml = TranslatableHtml {
    de: File::html(website::NETWORK_HTML_DE),
    en: File::html(website::NETWORK_HTML_EN),
};
const SETTINGS_HTML: TranslatableHtml = TranslatableHtml {
    de: File::html(website::SETTINGS_HTML_DE),
    en: File::html(website::SETTINGS_HTML_EN),
};
const ERROR_HTML: TranslatableHtml = TranslatableHtml {
    de: File::html(website::ERROR_HTML_DE),
    en: File::html(website::ERROR_HTML_EN),
};
const ERRORPW_HTML: TranslatableHtml = TranslatableHtml {
    de: File::html(website::ERRORPW_HTML_DE),
    en: File::html(website::ERRORPW_HTML_EN),
};
const REBOOT_HTML: TranslatableHtml = TranslatableHtml {
    de: File::html(website::REBOOT_HTML_DE),
    en: File::html(website::REBOOT_HTML_EN),
};
const PRIVACY_HTML: TranslatableHtml = TranslatableHtml {
    de: File::html(website::PRIVACY_HTML_DE),
    en: File::html(website::PRIVACY_HTML_EN),
};
const TERMS_HTML: TranslatableHtml = TranslatableHtml {
    de: File::html(website::TERMS_HTML_DE),
    en: File::html(website::TERMS_HTML_EN),
};
const STYLESHEET: File = File::with_content_type("text/css", website::STYLESHEET.as_bytes());
const WOLF_LOGO: File = File::with_content_type("image/svg+xml", website::WOLF_LOGO);
const FAVICON: File = File::with_content_type("image/x-icon", website::FAVICON);

#![allow(dead_code)]

use std::net::Ipv4Addr;

use nanoxml::derive::ser::SerXml;

use super::util::CheckedBool;

#[derive(<PERSON><PERSON>, Debug, SerXml)]
#[nanoxml(rename = "response")]
pub struct Status {
    pub wlan: StatusWlan,
    pub network: StatusNetwork,
    pub info: StatusInfo,
    pub portal: StatusPortal,
}

#[derive(<PERSON><PERSON>, Debug, SerXml)]
pub struct StatusWlan {
    pub ssid: Option<String>,
    #[nanoxml(rename = "activessid")]
    pub active_ssid: heapless::String<32>,
    #[nanoxml(rename = "activekey")]
    pub active_pass: Option<heapless::String<64>>,
    #[nanoxml(rename = "activesec")]
    pub active_auth_method: Option<u8>,
    #[nanoxml(rename = "backupssid")]
    pub backup_ssid: &'static str,
    #[nanoxml(rename = "rsii")]
    pub rssi: Option<u8>,
}

#[derive(<PERSON><PERSON>, Debug, SerXml)]
pub struct StatusNetwork {
    #[nanoxml(rename = "activedhcp")]
    pub active_dhcp: CheckedBool,
    #[nanoxml(rename = "activeip")]
    pub active_ip: Ipv4Addr,
    #[nanoxml(rename = "activesub")]
    pub active_subnet: Ipv4Addr,
    #[nanoxml(rename = "activegw")]
    pub active_gateway: Ipv4Addr,
    #[nanoxml(rename = "activedns1")]
    pub active_dns1: Ipv4Addr,
    #[nanoxml(rename = "activedns2")]
    pub active_dns2: Ipv4Addr,
    #[nanoxml(rename = "activehost")]
    pub active_hostname: heapless::String<30>,
    #[nanoxml(rename = "dhcp")]
    pub config_dhcp: Option<CheckedBool>,
    #[nanoxml(rename = "ip")]
    pub config_ip: Option<Ipv4Addr>,
    #[nanoxml(rename = "sub")]
    pub config_subnet: Option<Ipv4Addr>,
    #[nanoxml(rename = "gw")]
    pub config_gateway: Option<Ipv4Addr>,
    #[nanoxml(rename = "dns1")]
    pub config_dns1: Option<Ipv4Addr>,
    #[nanoxml(rename = "dns2")]
    pub config_dns2: Option<Ipv4Addr>,
    #[nanoxml(rename = "host")]
    pub config_hostname: Option<String>,
}

#[derive(Clone, Debug, SerXml)]
pub struct StatusInfo {
    pub mode: &'static str,
    #[nanoxml(rename = "apmode")]
    pub ap_mode: CheckedBool,
    #[nanoxml(rename = "apssid")]
    pub ap_ssid: Option<String>,
    #[nanoxml(rename = "activeapssid")]
    pub active_ap_ssid: Option<heapless::String<32>>,
    #[nanoxml(rename = "activeap")]
    pub ap_active: CheckedBool,
    #[nanoxml(rename = "apconnected")]
    pub ap_connected: CheckedBool,
    #[nanoxml(rename = "acon")]
    pub portal_enabled: CheckedBool,
    #[nanoxml(rename = "activeacon")]
    pub active_portal_enabled: CheckedBool,
    #[nanoxml(rename = "ota")]
    pub ota_enabled: Option<CheckedBool>,
    #[nanoxml(rename = "stateebus")]
    pub state_ebus: u8,
    #[nanoxml(rename = "statemodbus")]
    pub state_modbus: u8,
    #[nanoxml(rename = "stateportal")]
    pub state_portal: u8,
    #[nanoxml(rename = "statedirectlink")]
    pub state_directlink: u8,
    pub serial: &'static str,
    #[nanoxml(rename = "fwver")]
    pub firmware_version: &'static str,
    #[nanoxml(rename = "macwlan")]
    pub mac_wlan: &'static str,
    pub version: &'static str,
}

#[derive(Clone, Debug, SerXml)]
pub struct StatusPortal {
    #[nanoxml(rename = "wpip")]
    pub host: Option<String>,
    pub port: Option<u16>,
    #[nanoxml(rename = "sysname")]
    pub system_name: Option<String>,
    pub pass: Option<String>,
    #[nanoxml(rename = "activewpip")]
    pub active_host: &'static str,
    #[nanoxml(rename = "activeport")]
    pub active_port: u16,
    #[nanoxml(rename = "activesysname")]
    pub active_system_name: &'static str,
    #[nanoxml(rename = "activepass")]
    pub active_pass: Option<&'static str>,
}

// the integer values of these enums are relics of the old wolf link and are kept for backwards
// compatibility with stuff like the commissioning assistant.

#[derive(Clone, Copy, Default)]
pub enum PortalState {
    #[default]
    NotConnected = 9,
    Logon = 0,
    DelayedLogon = 1,
    SslConnecting = 4,
    SetupConnection = 11,
    Connected = 12,
}

#[derive(Clone, Copy, Default)]
pub enum LocalConnectionState {
    #[default]
    NotConnected = 9,
    Logon = 0,
    SslConnecting = 4,
    PortalLogonResponse = 5,
    ConnectedLocally = 7,
}

#[derive(Clone, Copy, Default)]
pub enum BusState {
    #[default]
    NotConnected = 1,
    Connected = 0,
}

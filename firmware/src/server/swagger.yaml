openapi: 3.0.0
info:
  title: Wolf Link API
  description: API for managing Wi-Fi settings and device reboot for the Wolf Link device.
  version: 1.0.0
servers:
  - url: http://***********:80
components:
  securitySchemes:
    basicAuth:
      type: http
      scheme: basic
      description: |
        The username is always "admin".
        The password is the Wolf Link device password.
security:
  - basicAuth: []
paths:
  /protect/wifi-scan.xml:
    get:
      summary: Get available Wi-Fi networks
      security:
        - basicAuth: []
      responses:
        '200':
          description: A list of available Wi-Fi networks
          content:
            application/xml:
              schema:
                type: object
                xml:
                  name: wifi-scan-response
                properties:
                  wifi-scan:
                    type: object
                    xml:
                      name: wifi-scan
                    properties:
                      data:
                        type: array
                        xml:
                          name: data
                        items:
                          type: object
                          properties:
                            ssid:
                              type: string
                            rssi:
                              type: integer
                            auth-method:
                              type: integer
  /protect/network.htm:
    post:
      summary: Set Wi-Fi settings
      security:
        - basicAuth: []
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                name:
                  type: string
                ssid:
                  type: string
                sec:
                  type: integer
                key:
                  type: string
                dhcp:
                  type: boolean
                ip:
                  type: string
                  format: ipv4
                sub:
                  type: string
                  format: ipv4
                gw:
                  type: string
                  format: ipv4
                dns1:
                  type: string
                  format: ipv4
                dns2:
                  type: string
                  format: ipv4
                host:
                  type: string
                wpip:
                  type: string
                port:
                  type: integer
                ap:
                  type: boolean
                apssid:
                  type: string
      responses:
        '302':
          description: Redirects to the reboot page or error page
  /protect/reboot.htm:
    post:
      summary: Reboot the device
      description: Reboot the device, necessary to apply wifi settings.
      security:
        - basicAuth: []
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: This value must be set to "reboKickoff"
                rebo:
                  type: string
                  description: This value must be set to "init"
      responses:
        '302':
          description: Redirects to the reboot confirmation page

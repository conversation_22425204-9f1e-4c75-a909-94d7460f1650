use std::{
    fmt::{Result as FmtResult, Write as FmtWrite},
    future::Future,
    net::Shutdown,
};

use async_net_mini::TcpStream;
use nanoxml::{
    derive::ser::{SerXml, SerXmlAsAttr},
    ser::XmlBuilder,
};
use picoserve::{
    io::{ErrorType, Read, Socket, Write},
    request::{Request, RequestParts},
    response::{
        status::StatusCode, Body, Connection, File, HeadersIter, IntoResponse, Response,
        ResponseWriter,
    },
    routing::{Layer, Next, RequestHandlerService},
    Error, ResponseSent, Timeouts, Timer,
};
use serde::{Deserialize, Deserializer, Serialize, Serializer};

use crate::prelude::*;

/* AuthLayer **************************************************************************************/

pub struct AuthLayer;

impl<State, PathParameters> Layer<State, PathParameters> for AuthLayer {
    type NextState = State;
    type NextPathParameters = PathParameters;

    async fn call_layer<
        'a,
        R: Read + 'a,
        NextLayer: Next<'a, R, Self::NextState, Self::NextPathParameters>,
        W: ResponseWriter<Error = R::Error>,
    >(
        &self,
        next: NextLayer,
        state: &State,
        path_parameters: PathParameters,
        request_parts: RequestParts<'_>,
        response_writer: W,
    ) -> Result<ResponseSent, W::Error> {
        let method = request_parts.method();
        let path = request_parts.path();

        if path.encoded().starts_with("/protect/") {
            let headers = request_parts.headers();
            let authorized = match headers.get("Authorization") {
                Some(auth_data) => match auth_data.as_str() {
                    Ok(auth_data) => self.check_auth_data(auth_data).await.is_ok(),
                    Err(_) => false,
                },
                None => false,
            };
            info!(
                "Got request: method={}, path={}, auth={}",
                method,
                path.encoded(),
                authorized
            );
            if authorized {
                next.run(state, path_parameters, response_writer).await
            } else {
                next.run(
                    state,
                    path_parameters,
                    UnauthorizedResponseWriter { response_writer },
                )
                .await
            }
        } else {
            info!("Got request: method={}, path={}", method, path.encoded());
            next.run(state, path_parameters, response_writer).await
        }
    }
}

impl AuthLayer {
    async fn check_auth_data(&self, data: &str) -> Result<(), &'static str> {
        if !data.starts_with("Basic ") {
            return Err("invalid authorization message");
        }

        let data = &data[6..];
        let mut buf = [0; 128];
        let decoded = crate::utils::base64::decode(data.as_bytes(), &mut buf)
            .map_err(|_| "invalid base64")?;
        let mut decoded = std::str::from_utf8(decoded)
            .map_err(|_| "invalid string")?
            .split(':');

        let user = decoded.next().ok_or("no username")?;
        if user != "admin" {
            return Err("invalid user");
        }

        let pass = decoded.remainder().ok_or("no password")?;
        if pass == env::active_params().password {
            Ok(())
        } else {
            Err("invalid password")
        }
    }
}

struct UnauthorizedResponseWriter<W> {
    response_writer: W,
}

impl<W: ResponseWriter> ResponseWriter for UnauthorizedResponseWriter<W> {
    type Error = W::Error;

    async fn write_response<R: Read<Error = Self::Error>, H: HeadersIter, B: Body>(
        self,
        connection: Connection<'_, R>,
        _response: Response<H, B>,
    ) -> Result<ResponseSent, Self::Error> {
        let resp = Response::new(StatusCode::UNAUTHORIZED, "");
        let resp = resp.with_header("WWW-Authenticate", "Basic realm=\"Protected\"");
        self.response_writer.write_response(connection, resp).await
    }
}

/* TranslatableHtml *******************************************************************************/

const VALID_LANGS: [&str; 2] = ["en", "de"];
const LANG_AUTO: &str = "_auto";

pub struct TranslatableHtml {
    pub de: File,
    pub en: File,
}

impl<State, PathParameters> RequestHandlerService<State, PathParameters> for &TranslatableHtml {
    async fn call_request_handler_service<R: Read, W: ResponseWriter<Error = R::Error>>(
        &self,
        state: &State,
        path_parameters: PathParameters,
        request: Request<'_, R>,
        response_writer: W,
    ) -> Result<ResponseSent, W::Error> {
        let lang = nvs::website_lang::get().await;
        let mut lang = lang.as_str();
        if lang == LANG_AUTO {
            if let Some(accept) = get_accept_language(&request) {
                lang = accept;
            }
        };

        let file = match lang {
            "de" => &self.de,
            _ => &self.en,
        };

        file.call_request_handler_service(state, path_parameters, request, response_writer)
            .await
    }
}

fn get_accept_language<R: Read>(request: &Request<'_, R>) -> Option<&'static str> {
    // TODO: this parsing is not ideal. for how to do it properly, refer to this page:
    //       https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Language
    //       however, since most browsers are sane, the current implementation works.
    let headers = request.parts.headers();
    let accept_language = headers.get("Accept-Language")?;
    let accept_language = accept_language.as_str().ok()?;
    for lang in accept_language.split(';') {
        for &check in &VALID_LANGS {
            let end = lang.find(['-', ',', ' ']).unwrap_or(lang.len());
            if &lang[..end] == check {
                return Some(check);
            }
        }
    }
    None
}

/* CheckedBool ************************************************************************************/

#[derive(Clone, Debug)]
pub struct CheckedBool(pub bool);

impl SerXml for CheckedBool {
    fn ser_body<W: FmtWrite>(&self, xml: &mut XmlBuilder<'_, W>) -> FmtResult {
        match self.0 {
            true => xml.text("checked"),
            false => Ok(()),
        }
    }

    fn ser_attrs<W: FmtWrite>(&self, _xml: &mut XmlBuilder<'_, W>) -> FmtResult {
        Ok(())
    }
}

impl SerXmlAsAttr for CheckedBool {}

impl Serialize for CheckedBool {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        serializer.serialize_str(match self.0 {
            true => "checked",
            false => "",
        })
    }
}

impl<'de> Deserialize<'de> for CheckedBool {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: Deserializer<'de>,
    {
        let value: String = Deserialize::deserialize(deserializer)?;
        match value.as_str() {
            "checked" => Ok(CheckedBool(true)),
            "" => Ok(CheckedBool(false)),
            _ => Err(serde::de::Error::custom("invalid checked bool value")),
        }
    }
}

/* Redirect ***************************************************************************************/

pub enum Redirect {
    Static(&'static str),
    Owned(String),
}

impl Redirect {
    pub fn to(location: &'static str) -> Self {
        Self::Static(location)
    }
}

impl IntoResponse for Redirect {
    async fn write_to<R: Read, W: ResponseWriter<Error = R::Error>>(
        self,
        connection: Connection<'_, R>,
        response_writer: W,
    ) -> Result<ResponseSent, W::Error> {
        let loc = match self {
            Self::Owned(ref s) => s.as_str(),
            Self::Static(s) => s,
        };

        (
            StatusCode::FOUND,
            ("Location", loc),
            format_args!("{loc}\n"),
        )
            .write_to(connection, response_writer)
            .await
    }
}

/* TimerImpl **************************************************************************************/

pub struct TimerImpl;

impl Timer for TimerImpl {
    type Duration = Duration;
    type TimeoutError = embassy_time::TimeoutError;

    async fn run_with_timeout<F: Future>(
        &mut self,
        duration: Self::Duration,
        future: F,
    ) -> Result<F::Output, Self::TimeoutError> {
        with_timeout(duration, future).await
    }
}

/* IoWrapper **************************************************************************************/

pub struct IoWrapper(pub TcpStream);

impl Clone for IoWrapper {
    fn clone(&self) -> Self {
        Self(self.0.clone())
    }
}

impl ErrorType for IoWrapper {
    type Error = std::io::Error;
}

impl Read for IoWrapper {
    async fn read(&mut self, buf: &mut [u8]) -> Result<usize, Self::Error> {
        use futures_lite::AsyncReadExt;

        self.0.read(buf).await
    }
}

impl Write for IoWrapper {
    async fn write(&mut self, buf: &[u8]) -> Result<usize, Self::Error> {
        use futures_lite::AsyncWriteExt;

        self.0.write(buf).await
    }
}

impl Socket for IoWrapper {
    type Error = std::io::Error;
    type ReadHalf<'a>
        = Self
    where
        Self: 'a;
    type WriteHalf<'a>
        = Self
    where
        Self: 'a;

    fn split(&mut self) -> (Self::ReadHalf<'_>, Self::WriteHalf<'_>) {
        // async_net::TcpStream does not implement .split() for some reason :(
        (self.clone(), self.clone())
    }

    async fn shutdown<Timer: picoserve::Timer>(
        self,
        _timeouts: &Timeouts<Timer::Duration>,
        _timer: &mut Timer,
    ) -> Result<(), Error<Self::Error>> {
        self.0.shutdown(Shutdown::Both).map_err(Error::Write)
    }
}

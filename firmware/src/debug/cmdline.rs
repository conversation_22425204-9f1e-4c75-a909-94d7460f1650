use std::str::FromStr;

use crate::{
    debug::{
        stdin::{send_dummy_cmd_response, ArgsIter},
        IDENT_CMD,
    },
    flash::eventlog::{
        events::{Event, ResetTrigger},
        task::{EventLogRange, EventLogResponse},
    },
    prelude::*,
    server::{
        pages::status::get_status,
        schema::{BusState, LocalConnectionState, PortalState},
    },
};

enum Command {
    Help,
    Version,
    NvsGet,
    NvsSet,
    NvsReset,
    NvsOverview,
    Status,
    Restart,
    Rollback,
    FreeMem,
    ReadMac,
    LogLevel,
    FactoryReset,
    EnterTestMode,
    IsProdMode,
    Stats,
    EventLog,
    EventLogFlush,
    Connect,
    _Len,
}

const COMMANDS_LEN: usize = Command::_Len as usize;
const COMMANDS: [(Command, &str, &str); COMMANDS_LEN] = [
    // command enum, command, parameter description
    (Command::Help, "help", ""),
    (Command::Version, "version", "[identifier]"),
    (Command::NvsGet, "nvs-get", "<key>"),
    (Command::NvsSet, "nvs-set", "<key> <data>"),
    (Command::NvsReset, "nvs-reset", "<key>"),
    (Command::NvsOverview, "nvs-overview", "[filter]"),
    (Command::Status, "status", ""),
    (Command::Restart, "restart", ""),
    (Command::Rollback, "rollback", ""),
    (Command::FreeMem, "free-mem", ""),
    (Command::ReadMac, "read-mac", ""),
    (Command::LogLevel, "log-level", "<target> <filter>"),
    (Command::FactoryReset, "factory-reset", ""),
    (Command::EnterTestMode, "enter-test-mode", ""),
    (Command::IsProdMode, "is-prod-mode", ""),
    (Command::Stats, "stats", ""),
    (Command::EventLog, "event-log", "[page] [offset] [limit]"),
    (Command::EventLogFlush, "event-log-flush", ""),
    (Command::Connect, "connect", "<ssid> <pass>"),
];

#[derive(Debug)]
enum CommandError {
    IncorrectUsage(String),
    Error(String),
}

impl From<&'static str> for CommandError {
    fn from(e: &'static str) -> Self {
        CommandError::Error(e.to_owned())
    }
}

impl From<String> for CommandError {
    fn from(e: String) -> Self {
        CommandError::Error(e)
    }
}

type CommandResult = Result<String, CommandError>;

pub async fn handle_command(
    cmd: &str,
    arguments: ArgsIter<'_>,
    cmd_id: u8,
) -> Result<String, String> {
    let Some(command_to_exec) = COMMANDS.iter().find(|x| cmd == x.1) else {
        return Err(format!(
            "Unknown command: {cmd}\nType \"help\" to obtain a list of valid commands."
        ));
    };

    match with_timeout(secs(4), execute_command(command_to_exec, arguments, cmd_id)).await {
        Ok(res) => res,
        Err(_) => Err("Command execution timeout".to_string()),
    }
}

async fn execute_command(
    command: &(Command, &str, &str),
    arguments: ArgsIter<'_>,
    cmd_id: u8,
) -> Result<String, String> {
    let res = match command.0 {
        Command::Help => print_command_help().await,
        Command::Version => print_version_info(arguments).await,
        Command::NvsGet => nvs_get(arguments).await,
        Command::NvsSet => nvs_set(arguments).await,
        Command::NvsReset => nvs_reset(arguments).await,
        Command::NvsOverview => nvs_overview(arguments).await,
        Command::Status => status().await,
        Command::Restart => restart(cmd_id).await,
        Command::Rollback => rollback(cmd_id).await,
        Command::FreeMem => free_mem().await,
        Command::ReadMac => read_mac().await,
        Command::LogLevel => log_level(arguments),
        Command::FactoryReset => factory_reset(cmd_id).await,
        Command::EnterTestMode => enter_test_mode(cmd_id).await,
        Command::IsProdMode => is_prod_mode().await,
        Command::Stats => stats().await,
        Command::EventLog => event_log(arguments).await,
        Command::EventLogFlush => event_log_flush().await,
        Command::Connect => connect(cmd_id, arguments).await,
        Command::_Len => defmt::unreachable!(),
    };

    match res {
        Ok(s) => Ok(s),
        Err(CommandError::Error(e)) => Err(e.to_string()),
        Err(CommandError::IncorrectUsage(e)) => {
            Err(format!("Error: {e}\nUsage: {} {}", command.1, command.2))
        }
    }
}

async fn nvs_get(mut arguments: ArgsIter<'_>) -> CommandResult {
    let key = arguments
        .next()
        .and_then(|bytes| std::str::from_utf8(bytes).ok())
        .ok_or_else(|| CommandError::IncorrectUsage("NVS key not specified".to_string()))?;

    match nvs::api::get_for_cmdline(key).await {
        Ok(value) => Ok(format!(
            "Successfully retrieved data ({} display bytes):\n{}",
            value.len(),
            value,
        )),
        Err(e) => Err(CommandError::Error(format!(
            "failed to get nvs entry {key}: {e}"
        ))),
    }
}

async fn nvs_set(mut arguments: ArgsIter<'_>) -> CommandResult {
    let key = arguments
        .next()
        .and_then(|bytes| std::str::from_utf8(bytes).ok())
        .ok_or_else(|| CommandError::IncorrectUsage("NVS key not specified".to_string()))?;

    let data = arguments
        .next()
        .and_then(|bytes| std::str::from_utf8(bytes).ok())
        .ok_or_else(|| CommandError::IncorrectUsage("Missing data param".to_owned()))?;

    match nvs::api::set_from_cmdline(key, data.to_owned()).await {
        Ok(()) => Ok(format!("Successfully set nvs entry {}", key)),
        Err(e) => Err(CommandError::Error(format!(
            "failed to set nvs entry {key}: {e}"
        ))),
    }
}

async fn nvs_reset(mut arguments: ArgsIter<'_>) -> CommandResult {
    let key = arguments
        .next()
        .and_then(|bytes| std::str::from_utf8(bytes).ok())
        .ok_or_else(|| CommandError::IncorrectUsage("NVS key not specified".to_string()))?;

    match nvs::api::reset_from_cmdline(key).await {
        Ok(()) => Ok(format!("Successfully reset nvs entry {}", key)),
        Err(e) => Err(CommandError::Error(format!(
            "failed to reset nvs entry {key}: {e}"
        ))),
    }
}

async fn nvs_overview(mut arguments: ArgsIter<'_>) -> CommandResult {
    let filter = arguments
        .next()
        .and_then(|bytes| std::str::from_utf8(bytes).ok());

    let mut out = String::new();
    out.push_str("key               value\n");
    out.push_str("================  ========================\n");
    for entry in nvs::api::iter() {
        let filtered_out = filter
            .map(|filter| !entry.key.contains(filter))
            .unwrap_or(false);
        if filtered_out {
            continue;
        }
        if let Some(value) = (entry.get)().await {
            out.push_str(&format!("{:16}  {}\n", entry.key, value));
        }
    }

    Ok(out)
}

async fn status() -> CommandResult {
    let status = get_status(false).await;

    let mut out = String::new();

    out.push_str("Network:\n");
    out.push_str(&format!("  Interface: {}\n", status.info.mode));
    if status.info.mode == "wlan" {
        out.push_str(&format!("  SSID: {}\n", status.wlan.active_ssid));
        out.push_str(&format!(
            "  Signal Strength: {}\n",
            match status.wlan.rssi {
                Some(0) => "very bad",
                Some(1) => "bad",
                Some(2) => "okay",
                Some(3) => "good",
                Some(4) => "excellent",
                _ => "?",
            }
        ));
    }
    out.push_str(&format!("  IP: {}\n", status.network.active_ip));
    out.push_str(&format!("  Gateway: {}\n", status.network.active_gateway));
    out.push_str(&format!("  Subnet: {}\n", status.network.active_subnet));
    out.push_str(&format!(
        "  DNS: {} (alt: {})\n",
        status.network.active_dns1, status.network.active_dns2
    ));
    out.push_str(&format!("  DHCP: {}\n", status.network.active_dhcp.0));

    out.push_str("Bus:\n");
    out.push_str(&format!(
        "  eBus: {}\n",
        match status.info.state_ebus {
            x if x == BusState::Connected as u8 => "connected",
            x if x == BusState::NotConnected as u8 => "not connected",
            _ => "?",
        }
    ));
    out.push_str(&format!(
        "  ModBus: {}\n",
        match status.info.state_modbus {
            x if x == BusState::Connected as u8 => "connected",
            x if x == BusState::NotConnected as u8 => "not connected",
            _ => "?",
        }
    ));

    if status.info.ap_mode.0 {
        out.push_str("Access Point: active\n");
        out.push_str(&format!(
            "  SSID: {}\n",
            status.info.active_ap_ssid.as_deref().unwrap_or("?")
        ));
    } else {
        out.push_str("Access Point: inactive\n");
    }

    if status.info.active_portal_enabled.0 {
        out.push_str(&format!(
            "Portal Connection: {}\n",
            match status.info.state_portal {
                x if x == PortalState::NotConnected as u8 => "not connected",
                x if x == PortalState::Logon as u8 => "log on",
                x if x == PortalState::SslConnecting as u8 => "ssl connecting",
                x if x == PortalState::SetupConnection as u8 => "setup connection",
                x if x == PortalState::Connected as u8 => "connected",
                x if x == PortalState::DelayedLogon as u8 => "delaying connection",
                _ => "?",
            }
        ));
        out.push_str(&format!(
            "  Host: {}:{}\n",
            status.portal.active_host, status.portal.active_port
        ));
        out.push_str(&format!(
            "  System Name: {}\n",
            status.portal.active_system_name
        ));
    } else if status.info.portal_enabled.0 {
        out.push_str("Portal Connection: enabled - restart required!\n");
    } else {
        out.push_str("Portal Connection: disabled\n");
    }

    out.push_str(&format!(
        "Direct Connection: {}\n",
        match status.info.state_directlink {
            x if x == LocalConnectionState::NotConnected as u8 => "not connected",
            x if x == LocalConnectionState::Logon as u8 => "log on",
            x if x == LocalConnectionState::SslConnecting as u8 => "ssl connecting",
            x if x == LocalConnectionState::PortalLogonResponse as u8 => "logon response",
            x if x == LocalConnectionState::ConnectedLocally as u8 => "connected",
            _ => "?",
        }
    ));

    Ok(out)
}

async fn restart(cmd_id: u8) -> CommandResult {
    // print dummy answer since we restart before it would get sent out regularly
    send_dummy_cmd_response(IDENT_CMD, cmd_id, "restarting...");
    crate::utils::restart(ResetTrigger::CmdLine).await
}

async fn rollback(cmd_id: u8) -> CommandResult {
    // print dummy answer since we restart before it would get sent out regularly
    send_dummy_cmd_response(IDENT_CMD, cmd_id, "starting rollback...");

    crate::flash::ota::rollback()
        .await
        .map_err(|_e| "Application rollback failed.")?;

    Ok("Rolled back to previous version".into())
}

async fn print_version_info(mut arguments: ArgsIter<'_>) -> CommandResult {
    match arguments.next() {
        None => Ok(format!(
            "SW: {}\\nHW: {}",
            crate::utils::version::FULL,
            crate::utils::hwversion::get_str(),
        )),
        Some(b"all") => {
            let mut out = String::new();
            out.push_str(&format!("SW full: {}\n", crate::utils::version::FULL));
            out.push_str(&format!("SW hash: {}\n", crate::utils::version::HASH));
            out.push_str(&format!("SW tag: {}\n", crate::utils::version::TAG));
            out.push_str(&format!("SW customer: {}\n", crate::utils::version::SHORT));
            out.push_str(&format!(
                "SW smartset: {}\n",
                crate::utils::version::SHORT_NUM
            ));
            out.push_str(&format!(
                "SW softwarenum: {}\n",
                crate::utils::version::SOFTWARE_NUM
            ));
            out.push_str(&format!("HW: {}", crate::utils::hwversion::get_str()));
            Ok(out)
        }
        Some(b"full") => Ok(crate::utils::version::FULL.to_string()),
        Some(b"hash") => Ok(crate::utils::version::HASH.to_string()),
        Some(b"tag") => Ok(crate::utils::version::TAG.to_string()),
        Some(b"customer") => Ok(crate::utils::version::SHORT.to_string()),
        Some(b"smartset") => Ok(crate::utils::version::SHORT_NUM.to_string()),
        Some(b"softwarenum") => Ok(crate::utils::version::SOFTWARE_NUM.to_string()),
        Some(b"hardware") => Ok(format!("linkr-{}", crate::utils::hwversion::get_str())),
        _ => {
            let msg = "Available options: all, full, hash, tag, customer, smartset, softwarenum, hardware";
            Err(CommandError::IncorrectUsage(msg.to_owned()))
        }
    }
}

async fn free_mem() -> CommandResult {
    let stats = crate::utils::mem::get_memory_info();

    Ok(format!(
        "Free stack size: {}\nFree heap (internal): {}\nFree heap (external): {}",
        stats.free_stack, stats.free_heap_internal, stats.free_heap_external,
    ))
}

async fn read_mac() -> CommandResult {
    Ok(crate::utils::mac::eth().to_owned())
}

fn log_level(mut arguments: ArgsIter<'_>) -> CommandResult {
    let target = arguments
        .next()
        .and_then(|bytes| std::str::from_utf8(bytes).ok())
        .ok_or_else(|| CommandError::IncorrectUsage("Target not specified".to_string()))?;
    let filter = arguments
        .next()
        .and_then(|bytes| std::str::from_utf8(bytes).ok())
        .ok_or_else(|| CommandError::IncorrectUsage("Filter not specified".to_owned()))?;
    let filter = log::LevelFilter::from_str(filter)
        .map_err(|_| CommandError::IncorrectUsage("Filter invalid".to_owned()))?;

    crate::utils::set_log_level(target, filter);

    Ok(format!("Log level of {target} set to {filter:?}"))
}

async fn factory_reset(cmd_id: u8) -> CommandResult {
    // print dummy answer since we restart before it would get sent out regularly
    send_dummy_cmd_response(IDENT_CMD, cmd_id, "resetting to factory defaults...");
    nvs::api::factory_reset().await;
    esp_idf_svc::hal::reset::restart();
}

async fn print_command_help() -> CommandResult {
    let mut out = String::new();
    out.push_str("Available Commands:\n");
    for i in &COMMANDS {
        out.push_str(&format!(" - {} {}\n", i.1, i.2));
    }
    Ok(out)
}

async fn enter_test_mode(cmd_id: u8) -> CommandResult {
    // print dummy answer since we restart before it would get sent out regularly
    send_dummy_cmd_response(IDENT_CMD, cmd_id, "entering test mode...");
    nvs::test_mode::set(true).await;
    info!("Setting test mode for next boot");
    crate::utils::restart(ResetTrigger::TestMode).await
}

async fn is_prod_mode() -> CommandResult {
    Ok(format!("{}", crate::utils::is_prod_mode()))
}

async fn stats() -> CommandResult {
    Ok(String::from("no stats implemented currently"))
}

static_slot!(EVENT_LOG_SLOT, EventLogResponse);

async fn event_log(mut arguments: ArgsIter<'_>) -> CommandResult {
    const DEFAULT_LIMIT: usize = 25;

    fn get_int_param(
        param: Option<&[u8]>,
        default: usize,
    ) -> std::result::Result<usize, CommandError> {
        match param.and_then(|bytes| std::str::from_utf8(bytes).ok()) {
            Some(x) => x
                .parse()
                .map_err(|_| CommandError::IncorrectUsage("Failed to parse number".to_owned())),
            None => Ok(default),
        }
    }

    let page = get_int_param(arguments.next(), 0)? as u8;
    let offset = get_int_param(arguments.next(), 0)?;
    let limit = get_int_param(arguments.next(), DEFAULT_LIMIT)?;

    itc().eventlog.flush(&EVENT_LOG_SLOT).await;
    let mut out = String::new();
    out.push_str("       time event    \n");
    out.push_str("----------- ----------------------------------------\n");
    let range = EventLogRange {
        page,
        offset,
        limit,
    };
    let data = itc()
        .eventlog
        .get(range, &EVENT_LOG_SLOT)
        .await
        .map_err(|e| e.to_string())?;
    for event in &data {
        let (time_s, time_ms) = (event.timestamp / 1000, event.timestamp % 1000);
        match event.event() {
            Some(Event::Start(e)) => {
                out.push_str(&format!("* {time_s:>5}.{time_ms:>3} Start({e:?})\n"))
            }
            Some(e) => out.push_str(&format!("{time_s:>7}.{time_ms:03} {e:?}\n")),
            None => out.push_str(&format!("{time_s:>7}.{time_ms:03} ?\n")),
        }
    }

    Ok(out)
}

async fn event_log_flush() -> CommandResult {
    itc().eventlog.flush(&EVENT_LOG_SLOT).await;
    Ok("Event log flushed".into())
}

async fn connect(cmd_id: u8, mut arguments: ArgsIter<'_>) -> CommandResult {
    let ssid = arguments
        .next()
        .and_then(|bytes| std::str::from_utf8(bytes).ok())
        .ok_or_else(|| CommandError::IncorrectUsage("SSID not specified".to_string()))?;
    let pass = arguments
        .next()
        .and_then(|bytes| std::str::from_utf8(bytes).ok())
        .ok_or_else(|| CommandError::IncorrectUsage("Password not specified".to_string()))?;

    nvs::wifi_ssid::set(ssid.to_owned()).await;
    nvs::wifi_pass::set(pass.to_owned()).await;

    restart(cmd_id).await
}

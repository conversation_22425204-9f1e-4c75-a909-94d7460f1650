use esp_idf_svc::hal::{gpio, uart::*};

use crate::prelude::*;

const MAX_INPUT_LEN: usize = 2048;

pub async fn stdin_task(uart: UART0, uart0_rx: gpio::Gpio3) -> ! {
    let config = config::Config {
        baudrate: hal::units::Hertz(921600),
        data_bits: config::DataBits::DataBits8,
        parity: config::Parity::ParityNone,
        stop_bits: config::StopBits::STOP1,
        flow_control: config::FlowControl::None,
        rx_fifo_size: 922,
        queue_size: 10,
        intr_flags: enumset::enum_set!(hal::interrupt::InterruptType::Level1),
        ..config::Config::new()
    };
    let uart = UartRxDriver::new(
        uart,
        uart0_rx,
        None::<gpio::Gpio3>,
        None::<gpio::Gpio1>,
        &config,
    )
    .unwrap();
    info!("Created stdin UART driver");
    let q = uart.event_queue().unwrap();

    let buf = Box::leak(Box::new([0u8; 256]));
    let mut input = Vec::new();

    loop {
        if input.len() > MAX_INPUT_LEN {
            log::warn!("Clearing input buffer due to surpassed max input len");
            input.clear();
        }

        q.recv_front(hal::delay::BLOCK);
        let num_bytes = match uart.read(&mut buf[..], 10) {
            Ok(num_bytes) => num_bytes,
            Err(e) if e.code() == sys::ESP_ERR_TIMEOUT => 0,
            Err(e) => defmt::panic!("stdin recv failed: {}", e.defmt()),
        };
        let slice = &buf[..num_bytes];

        for &c in slice {
            if c == 0 {
                let mut old_input = Vec::new();
                std::mem::swap(&mut input, &mut old_input);
                itc().stdin.send(old_input).await;
            } else {
                input.push(c);
            }
        }
    }
}

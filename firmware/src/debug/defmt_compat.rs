use std::ffi::CStr;

use defmt::Format;
use esp_idf_svc::sys::EspError;

use crate::prelude::*;

pub trait DefmtCompat {
    type DefmtCompatible<'a>: Format
    where
        Self: 'a;

    fn defmt<'a>(&'a self) -> Self::DefmtCompatible<'a>;
}

impl DefmtCompat for EspError {
    type DefmtCompatible<'a> = &'a str;

    fn defmt<'a>(&'a self) -> Self::DefmtCompatible<'a> {
        unsafe {
            let s = CStr::from_ptr(sys::esp_err_to_name(self.code()));
            std::str::from_utf8_unchecked(s.to_bytes())
        }
    }
}

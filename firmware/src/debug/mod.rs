use cobs2::cobsr;

use crate::prelude::*;

pub mod cmdline;
pub mod defmt_compat;
pub mod log;
pub mod stdin;
pub mod stdin_task;

pub const IDENT_LOG: u8 = 0x2A;
pub const IDENT_CMD: u8 = 0x45;
pub const IDENT_OTW: u8 = 0x89;
pub const IDENT_BAD_COBSR: u8 = 0xF6;

pub struct Encoder {
    raw_buffer: Vec<u8>,
    enc_buffer: Vec<u8>,
}

impl Encoder {
    pub const fn new() -> Self {
        Self {
            raw_buffer: Vec::new(),
            enc_buffer: Vec::new(),
        }
    }

    pub fn start(&mut self, ident: u8) {
        self.raw_buffer.clear();
        self.raw_buffer.push(ident);
    }

    pub fn push_bytes(&mut self, bytes: &[u8]) {
        self.raw_buffer.extend_from_slice(bytes);
    }

    pub fn encode(&mut self) -> &[u8] {
        let max_size = cobsr::encode_max_output_size(self.raw_buffer.len());
        self.enc_buffer.resize(max_size + 1, 0);
        let bytes = cobsr::encode_array(&mut self.enc_buffer, &self.raw_buffer).unwrap();
        let enc_len = bytes.len();
        self.enc_buffer[enc_len] = 0;
        &self.enc_buffer[0..=enc_len]
    }

    pub fn is_empty(&self) -> bool {
        // 1 byte of data considered "empty" since that one byte is the message
        // kind identifier (i.e. the part that decides whether) the message is
        // a log, cmd or otw message.
        self.raw_buffer.len() <= 1
    }
}

pub fn write_stdout_locked(bytes: &[u8]) {
    unsafe {
        let file = (*sys::__getreent())._stdout;
        let snlk = ((*file)._flags2 & sys::__SNLK as i32) == 0;
        let sstr = ((*file)._flags & sys::__SSTR as i16) == 0;
        let should_lock = snlk && sstr;
        if should_lock {
            sys::_lock_acquire_recursive(&mut (*file)._lock);
        }

        let mut written = 0;
        while written < bytes.len() {
            let ptr = bytes[written..].as_ptr() as *const _;
            let len = bytes.len() - written;
            written += sys::fwrite(ptr, 1, len as u32, file) as usize;
            sys::fflush(file);
        }

        if should_lock {
            sys::_lock_release_recursive(&mut (*file)._lock);
        }
    }
}

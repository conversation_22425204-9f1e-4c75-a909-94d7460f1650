use std::{
    cell::UnsafeCell,
    ffi::{c_char, c_int},
    sync::atomic::{AtomicBool, Ordering},
};

use critical_section::RestoreState;

use super::{write_stdout_locked, Encoder, IDENT_LOG};
use crate::prelude::*;

/* defmt implementation ***************************************************************************/

/// Enable global logging via defmt at run-time
pub fn enable() {
    STATEFUL_LOGGER.enabled.store(true, Ordering::SeqCst);
}

/// Disable global logging via defmt at run-time
pub fn disable() {
    STATEFUL_LOGGER.enabled.store(false, Ordering::SeqCst);
}

#[cfg(debug_assertions)]
const LOG_ENABLED_BY_DEFAULT: bool = true;
#[cfg(not(debug_assertions))]
const LOG_ENABLED_BY_DEFAULT: bool = false;

#[defmt::global_logger]
struct Logger;

unsafe impl defmt::Logger for Logger {
    fn acquire() {
        unsafe { STATEFUL_LOGGER.acquire() };
    }

    unsafe fn flush() {
        STATEFUL_LOGGER.flush();
    }

    unsafe fn release() {
        STATEFUL_LOGGER.release();
    }

    unsafe fn write(bytes: &[u8]) {
        STATEFUL_LOGGER.write(bytes);
    }
}

static STATEFUL_LOGGER: StatefulLogger = StatefulLogger::new();

struct StatefulLogger {
    taken: AtomicBool,
    enabled: AtomicBool,
    cs_restore: UnsafeCell<RestoreState>,
    encoder: UnsafeCell<Encoder>,
}

impl StatefulLogger {
    const fn new() -> Self {
        Self {
            taken: AtomicBool::new(false),
            enabled: AtomicBool::new(LOG_ENABLED_BY_DEFAULT),
            cs_restore: UnsafeCell::new(RestoreState::invalid()),
            encoder: UnsafeCell::new(Encoder::new()),
        }
    }

    unsafe fn acquire(&self) {
        let restore = critical_section::acquire();
        if self.taken.load(Ordering::Relaxed) {
            std::panic!("defmt logger taken reentrantly");
        }
        self.taken.store(true, Ordering::Relaxed);
        self.cs_restore.get().write(restore);
        let encoder: &mut Encoder = &mut *self.encoder.get();
        encoder.start(IDENT_LOG);
    }

    unsafe fn write(&self, bytes: &[u8]) {
        if self.enabled.load(Ordering::SeqCst) {
            let encoder: &mut Encoder = &mut *self.encoder.get();
            encoder.push_bytes(bytes);
        }
    }

    unsafe fn flush(&self) {}

    unsafe fn release(&self) {
        if !self.taken.load(Ordering::Relaxed) {
            std::panic!("defmt release out of context");
        }

        let encoder: &mut Encoder = &mut *self.encoder.get();

        if self.enabled.load(Ordering::SeqCst) && !encoder.is_empty() {
            let encoded = encoder.encode();
            write_stdout_locked(encoded);
        }

        let restore = self.cs_restore.get().read();
        self.taken.store(false, Ordering::Relaxed);
        critical_section::release(restore);
    }
}

unsafe impl Sync for StatefulLogger {}

#[defmt::panic_handler]
fn defmt_panic_handler() -> ! {
    unsafe { sys::abort() };
}

defmt::timestamp!("{=u32:ms}", { unsafe { sys::esp_log_timestamp() } });

/* log crate compatibility ************************************************************************/

/// Initialize logging with the `log` crate. All logs will be forwarded to `defmt`.
pub fn init_log() {
    ::log::set_logger(&LOG_LOGGER).unwrap();
    ::log::set_max_level(::log::LevelFilter::Info);
}

struct LogLogger;

static LOG_LOGGER: LogLogger = LogLogger;

impl ::log::Log for LogLogger {
    fn enabled(&self, metadata: &::log::Metadata) -> bool {
        metadata.level() <= ::log::LevelFilter::Info
    }

    fn log(&self, record: &::log::Record) {
        let args = format!("{}", record.args());
        defmt::debug!("[log] ({}) {}", record.target(), args.as_str());
    }

    fn flush(&self) {
        defmt::flush();
    }
}

/* esp-idf compatibility **************************************************************************/

/// Setup `esp_log` to forward all logs to `defmt`.
pub fn init_esplog() {
    unsafe { sys::esp_log_set_vprintf(Some(defmt_vprintf)) };
}

/// Enable `esp_log` logging by setting the log level filter of all targets to info.
pub fn enable_espidf_logging() {
    unsafe {
        sys::esp_log_level_set(c"*".as_ptr(), sys::esp_log_level_t_ESP_LOG_WARN);
        sys::esp_log_level_set(c"wifi".as_ptr(), sys::esp_log_level_t_ESP_LOG_ERROR);
    }
}

unsafe extern "C" fn defmt_vprintf(fmt: *const c_char, args: sys::va_list) -> c_int {
    extern "C" {
        fn vsnprintf(
            buffer: *mut c_char,
            bufsz: usize,
            format: *const c_char,
            vlist: sys::va_list,
        ) -> c_int;
    }

    const MAX_LEN: usize = 128;
    let mut buf = [0; MAX_LEN];
    let len = vsnprintf(buf.as_mut_ptr(), MAX_LEN, fmt, args);
    let buf = &buf[..(len as usize)];

    match std::str::from_utf8(buf) {
        Ok(s) => defmt::debug!("[esp] {}", s.trim()),
        Err(_) => defmt::debug!("[esp] (non-utf8 message)"),
    }

    len
}

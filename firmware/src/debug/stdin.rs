use std::cmp::Ordering;

use cobs2::cobsr;

use super::{write_stdout_locked, Encoder, IDENT_BAD_COBSR, IDENT_CMD, IDENT_OTW};
use crate::{
    debug::cmdline::handle_command, flash::ota::uart::handle_ota_msg, prelude::*, utils::rsa,
};

pub const OTW_VERIFYING_KEY_MODULUS: &[u8] =
    include_bytes!("../../sec/keys/otw-verifying-key-modulus.bin");
pub const OTW_VERIFYING_KEY_EXPONENT: &[u8] =
    include_bytes!("../../sec/keys/otw-verifying-key-exponent.bin");

pub async fn stdin_listener_task() {
    let mut current_challenge_code = generate_challenge_code();
    let mut decoded = Vec::new();
    let mut encoder = Encoder::new();

    loop {
        let raw_message = itc().stdin.recv().await;

        let decode_len_required = cobsr::decode_max_output_size(raw_message.len());
        decoded.resize(decode_len_required, 0);
        let frame = match cobsr::decode_array(&mut decoded, &raw_message) {
            Ok([]) => continue,
            Ok(frame) if !(frame[0] == IDENT_CMD || frame[0] == IDENT_OTW) => continue,
            Ok(frame) => frame,
            Err(_) => {
                encoder.start(IDENT_BAD_COBSR);
                let encoded = encoder.encode();
                write_stdout_locked(encoded);
                return;
            }
        };
        let identifier = frame[0];
        let message = &frame[1..];

        if !is_valid_message(message) {
            continue;
        }

        let id = message[0];

        if let Some(command) = message.get(258..262)
            && command == b"init"
            && identifier == IDENT_CMD
        {
            send_response(
                IDENT_CMD,
                id,
                &current_challenge_code,
                Status::Ok,
                "",
                &mut encoder,
            );
            continue;
        }

        let challenge_response = &message[1..257];
        if !is_challenge_response_ok(&current_challenge_code, challenge_response) {
            send_response(
                identifier,
                id,
                &current_challenge_code,
                Status::VerifyFailed,
                "",
                &mut encoder,
            );
            continue;
        }

        let command_len = message[257] as usize;
        let command = &message[258..(258 + command_len)];
        let Ok(command) = std::str::from_utf8(command) else {
            continue;
        };
        let arguments = ArgsIter {
            raw: &message[(259 + command_len)..],
            next_start_idx: 0,
        };

        let response = if identifier == IDENT_OTW {
            handle_ota_msg(command, arguments, id).await
        } else if identifier == IDENT_CMD {
            Box::pin(handle_command(command, arguments, id)).await
        } else {
            defmt::unreachable!();
        };

        let (status, data) = match response {
            Ok(data) => (Status::Ok, data),
            Err(err) => (Status::Err, err),
        };

        current_challenge_code = generate_challenge_code();
        send_response(
            identifier,
            id,
            &current_challenge_code,
            status,
            &data,
            &mut encoder,
        );
    }
}

fn is_valid_message(msg: &[u8]) -> bool {
    if msg.len() < 259 {
        return false;
    }
    let mut idx = 257;
    let cmd_len = msg[idx] as usize;
    if msg.len() < cmd_len + idx + 2 {
        return false;
    }
    idx += cmd_len + 1;
    let expected_arg_count = msg[idx] as usize;
    idx += 1;
    let mut arg_count = 0;
    if msg.len() == idx {
        return true;
    }
    loop {
        if arg_count >= expected_arg_count {
            return false;
        }
        arg_count += 1;
        if msg.len() <= idx + 1 {
            return false;
        }
        let len = u16::from_be_bytes([msg[idx], msg[idx + 1]]) as usize;
        let expected_arg_end = idx + len + 2;
        match msg.len().cmp(&expected_arg_end) {
            Ordering::Equal => return arg_count == expected_arg_count,
            Ordering::Less => return false,
            Ordering::Greater => idx += len + 2,
        }
    }
}

fn generate_challenge_code() -> heapless::String<16> {
    let mut challenge_code = heapless::String::new();
    for _ in 0..16 {
        challenge_code.push(fastrand::alphanumeric()).unwrap();
    }
    challenge_code
}

fn is_challenge_response_ok(code: &str, response: &[u8]) -> bool {
    rsa::verify(
        code.as_bytes(),
        response,
        OTW_VERIFYING_KEY_MODULUS,
        OTW_VERIFYING_KEY_EXPONENT,
    )
    .inspect_err(|e| error!("Failed to verify challenge response: {}", e))
    .unwrap_or(false)
}

pub fn send_dummy_cmd_response(identifier: u8, cmd_id: u8, msg: &str) {
    send_response(
        identifier,
        cmd_id,
        "................",
        Status::Ok,
        msg,
        &mut Encoder::new(),
    )
}

fn send_response(
    identifier: u8,
    cmd_id: u8,
    challenge_code: &str,
    status: Status,
    data: &str,
    encoder: &mut Encoder,
) {
    encoder.start(identifier);
    encoder.push_bytes(&[cmd_id]);
    encoder.push_bytes(challenge_code.as_bytes());
    encoder.push_bytes(&[status as u8]);
    encoder.push_bytes(data.as_bytes());
    let encoded = encoder.encode();
    write_stdout_locked(encoded);
}

#[repr(u8)]
enum Status {
    Ok = b'O',
    Err = b'E',
    VerifyFailed = b'V',
}

pub struct ArgsIter<'a> {
    raw: &'a [u8],
    next_start_idx: usize,
}

impl<'a> Iterator for ArgsIter<'a> {
    type Item = &'a [u8];

    fn next(&mut self) -> Option<Self::Item> {
        if self.raw.len() <= self.next_start_idx {
            return None;
        }
        let size_hi = self.raw[self.next_start_idx];
        let size_lo = self.raw[self.next_start_idx + 1];
        let size = u16::from_be_bytes([size_hi, size_lo]) as usize;
        let data_start = self.next_start_idx + 2;
        let data_end = data_start + size;
        let data = self.raw.get(data_start..data_end)?;
        self.next_start_idx = data_end;
        Some(data)
    }
}

// Message format:
// - 1 byte:   Message ID
// - 256 bytes: Challenge response
// - 1 byte:   Command length (c)
// - c bytes:  Command
// - 1 byte:   Argument count
// - 2 bytes:  Argument 1 length, BE (n)
// - n bytes:  Argument 1
// - 2 bytes:  Argument 2 length, BE (m)
// - m bytes:  Argument 2
// - ...

//! DNS server which is used within the access point.

use std::net::{IpAddr, Ipv4Addr, SocketAddr};

use async_net_mini::UdpSocket;
use byteorder::{<PERSON><PERSON>ndi<PERSON>, ByteOrder};

use crate::prelude::*;

pub async fn dns_server_task() {
    loop {
        if let Err(e) = run_dns_server().await {
            error!("Failed to run server: err={}", e);
            Timer::after(millis(1)).await;
        }
    }
}

async fn run_dns_server() -> Result<()> {
    let bind = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1)), 53);
    let sock = UdpSocket::bind(bind).await.context(intern!("bind UDP"))?;
    let mut req = [0u8; 512];
    let mut rsp = [0u8; 512];

    loop {
        let (len, peer) = sock
            .recv_from(&mut req)
            .await
            .context(intern!("receive dns request"))?;
        if let Some(rlen) = make_response(&req[..len], &mut rsp) {
            sock.send_to(&rsp[..rlen], peer)
                .await
                .context(intern!("send dns response"))?;
        }
    }
}

fn make_response(req: &[u8], out: &mut [u8]) -> Option<usize> {
    if req.len() < 12 {
        return None;
    }

    // — parse header —
    let id = &req[0..2];
    let flags = BigEndian::read_u16(&req[2..4]);
    let qdcount = BigEndian::read_u16(&req[4..6]);
    let opcode = (flags >> 11) & 0xF;
    let rd_bit = (flags >> 8) & 0x1 != 0;

    if opcode != 0 || qdcount == 0 {
        return Some(build_error(id, flags, 4 /* NOTIMP */, out));
    }

    // — parse the one question —
    let mut offset = 12;
    while offset < req.len() {
        let len = req[offset] as usize;
        offset += 1;
        if len == 0 {
            break;
        }
        offset = offset.checked_add(len)?;
    }

    // read QTYPE/QCLASS at current offset
    let qtype = BigEndian::read_u16(&req[offset..offset + 2]);
    let qclass = BigEndian::read_u16(&req[offset + 2..offset + 4]);
    offset += 4; // now `offset` = end of question section

    // reconstruct qname for comparison
    let mut scan = 12;
    let mut labels = Vec::new();
    while scan < offset - 4 {
        // -4 to skip the QTYPE/QCLASS we already read
        let len = req[scan] as usize;
        scan += 1;
        if len == 0 {
            break;
        }
        labels.push(&req[scan..scan + len]);
        scan += len;
    }
    let qname = labels
        .iter()
        .map(|s| std::str::from_utf8(s).unwrap_or(""))
        .collect::<Vec<_>>()
        .join(".");
    let want_me = qtype == 1
        && qclass == 1
        && qname.eq_ignore_ascii_case(env::active_params().hostname.as_str());

    // — build response header —
    out[0..2].copy_from_slice(id);
    let mut resp_flags = 0u16;
    resp_flags |= 1 << 15; // QR=1
    resp_flags |= opcode << 11; // same OPCODE
    if rd_bit {
        resp_flags |= 1 << 8;
    } // copy RD
    resp_flags |= 1 << 10; // AA=1
    resp_flags |= if want_me { 0 } else { 5 }; // RCODE
    BigEndian::write_u16(&mut out[2..4], resp_flags);

    BigEndian::write_u16(&mut out[4..6], 1); // QDCOUNT=1
    BigEndian::write_u16(&mut out[6..8], if want_me { 1 } else { 0 }); // ANCOUNT
    BigEndian::write_u16(&mut out[8..10], 0); // NSCOUNT
    BigEndian::write_u16(&mut out[10..12], 0); // ARCOUNT

    // copy the entire question section in one go
    out[12..12 + offset - 12].copy_from_slice(&req[12..offset]);
    let mut pos = offset;

    // if it's our hostname, append one A record answer
    if want_me {
        // pointer to name at offset 12: 0xC00C
        BigEndian::write_u16(&mut out[pos..pos + 2], 0xC00C);
        BigEndian::write_u16(&mut out[pos + 2..pos + 4], 1); // TYPE A
        BigEndian::write_u16(&mut out[pos + 4..pos + 6], 1); // CLASS IN
        BigEndian::write_u32(&mut out[pos + 6..pos + 10], 60); // TTL=60s
        BigEndian::write_u16(&mut out[pos + 10..pos + 12], 4); // RDLENGTH=4
        out[pos + 12..pos + 16].copy_from_slice(&[192, 168, 1, 1]); // RDATA
        pos += 16;
    }

    Some(pos)
}

fn build_error(id: &[u8], req_flags: u16, code: u8, out: &mut [u8]) -> usize {
    out[0..2].copy_from_slice(id);
    let mut f = 0u16;
    f |= 1 << 15; // QR
    f |= ((req_flags >> 11) & 0xF) << 11; // same OPCODE
    if (req_flags >> 8) & 1 != 0 {
        f |= 1 << 8;
    } // RD
    f |= code as u16;
    BigEndian::write_u16(&mut out[2..4], f);
    // zero counts except QDCOUNT=1
    BigEndian::write_u16(&mut out[4..6], 1);
    BigEndian::write_u16(&mut out[6..8], 0);
    BigEndian::write_u16(&mut out[8..10], 0);
    BigEndian::write_u16(&mut out[10..12], 0);
    // no question section echoed (we simplify)
    12
}

use std::net::{IpAddr, Ipv4<PERSON>ddr, SocketAddr};

use async_net_mini::UdpSocket;
use defmt::Display2Format;

use crate::{
    prelude::*,
    state::net::NetworkStateValue,
    utils::retry::{linear_delay, retry},
};

const PORT: u16 = 30303;
const DISCOVERY_PAYLOAD: &[u8] = b"Discovery: Who is out there?";
const BUFFER_SIZE: usize = DISCOVERY_PAYLOAD.len();

pub async fn broadcast_task() {
    _ = retry(service_broadcasts, "service_broadcasts", linear_delay(1)).await;
}

async fn service_broadcasts() -> Result<()> {
    let bind_address = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(0, 0, 0, 0)), PORT);
    let socket = UdpSocket::bind(bind_address)
        .await
        .context(intern!("bind to udp address"))?;

    info!("Listening for UDP broadcasts: port={}", PORT);

    let mut buffer = [0u8; BUFFER_SIZE];
    let mut reply_buf = Vec::with_capacity(64);

    loop {
        let Ok((size, source)) = socket
            .recv_from(&mut buffer)
            .await
            .context(intern!("receive udp broadcast"))
        else {
            continue;
        };

        let received_data = &buffer[..size];

        if received_data == DISCOVERY_PAYLOAD {
            debug!("Got discovery request: peer={}", Display2Format(&source));
            let client_ip = match source.ip() {
                IpAddr::V4(ip) => Some(ip),
                _ => None,
            };
            let client_connected_with_ap = generate_reply(&mut reply_buf, client_ip).await;
            // when the client is connected with the access point, it is only possible to
            // address it directly with address of the request. however, when the client
            // and the wolf link are in a network with the same subnet of the access point,
            // this will not work. in that case, a broadcast is required.
            let destination = match client_connected_with_ap {
                true => source,
                false => {
                    SocketAddr::new(IpAddr::V4(Ipv4Addr::new(255, 255, 255, 255)), source.port())
                }
            };
            let num_bytes = socket
                .send_to(&reply_buf, destination)
                .await
                .context(intern!("send reply"))?;
            info!(
                "sent reply ({} bytes) to {}",
                num_bytes,
                Display2Format(&destination)
            );
        }
    }
}

/// returns (u32, bool) where u32 is the ip that should be put into the reponse so that the client
/// can actually find the device, and bool is whether the client is connected via access point.
async fn get_ip(client_ip: Option<Ipv4Addr>) -> (u32, bool) {
    let ap_state = state().net.request_ap_state_update().await;

    if let Some(client_ip) = &client_ip
        && let Some(ap_state) = &ap_state
        && ap_state.connected_clients.contains(client_ip)
    {
        let ip = ap_state.ip_info.subnet.gateway;
        debug!(
            "Detected client connected to ap, returning gateway: ip={}",
            Display2Format(&ip)
        );
        return (ip.to_bits(), true);
    }

    let ip = match state().net.state.get() {
        NetworkStateValue::NotConnected => ap_state
            .map(|s| s.ip_info.subnet.gateway)
            .unwrap_or(Ipv4Addr::UNSPECIFIED),
        NetworkStateValue::Wifi(ip_info, _) => ip_info.ip,
        NetworkStateValue::Eth(ip_info) => ip_info.ip,
    };

    debug!(
        "Returning non-access-point gateway: ip={}",
        Display2Format(&ip)
    );

    (ip.to_bits(), false)
}

async fn generate_reply(buf: &mut Vec<u8>, client_ip: Option<Ipv4Addr>) -> bool {
    buf.clear();
    buf.push(0x02);
    buf.extend_from_slice(&crate::utils::mac::eth_bytes());
    buf.extend_from_slice(b"\r\n");
    buf.push(0x03);
    // -A is required for smartset to detect the device as Wolf Link, and not as ISM7
    buf.extend_from_slice(b"ESP32-LinkR-A");
    buf.extend_from_slice(b"\r\n");
    buf.push(0x04);
    buf.extend_from_slice(b"WOLFLINK");
    buf.extend_from_slice(b"\r\n");
    buf.push(0x05);

    let (ip, client_connected_with_ap) = get_ip(client_ip).await;
    buf.extend_from_slice(&ip.to_be_bytes());
    buf.extend_from_slice(b"\r\n");

    client_connected_with_ap
}

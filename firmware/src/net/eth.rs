use std::net::Ipv4Addr;

use esp_idf_svc::{
    eth::{AsyncEth, EspEth, EthDriver, RmiiClockConfig, RmiiEth, RmiiEthChipset},
    eventloop::EspSystemEventLoop,
    hal::{
        gpio::{self, Gpio4, Gpio5, PinDriver},
        mac::MAC,
        peripheral::Peripheral,
    },
    handle::RawHandle,
    ipv4::{self, IpInfo, Mask, Subnet},
    netif::{EspNetif, NetifConfiguration},
    timer::EspTaskTimerService,
};

use crate::{
    flash::eventlog::{
        events::{EthConnectResult, EthStartResult, Event, NetifUpResult},
        utils::{ResultEventLog, ResultEventLogAsync},
    },
    prelude::*,
    utils::error::AnyError,
};

pub async fn reset_eth(eth_pwr: Gpio5, clk_en: Gpio4) -> Result<()> {
    let mut eth_pwr = PinDriver::output(eth_pwr)?;
    let mut clk_en = PinDriver::output(clk_en)?;

    eth_pwr.set_low()?;
    clk_en.set_low()?;
    Timer::after(millis(100)).await;

    info!("Powering Ethernet");
    eth_pwr.set_high()?;
    Timer::after(millis(10)).await;
    clk_en.set_high()?;
    Timer::after(millis(10)).await;

    // leak pins so they stay configured as output
    std::mem::forget(eth_pwr);
    std::mem::forget(clk_en);

    Ok(())
}

pub struct EthPins {
    pub mac: MAC,
    pub rmii_rdx0: gpio::Gpio25,
    pub rmii_rdx1: gpio::Gpio26,
    pub rmii_crs_dv: gpio::Gpio27,
    pub rmii_mdc: gpio::Gpio23,
    pub rmii_txd1: gpio::Gpio22,
    pub rmii_tx_en: gpio::Gpio21,
    pub rmii_txd0: gpio::Gpio19,
    pub rmii_mdio: gpio::Gpio18,
    pub rmii_ref_clk_config: RmiiClockConfig<gpio::Gpio0, gpio::Gpio16, gpio::Gpio17>,
}

pub struct Eth<'d> {
    eth: AsyncEth<EspEth<'d, RmiiEth>>,
}

impl<'d> Eth<'d> {
    pub async fn init(
        pins: EthPins,
        rst: Option<impl Peripheral<P = impl gpio::OutputPin> + 'd>,
        chipset: RmiiEthChipset,
        phy_addr: Option<u32>,
        event_loop: EspSystemEventLoop,
        client_ip_config: ipv4::ClientConfiguration,
    ) -> Result<Self> {
        let ret = Self::try_init(pins, rst, chipset, phy_addr, event_loop, client_ip_config).await;

        match ret {
            Ok(eth) => {
                Event::EthStart(EthStartResult::Ok).log().await;
                Ok(eth)
            }
            Err((err, event)) => {
                Event::EthStart(event).log().await;
                itc().eventlog.flush(&ReturnSlot::new()).await;
                Err(err)
            }
        }
    }

    pub async fn try_init(
        pins: EthPins,
        rst: Option<impl Peripheral<P = impl gpio::OutputPin> + 'd>,
        chipset: RmiiEthChipset,
        phy_addr: Option<u32>,
        event_loop: EspSystemEventLoop,
        client_ip_config: ipv4::ClientConfiguration,
    ) -> Result<Self, (AnyError, EthStartResult)> {
        let driver = EthDriver::new(
            pins.mac,
            pins.rmii_rdx0,
            pins.rmii_rdx1,
            pins.rmii_crs_dv,
            pins.rmii_mdc,
            pins.rmii_txd1,
            pins.rmii_tx_en,
            pins.rmii_txd0,
            pins.rmii_mdio,
            pins.rmii_ref_clk_config,
            rst,
            chipset,
            phy_addr,
            event_loop.clone(),
        )
        .context(intern!("initialise eth driver"))
        .map_err(|e| (e, EthStartResult::ErrInit))?;

        let netif = get_netif_config(client_ip_config)
            .await
            .context(intern!("initialise eth netif"))
            .map_err(|e| (e, EthStartResult::ErrNetif))?;

        let eth = EspEth::wrap_all(driver, netif)
            .context(intern!("attach netif to eth"))
            .map_err(|e| (e, EthStartResult::ErrNetif))?;

        let timer = EspTaskTimerService::new()
            .context(intern!("initialise eth task timer"))
            .map_err(|e| (e, EthStartResult::ErrTimer))?;

        let mut eth = AsyncEth::wrap(eth, event_loop, timer)
            .context(intern!("wrap async eth"))
            .map_err(|e| (e, EthStartResult::ErrStartAsync))?;

        eth.start()
            .await
            .context(intern!("start eth"))
            .map_err(|e| (e, EthStartResult::ErrStart))?;

        Ok(Self { eth })
    }

    pub async fn wait_until_connected(&self) -> Result<()> {
        loop {
            match self.eth.wait_connected().await {
                Ok(()) => break,
                Err(e) if e.code() == sys::ESP_ERR_TIMEOUT => continue,
                Err(e) => {
                    Event::EthConnect(EthConnectResult::Err).log().await;
                    return Err(e).context(intern!("await layer 2 eth connection"));
                }
            }
        }

        Event::EthConnect(EthConnectResult::Ok).log().await;
        info!("Connected to layer 2");

        Ok(())
    }

    pub async fn wait_for_ip(&mut self) -> Result<()> {
        info!("Waiting for ip");
        if !self.eth.is_started().unwrap_or(false) {
            self.eth
                .start()
                .err_event(Event::NetifUp(NetifUpResult::ErrStartFailed))
                .await
                .context(intern!("start eth"))?;
        }

        self.eth
            .wait_netif_up()
            .err_event(Event::NetifUp(NetifUpResult::ErrUp))
            .await
            .context(intern!("await netif up eth"))?;
        info!("Connected");

        let netif = self.eth.eth().netif();
        let handle = netif.handle();
        unsafe { sys::esp!(sys::esp_netif_set_default_netif(handle)) }
            .err_event(Event::NetifUp(NetifUpResult::ErrSetDefaultNetifFailed))
            .await
            .context(intern!("set eth as default netif"))?;

        let ip = netif
            .get_ip_info()
            .map(|i| i.ip)
            .unwrap_or(Ipv4Addr::UNSPECIFIED);
        Event::NetifUp(NetifUpResult::Ok(ip)).log().await;

        Ok(())
    }

    pub async fn set_ip_info(&mut self, ip_info: IpInfo) -> Result<()> {
        Ok(self.eth.eth_mut().netif_mut().set_ip_info(ip_info)?)
    }

    pub fn is_connected(&self) -> bool {
        self.eth.is_connected().unwrap_or(false)
    }

    #[allow(dead_code)]
    pub fn get_ip_info(&self) -> Option<IpInfo> {
        self.eth.eth().netif().get_ip_info().ok()
    }
}

async fn get_netif_config(client_ip_config: ipv4::ClientConfiguration) -> Result<EspNetif> {
    let ip_configuration = ipv4::Configuration::Client(client_ip_config);
    let dns3 = nvs::net_dns3::get().await;

    let conf = NetifConfiguration {
        ip_configuration: Some(ip_configuration),
        ..NetifConfiguration::eth_default_client()
    };

    let mut netif = EspNetif::new_with_conf(&conf)?;

    let mut dns_info = sys::esp_netif_dns_info_t {
        ip: sys::_ip_addr {
            u_addr: sys::_ip_addr__bindgen_ty_1 {
                ip4: sys::esp_ip4_addr {
                    addr: dns3.to_bits(),
                },
            },
            type_: sys::lwip_ip_addr_type_IPADDR_TYPE_V4 as u8,
        },
    };
    let handle = netif.handle();
    unsafe {
        sys::esp!(sys::esp_netif_set_dns_info(
            handle,
            sys::esp_netif_dns_type_t_ESP_NETIF_DNS_FALLBACK,
            &mut dns_info
        ))?;
    }

    if state().test_mode() {
        info!("Using static ip for eth due to test mode");
        netif.set_ip_info(IpInfo {
            ip: Ipv4Addr::new(192, 168, 1, 67),
            subnet: Subnet {
                gateway: Ipv4Addr::new(192, 168, 1, 1),
                mask: Mask(24),
            },
            dns: None,
            secondary_dns: None,
        })?;
    }

    Ok(netif)
}

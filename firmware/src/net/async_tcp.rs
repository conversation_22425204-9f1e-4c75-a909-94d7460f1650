use std::{
    future::Future,
    io,
    net::TcpStream,
    os::fd::AsRawFd,
    pin::{pin, Pin},
    rc::Rc,
    task::{Context, Poll},
};

use async_io_mini::Async;
use defmt::Debug2Format;
use esp_idf_svc::{io::EspIOError, sys::ESP_FAIL};
use futures_lite::{AsyncRead, AsyncWrite};

use crate::{
    net::tls::{EspAsyncTls, PollableSocket, Socket},
    prelude::*,
};

pub struct AsyncTcp(pub Option<Async<TcpStream>>);

impl Socket for AsyncTcp {
    fn handle(&self) -> i32 {
        self.0.as_ref().unwrap().as_raw_fd()
    }

    fn release(&mut self) -> Result<(), EspError> {
        use std::net::Shutdown;

        let socket = self.0.take().unwrap();
        let socket = socket.into_inner().unwrap();
        let _ = socket.shutdown(Shutdown::Both);
        // Leak the socket because it will be closed by ESP IDF
        std::mem::forget(socket);

        Ok(())
    }
}

impl PollableSocket for AsyncTcp {
    fn poll_readable(&self, ctx: &mut std::task::Context) -> std::task::Poll<Result<(), EspError>> {
        self.0.as_ref().unwrap().poll_readable(ctx).map_err(|e| {
            error!("Got error from readable future: err={}", Debug2Format(&e));
            EspError::from_infallible::<ESP_FAIL>()
        })
    }

    fn poll_writable(&self, ctx: &mut std::task::Context) -> std::task::Poll<Result<(), EspError>> {
        self.0.as_ref().unwrap().poll_writable(ctx).map_err(|e| {
            error!("Got error from writable future: err={}", Debug2Format(&e));
            EspError::from_infallible::<ESP_FAIL>()
        })
    }
}

pub struct AsyncTcpStream(pub Option<Async<TcpStream>>);

impl Socket for AsyncTcpStream {
    fn handle(&self) -> i32 {
        self.0.as_ref().unwrap().as_raw_fd()
    }

    fn release(&mut self) -> std::prelude::v1::Result<(), EspError> {
        let socket = self.0.take().unwrap();

        // Leak the socket because it will be closed by ESP IDF
        std::mem::forget(socket);

        Ok(())
    }
}

impl PollableSocket for AsyncTcpStream {
    fn poll_readable(&self, ctx: &mut Context) -> Poll<std::prelude::v1::Result<(), EspError>> {
        self.0.as_ref().unwrap().poll_readable(ctx).map_err(|e| {
            error!("Got error from readable future: err={}", Debug2Format(&e));
            EspError::from_infallible::<ESP_FAIL>()
        })
    }

    fn poll_writable(&self, ctx: &mut Context) -> Poll<std::prelude::v1::Result<(), EspError>> {
        self.0.as_ref().unwrap().poll_writable(ctx).map_err(|e| {
            error!("Got error from writable future: err={}", Debug2Format(&e));
            EspError::from_infallible::<ESP_FAIL>()
        })
    }
}

pub struct AsyncReadWriter<S>(pub(crate) Rc<EspAsyncTls<S>>)
where
    S: PollableSocket;

impl<S> AsyncRead for AsyncReadWriter<S>
where
    S: PollableSocket,
{
    fn poll_read(
        self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &mut [u8],
    ) -> Poll<io::Result<usize>> {
        pin!(self.0.read(buf))
            .poll(cx)
            .map_err(|e| io::Error::new(io::ErrorKind::Other, EspIOError(e)))
    }
}

impl<S> AsyncWrite for AsyncReadWriter<S>
where
    S: PollableSocket,
{
    fn poll_write(
        self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &[u8],
    ) -> Poll<io::Result<usize>> {
        pin!(self.0.write(buf))
            .poll(cx)
            .map_err(|e| io::Error::new(io::ErrorKind::Other, EspIOError(e)))
    }

    fn poll_flush(self: Pin<&mut Self>, _cx: &mut Context<'_>) -> Poll<io::Result<()>> {
        Poll::Ready(Ok(()))
    }

    fn poll_close(self: Pin<&mut Self>, _cx: &mut Context<'_>) -> Poll<io::Result<()>> {
        Poll::Ready(Ok(()))
    }
}

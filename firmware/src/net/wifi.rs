use std::net::Ipv4Addr;

use esp_idf_svc::{
    eventloop::EspSystemEventLoop,
    hal::{modem::Modem, peripheral::Peripheral},
    handle::RawHandle,
    ipv4::{self, IpInfo, RouterConfiguration, Subnet},
    netif::{EspNetif, NetifConfiguration},
    timer::EspTaskTimerService,
    wifi::{
        self, AccessPointConfiguration, AccessPointInfo, AsyncWifi, AuthMethod, EspWifi,
        PmfConfiguration, Protocol, ScanMethod, ScanSortMethod, WifiDriver, WpsConfig,
        WpsFactoryInfo, WpsStatus, WpsType,
    },
};
use num_enum::TryFromPrimitive;

use super::task::default_ip_info;
use crate::{
    flash::eventlog::{
        events::{
            Event, NetifUpResult, StaConnectResult, WifiStartResult, WpsR<PERSON>ult, WpsStartResult,
        },
        utils::{ResultEventLog, ResultEventLogAsync},
    },
    prelude::*,
    state::net::{ApState, ClientList, RssiLevel},
    utils::error::AnyError,
};

pub struct Wifi<'d> {
    wifi: AsyncWifi<EspWifi<'d>>,
}

impl<'d> Wifi<'d> {
    pub async fn init(
        modem: impl Peripheral<P = Modem> + 'd,
        sysloop: EspSystemEventLoop,
        sta_ip_config: ipv4::ClientConfiguration,
    ) -> Result<Self> {
        match Self::try_init(modem, sysloop, sta_ip_config).await {
            Ok(wifi) => {
                Event::WifiStart(WifiStartResult::Ok).log().await;
                Ok(wifi)
            }
            Err((err, event)) => {
                Event::WifiStart(event).log().await;
                itc().eventlog.flush(&ReturnSlot::new()).await;
                Err(err)
            }
        }
    }

    async fn try_init(
        modem: impl Peripheral<P = Modem> + 'd,
        sysloop: EspSystemEventLoop,
        sta_ip_config: ipv4::ClientConfiguration,
    ) -> Result<Self, (AnyError, WifiStartResult)> {
        let driver = WifiDriver::new(modem, sysloop.clone(), None)
            .context(intern!("initialise wifi driver"))
            .map_err(|e| (e, WifiStartResult::ErrInit))?;

        let (netif_sta, netif_ap) = get_netif_config(sta_ip_config)
            .await
            .context(intern!("initialise wifi netif"))
            .map_err(|e| (e, WifiStartResult::ErrNetif))?;

        let wifi = EspWifi::wrap_all(driver, netif_sta, netif_ap)
            .context(intern!("attach wifi netif"))
            .map_err(|e| (e, WifiStartResult::ErrNetif))?;

        let timer_service = EspTaskTimerService::new()
            .context(intern!("init wifi task timer"))
            .map_err(|e| (e, WifiStartResult::ErrTimer))?;

        let mut wifi = AsyncWifi::wrap(wifi, sysloop.clone(), timer_service)
            .context(intern!("wrap async wifi"))
            .map_err(|e| (e, WifiStartResult::ErrStartAsync))?;

        let config = get_config().await;
        wifi.set_configuration(&config)
            .context(intern!("set wifi config"))
            .map_err(|e| (e, WifiStartResult::ErrSetConfig))?;

        wifi.start()
            .await
            .context(intern!("start wifi"))
            .map_err(|e| (e, WifiStartResult::ErrStart))?;

        Ok(Self { wifi })
    }

    pub async fn scan(&mut self) -> Result<Vec<AccessPointInfo>> {
        let res = self.wifi.scan().await?;
        info!("Finished Wi-Fi scan: num_aps={}", res.len());
        Ok(res)
    }

    pub async fn initiate_connection(&mut self) -> Result<()> {
        let _ = self.wifi.disconnect().await;
        Timer::after(secs(5)).await;

        let config = get_config().await;
        self.wifi
            .set_configuration(&config)
            .err_event(Event::StaConnect(StaConnectResult::ErrSetConfig))
            .await
            .context(intern!("set wifi config"))?;

        self.wifi
            .wifi_mut()
            .connect()
            .err_event(Event::StaConnect(StaConnectResult::ErrConnectOther))
            .await
            .context(intern!("initiate wifi connection"))?;

        Ok(())
    }

    pub async fn wait_until_connected(&self) -> Result<()> {
        self.wait_status_connected()
            .ok_event(Event::StaConnect(StaConnectResult::Ok))
            .err_event(Event::StaConnect(StaConnectResult::ErrWaitConnected))
            .await
            .context(intern!("connect wifi layer 2"))?;
        info!("Connected to layer 2");
        Ok(())
    }

    pub async fn wait_status_connected(&self) -> Result<()> {
        Ok(self
            .wifi
            .wifi_wait(|wifi| wifi.wifi().is_connected().map(|s| !s), None)
            .await?)
    }

    pub async fn wait_for_ip(&self) -> Result<()> {
        self.wifi
            .wait_netif_up()
            .err_event(Event::NetifUp(NetifUpResult::ErrUp))
            .await
            .context(intern!("get IP via DHCP"))?;

        let netif = self.wifi.wifi().sta_netif();
        let ip = netif
            .get_ip_info()
            .map(|i| i.ip)
            .unwrap_or(Ipv4Addr::UNSPECIFIED);

        let handle = netif.handle();
        unsafe { sys::esp!(sys::esp_netif_set_default_netif(handle)) }
            .err_event(Event::NetifUp(NetifUpResult::ErrSetDefaultNetifFailed))
            .await?;

        Event::NetifUp(NetifUpResult::Ok(ip)).log().await;

        Ok(())
    }

    pub async fn disconnect(&mut self) -> Result<()> {
        Ok(self.wifi.disconnect().await?)
    }

    pub async fn set_ip_info(&mut self, ip_info: IpInfo) -> Result<()> {
        self.wifi.wifi_mut().sta_netif_mut().set_ip_info(ip_info)?;
        let _ = self.wifi.disconnect().await;
        Ok(())
    }

    pub async fn start_wps(&mut self) -> Result<()> {
        let _ = self.wifi.disconnect().await;

        if !self.wifi.is_started().unwrap_or(false) {
            self.wifi
                .start()
                .err_event(Event::WpsStart(WpsStartResult::ErrWifiStart))
                .await
                .context(intern!("start wifi"))?;
        }

        let cfg = WpsConfig {
            factory_info: WpsFactoryInfo {
                device_name: "WOLF Link",
                manufacturer: "WOLF GmbH",
                model_name: "WOLF Link Redesign",
                model_number: "1",
            },
            wps_type: WpsType::Pbc,
        };

        let wps_status = self
            .wifi
            .start_wps(&cfg)
            .ok_event(Event::WpsStart(WpsStartResult::Ok))
            .err_event(Event::WpsStart(WpsStartResult::ErrWpsStart))
            .await
            .context(intern!("start wps"))?;

        match wps_status {
            WpsStatus::SuccessConnected => info!("Got credentials from WPS"),
            WpsStatus::SuccessMultipleAccessPoints(credentials) => {
                warn!("Got multiple APs from WPS, trying the first one");

                let wifi_cfg = wifi::Configuration::Client(wifi::ClientConfiguration {
                    ssid: credentials[0].ssid.clone(),
                    bssid: None,
                    auth_method: AuthMethod::WPA2Personal,
                    password: credentials[0].passphrase.clone(),
                    channel: None,
                    scan_method: ScanMethod::CompleteScan(ScanSortMethod::Signal),
                    pmf_cfg: PmfConfiguration::NotCapable,
                });

                self.wifi
                    .set_configuration(&wifi_cfg)
                    .err_event(Event::WpsFinished(WpsResult::ErrSetConfig))
                    .await
                    .context(intern!("set config after WPS"))?;
            }
            err => {
                let (e, code) = match err {
                    WpsStatus::Failure => (anyhow!("WPS Failure"), WpsResult::ErrFail),
                    WpsStatus::Timeout => (anyhow!("WPS Timeout"), WpsResult::ErrTimeout),
                    WpsStatus::Pin(_) => (anyhow!("WPS Pin"), WpsResult::ErrWrongMethod),
                    WpsStatus::PbcOverlap => (anyhow!("WPS PBC Overlap"), WpsResult::ErrPbcOverlap),
                    _ => defmt::unreachable!(),
                };
                Event::WpsFinished(code).log().await;
                return Err(e);
            }
        }

        let config = self
            .wifi
            .get_configuration()
            .err_event(Event::WpsFinished(WpsResult::ErrGetConfig))
            .await
            .context(intern!("get wifi config"))?;

        match config {
            wifi::Configuration::Client(config) | wifi::Configuration::Mixed(config, _) => {
                let mut active_wifi_data = env::active_params().wifi_data.lock().await;
                active_wifi_data.ssid = config.ssid.clone();
                active_wifi_data.pass = config.password.clone();
                active_wifi_data.auth_method = config.auth_method;
                drop(active_wifi_data);
                nvs::wifi_ssid::set(config.ssid.to_string()).await;
                nvs::wifi_pass::set(config.password.to_string()).await;
                nvs::wifi_authmethod::set(config.auth_method).await;
            }
            _ => {
                Event::WpsFinished(WpsResult::ErrNotInStationMode)
                    .log()
                    .await;
                bail!("Not in station mode after WPS")
            }
        }

        Event::WpsFinished(WpsResult::Ok).log().await;

        Ok(())
    }

    pub async fn refresh_config(&mut self) -> Result<()> {
        let config = get_config().await;
        self.wifi.set_configuration(&config)?;
        Ok(())
    }

    pub async fn ap_has_active_connection(&self) -> bool {
        let mut clients: sys::wifi_sta_list_t = Default::default();
        sys::esp!(unsafe { sys::esp_wifi_ap_get_sta_list(&mut clients as *mut _) })
            .map(|_| clients.num > 0)
            .unwrap_or(false)
    }

    pub fn is_ap_active(&self) -> bool {
        self.wifi.wifi().ap_netif().is_up().unwrap_or(false)
    }

    pub fn get_ip_info(&self) -> Option<IpInfo> {
        self.wifi.wifi().sta_netif().get_ip_info().ok()
    }

    pub fn get_rssi_level(&self) -> Result<RssiLevel> {
        if !self.wifi.is_connected()? {
            return Ok(RssiLevel(0));
        }

        let mut strength = 0;
        unsafe {
            sys::esp!(svc::sys::esp_wifi_sta_get_rssi(&mut strength))?;
        }

        // Map RSSI to 0..4 signal strength indicator
        let strength = match strength {
            -100..=-80 => 0,
            -79..=-60 => 1,
            -59..=-40 => 2,
            -39..=-30 => 3,
            -29..=10 => 4,
            _ => 0,
        };

        Ok(RssiLevel(strength))
    }

    pub fn get_ap_state(&self) -> Result<Option<ApState>> {
        let netif = self.wifi.wifi().ap_netif();
        if netif.is_up().unwrap_or(false) {
            let ip_info = netif.get_ip_info().unwrap_or_else(|_| default_ip_info());
            let connected_clients = self.get_connected_clients()?;
            let ssid = match self.wifi.get_configuration() {
                Ok(wifi::Configuration::AccessPoint(ap) | wifi::Configuration::Mixed(_, ap)) => {
                    ap.ssid
                }
                _ => heapless::String::new(),
            };
            Ok(Some(ApState {
                ip_info,
                connected_clients,
                ssid,
            }))
        } else {
            Ok(None)
        }
    }

    fn get_connected_clients(&self) -> Result<ClientList> {
        let ap_handle = self.wifi.wifi().ap_netif().handle();

        let mut clients: sys::wifi_sta_list_t = Default::default();
        sys::esp!(unsafe { sys::esp_wifi_ap_get_sta_list(&mut clients as *mut _) })?;

        let mut mac_ip_pairs: [sys::esp_netif_pair_mac_ip_t; sys::ESP_WIFI_MAX_CONN_NUM as usize] =
            Default::default();
        for (i, pair) in mac_ip_pairs
            .iter_mut()
            .enumerate()
            .take(clients.num as usize)
        {
            pair.mac = clients.sta[i].mac;
        }
        sys::esp!(unsafe {
            sys::esp_netif_dhcps_get_clients_by_mac(
                ap_handle,
                clients.num,
                &mut mac_ip_pairs as *mut _,
            )
        })?;

        let mut out = ClientList::new();
        for pair in &mac_ip_pairs[..(clients.num as usize)] {
            let ip = Ipv4Addr::from_bits(pair.ip.addr.to_be());
            out.push(ip).unwrap();
        }
        Ok(out)
    }
}

async fn get_config() -> wifi::Configuration {
    let test_mode = state().test_mode();
    let (mut sta_ssid, mut sta_pass, sta_auth_method) = if test_mode {
        let ssid = nvs::test_mode_ssid::get_heapless().await;
        let pass = nvs::test_mode_pass::get_heapless().await;
        let auth_method = nvs::test_mode_auth::get().await;
        (ssid, pass, auth_method)
    } else {
        let active_wifi_data = env::active_params().wifi_data.lock().await;
        let ssid = active_wifi_data.ssid.clone();
        let pass = active_wifi_data.pass.clone();
        let auth_method = active_wifi_data.auth_method;
        (ssid, pass, auth_method)
    };
    let ap_active = nvs::ap_enabled::get().await;

    // prevent wifi error loop if ssid invalid in test mode
    if test_mode && sta_ssid.is_empty() {
        warn!("Substituting empty SSID in test mode");
        sta_ssid = "invalid".try_into().unwrap();
    }

    // prevent wifi initialization failing when password is invalid
    if !test_mode && sta_auth_method == AuthMethod::None {
        sta_pass = "".try_into().unwrap();
    } else if sta_pass.len() < 8 {
        sta_pass = "xxxxxxxx".try_into().unwrap();
    }

    let cli = wifi::ClientConfiguration {
        ssid: sta_ssid,
        bssid: None,
        auth_method: sta_auth_method,
        password: sta_pass,
        channel: None,
        scan_method: ScanMethod::CompleteScan(ScanSortMethod::Signal),
        pmf_cfg: PmfConfiguration::NotCapable,
    };

    if ap_active {
        let ap_auth = nvs::ap_authmethod::get().await;
        let ap_channel = nvs::ap_channel::get().await;

        let ap = AccessPointConfiguration {
            ssid: env::active_params().ap_ssid.clone(),
            ssid_hidden: false,
            channel: ap_channel,
            secondary_channel: None,
            protocols: Protocol::P802D11B | Protocol::P802D11BG | Protocol::P802D11BGN,
            auth_method: ap_auth,
            password: env::active_params().password.clone(),
            max_connections: 2,
        };
        wifi::Configuration::Mixed(cli, ap)
    } else {
        wifi::Configuration::Client(cli)
    }
}

async fn get_netif_config(
    sta_ip_config: ipv4::ClientConfiguration,
) -> Result<(EspNetif, EspNetif)> {
    let sta_ip_config = ipv4::Configuration::Client(sta_ip_config);

    let dns3 = nvs::net_dns3::get().await;

    let netif_sta = EspNetif::new_with_conf(&NetifConfiguration {
        ip_configuration: Some(sta_ip_config),
        ..NetifConfiguration::wifi_default_client()
    })?;

    let mut dns_info = sys::esp_netif_dns_info_t {
        ip: sys::_ip_addr {
            u_addr: sys::_ip_addr__bindgen_ty_1 {
                ip4: sys::esp_ip4_addr {
                    addr: dns3.to_bits(),
                },
            },
            type_: sys::lwip_ip_addr_type_IPADDR_TYPE_V4 as u8,
        },
    };
    let handle = netif_sta.handle();
    unsafe {
        sys::esp!(sys::esp_netif_set_dns_info(
            handle,
            sys::esp_netif_dns_type_t_ESP_NETIF_DNS_FALLBACK,
            &mut dns_info
        ))?;
    }

    let ap_ip_config = ipv4::Configuration::Router(RouterConfiguration {
        subnet: Subnet {
            gateway: nvs::ap_gateway::get().await,
            mask: nvs::ap_mask::get().await,
        },
        dhcp_enabled: nvs::ap_dhcp::get().await,
        dns: Some(nvs::ap_dns1::get().await),
        secondary_dns: Some(nvs::ap_dns2::get().await),
    });

    let netif_ap = EspNetif::new_with_conf(&NetifConfiguration {
        ip_configuration: Some(ap_ip_config),
        ..NetifConfiguration::wifi_default_router()
    })?;

    Ok((netif_sta, netif_ap))
}

// https://docs.espressif.com/projects/esp-idf/en/stable/esp32/api-guides/wifi.html#wi-fi-reason-code
#[repr(u16)]
#[derive(TryFromPrimitive, Debug, Copy, Clone)]
pub enum WifiDisconnectReason {
    UnspecifiedInternalFailure = 1,
    AuthenticationExpired = 2,
    DeauthenticatedBecauseStationLeft = 3,
    DisassociatedDueToInactivity = 4,
    DisassociatedDueToTooManyStations = 5,
    StationNotAuthenticated = 6,
    StationNotAssociated = 7,
    StationLeft = 8,
    AssociatedButNotAuthenticated = 9,
    InvalidPowerCapabilityElement = 10,
    InvalidSupportedChannelsElement = 11,
    ApWantsToTransitionToOtherAp = 12,
    InvalidInfoElement = 13,
    MessageIntegrityCodeFailure = 14,
    FourWayHandshakeTimeout = 15,
    GroupKeyHandshakeTimeout = 16,
    InfoElementInHandshakeDiffersFromBeacon = 17,
    InvalidGroupCipher = 18,
    InvalidPairwiseCipher = 19,
    InvalidAkmp = 20,
    UnsupportedRsneVersion = 21,
    InvalidRsneCapabilities = 22,
    IEEE8021XAuthFailed = 23,
    CipherSuiteRejectedDueToSecurityPolicies = 24,
    TdlsPeerStationUnreachable = 25,
    UnspecifiedTdlsError = 26,
    SessionTerminatedBySspRequest = 27,
    NoSspRoamingAgreement = 28,
    InvalidSspCipherOrAkmRequirement = 29,
    NotAuthorizedInThisLocation = 30,
    QosApLacksSufficientBandwidthDueToBssChanges = 31,
    UnspecifiedQosError = 32,
    InsufficientBandwidth = 33,
    TooManyUnacknlowledgedFrames = 34,
    TransmitOutsideTxopLimits = 35,
    RequestingStaLeftBss = 36,
    RequestingStaNoLongerInSession = 37,
    RequestingStaUsesMechanismWhichIsNotSetUp = 38,
    Timeout = 39,
    AuthorizedAccessLimitReached = 46,
    DisassociatedDueToExternalServiceRequirements = 47,
    InvalidFtActionFrameCount = 48,
    InvalidPairwiseMasterKeyIdentifier = 49,
    InvalidMde = 50,
    InvalidFte = 51,
    TransmissionLinkEstablishmentFailed = 67,
    AlternativeChannelIsOccupied = 68,
    BeaconTimeout = 200,
    AccessPointNotFound = 201,
    AuthenticationFailure = 202,
    AssociationFailed = 203,
    HandshakeTimeout = 204,
    ConnectionFailed = 205,
    DisconnectDueToTsfResetOfAp = 206,
    StationIsRoamingToOtherAp = 207,
    AssociationComebackTimeTooLong = 208,
    SaQueryTimeout = 209,
    IncompatibleSecurity = 210,
    SecurityRequirementNotMet = 211,
    RssiRequirementNotMet = 212,
    EthernetConnected = 1001,
    Unknown = 0xFFFF,
}

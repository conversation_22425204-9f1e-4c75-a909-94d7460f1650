#![allow(dead_code)]

use std::{
    ffi::{c_int, CStr},
    rc::Rc,
    task::{Context, Poll},
    time::Duration,
};

use embedded_svc::io;
pub use esp_idf_svc::tls::X509;
use esp_idf_svc::{
    io::EspIOError,
    sys::{
        ESP_ERR_INVALID_SIZE, ESP_ERR_NO_MEM, ESP_FAIL, ESP_TLS_ERR_SSL_WANT_READ,
        ESP_TLS_ERR_SSL_WANT_WRITE, EWOULDBLOCK,
    },
};
use futures_lite::{AsyncRead, AsyncWrite};

use super::async_tcp::{AsyncReadWriter, AsyncTcp};
use crate::{flash::eventlog::events::TlsConnectResult, prelude::*};

/// str to cstr, will be truncated if str is larger than buf.len() - 1
///
/// # Panics
///
/// * Panics if buffer is empty.
pub fn cstr_from_str_truncating<'a>(rust_str: &str, buf: &'a mut [u8]) -> &'a CStr {
    defmt::assert!(!buf.is_empty());

    let max_str_size = buf.len() - 1; // account for NUL
    let truncated_str = &rust_str[..max_str_size.min(rust_str.len())];
    buf[..truncated_str.len()].copy_from_slice(truncated_str.as_bytes());
    buf[truncated_str.len()] = b'\0';

    CStr::from_bytes_with_nul(&buf[..truncated_str.len() + 1]).unwrap()
}

/// Convert slice of rust strs to NULL-terminated fixed size array of c string pointers
///
/// # Panics
///
/// * Panics if cbuf is empty.
/// * Panics if N is <= 1
pub fn cstr_arr_from_str_slice<const N: usize>(
    rust_strs: &[&str],
    mut cbuf: &mut [u8],
) -> Result<[*const i8; N], EspError> {
    defmt::assert!(N > 1);
    defmt::assert!(!cbuf.is_empty());

    // ensure last element stays NULL
    if rust_strs.len() > N - 1 {
        return Err(EspError::from_infallible::<ESP_ERR_INVALID_SIZE>());
    }

    let mut cstrs = [core::ptr::null(); N];

    for (i, s) in rust_strs.iter().enumerate() {
        let max_str_size = cbuf.len() - 1; // account for NUL
        if s.len() > max_str_size {
            return Err(EspError::from_infallible::<ESP_ERR_INVALID_SIZE>());
        }
        cbuf[..s.len()].copy_from_slice(s.as_bytes());
        cbuf[s.len()] = b'\0';
        let cstr = CStr::from_bytes_with_nul(&cbuf[..s.len() + 1]).unwrap();
        cstrs[i] = cstr.as_ptr() as *const i8;

        cbuf = &mut cbuf[s.len() + 1..];
    }

    Ok(cstrs)
}

/// see https://www.ietf.org/rfc/rfc3280.txt ub-common-name-length
const MAX_COMMON_NAME_LENGTH: usize = 64;

pub struct Config<'a> {
    /// up to 9 ALPNs allowed, with avg 10 bytes for each name
    pub alpn_protos: Option<&'a [&'a str]>,
    pub ca_cert: Option<X509<'a>>,
    pub client_cert: Option<X509<'a>>,
    pub client_key: Option<X509<'a>>,
    pub client_key_password: Option<&'a str>,
    pub non_block: bool,
    pub use_secure_element: bool,
    pub timeout_ms: u32,
    pub use_global_ca_store: bool,
    pub common_name: Option<&'a str>,
    pub skip_common_name: bool,
    pub keep_alive_cfg: Option<KeepAliveConfig>,
    pub psk_hint_key: Option<PskHintKey<'a>>,
    // TODO ds_data not implemented
    pub is_plain_tcp: bool,
}

impl Config<'_> {
    pub const fn new() -> Self {
        Self {
            alpn_protos: None,
            ca_cert: None,
            client_cert: None,
            client_key: None,
            client_key_password: None,
            non_block: false,
            use_secure_element: false,
            timeout_ms: 4000,
            use_global_ca_store: false,
            common_name: None,
            skip_common_name: false,
            keep_alive_cfg: None,
            psk_hint_key: None,
            is_plain_tcp: false,
        }
    }

    fn try_into_raw(&self, bufs: &mut RawConfigBufs) -> Result<sys::esp_tls_cfg, EspError> {
        let mut rcfg: sys::esp_tls_cfg = Default::default();

        if let Some(ca_cert) = self.ca_cert {
            rcfg.__bindgen_anon_1.cacert_buf = ca_cert.data().as_ptr();
            rcfg.__bindgen_anon_2.cacert_bytes = ca_cert.data().len() as u32;
        }

        if let Some(client_cert) = self.client_cert {
            rcfg.__bindgen_anon_3.clientcert_buf = client_cert.data().as_ptr();
            rcfg.__bindgen_anon_4.clientcert_bytes = client_cert.data().len() as u32;
        }

        if let Some(client_key) = self.client_key {
            rcfg.__bindgen_anon_5.clientkey_buf = client_key.data().as_ptr();
            rcfg.__bindgen_anon_6.clientkey_bytes = client_key.data().len() as u32;
        }

        if let Some(ckp) = self.client_key_password {
            rcfg.clientkey_password = ckp.as_ptr();
            rcfg.clientkey_password_len = ckp.len() as u32;
        }

        // allow up to 9 protocols
        if let Some(protos) = self.alpn_protos {
            bufs.alpn_protos = cstr_arr_from_str_slice(protos, &mut bufs.alpn_protos_cbuf)?;
            rcfg.alpn_protos = bufs.alpn_protos.as_mut_ptr() as *mut *const u8;
        }

        rcfg.non_block = self.non_block;
        rcfg.use_secure_element = self.use_secure_element;
        rcfg.timeout_ms = self.timeout_ms as i32;
        rcfg.use_global_ca_store = self.use_global_ca_store;

        if let Some(common_name) = self.common_name {
            rcfg.common_name =
                cstr_from_str_truncating(common_name, &mut bufs.common_name_buf).as_ptr();
        }

        rcfg.skip_common_name = self.skip_common_name;

        let mut raw_kac: sys::tls_keep_alive_cfg;
        if let Some(kac) = &self.keep_alive_cfg {
            raw_kac = sys::tls_keep_alive_cfg {
                keep_alive_enable: kac.enable,
                keep_alive_idle: kac.idle.as_secs() as i32,
                keep_alive_interval: kac.interval.as_secs() as i32,
                keep_alive_count: kac.count as i32,
            };
            rcfg.keep_alive_cfg = &mut raw_kac as *mut _;
        }

        let mut raw_psk: sys::psk_key_hint;
        if let Some(psk) = &self.psk_hint_key {
            raw_psk = sys::psk_key_hint {
                key: psk.key.as_ptr(),
                key_size: psk.key.len(),
                hint: psk.hint.as_ptr(),
            };
            rcfg.psk_hint_key = &mut raw_psk as *mut _;
        }

        rcfg.is_plain_tcp = self.is_plain_tcp;

        {
            rcfg.if_name = core::ptr::null_mut();
        }

        Ok(rcfg)
    }
}

impl Default for Config<'_> {
    fn default() -> Self {
        Self::new()
    }
}

struct RawConfigBufs {
    alpn_protos: [*const i8; 10],
    alpn_protos_cbuf: [u8; 99],
    common_name_buf: [u8; MAX_COMMON_NAME_LENGTH + 1],
}

impl Default for RawConfigBufs {
    fn default() -> Self {
        RawConfigBufs {
            alpn_protos: [core::ptr::null(); 10],
            alpn_protos_cbuf: [0; 99],
            common_name_buf: [0; MAX_COMMON_NAME_LENGTH + 1],
        }
    }
}

#[derive(Clone, Debug)]
pub struct KeepAliveConfig {
    /// Enable keep-alive timeout
    pub enable: bool,
    /// Keep-alive idle time (second)
    pub idle: Duration,
    /// Keep-alive interval time (second)
    pub interval: Duration,
    /// Keep-alive packet retry send count
    pub count: u32,
}

pub struct PskHintKey<'a> {
    pub key: &'a [u8],
    pub hint: &'a CStr,
}

pub struct ServerConfig<'a> {
    /// up to 9 ALPNs allowed, with avg 10 bytes for each name
    pub alpn_protos: Option<&'a [&'a str]>,
    pub ca_cert: Option<X509<'a>>,
    pub server_cert: Option<X509<'a>>,
    pub server_key: Option<X509<'a>>,
    pub server_key_password: Option<&'a str>,
    pub use_secure_element: bool,
    pub cert_callback: Option<&'a dyn Fn(*const sys::mbedtls_x509_crt)>,
}

impl ServerConfig<'_> {
    pub const fn new() -> Self {
        Self {
            alpn_protos: None,
            ca_cert: None,
            server_cert: None,
            server_key: None,
            server_key_password: None,
            use_secure_element: false,
            cert_callback: None,
        }
    }

    fn try_into_raw(&self, bufs: &mut RawConfigBufs) -> Result<sys::esp_tls_cfg_server, EspError> {
        let mut rcfg: sys::esp_tls_cfg_server = Default::default();

        if let Some(ca_cert) = self.ca_cert {
            rcfg.__bindgen_anon_1.cacert_buf = ca_cert.data().as_ptr();
            rcfg.__bindgen_anon_2.cacert_bytes = ca_cert.data().len() as u32;
        }

        if let Some(server_cert) = self.server_cert {
            rcfg.__bindgen_anon_3.servercert_buf = server_cert.data().as_ptr();
            rcfg.__bindgen_anon_4.servercert_bytes = server_cert.data().len() as u32;
        }

        if let Some(server_key) = self.server_key {
            rcfg.__bindgen_anon_5.serverkey_buf = server_key.data().as_ptr();
            rcfg.__bindgen_anon_6.serverkey_bytes = server_key.data().len() as u32;
        }

        if let Some(ckp) = self.server_key_password {
            rcfg.serverkey_password = ckp.as_ptr();
            rcfg.serverkey_password_len = ckp.len() as u32;
        }

        // allow up to 9 protocols
        if let Some(protos) = self.alpn_protos {
            bufs.alpn_protos = cstr_arr_from_str_slice(protos, &mut bufs.alpn_protos_cbuf)?;
            rcfg.alpn_protos = bufs.alpn_protos.as_mut_ptr() as *mut *const u8;
        }

        rcfg.use_secure_element = self.use_secure_element;

        Ok(rcfg)
    }
}

impl Default for ServerConfig<'_> {
    fn default() -> Self {
        Self::new()
    }
}

pub trait Socket {
    /// Returns the integer FD.
    fn handle(&self) -> i32;
    /// This is called before cleaning up the the tls context and is responsible
    /// for essentially giving up ownership of the socket such that it can safely
    /// be closed by the ESP IDF.
    fn release(&mut self) -> Result<(), EspError>;
}

pub trait PollableSocket: Socket {
    fn poll_readable(&self, ctx: &mut Context) -> Poll<Result<(), EspError>>;
    fn poll_writable(&self, ctx: &mut Context) -> Poll<Result<(), EspError>>;
}

pub struct InternalSocket(());

impl Socket for InternalSocket {
    fn handle(&self) -> i32 {
        defmt::unreachable!()
    }

    fn release(&mut self) -> Result<(), EspError> {
        Ok(())
    }
}

/// Wrapper for `esp-tls` module. Only supports synchronous operation for now.
pub struct EspTls<S>
where
    S: Socket,
{
    raw: *mut sys::esp_tls,
    socket: S,
    server_session: bool,
}

impl EspTls<InternalSocket> {
    /// Create a new `EspTls` instance using internally-managed socket.
    ///
    /// # Errors
    ///
    /// * `ESP_ERR_NO_MEM` if not enough memory to create the TLS connection
    pub fn new() -> Result<Self, EspError> {
        let raw = unsafe { sys::esp_tls_init() };
        if !raw.is_null() {
            Ok(Self {
                raw,
                socket: InternalSocket(()),
                server_session: false,
            })
        } else {
            Err(EspError::from_infallible::<ESP_ERR_NO_MEM>())
        }
    }

    /// Establish a TLS/SSL connection with the specified host and port, using an internally-managed socket.
    ///
    /// # Errors
    ///
    /// * `ESP_ERR_INVALID_SIZE` if `cfg.alpn_protos` exceeds 9 elements or avg 10 bytes/ALPN
    /// * `ESP_FAIL` if connection could not be established
    /// * `ESP_TLS_ERR_SSL_WANT_READ` if the socket is in non-blocking mode and it is not ready for reading
    /// * `ESP_TLS_ERR_SSL_WANT_WRITE` if the socket is in non-blocking mode and it is not ready for writing
    /// * `EWOULDBLOCK` if the socket is in non-blocking mode and it is not ready either for reading or writing (a peculiarity/bug of the `esp-tls` C module)
    pub fn connect(&mut self, host: &str, port: u16, cfg: &Config) -> Result<(), EspError> {
        let mut bufs = RawConfigBufs::default();
        let rcfg = cfg.try_into_raw(&mut bufs)?;

        let res = self.internal_connect(host, port, cfg.non_block, &rcfg);

        // Make sure buffers are held long enough
        #[allow(clippy::drop_non_drop)]
        drop(bufs);

        res
    }
}

impl<S> EspTls<S>
where
    S: Socket,
{
    /// Create a new `EspTls` instance adopting the supplied socket.
    /// The socket should be in a connected state.
    ///
    /// # Errors
    ///
    /// * `ESP_ERR_NO_MEM` if not enough memory to create the TLS connection
    pub fn adopt(socket: S) -> Result<Self, EspError> {
        let raw = unsafe { sys::esp_tls_init() };
        if !raw.is_null() {
            sys::esp!(unsafe { sys::esp_tls_set_conn_sockfd(raw, socket.handle()) })?;

            sys::esp!(unsafe {
                sys::esp_tls_set_conn_state(raw, sys::esp_tls_conn_state_ESP_TLS_CONNECTING)
            })?;

            Ok(Self {
                raw,
                socket,
                server_session: false,
            })
        } else {
            Err(EspError::from_infallible::<ESP_ERR_NO_MEM>())
        }
    }

    /// Establish a TLS/SSL connection using the adopted socket.
    ///
    /// # Errors
    ///
    /// * `ESP_ERR_INVALID_SIZE` if `cfg.alpn_protos` exceeds 9 elements or avg 10 bytes/ALPN
    /// * `ESP_FAIL` if connection could not be established
    /// * `ESP_TLS_ERR_SSL_WANT_READ` if the socket is in non-blocking mode and it is not ready for reading
    /// * `ESP_TLS_ERR_SSL_WANT_WRITE` if the socket is in non-blocking mode and it is not ready for writing
    /// * `EWOULDBLOCK` if the socket is in non-blocking mode and it is not ready either for reading or writing (a peculiarity/bug of the `esp-tls` C module)
    pub fn negotiate(&mut self, host: &str, cfg: &Config) -> Result<(), EspError> {
        let mut bufs = RawConfigBufs::default();
        let rcfg = cfg.try_into_raw(&mut bufs)?;

        let res = self.internal_connect(host, 0, cfg.non_block, &rcfg);

        // Make sure buffers are held long enough
        #[allow(clippy::drop_non_drop)]
        drop(bufs);

        res
    }

    /// Establish a TLS/SSL connection using the adopted connection, acting as the server.
    ///
    /// # Errors
    ///
    /// * `ESP_FAIL` if connection could not be established
    pub fn negotiate_server(&mut self, cfg: &ServerConfig) -> Result<(), EspError> {
        let mut bufs = RawConfigBufs::default();
        let mut rcfg = cfg.try_into_raw(&mut bufs)?;

        unsafe {
            let error =
                sys::esp_mbedtls_server_session_init(&mut rcfg, self.socket.handle(), self.raw);
            if error != 0 {
                error!("failed to init tls server session (error {})", error);
                return Err(EspError::from_infallible::<ESP_FAIL>());
            }

            loop {
                let error = sys::esp_mbedtls_server_session_continue_async(self.raw);
                if error == 0 {
                    break;
                }
                // TODO: make async?
                if error != ESP_TLS_ERR_SSL_WANT_READ || error != ESP_TLS_ERR_SSL_WANT_WRITE {
                    return EspError::convert(error);
                }
            }

            let peer_cert = sys::mbedtls_ssl_get_peer_cert(&(*self.raw).ssl);
            if !peer_cert.is_null() {
                if let Some(cb) = cfg.cert_callback {
                    cb(peer_cert);
                }
            }
        }

        self.server_session = true;

        // Make sure buffers are held long enough
        #[allow(clippy::drop_non_drop)]
        drop(bufs);

        Ok(())
    }

    #[allow(clippy::unnecessary_cast)]
    fn internal_connect(
        &mut self,
        host: &str,
        port: u16,
        asynch: bool,
        cfg: &sys::esp_tls_cfg,
    ) -> Result<(), EspError> {
        let ret = unsafe {
            if asynch {
                sys::esp_tls_conn_new_async(
                    host.as_bytes().as_ptr() as *const u8,
                    host.len() as i32,
                    port as i32,
                    cfg,
                    self.raw,
                )
            } else {
                sys::esp_tls_conn_new_sync(
                    host.as_bytes().as_ptr() as *const u8,
                    host.len() as i32,
                    port as i32,
                    cfg,
                    self.raw,
                )
            }
        };

        match ret {
            1 => Ok(()),
            ESP_TLS_ERR_SSL_WANT_READ => Err(EspError::from_infallible::<
                { ESP_TLS_ERR_SSL_WANT_READ as i32 },
            >()),
            ESP_TLS_ERR_SSL_WANT_WRITE => Err(EspError::from_infallible::<
                { ESP_TLS_ERR_SSL_WANT_WRITE as i32 },
            >()),
            0 => Err(EspError::from_infallible::<{ EWOULDBLOCK as i32 }>()),
            _ => Err(EspError::from_infallible::<ESP_FAIL>()),
        }
    }

    /// Read in the supplied buffer. Returns the number of bytes read.
    ///
    ///
    /// # Errors
    /// * `ESP_TLS_ERR_SSL_WANT_READ` if the socket is in non-blocking mode and it is not ready for reading
    /// * `ESP_TLS_ERR_SSL_WANT_WRITE` if the socket is in non-blocking mode and it is not ready for writing
    /// * Any other `EspError` for a general error
    pub fn read(&mut self, buf: &mut [u8]) -> Result<usize, EspError> {
        if buf.is_empty() {
            return Ok(0);
        }

        let ret = self.read_raw(buf);
        // ESP docs treat 0 as error, but in Rust it's common to return 0 from `Read::read` to indicate eof
        if ret >= 0 {
            Ok(ret as usize)
        } else {
            Err(EspError::from(ret as i32).unwrap())
        }
    }

    fn read_raw(&mut self, buf: &mut [u8]) -> isize {
        use core::ffi::c_void;

        unsafe { sys::esp_tls_conn_read(self.raw, buf.as_mut_ptr() as *mut c_void, buf.len()) }
    }

    /// Write the supplied buffer. Returns the number of bytes written.
    ///
    /// # Errors
    /// * `ESP_TLS_ERR_SSL_WANT_READ` if the socket is in non-blocking mode and it is not ready for reading
    /// * `ESP_TLS_ERR_SSL_WANT_WRITE` if the socket is in non-blocking mode and it is not ready for writing
    /// * Any other `EspError` for a general error
    pub fn write(&mut self, buf: &[u8]) -> Result<usize, EspError> {
        if buf.is_empty() {
            return Ok(0);
        }

        let ret = self.write_raw(buf);
        if ret >= 0 {
            Ok(ret as usize)
        } else {
            Err(EspError::from(ret as i32).unwrap())
        }
    }

    pub fn write_all(&mut self, buf: &[u8]) -> Result<(), EspError> {
        let mut buf = buf;

        while !buf.is_empty() {
            match self.write(buf) {
                Ok(0) => defmt::panic!("zero-length write."),
                Ok(n) => buf = &buf[n..],
                Err(e) => return Err(e),
            }
        }

        Ok(())
    }

    fn write_raw(&mut self, buf: &[u8]) -> isize {
        use core::ffi::c_void;

        unsafe { sys::esp_tls_conn_write(self.raw, buf.as_ptr() as *const c_void, buf.len()) }
    }

    pub fn context_handle(&self) -> *mut sys::esp_tls {
        self.raw
    }
}

impl<S> Drop for EspTls<S>
where
    S: Socket,
{
    fn drop(&mut self) {
        let _ = self.socket.release();

        unsafe {
            // use esp_tls_conn_destroy for both client and server
            sys::esp_tls_conn_destroy(self.raw);
        }
    }
}

impl<S> io::ErrorType for EspTls<S>
where
    S: Socket,
{
    type Error = EspIOError;
}

impl<S> io::Read for EspTls<S>
where
    S: Socket,
{
    fn read(&mut self, buf: &mut [u8]) -> Result<usize, EspIOError> {
        EspTls::read(self, buf).map_err(EspIOError)
    }
}

impl<S> io::Write for EspTls<S>
where
    S: Socket,
{
    fn write(&mut self, buf: &[u8]) -> Result<usize, EspIOError> {
        EspTls::write(self, buf).map_err(EspIOError)
    }

    fn flush(&mut self) -> Result<(), EspIOError> {
        Ok(())
    }
}

pub struct EspAsyncTls<S>(core::cell::RefCell<EspTls<S>>)
where
    S: PollableSocket;

impl<S> EspAsyncTls<S>
where
    S: PollableSocket,
{
    /// Create a new `AsyncEspTls` instance adopting the supplied socket.
    /// The socket should be in a connected state.
    ///
    /// # Errors
    ///
    /// * `ESP_ERR_NO_MEM` if not enough memory to create the TLS connection
    pub fn adopt(socket: S) -> Result<Self, EspError> {
        Ok(Self(core::cell::RefCell::new(EspTls::adopt(socket)?)))
    }

    /// Establish a TLS/SSL connection using the adopted socket.
    ///
    /// # Errors
    ///
    /// * `ESP_ERR_INVALID_SIZE` if `cfg.alpn_protos` exceeds 9 elements or avg 10 bytes/ALPN
    /// * `ESP_FAIL` if connection could not be established
    pub async fn negotiate(&mut self, hostname: &str, cfg: &Config<'_>) -> Result<(), EspError> {
        let mut bufs = RawConfigBufs::default();
        let mut rcfg: sys::esp_tls_cfg = cfg.try_into_raw(&mut bufs)?;

        // It is a bit unintuitive, but when an async socket is being adopted, `non_block` should be set to false.
        //
        // Background:
        // `non_block = true` is only used at one place in the ESP IDF code and that is to run
        // a check - with `select` - whether the socket is really connected.
        // However, we want to avoid the `select()` call, as
        // (a) It won't work, because we jump directly into the ESP_TLS_CONNECTING state as we adopt a socket.
        //.    As a side effect, the select() call is not properly initialized.
        // (b) The adopted socket might be registered in a select() loop already.
        //
        // Avoiding the connectivity check with `select()` should be fine, as the adopted socket
        // must be already connected anyway (API requirement).
        rcfg.non_block = false;

        let res = loop {
            let res = self
                .0
                .borrow_mut()
                .internal_connect(hostname, 0, true, &rcfg);

            match res {
                Err(e) => self.wait(e).await?,
                other => break other,
            }
        };

        // Make sure buffers are held long enough
        #[allow(clippy::drop_non_drop)]
        drop(bufs);

        res
    }

    /// Establish a TLS/SSL connection using the adopted connection, acting as the server.
    ///
    /// # Errors
    ///
    /// * `ESP_FAIL` if connection could not be established
    pub async fn negotiate_server(&mut self, cfg: &ServerConfig<'_>) -> Result<(), EspError> {
        self.0.borrow_mut().negotiate_server(cfg)
    }

    /// Read in the supplied buffer. Returns the number of bytes read.
    pub async fn read(&self, buf: &mut [u8]) -> Result<usize, EspError> {
        loop {
            let res = self.0.borrow_mut().read(buf);

            match res {
                Err(e) => self.wait(e).await?,
                other => break other,
            }
        }
    }

    /// Write the supplied buffer. Returns the number of bytes written.
    pub async fn write(&self, buf: &[u8]) -> Result<usize, EspError> {
        loop {
            let res = self.0.borrow_mut().write(buf);

            match res {
                Err(e) => self.wait(e).await?,
                other => break other,
            }
        }
    }

    pub async fn write_all(&self, buf: &[u8]) -> Result<(), EspError> {
        let mut buf = buf;

        while !buf.is_empty() {
            match self.write(buf).await {
                Ok(0) => defmt::panic!("zero-length write."),
                Ok(n) => buf = &buf[n..],
                Err(e) => return Err(e),
            }
        }

        Ok(())
    }

    async fn wait(&self, error: EspError) -> Result<(), EspError> {
        const EWOULDBLOCK_I32: i32 = EWOULDBLOCK as i32;

        match error.code() {
            // EWOULDBLOCK models the "0" return code of esp_mbedtls_handshake() which does not allow us
            // to figure out whether we need the socket to become readable or writable
            // The code below is therefore a hack which just waits with a timeout for the socket to (eventually)
            // become readable as we actually don't even know if that's what esp_tls wants
            EWOULDBLOCK_I32 => {
                core::future::poll_fn(|ctx| self.0.borrow_mut().socket.poll_writable(ctx)).await?;
                crate::hal::delay::FreeRtos::delay_ms(0);
            }
            ESP_TLS_ERR_SSL_WANT_READ => {
                core::future::poll_fn(|ctx| self.0.borrow_mut().socket.poll_readable(ctx)).await?
            }
            ESP_TLS_ERR_SSL_WANT_WRITE => {
                core::future::poll_fn(|ctx| self.0.borrow_mut().socket.poll_writable(ctx)).await?
            }
            _ => Err(error)?,
        }

        Ok(())
    }

    pub fn context_handle(&self) -> *mut sys::esp_tls {
        self.0.borrow().context_handle()
    }
}

impl<S> io::ErrorType for EspAsyncTls<S>
where
    S: PollableSocket,
{
    type Error = EspIOError;
}

impl<S> io::asynch::Read for EspAsyncTls<S>
where
    S: PollableSocket,
{
    async fn read(&mut self, buf: &mut [u8]) -> Result<usize, Self::Error> {
        EspAsyncTls::read(self, buf).await.map_err(EspIOError)
    }
}

impl<S> io::asynch::Write for EspAsyncTls<S>
where
    S: PollableSocket,
{
    async fn write(&mut self, buf: &[u8]) -> Result<usize, Self::Error> {
        EspAsyncTls::write(self, buf).await.map_err(EspIOError)
    }

    async fn flush(&mut self) -> Result<(), Self::Error> {
        Ok(())
    }
}

/**********************************************************************************************************************/

pub async fn upgrade_to_tls(
    tcp: async_io_mini::Async<std::net::TcpStream>,
    hostname: &str,
    skip_common_name: bool,
    ca_cert: &[u8],
    client_cert: &[u8],
    client_key: &[u8],
) -> Result<(Box<dyn AsyncRead + Unpin>, Box<dyn AsyncWrite + Unpin>), TlsConnectResult> {
    let cfg = Config {
        ca_cert: Some(X509::der(ca_cert)),
        client_cert: Some(X509::der(client_cert)),
        client_key: Some(X509::der(client_key)),
        timeout_ms: 5 * 60 * 1000,
        skip_common_name,
        ..Default::default()
    };
    let mut tls = EspAsyncTls::adopt(AsyncTcp(Some(tcp)))
        .inspect_err(|e| error!("Failed to create AsyncEspTls: err={}", e.defmt()))
        .map_err(|_| TlsConnectResult::AdoptFailed)?;

    if let Err(e) = tls.negotiate(hostname, &cfg).await {
        let handle = tls.context_handle();
        let mut code: c_int = 0;
        let mut err_handle: sys::esp_tls_error_handle_t = std::ptr::null_mut();
        unsafe { sys::esp_tls_get_error_handle(handle, &mut err_handle as *mut _) };
        let err = unsafe {
            sys::esp_tls_get_and_clear_last_error(
                err_handle,
                &mut code as *mut _,
                std::ptr::null_mut(),
            )
        };
        error!(
            "Got error from esp-tls: ret={}, err={:X}, code={:X}",
            e.defmt(),
            err,
            code
        );
        return Err(esp_err_to_tls_err(err));
    };

    let tls = std::rc::Rc::new(tls);

    Ok((
        Box::new(AsyncReadWriter(Rc::clone(&tls))),
        Box::new(AsyncReadWriter(tls)),
    ))
}

pub async fn upgrade_to_tls_server(
    tcp: async_io_mini::Async<std::net::TcpStream>,
    ca_cert: &[u8],
    server_cert: &[u8],
    server_key: &[u8],
) -> Result<(Box<dyn AsyncRead + Unpin>, Box<dyn AsyncWrite + Unpin>), TlsConnectResult> {
    let cfg = ServerConfig {
        ca_cert: Some(X509::der(ca_cert)),
        server_cert: Some(X509::der(server_cert)),
        server_key: Some(X509::der(server_key)),
        ..Default::default()
    };

    let mut tls = EspAsyncTls::adopt(AsyncTcp(Some(tcp)))
        .inspect_err(|e| error!("Failed to create AsyncEspTls: err={}", e.defmt()))
        .map_err(|_| TlsConnectResult::AdoptFailed)?;

    if let Err(e) = tls.negotiate_server(&cfg).await {
        let handle = tls.context_handle();
        let mut code: c_int = 0;
        let mut err_handle: sys::esp_tls_error_handle_t = std::ptr::null_mut();
        unsafe { sys::esp_tls_get_error_handle(handle, &mut err_handle as *mut _) };
        let err = unsafe {
            sys::esp_tls_get_and_clear_last_error(
                err_handle,
                &mut code as *mut _,
                std::ptr::null_mut(),
            )
        };
        error!(
            "Got error from esp-tls server: ret={}, err={:X}, code={:X}",
            e.defmt(),
            err,
            code
        );
        return Err(esp_err_to_tls_err(err));
    };

    if !verify_peer_cert_against_ca(tls.context_handle(), ca_cert) {
        error!("Received invalid peer certificate");
        return Err(TlsConnectResult::InvalidPeerCertificate);
    }

    let tls = std::rc::Rc::new(tls);

    Ok((
        Box::new(AsyncReadWriter(Rc::clone(&tls))),
        Box::new(AsyncReadWriter(tls)),
    ))
}

fn verify_peer_cert_against_ca(esp_tls: *mut sys::esp_tls_t, ca_cert_expect: &[u8]) -> bool {
    use std::ptr;

    unsafe {
        // Retrieve the SSL context from esp_tls
        let ssl = sys::esp_tls_get_ssl_context(esp_tls);
        let peer_cert = sys::mbedtls_ssl_get_peer_cert(ssl as *mut _);

        // Initialize an mbedtls_x509_crt structure for the CA certificate
        let mut ca_cert = std::mem::zeroed();
        sys::mbedtls_x509_crt_init(&mut ca_cert);

        // Parse the DER-encoded CA certificate bytes
        let parse_result = sys::mbedtls_x509_crt_parse_der(
            &mut ca_cert,
            ca_cert_expect.as_ptr(),
            ca_cert_expect.len(),
        );

        if parse_result == 0 {
            // Verify certificate chain
            let verify_result = sys::mbedtls_ssl_get_verify_result(ssl as *mut _);

            if verify_result == 0 {
                // Certificate chain is valid

                let mut flags = 0;
                let ca_verification_result = sys::mbedtls_x509_crt_verify(
                    peer_cert as *mut _,
                    &mut ca_cert,
                    ptr::null_mut(), // CA CRL, NULL for now
                    ptr::null(),     // Expected Common Name, NULL for now
                    &mut flags,
                    None,            // Verification callback, NULL for now
                    ptr::null_mut(), // Verification callback context, NULL for now
                );

                if ca_verification_result == 0 {
                    // Peer certificate is signed by the CA
                    info!("Verified peer certificate successfully");

                    // Free the memory used by the CA certificate
                    sys::mbedtls_x509_crt_free(&mut ca_cert);

                    return true;
                } else {
                    // Peer certificate is not signed by the CA
                    let fingerprint = get_cert_fingerprint(ssl as *mut _);
                    error!(
                        "Failed to verify peer certificate: fingerprint={}",
                        fingerprint.as_str()
                    );
                }
            } else {
                // Certificate chain is not valid
                error!("Failed to verfiy certificate chain: ret={}", verify_result);
            }
        } else {
            // Failed to parse CA certificate from bytes
            error!(
                "Failed to parse CA certificate from bytes: ret={}",
                parse_result
            );
        }

        // Free the memory used by the CA certificate in case of an error
        sys::mbedtls_x509_crt_free(&mut ca_cert);

        false
    }
}

fn get_cert_fingerprint(ssl: *mut sys::mbedtls_ssl_context) -> String {
    use std::ptr;

    unsafe {
        // Get the peer certificate
        let cert = sys::mbedtls_ssl_get_peer_cert(ssl);

        // Check if the certificate is present
        if !cert.is_null() {
            let mut fingerprint: [i8; 64] = [0; 64]; // Assuming a reasonable size for the fingerprint
            let _ = sys::mbedtls_x509_crt_info(
                fingerprint.as_mut_ptr() as *mut u8,
                fingerprint.len(),
                ptr::null(),
                cert,
            );

            // Convert the fingerprint bytes to a string with colons
            let fingerprint_string = fingerprint
                .iter()
                .map(|byte| format!("{:02x}", byte))
                .collect::<Vec<String>>()
                .join(":");

            // Return the fingerprint as a string
            fingerprint_string
        } else {
            // Return an empty string if the peer certificate is not available
            String::new()
        }
    }
}

fn esp_err_to_tls_err(err: c_int) -> TlsConnectResult {
    use TlsConnectResult::*;

    match err {
        sys::ESP_ERR_ESP_TLS_BASE => UnknownTlsError,
        sys::ESP_ERR_ESP_TLS_CANNOT_RESOLVE_HOSTNAME => CannotResolveHostname,
        sys::ESP_ERR_ESP_TLS_CANNOT_CREATE_SOCKET => CannotCreateSocket,
        sys::ESP_ERR_ESP_TLS_UNSUPPORTED_PROTOCOL_FAMILY => UnsupportedProtocolFamily,
        sys::ESP_ERR_ESP_TLS_FAILED_CONNECT_TO_HOST => FailedToConnectToHost,
        sys::ESP_ERR_ESP_TLS_SOCKET_SETOPT_FAILED => SocketSetOptFailed,
        sys::ESP_ERR_ESP_TLS_CONNECTION_TIMEOUT => ConnectionTimeout,
        sys::ESP_ERR_ESP_TLS_SE_FAILED => SecureElementFailed,
        sys::ESP_ERR_ESP_TLS_TCP_CLOSED_FIN => TcpConnectionClosedFin,
        sys::ESP_ERR_MBEDTLS_CERT_PARTLY_OK => CertificateParsingPartlyOk,
        sys::ESP_ERR_MBEDTLS_CTR_DRBG_SEED_FAILED => CtrDrbgSeedFailed,
        sys::ESP_ERR_MBEDTLS_SSL_SET_HOSTNAME_FAILED => SetHostnameFailed,
        sys::ESP_ERR_MBEDTLS_SSL_CONFIG_DEFAULTS_FAILED => SslConfigDefaultsFailed,
        sys::ESP_ERR_MBEDTLS_SSL_CONF_ALPN_PROTOCOLS_FAILED => SslConfigAlpnProtocolsFailed,
        sys::ESP_ERR_MBEDTLS_X509_CRT_PARSE_FAILED => CertificateParsingFailed,
        sys::ESP_ERR_MBEDTLS_SSL_CONF_OWN_CERT_FAILED => SslConfigOwnCertificateFailed,
        sys::ESP_ERR_MBEDTLS_SSL_SETUP_FAILED => SslSetupFailed,
        sys::ESP_ERR_MBEDTLS_SSL_WRITE_FAILED => SslWriteFailed,
        sys::ESP_ERR_MBEDTLS_PK_PARSE_KEY_FAILED => PrivateKeyParsingFailed,
        sys::ESP_ERR_MBEDTLS_SSL_HANDSHAKE_FAILED => SslHandshakeFailed,
        sys::ESP_ERR_MBEDTLS_SSL_CONF_PSK_FAILED => SslConfigPskFailed,
        sys::ESP_ERR_MBEDTLS_SSL_TICKET_SETUP_FAILED => SslTicketSetupFailed,
        sys::ESP_TLS_ERR_SSL_WANT_READ => ConnectionRequiresRead,
        sys::ESP_TLS_ERR_SSL_WANT_WRITE => ConnectionRequiresWrite,
        sys::ESP_TLS_ERR_SSL_TIMEOUT => Timeout,
        _ => OtherError,
    }
}

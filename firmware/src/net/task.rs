use std::{
    future,
    net::Ipv4Addr,
    num::NonZeroU32,
    sync::{atomic::Ordering, Arc},
};

use embassy_futures::select::{select3, select4, Either3, Either4};
use embassy_time::{Instant, WithTimeout};
use esp_idf_svc::{
    eth::{EthEvent, RmiiEthChipset},
    eventloop::{EspEventDeserializer, EspSystemEventLoop},
    hal::{gpio, modem::Modem, peripheral::Peripheral, task::asynch::Notification},
    ipv4::{self, IpInfo, Mask, Subnet},
    wifi::{AccessPointInfo, AuthMethod, WifiEvent},
};

use super::{
    eth::{Eth, EthPins},
    wifi::{Wifi, WifiDisconnectReason},
};
use crate::{
    flash::eventlog::{
        events::{Event, ResetTrigger, StaConnectResult, StartTempIpResult, WpsStartResult},
        utils::ResultEventLogAsync,
    },
    prelude::*,
    state::net::{NetworkStateValue, RssiLevel},
    utils::{self, cod::call_on_drop, restart, secs},
};

const NETWORK_RETRY_LIMIT: usize = 10;
const NETWORK_RETRY_MIN_TIME: Duration = mins(25);
const CONNECT_TIMEOUT: Duration = secs(150);
const BETWEEN_ATTEMPT_TIME: Duration = secs(1);

#[derive(Copy, Clone, Eq, PartialEq)]
pub enum NetworkRequest {
    ReloadAccessPoint,
    StaticIP,
    Wps,
    RefreshApInfo,
}

pub struct NetworkTask {
    wifi: Wifi<'static>,
    eth: Eth<'static>,
    sysloop: EspSystemEventLoop,
}

impl NetworkTask {
    pub async fn new(
        wifi_modem: impl Peripheral<P = Modem> + 'static,
        eth_pins: EthPins,
        eth_rst: Option<impl Peripheral<P = impl gpio::OutputPin> + 'static>,
        eth_chipset: RmiiEthChipset,
        eth_phy_addr: Option<u32>,
        sysloop: EspSystemEventLoop,
    ) -> Result<Self> {
        let client_ip_config = get_client_ip_config().await;

        info!("Initializing Ethernet driver");
        let eth = Eth::init(
            eth_pins,
            eth_rst,
            eth_chipset,
            eth_phy_addr,
            sysloop.clone(),
            client_ip_config.clone(),
        )
        .await?;

        info!("Device serial number: sn={}", utils::mac::eth());

        info!("Initializing Wi-Fi driver");
        let wifi = Wifi::init(wifi_modem, sysloop.clone(), client_ip_config).await?;

        Ok(Self { wifi, eth, sysloop })
    }

    pub async fn run(mut self) -> ! {
        if let Err(e) = self.refresh_ap_state().await {
            warn!("Failed to refresh access point state: err={}", e);
        };

        let aps = self.initial_wifi_scan().await;
        state().net.set_scanned_access_points(aps).await;

        let start = Instant::now();
        let mut error_counter = 0;
        loop {
            if let Err(e) = self.try_connect().await {
                error!("Failed to connect to network: err={}", e);
                error_counter += 1;
                Timer::after(BETWEEN_ATTEMPT_TIME).await;
            }

            if error_counter >= NETWORK_RETRY_LIMIT && start.elapsed() > NETWORK_RETRY_MIN_TIME {
                break;
            }
        }

        error!("Exceeded retry limit");
        restart(ResetTrigger::NetworkTimeout).await;
    }

    async fn try_connect(&mut self) -> Result<()> {
        let should_connect_to_wifi = Self::should_connect_to_wifi().await;

        let _guard = call_on_drop(|| {
            let wifi_leds = LedBits::WifiConnecting | LedBits::WifiConnected | LedBits::WpsActive;
            state().led.disable(wifi_leds);
            state().net.state.update(NetworkStateValue::NotConnected);
        });

        if should_connect_to_wifi {
            self.wifi.initiate_connection().await?;
            state().led.enable(LedBits::WifiConnecting);
        }

        let wifi_futures = async {
            if should_connect_to_wifi {
                select4(
                    self.wait_device_connected_to_ap(),
                    self.wifi.wait_until_connected(),
                    self.wait_wifi_disconnected(),
                    Timer::after(CONNECT_TIMEOUT),
                )
                .await
            } else {
                select4(
                    future::pending(),
                    future::pending(),
                    future::pending(),
                    future::pending(),
                )
                .await
            }
        };

        let futures = select3(
            self.wait_network_request(),
            self.eth.wait_until_connected(),
            wifi_futures,
        );

        match futures.await {
            Either3::First(network_request) => self.handle_network_request(network_request).await,
            Either3::Second(eth_connected) => {
                state().led.disable(LedBits::WifiConnecting);
                eth_connected.context(intern!("connect eth"))?;
                info!("Connected to Ethernet");
                self.handle_ethernet().await
            }
            Either3::Third(wifi_either) => {
                match wifi_either {
                    Either4::First(device_connected_to_ap) => {
                        device_connected_to_ap?;
                        warn!("Disallowing Wi-Fi station connect due to existing access point connection");
                        self.wait_device_disconnected_from_ap().await
                    }
                    Either4::Second(wifi_connected) => {
                        wifi_connected?;
                        info!("Connected to Wi-Fi");
                        self.handle_wifi().await
                    }
                    Either4::Third(wifi_disconnected) => {
                        let disconnect_reason = wifi_disconnected?;
                        state().led.disable(LedBits::WifiConnecting);
                        Event::StaDisconnect(disconnect_reason).log().await;
                        bail!("wifi connection failure: {disconnect_reason}");
                    }
                    Either4::Fourth(_) => {
                        Event::StaConnect(StaConnectResult::ErrConnectTimeout)
                            .log()
                            .await;
                        Err(anyhow!("connection attempt timeout"))
                    }
                }
            }
        }
    }

    async fn handle_ethernet(&mut self) -> Result<()> {
        self.eth
            .wait_for_ip()
            .with_timeout(CONNECT_TIMEOUT)
            .await
            .context(intern!("connect eth ip due to timeout"))?
            .context(intern!("connect eth ip"))?;
        info!("Got IP from Ethernet");

        let ip_info = self.wifi.get_ip_info().unwrap_or_else(default_ip_info);
        state().net.state.update(NetworkStateValue::Eth(ip_info));

        loop {
            let select = select3(
                self.wait_network_request(),
                self.wait_eth_disconnected(),
                self.wifi.wait_status_connected(),
            );

            match select.await {
                Either3::First(network_request) => match network_request {
                    NetworkRequest::ReloadAccessPoint => {
                        if let Err(e) = self.reload_access_point().await {
                            error!("Failed to enable access point: err={}", e);
                        }
                    }
                    NetworkRequest::StaticIP => self.enable_temp_ip().await?,
                    NetworkRequest::Wps => continue,
                    NetworkRequest::RefreshApInfo => self.refresh_ap_state().await?,
                },
                Either3::Second(eth_disconnected) => {
                    eth_disconnected?;
                    Event::EthDisconnect.log().await;
                    bail!("ethernet disconnected");
                }
                Either3::Third(wifi_connected) => {
                    _ = wifi_connected;
                    warn!("Connected to Wi-Fi while Ethernet is connected, disconnecting Wi-Fi");
                    self.wifi.disconnect().await?;
                    Event::StaDisconnect(WifiDisconnectReason::EthernetConnected)
                        .log()
                        .await;
                }
            }
        }
    }

    async fn handle_wifi(&mut self) -> Result<()> {
        state().led.disable(LedBits::WifiConnecting);
        state().led.enable(LedBits::WifiConnected);

        self.wifi
            .wait_for_ip()
            .with_timeout(CONNECT_TIMEOUT)
            .await
            .context(intern!("connect wifi ip due to timeout"))?
            .context(intern!("connect wifi ip"))?;
        info!("Got IP from Wi-Fi");

        let ip_info = self.wifi.get_ip_info().unwrap_or_else(default_ip_info);
        let rssi = self.wifi.get_rssi_level().unwrap_or(RssiLevel(0));
        state()
            .net
            .state
            .update(NetworkStateValue::Wifi(ip_info, rssi));

        loop {
            let select = select3(
                self.wait_network_request(),
                self.wait_wifi_disconnected(),
                self.wait_eth_connected(),
            );

            match select.await {
                Either3::First(button_action) => self.handle_network_request(button_action).await?,
                Either3::Second(wifi_disconnected) => {
                    let disconnect_reason = wifi_disconnected?;
                    state().led.disable(LedBits::WifiConnected);
                    Event::StaDisconnect(disconnect_reason).log().await;
                    bail!("wifi disconnected: {disconnect_reason}");
                }
                Either3::Third(eth_connected) => {
                    eth_connected?;
                    warn!("Connected to Ethernet while Wi-Fi is connected, disconnecting Wi-Fi");
                    self.wifi.disconnect().await?;
                    Event::StaDisconnect(WifiDisconnectReason::EthernetConnected)
                        .log()
                        .await;
                    state().led.disable(LedBits::WifiConnected);
                    return Ok(());
                }
            }
        }
    }

    async fn initial_wifi_scan(&mut self) -> Vec<AccessPointInfo> {
        info!("Scanning Wi-Fi networks");
        self.wifi
            .scan()
            .await
            .inspect_err(|e| error!("Failed to scan Wi-Fi networks: err={}", e))
            .unwrap_or_default()
    }

    async fn wait_device_connected_to_ap(&self) -> Result<()> {
        self.wait_until::<WifiEvent>(|| async { Ok(self.wifi.ap_has_active_connection().await) })
            .await?;
        Ok(())
    }

    async fn wait_device_disconnected_from_ap(&self) -> Result<()> {
        self.wait_until::<WifiEvent>(|| async { Ok(!self.wifi.ap_has_active_connection().await) })
            .await?;
        Ok(())
    }

    async fn wait_wifi_disconnected(&self) -> Result<WifiDisconnectReason> {
        let notification = Arc::new(Notification::new());
        let _subscription = {
            let notification = Arc::clone(&notification);
            self.sysloop.subscribe::<WifiEvent, _>(move |event| {
                if let WifiEvent::StaDisconnected(event) = event {
                    notification.notify(
                        (event.reason() as u32)
                            .try_into()
                            .unwrap_or(NonZeroU32::MIN),
                    );
                };
            })
        };

        let reason = notification.wait().await.get() as u16;
        Ok(reason.try_into().unwrap_or(WifiDisconnectReason::Unknown))
    }

    async fn wait_eth_connected(&self) -> Result<()> {
        self.wait_until::<EthEvent>(|| async { Ok(self.eth.is_connected()) })
            .await?;
        Ok(())
    }

    async fn wait_eth_disconnected(&self) -> Result<()> {
        self.wait_until::<EthEvent>(|| async { Ok(!self.eth.is_connected()) })
            .await?;
        Ok(())
    }

    async fn wait_until<V: EspEventDeserializer>(
        &self,
        mut condition: impl AsyncFnMut() -> Result<bool>,
    ) -> Result<()> {
        let notification = Arc::new(Notification::new());
        let _subscription = {
            let notification = Arc::clone(&notification);
            self.sysloop.subscribe::<V, _>(move |_| {
                notification.notify_lsb();
            })
        };

        while !condition().await? {
            notification.wait().await;
        }

        Ok(())
    }

    async fn wait_network_request(&self) -> NetworkRequest {
        loop {
            let req = itc().net.recv().await;
            if req == NetworkRequest::ReloadAccessPoint && self.wifi.is_ap_active() {
                continue;
            }
            return req;
        }
    }

    async fn handle_network_request(&mut self, req: NetworkRequest) -> Result<()> {
        match req {
            NetworkRequest::ReloadAccessPoint => self.reload_access_point().await,
            NetworkRequest::StaticIP => self.enable_temp_ip().await,
            NetworkRequest::Wps => self.start_wps().await,
            NetworkRequest::RefreshApInfo => self.refresh_ap_state().await,
        }
    }

    async fn start_wps(&mut self) -> Result<()> {
        // note: when ethernet is connected during wps progress, it does not take priority. if it
        // would, wps_stop() would not be called inside esp-idf-svc, and who knows what will happen
        // then.

        if self.eth.is_connected() {
            Event::WpsStart(WpsStartResult::ErrEthActive).log().await;
            warn!("Ignoring WPS start due to existing Ethernet connection");
            return Ok(());
        }

        state().led.enable(LedBits::WpsActive);
        let _guard = call_on_drop(|| state().led.disable(LedBits::WpsActive));

        info!("Starting WPS");
        self.wifi.start_wps().await
    }

    async fn reload_access_point(&mut self) -> Result<()> {
        self.wifi.refresh_config().await?;
        self.refresh_ap_state().await
    }

    async fn enable_temp_ip(&mut self) -> Result<()> {
        self.try_enable_temp_ip()
            .ok_event(Event::StartTempIp(StartTempIpResult::Ok))
            .err_event(Event::StartTempIp(StartTempIpResult::Err))
            .await
    }

    async fn try_enable_temp_ip(&mut self) -> Result<()> {
        let ip_info = IpInfo {
            ip: Ipv4Addr::new(192, 168, 1, 67),
            subnet: Subnet {
                gateway: Ipv4Addr::new(192, 168, 1, 1),
                mask: Mask(24),
            },
            dns: Some(Ipv4Addr::new(1, 1, 1, 1)),
            secondary_dns: Some(Ipv4Addr::new(1, 1, 1, 1)),
        };

        self.wifi.set_ip_info(ip_info).await?;
        self.eth.set_ip_info(ip_info).await?;

        info!("enabled static ip");
        state().net.temp_ip.store(true, Ordering::SeqCst);

        Ok(())
    }

    async fn refresh_ap_state(&self) -> Result<()> {
        let ap_state = self.wifi.get_ap_state()?;
        state().net.ap.update(ap_state);
        Ok(())
    }

    async fn should_connect_to_wifi() -> bool {
        if state().test_mode() {
            true
        } else {
            let wifi_data = env::active_params().wifi_data.lock().await;
            let ssid_exists = !wifi_data.ssid.is_empty();
            let pass_exists =
                wifi_data.auth_method == AuthMethod::None || !wifi_data.pass.is_empty();
            ssid_exists && pass_exists
        }
    }
}

pub fn default_ip_info() -> IpInfo {
    IpInfo {
        ip: Ipv4Addr::UNSPECIFIED,
        subnet: Subnet {
            gateway: Ipv4Addr::UNSPECIFIED,
            mask: Mask(0),
        },
        dns: Some(Ipv4Addr::UNSPECIFIED),
        secondary_dns: Some(Ipv4Addr::UNSPECIFIED),
    }
}

async fn get_client_ip_config() -> ipv4::ClientConfiguration {
    let dhcp = match state().test_mode() {
        true => {
            info!("Disabling DHCP forcibly due to test mode");
            false
        }
        false => nvs::net_dhcp::get().await,
    };

    match dhcp {
        true => ipv4::ClientConfiguration::DHCP(ipv4::DHCPClientSettings {
            hostname: Some(nvs::net_hostname::get_heapless().await),
        }),
        false => ipv4::ClientConfiguration::Fixed(ipv4::ClientSettings {
            ip: nvs::net_ip::get().await,
            subnet: Subnet {
                gateway: nvs::net_gateway::get().await,
                mask: nvs::net_mask::get().await,
            },
            dns: Some(nvs::net_dns1::get().await),
            secondary_dns: Some(nvs::net_dns2::get().await),
        }),
    }
}

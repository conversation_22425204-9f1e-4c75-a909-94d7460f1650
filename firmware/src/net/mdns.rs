use std::io::{Cursor, Write};

use esp_idf_svc::mdns::EspMdns;

use crate::{eebus::ski::get_own_ski, prelude::*};

pub async fn setup_mdns() {
    info!("Starting MDNS server");

    let mut mdns = EspMdns::take().unwrap();
    let hostname = nvs::net_hostname::get().await;
    mdns.set_hostname(&hostname).unwrap();
    let mac = crate::utils::mac::eth_bytes();
    let mut instance_name = *b"wolflinkXXXXXX";
    let instance_cursor = &mut Cursor::new(&mut instance_name[8..]);
    for byte in &mac[3..6] {
        write!(instance_cursor, "{byte:02x}").unwrap();
    }

    let instance_name = std::str::from_utf8(&instance_name).unwrap();
    mdns.set_instance_name(instance_name).unwrap();
    mdns.add_service(Some(instance_name), "_http", "_tcp", 80, &[])
        .unwrap();

    mdns.add_service(
        Some(instance_name),
        "_ship",
        "_tcp",
        4711,
        &[
            ("path", "/ship/"),
            (
                "ski",
                ski_to_string(&get_own_ski().await.bytes, &mut [0; 40]),
            ),
            ("register", "true"), // TODO
            ("model", "CHA"),
            ("brand", "WOLF"),
            ("type", "HeatPump"),
            ("id", instance_name),
            ("txtvers", "1"),
        ],
    )
    .unwrap();

    info!("configured mdns");

    // destructor of mdns shuts down mDNS, so we leak it intentionally
    std::mem::forget(mdns);
}

fn ski_to_string<'a>(ski: &[u8; 20], buf: &'a mut [u8; 40]) -> &'a str {
    let mut i = 0;

    for &b in ski {
        write!(&mut buf[i..i + 2], "{:02X}", b).unwrap();
        i += 2;
    }

    unsafe { core::str::from_utf8_unchecked(buf) }
}

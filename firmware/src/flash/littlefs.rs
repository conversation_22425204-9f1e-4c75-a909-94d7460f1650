use std::ffi::CStr;

use crate::prelude::*;

const BASE_PATH: &CStr = c"/littlefs";
const PARTITION: &CStr = c"littlefs";

pub fn init() -> Result<()> {
    let conf = sys::esp_vfs_littlefs_conf_t {
        base_path: BASE_PATH.as_ptr(),
        partition_label: PARTITION.as_ptr(),
        _bitfield_1: sys::esp_vfs_littlefs_conf_t::new_bitfield_1(
            1, // format_if_mount_failed
            0, // read_only
            0, // dont_mount
            0, // grow_on_mount
        ),
        ..Default::default()
    };

    sys::esp!(unsafe { sys::esp_vfs_littlefs_register(&conf as *const _) })?;

    let (mut total, mut used) = (0usize, 0usize);
    let info_ret = sys::esp!(unsafe {
        sys::esp_littlefs_info(
            conf.partition_label,
            &mut total as *mut _,
            &mut used as *mut _,
        )
    });
    match info_ret {
        Ok(()) => {
            info!("Mounted LittleFS: partition_size={}, used={}", total, used)
        }
        Err(e) => {
            error!("Failed to get LittleFS partition info, err={}", e.defmt());
            sys::esp!(unsafe { sys::esp_littlefs_format(conf.partition_label) })?;
        }
    }

    Ok(())
}

pub fn format() {
    let _ = unsafe { sys::esp_littlefs_format(PARTITION.as_ptr()) };
}

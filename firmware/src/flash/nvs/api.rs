use std::{
    fmt::Display, future::Future, marker::PhantomData, net::Ipv4Addr, pin::Pin, str::FromStr,
};

use esp_idf_svc::{ipv4::Mask, wifi::AuthMethod};
use futures_lite::FutureExt;
use num_enum::TryFromPrimitive;

use super::task::{NvsRequest, NvsType, NvsValue};
use crate::{prelude::*, utils::alloc::InternalVec};

/* nvs keys ***********************************************************************************************************/

macro_rules! define_nvs_keys {
    ($(($key:ident, $ty:ty, $default:ty, $access:ty, $factory_resettable:literal)),* $(,)?) => {
        mod keys {
            use super::NvsKey;
            $(
                #[allow(non_camel_case_types)]
                pub struct $key;
                impl NvsKey for $key {
                    const KEY: &'static str = stringify!($key);
                }
                const _: () = {
                    assert!($key::KEY.len() <= 15, "nvs key too long");
                };
            )*
        }

        $(
            #[allow(non_camel_case_types)]
            pub type $key = NvsEntry<keys::$key, $ty, $default, $access, $factory_resettable>;
        )*

        const NVS_ITER_ITEMS: &[NvsIterItem] = &[
            $(
                NvsIterItem {
                    get: || $key::get_for_cmdline().boxed(),
                    set: |value| $key::set_from_cmdline(value).boxed(),
                    reset: || $key::factory_reset().boxed(),
                    key: $key::key(),
                },
            )*
        ];
    };
}

define_nvs_keys! {
    /* NVS KEY,       TYPE,            DEFAULT VALUE,                ACCESS,    FACTORY RESETTABLE) */
    (ap_authmethod,   AuthMethod,      DefaultWpa2Personal,          ReadWrite, true),
    (ap_channel,      u8,              DefaultU8<1>,                 ReadWrite, true),
    (ap_dhcp,         bool,            DefaultBool<true>,            ReadWrite, true),
    (ap_dns1,         Ipv4Addr,        DefaultIpv4<192, 168, 1, 1>,  ReadWrite, true),
    (ap_dns2,         Ipv4Addr,        DefaultIpv4<192, 168, 1, 1>,  ReadWrite, true),
    (ap_enabled,      bool,            DefaultBool<true>,            ReadWrite, true),
    (ap_gateway,      Ipv4Addr,        DefaultIpv4<192, 168, 1, 1>,  ReadWrite, true),
    (ap_mask,         Mask,            DefaultMask<24>,              ReadWrite, true),
    (ap_ssid,         String,          DefaultApSsid,                ReadWrite, true),
    (ca_cert,         InternalVec<u8>, NoDefault<InternalVec<u8>>,   Private,   false),
    (client_cert,     InternalVec<u8>, NoDefault<InternalVec<u8>>,   Private,   false),
    (client_key,      InternalVec<u8>, NoDefault<InternalVec<u8>>,   Private,   false),
    (eebus_cert,      InternalVec<u8>, NoDefault<InternalVec<u8>>,   Private,   true), // TODO: set to false
    (eebus_enabled,   bool,            DefaultBool<false>,           ReadWrite, true),
    (eebus_key,       InternalVec<u8>, NoDefault<InternalVec<u8>>,   Private,   true), // TODO: set to false
    (error_history,   Vec<u8>,         DefaultEmptyVec,              ReadOnly,  true),
    (event_log_file,  u8,              DefaultU8<0>,                 ReadOnly,  true),
    (factory_pass,    String,          NoDefault<String>,            ReadOnly,  false),
    (local_con_ca,    InternalVec<u8>, NoDefault<InternalVec<u8>>,   Private,   false),
    (local_con_cert,  InternalVec<u8>, NoDefault<InternalVec<u8>>,   Private,   false),
    (local_con_key,   InternalVec<u8>, NoDefault<InternalVec<u8>>,   Private,   false),
    (net_dhcp,        bool,            DefaultBool<true>,            ReadWrite, true),
    (net_dns1,        Ipv4Addr,        DefaultIpv4<1, 1, 1, 1>,      ReadWrite, true),
    (net_dns2,        Ipv4Addr,        DefaultIpv4<1, 1, 1, 1>,      ReadWrite, true),
    (net_dns3,        Ipv4Addr,        DefaultIpv4<1, 1, 1, 1>,      ReadWrite, true),
    (net_gateway,     Ipv4Addr,        DefaultIpv4<192, 168, 1, 1>,  ReadWrite, true),
    (net_hostname,    String,          DefaultHostname,              ReadWrite, true),
    (net_ip,          Ipv4Addr,        DefaultIpv4<192, 168, 1, 67>, ReadWrite, true),
    (net_mask,        Mask,            DefaultMask<24>,              ReadWrite, true),
    (ota_disabled,    bool,            DefaultBool<false>,           ReadWrite, true),
    (password,        String,          DefaultPassword,              ReadWrite, true),
    (portal_enabled,  bool,            DefaultBool<false>,           ReadWrite, true),
    (serial_key_exp,  InternalVec<u8>, NoDefault<InternalVec<u8>>,   Private,   false),
    (serial_key_mod,  InternalVec<u8>, NoDefault<InternalVec<u8>>,   Private,   false),
    (smartset_config, u8,              DefaultU8<1>,                 ReadWrite, true),
    (smartset_host,   String,          DefaultSmartsetHost,          ReadWrite, true),
    (smartset_port,   u16,             DefaultU16<56155>,            ReadWrite, true),
    (system_name,     String,          DefaultSystemName,            ReadWrite, true),
    (test_mode,       bool,            DefaultBool<false>,           ReadWrite, true),
    (test_mode_auth,  AuthMethod,      DefaultWpa2Personal,          ReadWrite, true),
    (test_mode_pass,  String,          DefaultEmptyString,           ReadWrite, true),
    (test_mode_ssid,  String,          DefaultEmptyString,           ReadWrite, true),
    (tls_skip_cn,     bool,            DefaultBool<false>,           ReadWrite, true),
    (website_lang,    String,          DefaultWebsiteLang,           ReadWrite, true),
    (wifi_authmethod, AuthMethod,      DefaultWpa2Personal,          ReadWrite, true),
    (wifi_pass,       String,          DefaultEmptyString,           ReadWrite, true),
    (wifi_ssid,       String,          DefaultEmptyString,           ReadWrite, true),
}

/* NvsEntry ***********************************************************************************************************/

pub struct NvsEntry<
    KEY: NvsKey,
    T: NvsEntryType,
    DEFAULT: NvsDefault,
    ACCESS: NvsUserPolicy,
    const FACTORY_RESETTABLE: bool,
> {
    _marker: PhantomData<(KEY, T, DEFAULT, ACCESS)>,
}

impl<KEY, T, DEFAULT, ACCESS, const FACTORY_RESETTABLE: bool>
    NvsEntry<KEY, T, DEFAULT, ACCESS, FACTORY_RESETTABLE>
where
    KEY: NvsKey,
    T: NvsEntryType,
    DEFAULT: NvsDefault<Type = T>,
    ACCESS: NvsUserPolicy,
{
    pub async fn get() -> T {
        let req = NvsRequest::Get(KEY::KEY, T::GET);
        debug!("Getting entry: key={}", KEY::KEY);
        let resp = itc().nvs.process(req, &ReturnSlot::new()).await;
        let value = resp.expect_get();
        match value {
            Some(value) => T::from_nvs_value(value).expect("nvs response type mismatch"),
            None => DEFAULT::get_default().await,
        }
    }

    pub async fn set(value: T) {
        debug!("Setting entry: key={}", KEY::KEY);
        let req = NvsRequest::Set(KEY::KEY, T::to_nvs_value(value));
        let resp = itc().nvs.process(req, &ReturnSlot::new()).await;
        resp.expect_set();
    }

    pub async fn exists() -> bool {
        let req = NvsRequest::Contains(KEY::KEY);
        let resp = itc().nvs.process(req, &ReturnSlot::new()).await;
        resp.expect_contains()
    }

    pub async fn remove() {
        let req = NvsRequest::Remove(KEY::KEY);
        let resp = itc().nvs.process(req, &ReturnSlot::new()).await;
        resp.expect_remove()
    }

    pub const fn key() -> &'static str {
        KEY::KEY
    }
}

impl<KEY, DEFAULT, ACCESS, const FACTORY_RESETTABLE: bool>
    NvsEntry<KEY, String, DEFAULT, ACCESS, FACTORY_RESETTABLE>
where
    KEY: NvsKey,
    DEFAULT: NvsDefault<Type = String>,
    ACCESS: NvsUserPolicy,
{
    pub async fn get_heapless<const N: usize>() -> heapless::String<N> {
        let s = Self::get().await;
        let end = s.floor_char_boundary(N);
        let trimmed = &s[0..end];
        match heapless::String::try_from(trimmed) {
            Ok(s) => s,
            Err(()) => defmt::unreachable!(),
        }
    }

    pub async fn set_static(value: &'static str) {
        debug!("Setting entry: key={}", KEY::KEY);
        let req = NvsRequest::Set(KEY::KEY, NvsValue::StrStatic(value));
        let resp = itc().nvs.process(req, &ReturnSlot::new()).await;
        resp.expect_set();
    }
}

impl<KEY, DEFAULT, ACCESS, const FACTORY_RESETTABLE: bool>
    NvsEntry<KEY, InternalVec<u8>, DEFAULT, ACCESS, FACTORY_RESETTABLE>
where
    KEY: NvsKey,
    DEFAULT: NvsDefault<Type = InternalVec<u8>>,
    ACCESS: NvsUserPolicy,
{
    pub async fn set_static(value: &'static [u8]) {
        debug!("Setting entry: key={}", KEY::KEY);
        let req = NvsRequest::Set(KEY::KEY, NvsValue::BlobStatic(value));
        let resp = itc().nvs.process(req, &ReturnSlot::new()).await;
        resp.expect_set();
    }
}

impl<KEY, T, DEFAULT, ACCESS> NvsEntry<KEY, T, DEFAULT, ACCESS, true>
where
    KEY: NvsKey,
    T: NvsEntryType,
    DEFAULT: NvsDefault<Type = T>,
    ACCESS: NvsUserPolicy,
{
    pub async fn factory_reset() {
        Self::remove().await;
    }
}

impl<KEY, T, DEFAULT, ACCESS> NvsEntry<KEY, T, DEFAULT, ACCESS, false>
where
    KEY: NvsKey,
    T: NvsEntryType,
    DEFAULT: NvsDefault<Type = T>,
    ACCESS: NvsUserPolicy,
{
    pub async fn factory_reset() {}
}

impl<KEY, T, DEFAULT, const FACTORY_RESETTABLE: bool>
    NvsEntry<KEY, T, DEFAULT, ReadWrite, FACTORY_RESETTABLE>
where
    KEY: NvsKey,
    T: NvsEntryType,
    DEFAULT: NvsDefault<Type = T>,
{
    pub async fn get_for_cmdline() -> Option<String> {
        let value = Self::get().await;
        Some(T::cmdline_string(&value))
    }

    pub async fn set_from_cmdline(value: String) -> Result<(), NvsCmdlineError> {
        let value = T::from_cmdline_string(&value)?;
        Self::set(value).await;
        Ok(())
    }
}

impl<KEY, T, DEFAULT, const FACTORY_RESETTABLE: bool>
    NvsEntry<KEY, T, DEFAULT, ReadOnly, FACTORY_RESETTABLE>
where
    KEY: NvsKey,
    T: NvsEntryType,
    DEFAULT: NvsDefault<Type = T>,
{
    pub async fn get_for_cmdline() -> Option<String> {
        let value = Self::get().await;
        Some(T::cmdline_string(&value))
    }

    pub async fn set_from_cmdline(_value: String) -> Result<(), NvsCmdlineError> {
        Err(NvsCmdlineError::AccessDenied)
    }
}

impl<KEY, T, DEFAULT, const FACTORY_RESETTABLE: bool>
    NvsEntry<KEY, T, DEFAULT, Private, FACTORY_RESETTABLE>
where
    KEY: NvsKey,
    T: NvsEntryType,
    DEFAULT: NvsDefault<Type = T>,
{
    pub async fn get_for_cmdline() -> Option<String> {
        None
    }

    pub async fn set_from_cmdline(_value: String) -> Result<(), NvsCmdlineError> {
        Err(NvsCmdlineError::AccessDenied)
    }
}

/* iterators **********************************************************************************************************/

pub fn iter() -> impl Iterator<Item = &'static NvsIterItem> {
    NVS_ITER_ITEMS.iter()
}

pub async fn get_for_cmdline(key: &str) -> Result<String, NvsCmdlineError> {
    for entry in iter() {
        if entry.key == key {
            return match (entry.get)().await {
                Some(s) => Ok(s),
                None => Err(NvsCmdlineError::AccessDenied),
            };
        }
    }
    Err(NvsCmdlineError::InvalidKey)
}

pub async fn set_from_cmdline(key: &str, value: String) -> Result<(), NvsCmdlineError> {
    for entry in iter() {
        if entry.key == key {
            return (entry.set)(value).await;
        }
    }
    Err(NvsCmdlineError::InvalidKey)
}

pub async fn reset_from_cmdline(key: &str) -> Result<(), NvsCmdlineError> {
    for entry in iter() {
        if entry.key == key {
            (entry.reset)().await;
            return Ok(());
        }
    }
    Err(NvsCmdlineError::InvalidKey)
}

pub async fn factory_reset() {
    for entry in iter() {
        (entry.reset)().await;
    }
}

type BoxFuture<'a, T> = Pin<Box<dyn Future<Output = T> + Send + 'a>>;

pub struct NvsIterItem {
    pub get: fn() -> BoxFuture<'static, Option<String>>,
    pub set: fn(String) -> BoxFuture<'static, Result<(), NvsCmdlineError>>,
    pub reset: fn() -> BoxFuture<'static, ()>,
    pub key: &'static str,
}

#[derive(Debug)]
pub enum NvsCmdlineError {
    InvalidKey,
    InvalidFormat,
    UnsupportedFormat,
    AccessDenied,
}

impl Display for NvsCmdlineError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::InvalidKey => write!(f, "key does not exist"),
            Self::InvalidFormat => write!(f, "malformatted value"),
            Self::UnsupportedFormat => {
                write!(f, "this data type does not support writing from string")
            }
            Self::AccessDenied => write!(f, "access to nvs entry denied"),
        }
    }
}

/* traits *************************************************************************************************************/

pub trait NvsKey {
    const KEY: &'static str;
}

pub trait NvsEntryType: Clone + Sized {
    const GET: NvsType;
    fn from_nvs_value(value: NvsValue) -> Option<Self>;
    fn to_nvs_value(self) -> NvsValue;
    fn cmdline_string(&self) -> String;
    fn from_cmdline_string(s: &str) -> Result<Self, NvsCmdlineError>;
}

pub trait NvsUserPolicy {}

pub trait NvsDefault {
    type Type: Clone;
    async fn get_default() -> Self::Type;
}

/* NvsUserPolicy impls ************************************************************************************************/

pub struct ReadWrite;
impl NvsUserPolicy for ReadWrite {}

pub struct ReadOnly;
impl NvsUserPolicy for ReadOnly {}

pub struct Private;
impl NvsUserPolicy for Private {}

/* NvsDefault impls ***************************************************************************************************/

macro_rules! primitive_nvs_default {
    ($name:ident, $ty:ty) => {
        pub struct $name<const DEFAULT: $ty>;
        impl<const DEFAULT: $ty> NvsDefault for $name<DEFAULT> {
            type Type = $ty;
            async fn get_default() -> Self::Type {
                DEFAULT
            }
        }
    };
}

macro_rules! value_nvs_default {
    ($name:ident, $ty:ty, $value:expr) => {
        pub struct $name;
        impl NvsDefault for $name {
            type Type = $ty;
            async fn get_default() -> Self::Type {
                $value
            }
        }
    };
}

macro_rules! str_nvs_default {
    ($name:ident, $str:literal) => {
        value_nvs_default!($name, String, String::from($str));
    };
}

primitive_nvs_default!(DefaultU8, u8);
primitive_nvs_default!(DefaultU16, u16);
primitive_nvs_default!(DefaultU32, u32);
primitive_nvs_default!(DefaultU64, u64);
primitive_nvs_default!(DefaultI8, i8);
primitive_nvs_default!(DefaultI16, i16);
primitive_nvs_default!(DefaultI32, i32);
primitive_nvs_default!(DefaultI64, i64);
primitive_nvs_default!(DefaultBool, bool);

value_nvs_default!(DefaultWpa2Personal, AuthMethod, AuthMethod::WPA2Personal);
value_nvs_default!(DefaultPassword, String, factory_pass::get().await);
value_nvs_default!(DefaultEmptyVec, Vec<u8>, Vec::<u8>::new());
value_nvs_default!(DefaultEmptyString, String, String::new());
value_nvs_default!(DefaultApSsid, String, default_ap_ssid());

str_nvs_default!(DefaultHostname, "wolflink");
str_nvs_default!(DefaultSmartsetHost, "devices.wolf-smartset.com");
str_nvs_default!(DefaultSystemName, "WOLF Link");
str_nvs_default!(DefaultWebsiteLang, "_auto");

fn default_ap_ssid() -> String {
    let mac = crate::utils::mac::eth();
    format!("WOLFLINK-{}", &mac[6..])
}

pub struct DefaultIpv4<const A: u8, const B: u8, const C: u8, const D: u8>;
impl<const A: u8, const B: u8, const C: u8, const D: u8> NvsDefault for DefaultIpv4<A, B, C, D> {
    type Type = Ipv4Addr;
    async fn get_default() -> Self::Type {
        Ipv4Addr::new(A, B, C, D)
    }
}

pub struct DefaultMask<const DEFAULT: u8>;
impl<const DEFAULT: u8> NvsDefault for DefaultMask<DEFAULT> {
    type Type = Mask;
    async fn get_default() -> Self::Type {
        Mask(DEFAULT)
    }
}

pub struct NoDefault<T: Clone>(PhantomData<T>);
impl<T: Clone> NvsDefault for NoDefault<T> {
    type Type = T;
    async fn get_default() -> Self::Type {
        defmt::panic!(
            "nvs entry {} does not have a default value",
            std::any::type_name::<T>()
        );
    }
}

/* NvsEntryType impls *************************************************************************************************/

macro_rules! primitive_nvs_entry_type {
    ($ty:ty, $variant:ident) => {
        impl NvsEntryType for $ty {
            const GET: NvsType = NvsType::$variant;
            fn from_nvs_value(value: NvsValue) -> Option<Self> {
                match value {
                    NvsValue::$variant(v) => Some(v),
                    _ => None,
                }
            }
            fn to_nvs_value(self) -> NvsValue {
                NvsValue::$variant(self)
            }
            fn cmdline_string(&self) -> String {
                self.to_string()
            }
            fn from_cmdline_string(s: &str) -> Result<Self, NvsCmdlineError> {
                FromStr::from_str(s).map_err(|_| NvsCmdlineError::InvalidFormat)
            }
        }
    };
}

primitive_nvs_entry_type!(u8, U8);
primitive_nvs_entry_type!(u16, U16);
primitive_nvs_entry_type!(u32, U32);
primitive_nvs_entry_type!(u64, U64);
primitive_nvs_entry_type!(i8, I8);
primitive_nvs_entry_type!(i16, I16);
primitive_nvs_entry_type!(i32, I32);
primitive_nvs_entry_type!(i64, I64);
primitive_nvs_entry_type!(String, Str);

macro_rules! vec_nvs_entry_type {
    ($ty:ty, $nvs_type:ident) => {
        impl NvsEntryType for $ty {
            const GET: NvsType = NvsType::$nvs_type;
            fn from_nvs_value(value: NvsValue) -> Option<Self> {
                match value {
                    NvsValue::$nvs_type(v) => Some(v),
                    _ => None,
                }
            }
            fn to_nvs_value(self) -> NvsValue {
                NvsValue::$nvs_type(self)
            }
            fn cmdline_string(&self) -> String {
                format!("blob ({} bytes)", self.len())
            }
            fn from_cmdline_string(_s: &str) -> Result<Self, NvsCmdlineError> {
                Err(NvsCmdlineError::UnsupportedFormat)
            }
        }
    };
}

vec_nvs_entry_type!(Vec<u8>, Blob);
vec_nvs_entry_type!(InternalVec<u8>, BlobInternal);

impl NvsEntryType for bool {
    const GET: NvsType = NvsType::U8;
    fn from_nvs_value(value: NvsValue) -> Option<Self> {
        match value {
            NvsValue::U8(v) => Some(v != 0),
            _ => None,
        }
    }
    fn to_nvs_value(self) -> NvsValue {
        NvsValue::U8(if self { 1 } else { 0 })
    }
    fn cmdline_string(&self) -> String {
        self.to_string()
    }
    fn from_cmdline_string(s: &str) -> Result<Self, NvsCmdlineError> {
        FromStr::from_str(s).map_err(|_| NvsCmdlineError::InvalidFormat)
    }
}

impl NvsEntryType for AuthMethod {
    const GET: NvsType = NvsType::U8;
    fn from_nvs_value(value: NvsValue) -> Option<Self> {
        match value {
            NvsValue::U8(v) => Self::try_from_primitive(v).ok(),
            _ => None,
        }
    }
    fn to_nvs_value(self) -> NvsValue {
        NvsValue::U8(self as u8)
    }
    fn cmdline_string(&self) -> String {
        (*self as u8).to_string()
    }
    fn from_cmdline_string(s: &str) -> Result<Self, NvsCmdlineError> {
        let n = FromStr::from_str(s).map_err(|_| NvsCmdlineError::InvalidFormat)?;
        let v = Self::try_from_primitive(n).map_err(|_| NvsCmdlineError::InvalidFormat)?;
        Ok(v)
    }
}

impl NvsEntryType for Ipv4Addr {
    const GET: NvsType = NvsType::U32;
    fn from_nvs_value(value: NvsValue) -> Option<Self> {
        match value {
            NvsValue::U32(v) => Some(Self::from_bits(v)),
            _ => None,
        }
    }
    fn to_nvs_value(self) -> NvsValue {
        NvsValue::U32(self.to_bits())
    }
    fn cmdline_string(&self) -> String {
        self.to_string()
    }
    fn from_cmdline_string(s: &str) -> Result<Self, NvsCmdlineError> {
        FromStr::from_str(s).map_err(|_| NvsCmdlineError::InvalidFormat)
    }
}

impl NvsEntryType for Mask {
    const GET: NvsType = NvsType::U8;
    fn from_nvs_value(value: NvsValue) -> Option<Self> {
        match value {
            NvsValue::U8(v) => Some(Self(v)),
            _ => None,
        }
    }
    fn to_nvs_value(self) -> NvsValue {
        NvsValue::U8(self.0)
    }
    fn cmdline_string(&self) -> String {
        self.to_string()
    }
    fn from_cmdline_string(s: &str) -> Result<Self, NvsCmdlineError> {
        FromStr::from_str(s)
            .map(Self)
            .map_err(|_| NvsCmdlineError::InvalidFormat)
    }
}

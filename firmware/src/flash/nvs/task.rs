use std::alloc::{Allocator, Global};

use esp_idf_svc::nvs::EspNvs;
#[cfg(debug_assertions)]
use esp_idf_svc::nvs::{EspCustomNvsPartition, NvsCustom};
#[cfg(not(debug_assertions))]
use esp_idf_svc::nvs::{EspEncryptedNvsPartition, NvsEncrypted};

use crate::{
    prelude::*,
    utils::alloc::{vec_zeroed, InternalAllocator},
};

pub enum NvsRequest {
    Get(&'static str, NvsType),
    Set(&'static str, NvsValue),
    Contains(&'static str),
    Remove(&'static str),
}

pub enum NvsResponse {
    Get(Option<NvsValue>),
    SetAck,
    Contains(bool),
    RemoveAck,
}

impl NvsResponse {
    pub fn expect_get(self) -> Option<NvsValue> {
        match self {
            Self::Get(value) => value,
            _ => defmt::panic!("nvs response type mismatch"),
        }
    }

    pub fn expect_set(self) {
        match self {
            Self::SetAck => (),
            _ => defmt::panic!("nvs response type mismatch"),
        }
    }

    pub fn expect_contains(self) -> bool {
        match self {
            Self::Contains(value) => value,
            _ => defmt::panic!("nvs response type mismatch"),
        }
    }

    pub fn expect_remove(self) {
        match self {
            Self::RemoveAck => (),
            _ => defmt::panic!("nvs response type mismatch"),
        }
    }
}

pub enum NvsType {
    U8,
    U16,
    U32,
    U64,
    I8,
    I16,
    I32,
    I64,
    Str,
    Blob,
    BlobInternal,
}

pub enum NvsValue {
    U8(u8),
    U16(u16),
    U32(u32),
    U64(u64),
    I8(i8),
    I16(i16),
    I32(i32),
    I64(i64),
    Str(String),
    StrStatic(&'static str),
    Blob(Vec<u8>),
    BlobStatic(&'static [u8]),
    BlobInternal(Vec<u8, InternalAllocator>),
}

pub async fn task() -> ! {
    #[cfg(debug_assertions)]
    let nvs_partition = EspCustomNvsPartition::take("nvs").expect("failed to get nvs");
    #[cfg(not(debug_assertions))]
    let nvs_partition = EspEncryptedNvsPartition::take("nvs", None).expect("failed to get nvs");

    let mut nvs = EspNvs::new(nvs_partition, "nvs", true).expect("failed to get nvs");

    loop {
        let (req, slot) = itc().nvs.recv().await;
        let resp = match process(&mut nvs, &req) {
            Ok(res) => res,
            Err(e) => defmt::panic!("failed to perform nvs action: {}", e.defmt()),
        };
        slot.ret(resp);
    }
}

fn process(nvs: &mut Nvs, req: &NvsRequest) -> Result<NvsResponse> {
    match req {
        NvsRequest::Get(key, ty) => get(nvs, key, ty).map(NvsResponse::Get),
        NvsRequest::Set(key, value) => set(nvs, key, value).map(|_| NvsResponse::SetAck),
        NvsRequest::Contains(key) => nvs.contains(key).map(NvsResponse::Contains),
        NvsRequest::Remove(key) => nvs.remove(key).map(|_| NvsResponse::RemoveAck),
    }
}

fn get(nvs: &Nvs, key: &'static str, ty: &NvsType) -> Result<Option<NvsValue>> {
    match ty {
        NvsType::U8 => Ok(nvs.get_u8(key)?.map(NvsValue::U8)),
        NvsType::U16 => Ok(nvs.get_u16(key)?.map(NvsValue::U16)),
        NvsType::U32 => Ok(nvs.get_u32(key)?.map(NvsValue::U32)),
        NvsType::U64 => Ok(nvs.get_u64(key)?.map(NvsValue::U64)),
        NvsType::I8 => Ok(nvs.get_i8(key)?.map(NvsValue::I8)),
        NvsType::I16 => Ok(nvs.get_i16(key)?.map(NvsValue::I16)),
        NvsType::I32 => Ok(nvs.get_i32(key)?.map(NvsValue::I32)),
        NvsType::I64 => Ok(nvs.get_i64(key)?.map(NvsValue::I64)),
        NvsType::Str => Ok(get_str(nvs, key)?.map(NvsValue::Str)),
        NvsType::Blob => Ok(get_blob::<Global>(nvs, key, Global)?.map(NvsValue::Blob)),
        NvsType::BlobInternal => {
            Ok(get_blob::<InternalAllocator>(nvs, key, InternalAllocator)?
                .map(NvsValue::BlobInternal))
        }
    }
}

fn get_str(nvs: &Nvs, key: &str) -> Result<Option<String>> {
    let Some(len) = nvs.str_len(key)? else {
        return Ok(None);
    };
    let mut buf = vec![0; len];
    match nvs.get_str(key, &mut buf)? {
        Some(_) => {
            if buf.last() == Some(&b'\0') {
                buf.pop();
            }
            Ok(Some(String::from_utf8(buf).map_err(|_| {
                EspError::from_infallible::<{ sys::ESP_ERR_CODING }>()
            })?))
        }
        None => Ok(None),
    }
}

fn get_blob<A: Allocator>(nvs: &Nvs, key: &str, allocator: A) -> Result<Option<Vec<u8, A>>> {
    let Some(len) = nvs.blob_len(key)? else {
        return Ok(None);
    };
    let mut buf = vec_zeroed(len, allocator);
    match nvs.get_blob(key, &mut buf)? {
        Some(_) => Ok(Some(buf)),
        None => Ok(None),
    }
}

fn set(nvs: &mut Nvs, key: &'static str, value: &NvsValue) -> Result<()> {
    match value {
        NvsValue::U8(v) => nvs.set_u8(key, *v),
        NvsValue::U16(v) => nvs.set_u16(key, *v),
        NvsValue::U32(v) => nvs.set_u32(key, *v),
        NvsValue::U64(v) => nvs.set_u64(key, *v),
        NvsValue::I8(v) => nvs.set_i8(key, *v),
        NvsValue::I16(v) => nvs.set_i16(key, *v),
        NvsValue::I32(v) => nvs.set_i32(key, *v),
        NvsValue::I64(v) => nvs.set_i64(key, *v),
        NvsValue::Str(v) => nvs.set_str(key, v),
        NvsValue::StrStatic(v) => nvs.set_str(key, v),
        NvsValue::Blob(v) => nvs.set_blob(key, v),
        NvsValue::BlobStatic(v) => nvs.set_blob(key, v),
        NvsValue::BlobInternal(v) => nvs.set_blob(key, v),
    }
}

#[cfg(debug_assertions)]
type Nvs = EspNvs<NvsCustom>;
#[cfg(not(debug_assertions))]
type Nvs = EspNvs<NvsEncrypted>;

type Result<T> = std::result::Result<T, EspError>;

pub mod api;
pub mod task;

pub use keys::*;

#[allow(unused_imports)]
#[rustfmt::skip]
mod keys {
    pub use super::api::{
        ap_authmethod,
        ap_channel,
        ap_dhcp,
        ap_dns1,
        ap_dns2,
        ap_enabled,
        ap_gateway,
        ap_mask,
        ap_ssid,
        ca_cert,
        client_cert,
        client_key,
        eebus_cert,
        eebus_enabled,
        eebus_key,
        error_history,
        event_log_file,
        factory_pass,
        local_con_ca,
        local_con_cert,
        local_con_key,
        net_dhcp,
        net_dns1,
        net_dns2,
        net_dns3,
        net_gateway,
        net_hostname,
        net_ip,
        net_mask,
        ota_disabled,
        password,
        portal_enabled,
        serial_key_exp,
        serial_key_mod,
        smartset_config,
        smartset_host,
        smartset_port,
        system_name,
        test_mode,
        test_mode_auth,
        test_mode_pass,
        test_mode_ssid,
        tls_skip_cn,
        website_lang,
        wifi_authmethod,
        wifi_pass,
        wifi_ssid,
    };
}

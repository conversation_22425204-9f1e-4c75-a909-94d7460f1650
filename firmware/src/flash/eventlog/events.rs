use std::net::Ipv4Addr;

use defmt::Format;
use esp_idf_svc::hal::reset::ResetReason;
use num_enum::TryFromPrimitive;

use super::task::{EventData, EventSubcode};
use crate::{net::wifi::WifiDisconnectReason, prelude::*};

#[derive(Debug)]
pub enum Event {
    WifiStart(WifiStartResult),
    StaConnect(StaConnectResult),
    StaDisconnect(WifiDisconnectReason),
    WpsStart(WpsStartResult),
    WpsFinished(WpsResult),
    EthStart(EthStartResult),
    EthConnect(EthConnectResult),
    EthDisconnect,
    NetifUp(NetifUpResult),
    StartTempIp(StartTempIpResult),
    DnsResolution(DnsResolutionResult),
    PortalConnect(PortalConnectResult),
    PortalTlsConnect(TlsConnectResult),
    PortalLogonResponse(PortalLogonResponseResult),
    PortalDisconnect(PortalDisconnectReason),
    LocalConnect(LocalConnectResult),
    LocalTlsConnect(TlsConnectResult),
    LocalDisconnect(PortalDisconnectReason),
    BusDiscover(BusDevice),
    BusDisconnect(BusDevice),
    Config(ConfigAction),
    ResetTrigger(ResetTrigger),
    Start(ResetReason),
    OtaUpdate(OtaUpdateResult),
}

impl Event {
    pub async fn log(self) {
        itc().eventlog.write(self).await
    }
}

#[repr(i16)]
#[derive(TryFromPrimitive, Debug, Clone, Copy)]
pub enum WifiStartResult {
    Ok = 0,
    ErrInit = -1,
    ErrNvs = -2,
    ErrNetif = -3,
    ErrStartAsync = -4,
    ErrSetConfig = -5,
    ErrStart = -6,
    ErrTimer = -7,
}

#[repr(i16)]
#[derive(TryFromPrimitive, Debug, Clone, Copy)]
pub enum StaConnectResult {
    Ok = 0,
    ErrNvs = -1,
    ErrSetConfig = -2,
    ErrConnectTimeout = -3,
    ErrConnectOther = -4,
    ErrWaitConnected = -5,
}

#[repr(i16)]
#[derive(TryFromPrimitive, Debug, Clone, Copy)]
pub enum WpsStartResult {
    Ok = 0,
    ErrEthActive = -1,
    ErrGetStatus = -2,
    ErrWifiStart = -3,
    ErrWpsStart = -4,
}

#[repr(i16)]
#[derive(TryFromPrimitive, Debug, Clone, Copy)]
pub enum WpsResult {
    Ok = 0,
    ErrSetConfig = -1,
    ErrGetConfig = -2,
    ErrNvs = -3,
    ErrNotInStationMode = -4,
    ErrFail = -5,
    ErrTimeout = -6,
    ErrWrongMethod = -7,
    ErrPbcOverlap = -8,
}

#[repr(i16)]
#[derive(TryFromPrimitive, Debug, Clone, Copy)]
pub enum EthStartResult {
    Ok = 0,
    ErrInit = -1,
    ErrNetif = -2,
    ErrTimer = -3,
    ErrStartAsync = -4,
    ErrStart = -5,
}

#[repr(i16)]
#[derive(TryFromPrimitive, Debug, Clone, Copy)]
pub enum EthConnectResult {
    Ok = 0,
    Err = -1,
}

#[derive(Debug, Clone)]
pub enum NetifUpResult {
    Ok(Ipv4Addr),
    ErrUp,
    ErrAltDnsFailed,
    ErrStartFailed,
    ErrSetDefaultNetifFailed,
}

#[repr(i16)]
#[derive(TryFromPrimitive, Debug, Clone, Copy)]
pub enum StartTempIpResult {
    Ok = 0,
    Err = -1,
}

#[derive(Debug, Clone)]
pub enum DnsResolutionResult {
    Ok(Ipv4Addr),
    ErrConnRefused,
    ErrConnReset,
    ErrConnAbort,
    ErrNotConnected,
    ErrTimeout,
    ErrInterrupted,
    ErrUnexpectedEof,
    ErrOutOfMemory,
    ErrZeroAddrs,
    ErrThread,
    ErrOther,
}

#[repr(i16)]
#[derive(TryFromPrimitive, Debug, Clone, Copy)]
pub enum PortalConnectResult {
    Ok = 0,
    ErrConnRefused = -1,
    ErrConnReset = -2,
    ErrConnAbort = -3,
    ErrNotConnected = -4,
    ErrTimeout = -5,
    ErrInterrupted = -6,
    ErrUnexpectedEof = -7,
    ErrOutOfMemory = -8,
    ErrOther = -9,
}

#[repr(i16)]
#[derive(TryFromPrimitive, Debug, Clone, Copy, Format)]
pub enum TlsConnectResult {
    Ok = 0,
    UnknownTlsError = -1,
    CannotResolveHostname = -2,
    CannotCreateSocket = -3,
    UnsupportedProtocolFamily = -4,
    FailedToConnectToHost = -5,
    SocketSetOptFailed = -6,
    ConnectionTimeout = -7,
    SecureElementFailed = -8,
    TcpConnectionClosedFin = -9,
    CertificateParsingPartlyOk = -10,
    CtrDrbgSeedFailed = -11,
    SetHostnameFailed = -12,
    SslConfigDefaultsFailed = -13,
    SslConfigAlpnProtocolsFailed = -14,
    CertificateParsingFailed = -15,
    SslConfigOwnCertificateFailed = -16,
    SslSetupFailed = -17,
    SslWriteFailed = -18,
    PrivateKeyParsingFailed = -19,
    SslHandshakeFailed = -20,
    SslConfigPskFailed = -21,
    SslTicketSetupFailed = -22,
    ConnectionRequiresRead = -23,
    ConnectionRequiresWrite = -24,
    Timeout = -25,
    NvsError = -26,
    AdoptFailed = -27,
    InvalidPeerCertificate = -28,
    OtherError = -29,
}

#[repr(i16)]
#[derive(TryFromPrimitive, Debug, Clone, Copy)]
pub enum PortalLogonResponseResult {
    Ok = 0,
    ErrSocketWrite = -1,
    ErrSocketRead = -2,
    ErrTimeout = -3,
    ErrInvalidResponse = -4,
}

#[repr(i16)]
#[derive(TryFromPrimitive, Debug, Clone, Copy)]
pub enum PortalDisconnectReason {
    Unknown = 1,
}

#[repr(i16)]
#[derive(TryFromPrimitive, Debug, Clone, Copy)]
pub enum LocalConnectResult {
    Ok = 0,
    Err = -1,
}

#[derive(Debug, Clone)]
pub struct BusDevice {
    pub device_id: u16,
    pub bus: Bus,
}

#[repr(u8)]
#[derive(TryFromPrimitive, Debug, Clone, Copy)]
pub enum Bus {
    Ebus = 1,
    Modbus = 2,
}

#[repr(u32)]
#[derive(TryFromPrimitive, Debug, Clone, Copy)]
pub enum ConfigAction {
    ChangeWifi = 1,
    EnablePortal = 2,
    DisablePortal = 3,
    EnableDhcp = 4,
    DisableDhcp = 5,
    EnableAp = 6,
    DisableAp = 7,
    ChangePassword = 8,
}

#[repr(u32)]
#[derive(TryFromPrimitive, Debug, Clone, Copy)]
pub enum ResetTrigger {
    Button = 1,
    Update = 2,
    Smartset = 3,
    BusError = 4,
    CmdLine = 5,
    Website = 6,
    Rollback = 7,
    TestMode = 8,
    NetworkTimeout = 9,
    PortalTimeout = 10,
    BusFailure = 11,
    BundleTimeouts = 12,
    FullChannelForceReset = 13,
}

#[derive(Debug, Clone)]
pub enum OtaUpdateResult {
    Ok(EventLogVersion),
    ErrUpdatesDisabled,
    ErrSecurity,
    ErrBusy,
    ErrInvalidData,
    ErrAbortSmartset,
    ErrOther,
}

impl From<BusDevice> for EventData {
    fn from(device: BusDevice) -> EventData {
        ((device.bus as u8 as u32) << 16) | (device.device_id as u32)
    }
}

impl TryFrom<EventData> for BusDevice {
    type Error = ();

    fn try_from(data: EventData) -> Result<BusDevice, Self::Error> {
        Ok(BusDevice {
            device_id: data as u16,
            bus: Bus::try_from((data >> 16) as u8).map_err(|_| ())?,
        })
    }
}

impl From<NetifUpResult> for (EventSubcode, EventData) {
    fn from(result: NetifUpResult) -> (EventSubcode, EventData) {
        match result {
            NetifUpResult::Ok(ip) => (0, ip.to_bits()),
            NetifUpResult::ErrUp => (-1, 0),
            NetifUpResult::ErrAltDnsFailed => (-2, 0),
            NetifUpResult::ErrStartFailed => (-3, 0),
            NetifUpResult::ErrSetDefaultNetifFailed => (-4, 0),
        }
    }
}

impl TryFrom<(EventSubcode, EventData)> for NetifUpResult {
    type Error = ();

    fn try_from(data: (EventSubcode, EventData)) -> Result<NetifUpResult, Self::Error> {
        match data {
            (0, ip) => Ok(NetifUpResult::Ok(Ipv4Addr::from_bits(ip))),
            (-1, _) => Ok(NetifUpResult::ErrUp),
            (-2, _) => Ok(NetifUpResult::ErrAltDnsFailed),
            (-3, _) => Ok(NetifUpResult::ErrStartFailed),
            (-4, _) => Ok(NetifUpResult::ErrSetDefaultNetifFailed),
            _ => Err(()),
        }
    }
}

impl From<OtaUpdateResult> for (EventSubcode, EventData) {
    fn from(result: OtaUpdateResult) -> (EventSubcode, EventData) {
        match result {
            OtaUpdateResult::Ok(v) => (0, v.0),
            OtaUpdateResult::ErrUpdatesDisabled => (-1, 0),
            OtaUpdateResult::ErrSecurity => (-2, 0),
            OtaUpdateResult::ErrBusy => (-3, 0),
            OtaUpdateResult::ErrInvalidData => (-4, 0),
            OtaUpdateResult::ErrAbortSmartset => (-5, 0),
            OtaUpdateResult::ErrOther => (-6, 0),
        }
    }
}

impl TryFrom<(EventSubcode, EventData)> for OtaUpdateResult {
    type Error = ();

    fn try_from(data: (EventSubcode, EventData)) -> Result<OtaUpdateResult, Self::Error> {
        match data {
            (0, v) => Ok(OtaUpdateResult::Ok(EventLogVersion(v))),
            (-1, _) => Ok(OtaUpdateResult::ErrUpdatesDisabled),
            (-2, _) => Ok(OtaUpdateResult::ErrSecurity),
            (-3, _) => Ok(OtaUpdateResult::ErrBusy),
            (-4, _) => Ok(OtaUpdateResult::ErrInvalidData),
            (-5, _) => Ok(OtaUpdateResult::ErrAbortSmartset),
            (-6, _) => Ok(OtaUpdateResult::ErrOther),
            _ => Err(()),
        }
    }
}

#[derive(Copy, Clone)]
pub struct EventLogVersion(u32);

impl From<&str> for EventLogVersion {
    fn from(v: &str) -> EventLogVersion {
        let parts = {
            let mut split = v.split([':', '-']);
            let major: Option<u32> = split.next().and_then(|x| x.parse().ok());
            let minor: Option<u32> = split.next().and_then(|x| x.parse().ok());
            let patch: u32 = split.next().and_then(|x| x.parse().ok()).unwrap_or(0);
            major.zip(minor.map(|x| (x, patch)))
        };
        Self(match parts {
            Some((a, (b, c))) => ((a & 0xFF) << 16) | ((b & 0xFF) << 8) | (c & 0xFF),
            None => u32::MAX,
        })
    }
}

impl std::fmt::Debug for EventLogVersion {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self.0 {
            u32::MAX => write!(f, "invalid version"),
            x => write!(f, "{}.{}.{}", (x >> 16) & 0xFF, (x >> 8) & 0xFF, x & 0xFF),
        }
    }
}

impl From<DnsResolutionResult> for (EventSubcode, EventData) {
    fn from(result: DnsResolutionResult) -> (EventSubcode, EventData) {
        match result {
            DnsResolutionResult::Ok(ip) => (0, ip.to_bits()),
            DnsResolutionResult::ErrConnRefused => (-1, 0),
            DnsResolutionResult::ErrConnReset => (-2, 0),
            DnsResolutionResult::ErrConnAbort => (-3, 0),
            DnsResolutionResult::ErrNotConnected => (-4, 0),
            DnsResolutionResult::ErrTimeout => (-5, 0),
            DnsResolutionResult::ErrInterrupted => (-6, 0),
            DnsResolutionResult::ErrUnexpectedEof => (-7, 0),
            DnsResolutionResult::ErrOutOfMemory => (-8, 0),
            DnsResolutionResult::ErrZeroAddrs => (-9, 0),
            DnsResolutionResult::ErrThread => (-10, 0),
            DnsResolutionResult::ErrOther => (-11, 0),
        }
    }
}

impl TryFrom<(EventSubcode, EventData)> for DnsResolutionResult {
    type Error = ();

    fn try_from(data: (EventSubcode, EventData)) -> Result<DnsResolutionResult, Self::Error> {
        match data {
            (0, ip) => Ok(DnsResolutionResult::Ok(Ipv4Addr::from_bits(ip))),
            (-1, _) => Ok(DnsResolutionResult::ErrConnRefused),
            (-2, _) => Ok(DnsResolutionResult::ErrConnReset),
            (-3, _) => Ok(DnsResolutionResult::ErrConnAbort),
            (-4, _) => Ok(DnsResolutionResult::ErrNotConnected),
            (-5, _) => Ok(DnsResolutionResult::ErrTimeout),
            (-6, _) => Ok(DnsResolutionResult::ErrInterrupted),
            (-7, _) => Ok(DnsResolutionResult::ErrUnexpectedEof),
            (-8, _) => Ok(DnsResolutionResult::ErrOutOfMemory),
            (-9, _) => Ok(DnsResolutionResult::ErrZeroAddrs),
            (-10, _) => Ok(DnsResolutionResult::ErrThread),
            (-11, _) => Ok(DnsResolutionResult::ErrOther),
            _ => Err(()),
        }
    }
}

use std::future::Future;

use super::events::Event;
use crate::prelude::*;

pub trait ResultEventLog {
    type Ok;
    type Err;
    type Out;

    async fn ok_event(self, event: Event) -> Self::Out;
    async fn err_event(self, event: Event) -> Self::Out;
    async fn err_event_map<F: FnOnce(&Self::Err) -> Event>(self, f: F) -> Self::Out;

    #[allow(dead_code)]
    async fn err_evcontext(self, event: Event, context: defmt::Str) -> Result<Self::Ok>
    where
        Self: Sized,
        Self::Out: ErrorContext<Self::Ok>,
        Self::Err: std::fmt::Debug,
    {
        self.err_event(event).await.context(context)
    }
}

impl<T, E> ResultEventLog for std::result::Result<T, E> {
    type Ok = T;
    type Err = E;
    type Out = Self;

    async fn ok_event(self, event: Event) -> Self {
        if self.is_ok() {
            itc().eventlog.write(event).await;
        }
        self
    }

    async fn err_event(self, event: Event) -> Self {
        if self.is_err() {
            itc().eventlog.write(event).await;
        }
        self
    }

    async fn err_event_map<F: FnOnce(&E) -> Event>(self, f: F) -> Self {
        if let Err(ref e) = self {
            itc().eventlog.write(f(e)).await;
        }
        self
    }
}

pub trait ResultEventLogAsync {
    type Ok;
    type Err;
    type Out;

    async fn ok_event(self, event: Event) -> Self::Out;
    async fn err_event(self, event: Event) -> Self::Out;
    async fn err_event_map<F: FnOnce(&Self::Err) -> Event>(self, f: F) -> Self::Out;

    async fn err_evcontext(self, event: Event, context: defmt::Str) -> Result<Self::Ok>
    where
        Self: Sized,
        Self::Out: ErrorContext<Self::Ok>,
        Self::Err: std::fmt::Debug,
    {
        self.err_event(event).await.context(context)
    }
}

impl<T, E, R> ResultEventLogAsync for R
where
    R: Future<Output = std::result::Result<T, E>>,
{
    type Ok = T;
    type Err = E;
    type Out = R::Output;

    async fn ok_event(self, event: Event) -> Self::Out {
        self.await.ok_event(event).await
    }

    async fn err_event(self, event: Event) -> Self::Out {
        self.await.err_event(event).await
    }

    async fn err_event_map<F: FnOnce(&E) -> Event>(self, f: F) -> Self::Out {
        self.await.err_event_map(f).await
    }
}

use std::{
    fmt::Write as FmtWrite,
    fs::{File, OpenOptions},
    io::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Seek, Seek<PERSON>rom, Write},
};

use esp_idf_svc::hal::reset::ResetReason;

use super::events::Event;
use crate::{flash::littlefs, prelude::*, utils::task};

pub enum EventLogRequest {
    /// Write an event to the event log.
    Write(Event),
    /// Flush the cached events to the event log.
    Flush,
    /// Get entries from the event log.
    ///
    /// Returns the event log entries as an array with the latest entry at the end of the array.
    /// The parameters behave as follows:
    /// - page: 0 = current page, 1 = previous page, ...
    /// - offset: number of entries to go back in time on the page (0 = get the most recent
    ///   entries of the page, 10 = omit the 10 most recent entries and start from there).
    /// - limit: number of entries to return
    Get(EventLogRange),
    /// Get the numbers of entries on a given page.
    GetPageEntryCount(u8),
}

pub struct EventLogRange {
    pub page: u8,
    pub offset: usize,
    pub limit: usize,
}

pub enum EventLogResponse {
    WriteAck,
    FlushAck,
    Get(Result<Vec<Entry>>),
    GetPageEntryCount(Result<usize>),
}

pub fn create_eventlog_task() {
    task::spawn("eventlog", 10000, move || {
        info!("Started task");

        let littlefs_available = match littlefs::init() {
            Ok(()) => true,
            Err(e) => {
                error!("Failed to initialise LittleFS: err={}", e);
                littlefs::format();
                false
            }
        };

        hal::task::block_on(async {
            match littlefs_available {
                true => run().await,
                false => dummy_run().await,
            }
        });
    });
}

async fn run() {
    let mut eventlog = InnerEventLog::init().await;

    loop {
        let (req, ret) = itc().eventlog.recv().await;
        match req {
            EventLogRequest::Write(event) => {
                eventlog.write(event).await;
                ret.ret(EventLogResponse::WriteAck)
            }
            EventLogRequest::Flush => {
                eventlog.flush().await;
                ret.ret(EventLogResponse::FlushAck)
            }
            EventLogRequest::Get(params) => {
                let resp = eventlog.get(params.page, params.offset, params.limit).await;
                ret.ret(EventLogResponse::Get(resp))
            }
            EventLogRequest::GetPageEntryCount(page) => {
                let resp = eventlog.get_page_entry_count(page).await;
                ret.ret(EventLogResponse::GetPageEntryCount(resp))
            }
        }
    }
}

async fn dummy_run() {
    warn!("Running dummy task");

    fn err<T>() -> Result<T> {
        Err(anyhow!("event log not available"))
    }

    loop {
        let (req, ret) = itc().eventlog.recv().await;
        match req {
            EventLogRequest::Write(_) => ret.ret(EventLogResponse::WriteAck),
            EventLogRequest::Flush => ret.ret(EventLogResponse::FlushAck),
            EventLogRequest::Get(_) => ret.ret(EventLogResponse::Get(err())),
            EventLogRequest::GetPageEntryCount(_) => {
                ret.ret(EventLogResponse::GetPageEntryCount(err()))
            }
        }
    }
}

pub const BUFFER_SIZE: usize = 32;
pub const FILE_COUNT: u8 = 4;
pub const ENTRY_SIZE: usize = std::mem::size_of::<Entry>();
pub const ITEMS_PER_FILE: usize = 1024;

#[allow(dead_code)]
pub const BYTES_PER_FILE: usize = ITEMS_PER_FILE * ENTRY_SIZE;

// littlefs is most efficient with file sizes that are a multiple of 4 KiB. this ensures that
// ITEMS_PER_FILE is configured correctly for improved performance.
const _: () = std::assert!(BYTES_PER_FILE % 4096 == 0);

struct InnerEventLog {
    buffer: Vec<Entry>,
    current_file: u8,
    current_file_entries: usize,
}

impl InnerEventLog {
    async fn init() -> Self {
        let current_file = nvs::event_log_file::get().await;

        let mut ret = Self {
            buffer: Vec::with_capacity(BUFFER_SIZE),
            current_file,
            current_file_entries: 0,
        };

        ret.current_file_entries = ret
            .open_current_file()
            .ok()
            .and_then(|f| f.metadata().ok())
            .map(|m| (m.len() as usize) / ENTRY_SIZE)
            .unwrap_or(0);

        ret
    }

    async fn write(&mut self, event: Event) {
        let (code, subcode, data) = event.into();
        let entry = Entry {
            code,
            subcode,
            data,
            timestamp: crate::utils::get_os_timestamp(),
            reserved: 0,
        };

        // prevent memory build-up when buffer cannot get flushed for some reason
        if self.buffer.len() >= (BUFFER_SIZE * 2) {
            return;
        }

        self.buffer.push(entry);
        if self.buffer.len() == BUFFER_SIZE {
            self.flush().await;
        }
    }

    async fn flush(&mut self) {
        if let Err(e) = self.try_flush().await {
            error!("Failed to flush event log: err={}", e);
        };
    }

    async fn try_flush(&mut self) -> Result<()> {
        if self.buffer.is_empty() {
            return Ok(());
        }

        let mut offset = 0;
        let mut file = self
            .open_current_file()
            .context(intern!("open current file"))?;
        let mut num_entries_left = ITEMS_PER_FILE - self.current_file_entries;

        while (self.buffer.len() - offset) > 0 {
            if num_entries_left == 0 {
                file = self
                    .open_next_file()
                    .await
                    .context(intern!("open next file"))?;
                num_entries_left = ITEMS_PER_FILE;
                self.current_file_entries = 0;
            }
            debug_assert!(
                self.current_file_entries
                    == (file.metadata().context(intern!("get metadata"))?.len() as usize)
                        / ENTRY_SIZE
            );
            let num_entries_to_write = num_entries_left.min(self.buffer.len() - offset);
            for i in 0..num_entries_to_write {
                file.write_all(self.buffer[offset + i].as_bytes())
                    .context(intern!("get fs metadata"))?;
                self.current_file_entries += 1;
            }
            offset += num_entries_to_write;
            num_entries_left = 0;
            let _ = file.flush();
        }
        self.buffer.clear();

        Ok(())
    }

    async fn get(&self, page: u8, offset: usize, limit: usize) -> Result<Vec<Entry>> {
        if limit == 0 {
            return Ok(Vec::new());
        }
        let Ok(mut file) = self.open_previous_file_readonly(page) else {
            warn!("Failed to open file");
            return Ok(Vec::new());
        };
        let num_entries = match page {
            i if i % FILE_COUNT == 0 => self.current_file_entries,
            _ => (file.metadata().context(intern!("get fs metadata"))?.len() as usize) / ENTRY_SIZE,
        };
        if offset >= num_entries {
            warn!("Read more entries than max from file");
            return Ok(Vec::new());
        }

        let seek = usize::min(num_entries, offset + limit);
        let read = usize::min(num_entries - offset, limit);
        info!(
            "Reading entries: seek={}, read={}, num_entries={}",
            seek, read, num_entries
        );
        if seek < num_entries {
            _ = file
                .seek(SeekFrom::End(-((seek * ENTRY_SIZE) as i64)))
                .context(intern!("seek"));
        }
        // read bytes from file directly into Vec<Entry>
        let reader = BufReader::new(file);
        let buf = reader
            .take((read * ENTRY_SIZE) as u64)
            .bytes()
            .take_while(|b| b.is_ok())
            .map(|b| unsafe { b.unwrap_unchecked() })
            .array_chunks::<ENTRY_SIZE>()
            .map(|bytes| unsafe { std::mem::transmute::<[u8; ENTRY_SIZE], Entry>(bytes) })
            .collect();
        Ok(buf)
    }

    async fn get_page_entry_count(&self, page: u8) -> Result<usize> {
        match page {
            i if i % FILE_COUNT == 0 => Ok(self.current_file_entries),
            _ => {
                let file = self
                    .open_previous_file_readonly(page)
                    .context(intern!("open page"))?;
                Ok(
                    (file.metadata().context(intern!("get fs metadata"))?.len() as usize)
                        / ENTRY_SIZE,
                )
            }
        }
    }

    fn get_file_path(idx: u8) -> heapless::String<32> {
        let mut file_path = heapless::String::<32>::new();
        _ = write!(&mut file_path, "/littlefs/event-log-{}.bin", idx);
        file_path
    }

    fn get_current_file_path(&self) -> heapless::String<32> {
        Self::get_file_path(self.current_file)
    }

    fn open_current_file(&self) -> std::io::Result<File> {
        OpenOptions::new()
            .create(true)
            .append(true)
            .open(&*self.get_current_file_path())
    }

    async fn open_next_file(&mut self) -> std::io::Result<File> {
        self.current_file = (self.current_file + 1) % FILE_COUNT;
        nvs::event_log_file::set(self.current_file).await;
        OpenOptions::new()
            .create(true)
            .write(true)
            .truncate(true)
            .open(&*self.get_current_file_path())
    }

    fn open_previous_file_readonly(&self, offset: u8) -> std::io::Result<File> {
        // offset 0 = current, offset 1 = previous, ...
        let idx = (self.current_file + FILE_COUNT - (offset % FILE_COUNT)) % FILE_COUNT;
        OpenOptions::new()
            .read(true)
            .open(&*Self::get_file_path(idx))
    }
}

#[repr(Rust, packed(4))]
pub struct Entry {
    pub code: EventCode,
    pub subcode: EventSubcode,
    pub data: EventData,
    pub timestamp: EventTimestamp,
    #[allow(dead_code)]
    pub reserved: u32, // for future use
}

impl Entry {
    pub fn event(&self) -> Option<Event> {
        match (self.code, self.subcode, self.data) {
            (0x0001, r, _) => Some(Event::WifiStart(r.try_into().ok()?)),
            (0x0002, r, _) => Some(Event::StaConnect(r.try_into().ok()?)),
            (0x0003, _, d) => Some(Event::StaDisconnect((d as u16).try_into().ok()?)),
            (0x0004, r, _) => Some(Event::WpsStart(r.try_into().ok()?)),
            (0x0005, r, _) => Some(Event::WpsFinished(r.try_into().ok()?)),
            (0x0006, r, _) => Some(Event::EthStart(r.try_into().ok()?)),
            (0x0007, r, _) => Some(Event::EthConnect(r.try_into().ok()?)),
            (0x0008, _, _) => Some(Event::EthDisconnect),
            (0x0009, r, d) => Some(Event::NetifUp((r, d).try_into().ok()?)),
            (0x000a, r, _) => Some(Event::StartTempIp(r.try_into().ok()?)),
            (0x000b, r, d) => Some(Event::DnsResolution((r, d).try_into().ok()?)),
            (0x000c, r, _) => Some(Event::PortalConnect(r.try_into().ok()?)),
            (0x000d, r, _) => Some(Event::PortalTlsConnect(r.try_into().ok()?)),
            (0x000e, r, _) => Some(Event::PortalLogonResponse(r.try_into().ok()?)),
            (0x000f, r, _) => Some(Event::PortalDisconnect(r.try_into().ok()?)),
            (0x0010, r, _) => Some(Event::LocalConnect(r.try_into().ok()?)),
            (0x0011, r, _) => Some(Event::LocalTlsConnect(r.try_into().ok()?)),
            (0x0012, r, _) => Some(Event::LocalDisconnect(r.try_into().ok()?)),
            (0x0013, _, d) => Some(Event::BusDiscover(d.try_into().ok()?)),
            (0x0014, _, d) => Some(Event::BusDisconnect(d.try_into().ok()?)),
            (0x0015, _, d) => Some(Event::Config(d.try_into().ok()?)),
            (0x0016, _, d) => Some(Event::ResetTrigger(d.try_into().ok()?)),
            (0x0017, _, d) => Some(Event::Start(decode_reset_reason(d)?)),
            (0x0018, r, d) => Some(Event::OtaUpdate((r, d).try_into().ok()?)),
            _ => None,
        }
    }

    fn as_bytes(&self) -> &[u8] {
        let data = self as *const Self as *const u8;
        unsafe { std::slice::from_raw_parts(data, std::mem::size_of::<Entry>()) }
    }
}

pub type EventCode = u16;
pub type EventSubcode = i16;
pub type EventData = u32;
pub type EventTimestamp = u32;

// ensure that Entry has 16 bytes for optimal storing
const _: () = std::assert!(std::mem::size_of::<Entry>() == 16);

impl From<Event> for (EventCode, EventSubcode, EventData) {
    fn from(event: Event) -> (EventCode, EventSubcode, EventData) {
        match event {
            Event::WifiStart(r) => (0x0001, r as i16, 0),
            Event::StaConnect(r) => (0x0002, r as i16, 0),
            Event::StaDisconnect(d) => (0x0003, 0, d as u16 as u32),
            Event::WpsStart(r) => (0x0004, r as i16, 0),
            Event::WpsFinished(r) => (0x0005, r as i16, 0),
            Event::EthStart(r) => (0x0006, r as i16, 0),
            Event::EthConnect(r) => (0x0007, r as i16, 0),
            Event::EthDisconnect => (0x0008, 0, 0),
            Event::NetifUp(x) => {
                let (r, d) = x.into();
                (0x0009, r, d)
            }
            Event::StartTempIp(r) => (0x000a, r as i16, 0),
            Event::DnsResolution(x) => {
                let (r, d) = x.into();
                (0x000b, r, d)
            }
            Event::PortalConnect(r) => (0x000c, r as i16, 0),
            Event::PortalTlsConnect(r) => (0x000d, r as i16, 0),
            Event::PortalLogonResponse(r) => (0x000e, r as i16, 0),
            Event::PortalDisconnect(r) => (0x000f, r as i16, 0),
            Event::LocalConnect(r) => (0x0010, r as i16, 0),
            Event::LocalTlsConnect(r) => (0x0011, r as i16, 0),
            Event::LocalDisconnect(r) => (0x0012, r as i16, 0),
            Event::BusDiscover(d) => (0x0013, 0, EventData::from(d)),
            Event::BusDisconnect(d) => (0x0014, 0, EventData::from(d)),
            Event::Config(d) => (0x0015, 0, d as u32),
            Event::ResetTrigger(d) => (0x0016, 0, d as u32),
            Event::Start(d) => (0x0017, 0, encode_reset_reason(d)),
            Event::OtaUpdate(x) => {
                let (r, d) = x.into();
                (0x0018, r, d)
            }
        }
    }
}

fn encode_reset_reason(r: ResetReason) -> EventData {
    match r {
        ResetReason::Software => 0,
        ResetReason::ExternalPin => 1,
        ResetReason::Watchdog => 2,
        ResetReason::Sdio => 3,
        ResetReason::Panic => 4,
        ResetReason::InterruptWatchdog => 5,
        ResetReason::PowerOn => 6,
        ResetReason::Unknown => 7,
        ResetReason::Brownout => 8,
        ResetReason::TaskWatchdog => 9,
        ResetReason::DeepSleep => 10,
        ResetReason::USBPeripheral => 11,
        ResetReason::JTAG => 12,
        ResetReason::EfuseError => 13,
        ResetReason::PowerGlitch => 14,
        ResetReason::CPULockup => 15,
    }
}

fn decode_reset_reason(r: EventData) -> Option<ResetReason> {
    match r {
        0 => Some(ResetReason::Software),
        1 => Some(ResetReason::ExternalPin),
        2 => Some(ResetReason::Watchdog),
        3 => Some(ResetReason::Sdio),
        4 => Some(ResetReason::Panic),
        5 => Some(ResetReason::InterruptWatchdog),
        6 => Some(ResetReason::PowerOn),
        7 => Some(ResetReason::Unknown),
        8 => Some(ResetReason::Brownout),
        9 => Some(ResetReason::TaskWatchdog),
        10 => Some(ResetReason::DeepSleep),
        _ => None,
    }
}

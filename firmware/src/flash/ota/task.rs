use esp_idf_svc::ota::{<PERSON><PERSON><PERSON><PERSON>, EspOtaUpdate};

use super::OtaMessage;
use crate::{prelude::*, utils::task};

pub fn create_ota_task() {
    task::spawn("ota", 6000, move || {
        info!("Started OTA task");
        hal::task::block_on(run());
    });
}

async fn run() {
    loop {
        let (msg, slot) = itc().ota.recv().await;
        match msg {
            OtaMessage::Start { version, via_uart } => start_update(version, via_uart, slot).await,
            OtaMessage::Cancel => slot.ret(Ok(())),
            _ => slot.ret(Err(anyhow!("unexpected message type"))),
        }
    }
}

async fn start_update(version: String, via_uart: bool, slot: ReturnSlot<Result<()>>) {
    let typ = if via_uart { "UART" } else { "network" };

    let mut ota = EspOta::new().unwrap();

    info!(
        "Starting update: type={}, current_slot={}, update_slot={}",
        typ,
        ota.get_running_slot().unwrap().label.as_str(),
        ota.get_update_slot().unwrap().label.as_str(),
    );

    let update = match ota.initiate_update() {
        Ok(x) => x,
        Err(e) => {
            error!("Failed to initiate update: type={}, err={}", typ, e.defmt());
            slot.ret(Err(e.into()));
            return;
        }
    };

    slot.ret(Ok(()));

    let update = OtaUpdate::new(update, version);
    update.run().await;
}

struct OtaUpdate<'a> {
    update: Option<EspOtaUpdate<'a>>,
    expect_version: String,
    first_chunk: bool,
}

impl<'a> OtaUpdate<'a> {
    fn new(update: EspOtaUpdate<'a>, expect_version: String) -> Self {
        Self {
            update: Some(update),
            expect_version,
            first_chunk: true,
        }
    }

    async fn run(mut self) {
        let mut running = true;
        while running {
            // previously, this had a timeout. however, Smartset sometimes does Smartset things and has
            // a ginormous delay (that apparently is customizable) before an OTA update starts. So for
            // now, no timeout is implemented.
            // ---
            // let msg = select_biased! {
            //     msg = itc().ota.recv_req().fuse() => msg,
            //     _ = Timer::after(Duration::from_secs(30)).fuse() => OtaMessage::Cancel,
            // };
            let (msg, slot) = itc().ota.recv().await;
            let res = match msg {
                OtaMessage::Start { .. } => self.handle_start().await,
                OtaMessage::Chunk { data } => {
                    let res = self.handle_data(data).await;
                    if res.is_err() {
                        running = false;
                    }
                    res
                }
                OtaMessage::Finish => {
                    running = false;
                    self.handle_finish().await
                }
                OtaMessage::Cancel => {
                    running = false;
                    self.handle_cancel().await
                }
            };
            slot.ret(res);
        }
    }

    async fn handle_start(&self) -> Result<()> {
        error!("Received unexpected OTA start message");
        bail!("unexpected ota start message");
    }

    async fn handle_data(&mut self, data: Box<[u8]>) -> Result<()> {
        if self.first_chunk {
            // we should only ever receive larger chunks via Smartset or UART, so only
            // checking the first chunk should be fine.
            if data.len() >= 80 {
                let chunk_version = &data[48..80];
                let chunk_version_end = chunk_version
                    .iter()
                    .position(|c| *c == b'\0')
                    .unwrap_or(chunk_version.len());
                let chunk_version = &chunk_version[..chunk_version_end];
                if chunk_version != self.expect_version.as_bytes() {
                    // itc()
                    //     .ota()
                    //     .send_resp(Err(anyhow!("version mismatch")))
                    //     .await;
                    let _ = self.update.take().unwrap().abort();
                    bail!("version mismatch");
                }
            } else {
                warn!("Failed to check OTA update version information");
            }
        }
        self.first_chunk = false;

        self.update.as_mut().unwrap().write(&data)?;

        Ok(())
    }

    async fn handle_finish(&mut self) -> Result<()> {
        match self.update.take().unwrap().complete() {
            Ok(_) => {
                info!("Successfully finished OTA update");
                Ok(())
            }
            Err(e) => {
                error!("Error finishing OTA update: err={}", e.defmt());
                Err(e.into())
            }
        }
    }

    async fn handle_cancel(&mut self) -> Result<()> {
        match self.update.take().unwrap().abort() {
            Ok(_) => {
                info!("Cancelled OTA update");
                Ok(())
            }
            Err(e) => {
                error!("Failed to cancel OTA update: err={}", e.defmt());
                Err(e.into())
            }
        }
    }
}

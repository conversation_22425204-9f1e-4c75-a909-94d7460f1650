use std::cmp::Ordering;

use super::{OtaMessage, OtaNetworkState, OtaState, STATE};
use crate::{
    flash::eventlog::{
        events::{Event, OtaUpdateResult},
        utils::{ResultEventLog, ResultEventLogAsync},
    },
    prelude::*,
    smartset::msg::{
        BinaryDataWrapper, OtaUpdateCompletionEnum, OtaUpdateCompletionStatus, OtaUpdateRequest,
        OtaUpdateResponse, OtaUpdateResponseIntent, OtaUpdateResponseRejectReason, Telegram,
    },
};

const CHUNK_SIZE: usize = 3 * 1450;

static_slot!(SLOT, Result<()>);

pub async fn start(request: &OtaUpdateRequest) -> OtaUpdateResponse {
    let mut guard = STATE.lock().await;

    if matches!(*guard, OtaState::Uart(_)) {
        Event::OtaUpdate(OtaUpdateResult::ErrBusy).log().await;
        return OtaUpdateResponse {
            intent: OtaUpdateResponseIntent::Reject,
            reject_reason: Some(OtaUpdateResponseRejectReason::Busy),
            chunk_size: None,
        };
    }

    let security_version = get_security_version()
        .err_event(Event::OtaUpdate(OtaUpdateResult::ErrOther))
        .await;
    let Ok(security_version) = security_version else {
        return OtaUpdateResponse {
            intent: OtaUpdateResponseIntent::Reject,
            reject_reason: Some(OtaUpdateResponseRejectReason::InternalError),
            chunk_size: None,
        };
    };

    if security_version > request.security_version.parse::<u32>().unwrap() {
        Event::OtaUpdate(OtaUpdateResult::ErrSecurity).log().await;
        return OtaUpdateResponse {
            intent: OtaUpdateResponseIntent::Reject,
            reject_reason: Some(OtaUpdateResponseRejectReason::Security),
            chunk_size: None,
        };
    }

    let ota_disabled = nvs::ota_disabled::get().await;
    if ota_disabled {
        Event::OtaUpdate(OtaUpdateResult::ErrUpdatesDisabled)
            .log()
            .await;
        return OtaUpdateResponse {
            intent: OtaUpdateResponseIntent::Reject,
            reject_reason: Some(OtaUpdateResponseRejectReason::UpdatesDisabled),
            chunk_size: None,
        };
    }

    *guard = OtaState::Network(OtaNetworkState {
        expected_id: 0,
        total_size: request.total_size,
        transmitted: 0,
        stream_id: request.stream_id,
        event_log_version: request.version.as_str().into(),
    });

    let msg = OtaMessage::Start {
        version: request.version.clone(),
        via_uart: false,
    };

    let _ = itc().ota.process(OtaMessage::Cancel, &SLOT).await;
    match itc()
        .ota
        .process(msg, &SLOT)
        .err_event(Event::OtaUpdate(OtaUpdateResult::ErrOther))
        .await
    {
        Ok(()) => OtaUpdateResponse {
            intent: OtaUpdateResponseIntent::Accept,
            reject_reason: None,
            chunk_size: Some(CHUNK_SIZE),
        },
        Err(_) => {
            *guard = OtaState::None;
            OtaUpdateResponse {
                intent: OtaUpdateResponseIntent::Reject,
                reject_reason: Some(OtaUpdateResponseRejectReason::InternalError),
                chunk_size: None,
            }
        }
    }
}

pub async fn write(data: &[u8]) -> Telegram {
    const SEND: u8 = 1;
    const ACK: u8 = 2;
    const ABORT_DEVICE: u8 = 3;
    const ABORT_SMARTSET: u8 = 5;
    const PAUSE: u8 = 6;

    let mut guard = STATE.lock().await;

    let state_ref = match *guard {
        OtaState::Network(ref mut state) => state,
        _ => {
            Event::OtaUpdate(OtaUpdateResult::ErrBusy).log().await;
            return Telegram::BinaryData(BinaryDataWrapper(vec![1, ABORT_DEVICE, 0xFF, 0xFF]));
        }
    };

    if data.len() < 4 || data[0] != state_ref.stream_id {
        Event::OtaUpdate(OtaUpdateResult::ErrInvalidData)
            .log()
            .await;
        return Telegram::BinaryData(BinaryDataWrapper(vec![1, ABORT_DEVICE, 0xFF, 0xFF]));
    }

    if data[1] == ABORT_SMARTSET {
        Event::OtaUpdate(OtaUpdateResult::ErrAbortSmartset)
            .log()
            .await;
        let _ = itc().ota.process(OtaMessage::Cancel, &SLOT).await;
        return Telegram::BinaryData(BinaryDataWrapper(vec![1, ACK, 0xFF, 0xFF]));
    }

    if data[1] == PAUSE {
        return Telegram::BinaryData(BinaryDataWrapper(vec![1, ACK, 0xFF, 0xFF]));
    }

    if data[1] != SEND {
        Event::OtaUpdate(OtaUpdateResult::ErrInvalidData)
            .log()
            .await;
        let _ = itc().ota.process(OtaMessage::Cancel, &SLOT).await;
        return Telegram::BinaryData(BinaryDataWrapper(vec![1, ABORT_DEVICE, 0xFF, 0xFF]));
    }

    let (id_hi, id_lo) = (data[2], data[3]);
    let id = ((id_hi as u16) << 8) | (id_lo as u16);

    if id != state_ref.expected_id {
        Event::OtaUpdate(OtaUpdateResult::ErrInvalidData)
            .log()
            .await;
        return Telegram::BinaryData(BinaryDataWrapper(vec![1, ABORT_DEVICE, id_hi, id_lo]));
    }

    if id % 32 == 0 {
        info!(
            "Reporting OTA update progress: tx={}kiB, total={}kiB",
            state_ref.transmitted / 1024,
            state_ref.total_size / 1024,
        );
    }

    let binary_data = &data[4..];
    let msg = OtaMessage::Chunk {
        data: Box::from(binary_data),
    };

    if let Err(e) = itc()
        .ota
        .process(msg, &SLOT)
        .err_event(Event::OtaUpdate(OtaUpdateResult::ErrOther))
        .await
    {
        error!("Failed to write form network ota update: err={}", e);
        *guard = OtaState::None;
        let _ = itc().ota.process(OtaMessage::Cancel, &SLOT).await;
        return Telegram::BinaryData(BinaryDataWrapper(vec![1, ABORT_DEVICE, id_hi, id_lo]));
    }

    state_ref.expected_id += 1;
    state_ref.transmitted += binary_data.len();

    match state_ref.transmitted.cmp(&state_ref.total_size) {
        Ordering::Equal => {
            let event_log_version = state_ref.event_log_version;
            *guard = OtaState::None;

            let status = match itc().ota.process(OtaMessage::Finish, &SLOT).await {
                Ok(()) => {
                    Event::OtaUpdate(OtaUpdateResult::Ok(event_log_version))
                        .log()
                        .await;
                    OtaUpdateCompletionStatus {
                        status: OtaUpdateCompletionEnum::Success,
                        message: None,
                    }
                }
                Err(e) => {
                    error!("Failed to finish ota network update: err={}", e);
                    OtaUpdateCompletionStatus {
                        status: OtaUpdateCompletionEnum::Failure,
                        message: Some(format!("failed to finish: {e}")),
                    }
                }
            };

            Telegram::OtaUpdateCompletionStatus(status)
        }
        Ordering::Greater => {
            let _ = itc().ota.process(OtaMessage::Cancel, &SLOT).await;
            Telegram::BinaryData(BinaryDataWrapper(vec![1, ABORT_DEVICE, id_hi, id_lo]))
        }
        Ordering::Less => Telegram::BinaryData(BinaryDataWrapper(vec![1, ACK, id_hi, id_lo])),
    }
}

pub async fn cancel() {
    let mut guard = STATE.lock().await;
    let _ = itc().ota.process(OtaMessage::Cancel, &SLOT).await;
    *guard = OtaState::None;
}

pub async fn cancel_network() {
    let mut guard = STATE.lock().await;
    if !matches!(*guard, OtaState::Network(_)) {
        return;
    }
    let _ = itc().ota.process(OtaMessage::Cancel, &SLOT).await;
    *guard = OtaState::None;
}

fn get_security_version() -> Result<u32> {
    let partition = unsafe { sys::esp_ota_get_running_partition().as_ref().unwrap() };
    let mut app_desc: sys::esp_app_desc_t = Default::default();
    sys::esp!(unsafe {
        sys::esp_ota_get_partition_description(partition as *const _, &mut app_desc)
    })?;
    let secure_version = app_desc.secure_version;

    Ok(secure_version)
}

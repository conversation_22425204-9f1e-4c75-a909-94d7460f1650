use crate::{
    flash::eventlog::events::{EventLogVersion, ResetTrigger},
    prelude::*,
};

pub mod net;
pub mod task;
pub mod uart;

pub enum OtaMessage {
    Start { version: String, via_uart: bool },
    Chunk { data: Box<[u8]> },
    Finish,
    Cancel,
}

struct OtaUartState {
    expected_id: u16,
}

struct OtaNetworkState {
    expected_id: u16,
    total_size: usize,
    transmitted: usize,
    stream_id: u8,
    event_log_version: EventLogVersion,
}

enum OtaState {
    Uart(OtaUartState),
    Network(OtaNetworkState),
    None,
}

static STATE: Mutex<OtaState> = Mutex::new(OtaState::None);

pub async fn rollback() -> Result<()> {
    let partition = unsafe { sys::esp_ota_get_next_update_partition(core::ptr::null()).as_ref() };

    match partition {
        Some(p) => {
            unsafe { sys::esp_ota_set_boot_partition(p) };
            crate::utils::restart(ResetTrigger::Rollback).await
        }
        None => Err(anyhow!("No rollback partition found")),
    }
}

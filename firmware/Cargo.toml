[package]
name = "esp-firmware"
version = "0.1.0"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"
resolver = "2"
rust-version = "1.85"
default-run = "esp-firmware"

[[bin]]
name = "esp-firmware"
harness = false

[dependencies]
async-tungstenite = { version = "0.29", default-features = false }
async-executor = { version = "1.12.0", default-features = false }
async-io-mini = { version = "0.3", features = [
    "embassy-time",
], default-features = false }
async-net-mini = { version = "2", default-features = false }
bitflags = { version = "2.9", default-features = false }
byteorder = { version = "1.4.3", features = ["std"], default-features = false }
defmt = { version = "1.0.1", default-features = false, features = ["alloc", "encoding-raw"] }
ebus = { git = "https://github.com/torkleyy/ebus", tag = "v0.3.0", default-features = false }
eebus = { path = "../eebus", features = [
    "defmt",
    "tungstenite",
], default-features = false }
embassy-futures = { version = "0.1.1", default-features = false }
embassy-sync = { version = "0.6.2", default-features = false }
embassy-time = { version = "0.4.0", default-features = false, features = [
    "defmt",
] }
embassy-time-queue-utils = { version = "0.1.0", features = [
    "generic-queue-64",
], default-features = false }
embedded-svc = { version = "0.28", default-features = false, features = [
    "alloc",
    "std",
    "use_numenum",
] }
enumset = { version = "1.1.6", default-features = false }
esp-idf-svc = { git = "https://github.com/torkleyy/esp-idf-svc", branch = "fork-0.51", default-features = false, features = [
    "alloc",
    "binstart",
    "critical-section",
    "std",
    "experimental",
    "embassy-time-driver",
] }
fastrand = { version = "2.0.0", default-features = false }
futures-lite = { version = "*", default-features = false }
heapless = { version = "0.8", default-features = false }
hex = { version = "0.4", features = ["serde"], default-features = false }
log = { version = "0.4.27", default-features = false }
nanoxml = { version = "0.2.0", features = [
    "alloc",
    "de",
    "ser",
    "derive",
    "heapless",
    "defmt",
], default-features = false }
num_enum = { version = "0.7.2", default-features = false }
picoserve = { version = "0.15", features = ["std"], default-features = false }
postcard = { version = "*", default-features = false }
serde = { version = "1.0", features = ["derive"], default-features = false }
tungstenite = { version = "0.26.0", optional = true, default-features = false }
website = { path = "../website", default-features = false }
whitelist = { path = "../whitelist", default-features = false }
cobs2 = { version = "0.1.4", default-features = false }
critical-section = { version = "1.2.0", default-features = false }

[dev-dependencies]
pretty_assertions = "1.4.0"
regex = "1.9.3"

[build-dependencies]
embuild = { version = "0.33", features = ["espidf"] }
vergen = { version = "8", features = ["build", "git", "gitcl", "rustc"] }

[package.metadata.esp-idf-sys]
esp_idf_tools_install_dir = "global"
esp_idf_version = "v5.3.2"
# in order to perform a production build, set the ESP_IDF_SDKCONFIG_DEFAULTS="config/sdkconfig.defaults;config/sdkconfig.prod" environment variable
esp_idf_sdkconfig_defaults = [
    "config/sdkconfig.defaults",
    "config/sdkconfig.dev",
]

[[package.metadata.esp-idf-sys.extra_components]]
remote_component = { name = "espressif/mdns", version = "1.2.3" }
[[package.metadata.esp-idf-sys.extra_components]]
remote_component = { name = "joltwallet/littlefs", version = "1.14.8" }
[[package.metadata.esp-idf-sys.extra_components]]
bindings_header = "extra/include/bindings.h"
component_dirs = ["extra"]

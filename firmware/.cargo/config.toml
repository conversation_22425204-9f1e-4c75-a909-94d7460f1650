[build]
target = "xtensa-esp32-espidf"

[profile.release]
opt-level = "z"
lto = "fat"
panic = "abort"
codegen-units = 1
debug = true      # Symbols are nice and they don't increase the size on Flash

[profile.dev]
debug = true
opt-level = "z"
codegen-units = 1

[target.xtensa-esp32-espidf]
linker = "ldproxy"
# runner = "espflash flash --monitor --baud 921600 --monitor-baud 921600 --partition-table partitions.csv --erase-parts ota_1 --bootloader target/xtensa-esp32-espidf/release/bootloader.bin --partition-table-offset 61440"
runner = "link-tool flash --monitor"
rustflags = [
    "--cfg", "espidf_time64",
    "-C", "llvm-args=--inline-threshold=225",
    "-C", "link-arg=-Tdefmt.x",
]

[unstable]
build-std = ["std", "panic_abort"]
#build-std-features = ["panic_immediate_abort"]

[env]
#CARGO_WORKSPACE_DIR = { value = "", relative = true }
DEFMT_LOG = "debug"

[net]
git-fetch-with-cli = true

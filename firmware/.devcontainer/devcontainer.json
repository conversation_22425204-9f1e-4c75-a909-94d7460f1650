{
    "name": "link-firmware",
    // Select between image and build propieties to pull or build the image.
    // "image": "docker.io/espressif/idf-rust:{{ mcu }}_latest",
    "build": {
        "dockerfile": "Dockerfile",
        "args": {
            "CONTAINER_USER": "esp",
            "CONTAINER_GROUP": "esp",
            "ESP_BOARD": "esp32"
        }
    },
    // https://github.com/serialport/serialport-rs/issues/153
    // "runArgs": [
    //     "--mount",
    //     "type=bind,source=/run/udev,target=/run/udev,readonly"
    // ],
    "customizations": {
        "vscode": {
            "settings": {
                "editor.formatOnPaste": true,
                "editor.formatOnSave": true,
                "editor.formatOnSaveMode": "file",
                "editor.formatOnType": true,
                "lldb.executable": "/usr/bin/lldb",
                "files.watcherExclude": {
                    "**/target/**": true
                },
                "rust-analyzer.checkOnSave.command": "clippy",
                "rust-analyzer.checkOnSave.allTargets": false,
                "[rust]": {
                    "editor.defaultFormatter": "rust-lang.rust-analyzer"
                }
            },
            "extensions": [
                "rust-lang.rust-analyzer",
                "tamasfe.even-better-toml",
                "fill-labs.dependi",
                "mutantdino.resourcemonitor",
                "yzhang.markdown-all-in-one",
                "ms-vscode.cpptools",
                "actboy168.tasks"
            ]
        }
    },
    "forwardPorts": [
        3333,
        8000
    ],
    "workspaceMount": "source=${localWorkspaceFolder}/..,target=/home/<USER>/link,type=bind,consistency=cached",
    "workspaceFolder": "/home/<USER>/link/firmware"
}
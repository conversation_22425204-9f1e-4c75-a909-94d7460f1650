### Application ###
CONFIG_BOOTLOADER_APP_SECURE_VERSION=0

### Target ###
CONFIG_IDF_TARGET="esp32"
CONFIG_ESP32_REV_MIN=3
CONFIG_ESP32_REV_MIN_3=y
CONFIG_FREERTOS_HZ=400
CONFIG_ESPTOOLPY_FLASHSIZE_16MB=y

### Task settings ###
CONFIG_ESP_MAIN_TASK_AFFINITY=0x0
CONFIG_ESP_MAIN_TASK_STACK_SIZE=4000
CONFIG_COMPILER_STACK_CHECK_MODE_ALL=y
CONFIG_PTHREAD_TASK_CORE_DEFAULT=0
CONFIG_PTHREAD_DEFAULT_CORE_0=y
CONFIG_PTHREAD_TASK_STACK_SIZE_DEFAULT=12000
CONFIG_SPIRAM_ALLOW_STACK_EXTERNAL_MEMORY=y

### Watchdog settings ###
CONFIG_BOOTLOADER_WDT_TIME_MS=30000
CONFIG_ESP_TASK_WDT_TIMEOUT_S=25
CONFIG_ESP_TASK_WDT_PANIC=y

### Required settings ###
CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=3000

### Time handling ###
CONFIG_LWIP_DHCP_GET_NTP_SRV=y

# CONFIG_LWIP_DEBUG=y

### Disable false positive warnings ###
CONFIG_PCNT_SUPPRESS_DEPRECATE_WARN=y
CONFIG_RMT_SUPPRESS_DEPRECATE_WARN=y
CONFIG_GPTIMER_SUPPRESS_DEPRECATE_WARN=y

### Flash optimzations ###
CONFIG_NEWLIB_NANO_FORMAT=y

### External RAM settings ###
CONFIG_SPIRAM=y
CONFIG_SPIRAM_USE_MALLOC=y
CONFIG_SPIRAM_MALLOC_RESERVE_INTERNAL=4096
CONFIG_SPIRAM_MALLOC_ALWAYSINTERNAL=16
CONFIG_SPIRAM_ALLOW_BSS_SEG_EXTERNAL_MEMORY=y
CONFIG_SPIRAM_ALLOW_NOINIT_SEG_EXTERNAL_MEMORY=y
CONFIG_SPIRAM_CACHE_WORKAROUND=n

### RAM optimizations ###
CONFIG_FREERTOS_IDLE_TASK_STACKSIZE=4096
#CONFIG_ESP_TIMER_TASK_STACK_SIZE=2048
#CONFIG_LWIP_MAX_SOCKETS=3

### UART ###
CONFIG_UART_ISR_IN_IRAM=y # enables low latency
CONFIG_NEWLIB_STDIN_LINE_ENDING_LF=y
CONFIG_ESP_CONSOLE_UART_CUSTOM=y
CONFIG_ESP_CONSOLE_UART_BAUDRATE=921600

### Network Configuration ###
CONFIG_LWIP_LOCAL_HOSTNAME="WOLFLINK"

### Network features ###
CONFIG_HTTPD_WS_SUPPORT=y

### Wifi ###
CONFIG_ESP_WIFI_WPS_PASSPHRASE=y
CONFIG_ESP_WIFI_ENTERPRISE_SUPPORT=n
CONFIG_SPIRAM_TRY_ALLOCATE_WIFI_LWIP=y
CONFIG_ESP_WIFI_STATIC_RX_BUFFER_NUM=16
CONFIG_ESP_WIFI_DYNAMIC_RX_BUFFER_NUM=16
CONFIG_ESP_WIFI_STATIC_TX_BUFFER=y
CONFIG_ESP_WIFI_CACHE_TX_BUFFER_NUM=16
CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER_NUM=16
CONFIG_ESP_WIFI_STATIC_MGMT_RX_BUFFER=y

### Ethernet ### --- done for Olimex according to https://github.com/bbulkow/Olimex-ESP32-POE-example/tree/main#sdkconfig-for-ethernet-component
CONFIG_ETH_ENABLED=y
CONFIG_ETH_USE_ESP32_EMAC=y
CONFIG_ETH_PHY_INTERFACE_RMII=y
CONFIG_ETH_RMII_CLK_INPUT=y
# CONFIG_ETH_RMII_CLK_OUTPUT_GPIO0=y
CONFIG_ETH_DMA_BUFFER_SIZE=1524
CONFIG_ETH_DMA_RX_BUFFER_NUM=4
CONFIG_ETH_DMA_TX_BUFFER_NUM=4

### Bluetooth ###
CONFIG_BT_ENABLED=n
CONFIG_BT_BLE_ENABLED=n
CONFIG_BT_BLUEDROID_ENABLED=n
CONFIG_BT_NIMBLE_ENABLED=y
CONFIG_BT_NIMBLE_MAX_CONNECTIONS=1
CONFIG_BT_NIMBLE_HOST_TASK_STACK_SIZE=3000

CONFIG_BTDM_CTRL_MODE_BLE_ONLY=y
CONFIG_BTDM_CTRL_MODE_BR_EDR_ONLY=n
CONFIG_BTDM_CTRL_MODE_BTDM=n

### TLS / Encryption ###
CONFIG_ESP_TLS_SERVER=y
CONFIG_MBEDTLS_GCM_C=y
CONFIG_MBEDTLS_AES_C=y
CONFIG_MBEDTLS_SECURITY_RISKS=y
CONFIG_MBEDTLS_PEM_PARSE_C=y
CONFIG_MBEDTLS_PEM_WRITE_C=y
CONFIG_MBEDTLS_X509_CRL_PARSE_C=y
CONFIG_MBEDTLS_X509_CSR_PARSE_C=y
CONFIG_MBEDTLS_CERTIFICATE_BUNDLE=n
CONFIG_MBEDTLS_EXTERNAL_MEM_ALLOC=y

# for eebus
CONFIG_MBEDTLS_ECP_C=y
CONFIG_MBEDTLS_ECDSA_C=y
CONFIG_MBEDTLS_ECP_DP_SECP256R1_ENABLED=y
CONFIG_MBEDTLS_PK_WRITE_C=y

CONFIG_MBEDTLS_DEBUG=n

### OTA Rollback ###
CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE=y
CONFIG_BOOTLOADER_APP_ANTI_ROLLBACK=y

### Development settings ###
CONFIG_BOOTLOADER_LOG_LEVEL=1
CONFIG_BOOTLOADER_LOG_LEVEL_ERROR=y
CONFIG_LOG_MAXIMUM_LEVEL=3
CONFIG_LOG_MAXIMUM_LEVEL_INFO=y
CONFIG_LOG_DEFAULT_LEVEL=0
CONFIG_LOG_DEFAULT_LEVEL_NONE=y

### No bogus line endings please ###
CONFIG_LIBC_STDOUT_LINE_ENDING_CRLF=n
CONFIG_LIBC_STDOUT_LINE_ENDING_LF=y
CONFIG_NEWLIB_STDOUT_LINE_ENDING_CRLF=n
CONFIG_NEWLIB_STDOUT_LINE_ENDING_LF=y

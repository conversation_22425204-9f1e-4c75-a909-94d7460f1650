{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            // more info at: https://github.com/Marus/cortex-debug/blob/master/package.json
            "name": "Attach",
            "type": "cortex-debug",
            "request": "attach", // attach instead of launch, because otherwise flash write is attempted, but fails
            "executable": "target/xtensa-esp32-espidf/debug/esp-firmware",
            "cwd": "${workspaceRoot}",
            "servertype": "openocd",
            "interface": "jtag",
            "svdFile": "svd/esp32.svd",
            "toolchainPrefix": "xtensa-esp32-elf",
            "gdbPath": "/Users/<USER>/.espressif/tools/xtensa-esp-elf-gdb/bin/xtensa-esp32-elf-gdb",
            "openOCDPreConfigLaunchCommands": [
                "set ESP_RTOS FreeRTOS"
            ],
            "serverpath": "/Users/<USER>/Apps/openocd-esp32/bin/openocd.exe",
            "configFiles": [
                "board/esp32-wrover-kit-3.3v.cfg"
            ],
            "overrideAttachCommands": [
                "set remote hardware-watchpoint-limit 2",
                "mon halt",
                "flushregs"
            ],
            "overrideRestartCommands": [
                "mon reset halt",
                "flushregs",
                "c"
            ]
        }
    ]
}
use std::{
    collections::BTreeSet,
    env,
    fs::File,
    io::{BufWriter, Write},
    path::Path,
};

fn wl_to_set(wl: &whitelist_parser::WhiteListConfig) -> phf_codegen::Set<[u8; 3]> {
    let mut added = BTreeSet::new();
    let mut s = phf_codegen::Set::new();

    for l in &wl.whitelists {
        for addr in &l.1.addresses {
            for info in &l.1.info_numbers {
                let value = [addr.0 as u8, info.to_be_bytes()[0], info.to_be_bytes()[1]];
                if added.insert(value) {
                    s.entry(value);
                }
            }

            for range in &l.1.ranges {
                for info in range.clone() {
                    let value = [addr.0 as u8, info.to_be_bytes()[0], info.to_be_bytes()[1]];
                    if added.insert(value) {
                        s.entry(value);
                    }
                }
            }
        }
    }

    s
}

fn main() {
    println!("cargo:rerun-if-changed=whitelists-smartset.json");
    println!("cargo:rerun-if-changed=whitelists-ism9.json");

    let path = Path::new(&env::var("OUT_DIR").unwrap()).join("codegen.rs");
    let mut file = BufWriter::new(File::create(path).unwrap());

    let wl_smartset = whitelist_parser::read_white_list_config("whitelists-smartset.json").unwrap();
    let wl_ism9 = whitelist_parser::read_white_list_config("whitelists-ism9.json").unwrap();

    writeln!(
        &mut file,
        "pub static SMARTSET: phf::Set<[u8; 3]> = {};",
        wl_to_set(&wl_smartset).build()
    )
    .unwrap();
    writeln!(
        &mut file,
        "pub static ISM9: phf::Set<[u8; 3]> = {};",
        wl_to_set(&wl_ism9).build()
    )
    .unwrap();
}

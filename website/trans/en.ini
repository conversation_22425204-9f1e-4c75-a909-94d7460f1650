meta-description = WOLF Link Configuration Website
overview = Overview
network = Network
settings = Settings
general-info = General Info
serial-number = Serial number
firmware-version = Firmware version
system-name = System name
eth-status = Ethernet status
eth-mac = Ethernet MAC address
wifi-status = Wi-Fi status
wifi-mac = Wi-Fi MAC address
wifi-ssid = Wi-Fi SSID
ip-addr = IP address
ap = Access point
ap-header = Access Point
ap-ssid = Access point SSID
portal-status = Smartset portal status
local-conn-status = Local connection status
bus = Bus
ebus = eBus
modbus = ModBus
connected = Connected
not-connected = Not connected
log-on = Log on
delayed-log-on = Delayed log on
delayed-log-on-2 = Delayed log on 2
try-connect-log-on = Try to connect + log on
ssl-connecting = SSL connecting
portal-response = Portal response
read-sysconfig = Reading system config
connected-locally = Connected locally
disconnecting = Disconnecting
init = Init
init-connection = Initiating Connection
debug-server-online = Debug server online
active = Active
enabled = Enabled
disabled = Disabled
portal-reboot-required = Reboot required to activate connection
portal-not-activated = Portal connection not activated, go to <a href="/protect/settings.htm" style="color: #4444ff">settings</a> to activate it
error-generic = An error occured, most likely since you entered invalid configuration data.
go-back = Go back
go-back-to-settings = Go back to settings
invalid-password = Invalid Password
invalid-password-msg = Password needs to have at least 8 characters. It has to contain at least one lowercase letter, at least one uppercase letter and at least one digit or special symbol. It cannot contain whitespace.
wifi-settings = Wi-Fi Settings
ssid = SSID
password = Password
wifi-auth = Authentication
wifi-auth-none = None
connect = Connect
network-settings = Network Settings
dhcp = Obtain network settings automatically (DHCP)
subnet-mask = Subnet mask
standard-gateway = Standard gateway
dns-1 = Primary DNS server
dns-2 = Secondary DNS server
show-advanced-settings = Show Advanced Settings
portal-host = Target server address
portal-port = Target server port
save-and-reboot = Save and Reboot
ap-enabled = Access point enabled
save = Save
wps = Wi-Fi Protected Setup (WPS)
start-wps = Start WPS
invalid-ssid = Invalid SSID
invalid-ip = Invalid IPv4 for static IP address. IPv4 addresses consist of 4 numbers from 0 to 255, separated by dots. Example for correct IP: ************ (no leading zeros allowed, use 42 instead of 042)
invalid-gateway = Invalid IPv4 for gateway. IPv4 addresses consist of 4 numbers from 0 to 255, separated by dots. Example for correct gateway: *********** (no leading zeros allowed, use 1 instead of 001)
invalid-dns-1 = Invalid IPv4 for primary DNS. IPv4 addresses consist of 4 numbers from 0 to 255, separated by dots. Example for correct DNS: *********** (no leading zeros allowed, use 1 instead of 001)
invalid-dns-2 = Invalid IPv4 for secondary DNS. IPv4 addresses consist of 4 numbers from 0 to 255, separated by dots. Example for correct DNS: *********** (no leading zeros allowed, use 1 instead of 001)
invalid-subnet = Invalid subnet mask
invalid-portal-host = Invalid target server host
invalid-portal-port = Invalid target server port
invalid-ap-ssid = Invalid access point SSID
needs-rebooting = The WOLF Link needs to be rebooted.
reboot = Reboot
reboot-title = Reboot
portal-connection = Portal Connection
portal-enabled-not-active = Portal connection enabled, but not active. Please <a href="/protect/reboot.htm">reboot</a> the WOLF Link.
enable-portal-connection = Enable internet connection to the WOLF Smartset portal server
agree-tos-privacy = I hereby agree to the <a href="/conditionsofuse.htm">terms of use</a> and the <a href="/privacypolicy.htm">data protection notice</a>.
save-settings = Save Settings
enter-new-password = Enter new password
repeat-password = Repeat new password
passwords-no-match = Passwords do not match
password-length = Password must be between 8 and 63 characters long
password-invalid-char = Invalid character in password. Allowed are: A-Z, a-z, 0-9 and special characters.
password-does-not-meet-requirements = Password must contain at least one lowercase letter (a-z), at least one uppercase letter (A-Z) and at least one digit or special character.
invalid-system-name = Invalid system name, 1 - 32 characters required
language = Language
lang-active-de =
lang-active-en = class="active" 
no-auth-required = (no authorization required)
auth-required = (authorization required)
rssi-0 = (signal strength: very bad)
rssi-1 = (signal strength: bad)
rssi-2 = (signal strength: ok)
rssi-3 = (signal strength: good)
rssi-4 = (signal strength: excellent)
available-wifi-networks = Available Wi-Fi networks:
custom-wifi-network = Manual Wi-Fi setup
scanning-for-networks = Scanning for networks, please wait...
wifi-scan-failed = No Wi-Fi networks found
ap-sta-interference-notice = Note: Wi-Fi connection will only become active after disconnecting from the Access Point.
hostname = Hostname
advanced-settings-notice = These advanced settings are intended for experienced users only. Modifying these settings without a thorough understanding of their purpose and implications could result in unexpected or undesirable behavior.
invalid-hostname = Invalid hostname. Required length: 1 - 30. Valid symbols: a-z, A-Z, 0-9, -
ota = Automatic Over-The-Air Updates
enable-ota = Enable automatic over-the-air WOLF Link security and feature updates

privacy-content = \
  <h1>WOLF Smartset Portal, WOLF Smartset Apps and ISM7 / WOLF Link interface modules</h1> \
  <h2>We take data protection seriously</h2> \
  <p>We believe that protecting your privacy when processing personal data is important. When you visit our website, our web servers store the following as standard: the IP of your internet service provider, the website you were visiting prior to ours, the pages of our website that you visit and the date and duration of your visit. This information is vital for the technical transmission of the website and secure server operation. We do not analyse this data in relation to you as an individual.</p> \
  <p>If you send us your data via a contact form, this data is stored and backed up securely on our servers. We only use your data to process your request. We treat your data as strictly confidential. We do not pass it on to third parties.</p> \
  <p>The controller is</p> \
  <p> <span>Wolf GmbH</span> <br> <span>Industriestr. 1</span> <br> <span>84048 Mainburg</span> <br> <span>Phone +49 8751/74-0</span> <br> <span>Email: <EMAIL></span></p> \
  <h2>Personal data</h2> \
  <p>Personal data is information about you as an individual. This includes your name, address and email address. You do not actually have to disclose any personal data to be able to visit our website. In some cases, we need your name and address and more information about you so that we can provide you with the service you want.</p> \
  <p>The same applies if you ask us to supply you with information material and/or if we are responding to your enquiries. We will always make you aware of this in such cases. Moreover, we only store the data that you have passed on to us automatically or voluntarily.</p> \
  <p>When you use one of our services, as a general rule we only collect the data that we need to be able to provide you with our service. We may ask you for further information, but this is on a voluntary basis. Whenever we process personal data, we do so in order to be able to provide you with our service or to pursue our commercial objectives.</p> \
  <h2>Interface modules</h2> \
  <p>The following data is stored on the ISM7 / WOLF Link interface module.</p> \
  <ul> \
    <li>LAN MAC address (also used as the unique serial number for the interface module),</li> \
    <li>WLAN MAC address,</li> \
    <li>Yes/no choice for “Obtain network settings automatically (DHCP)",</li> \
    <li>Network settings for the interface module (IP address, standard gateway, subnet mask, primary DNS, secondary DNS),</li> \
    <li>Name of the interface module,</li> \
    <li>Password used by the interface module,</li> \
    <li>Yes/no choice for “Create Internet connection to WOLF Smartset Portal”,</li> \
    <li>Data related to the WLAN network which the interface module connects to (WLAN name, password, encryption).</li> \
  </ul> \
  <p>The WOLF Link interface module also stores information about WLAN networks which it detects in its vicinity.</p> \
  <p>The operator can decide to restrict use of the interface module to its local home network (using the WOLF Smartset PC application for Windows and the WOLF Smartset mobile app for android and iOS) or to allow the interface module to connect to our WOLF Smartset Portal (using the WOLF Smartset website and the WOLF Smartset Mobile app for android and iOS; Internet connection required).</p> \
  <p>The Internet connection is deactivated by default and must be activated by the operator. This can be done using the configuration interface of the interface module or the setup assistant.</p> \
  <h2>WOLF Smartset Portal (Internet connection)</h2> \
  <h3>Interface modules which connect to the WOLF Smartset Portal</h3> \
  <p>The following data is recorded and stored for each interface module which connects to the WOLF Smartset Portal:</p> \
  <ul> \
    <li>Name of the interface module,</li> \
    <li>Serial number of the interface module,</li> \
    <li>Password used by the interface module,</li> \
    <li>Internet IP address and port of the interface module,</li> \
    <li>Firmware version of the interface module,</li> \
    <li>Hardware version of the interface module,</li> \
    <li>Interface module model (ISM7 or WOLF Link),</li> \
    <li>Interface module mode (LAN or WLAN),</li> \
    <li>The date and time on the WOLF system which the interface module is integrated into,</li> \
    <li>WOLF devices within the WOLF system which the interface module is integrated into,</li> \
  </ul> \
  <p>The following data is stored for each interface module which connects to the WOLF Smartset Portal:</p> \
  <ul> \
    <li>A unique number (ID) which is assigned by the WOLF Smartset Portal to a specific interface module,</li> \
    <li>Connection status (is the interface module online or off-line),</li> \
    <li>Interface module blocked, yes/no,</li> \
    <li>The date and time when the interface module first connected to the WOLF Smartset Portal,</li> \
    <li>The date and time of the most recent connection,</li> \
    <li>The date and time when the most recent connection was terminated,</li> \
    <li>The date and time of the most recent communication with the interface module,</li> \
    <li>The date and time when interface module data was last altered,</li> \
    <li>Number of successful connections,</li> \
    <li>Time and date offset between the interface module and the WOLF Smartset Portal.</li> \
  </ul> \
  <h2>Security</h2> \
  <p>We have taken technical and administrative security measures to protect your personal data from being lost, destroyed, manipulated or accessed by unauthorised persons. All our employees and the service providers who work for us are bound by the applicable data protection laws.</p> \
  <p>Whenever we collect and process personal data, it is always encrypted prior to transmission. This means that your data cannot be abused by third parties. Our security measures undergo constant improvement, and our privacy policies are continually revised. Please ensure that you have the latest version.</p> \
  <h2>Rights of data subjects</h2> \
  <p>Please do not hesitate to contact us at any time if you wish to know what personal data we hold about you or if you wish to have this data rectified or erased. You also have the right to restrict processing (Article 18 GDPR), the right to object to processing (Article 21 DSGVO) and the right to data portability (Article 20 DSGVO).</p> \
  <p>Please contact us directly if you wish to exercise any of these rights.</p> \
  <h2>Changes to this privacy policy</h2> \
  <p>We reserve the right to change our privacy policies should new technologies make this necessary. Please ensure that you have the latest version. If fundamental changes are made to this privacy policy, we will announce these changes on our website.</p> \
  <p>Interested parties and visitors to our website who have queries regarding privacy can contact:</p> \
  <p> <span>Herrn Christian Volkmer</span> <br> <span>Projekt 29 GmbH &amp; Co. KG</span> <br> <span>Ostengasse 14</span> <br> <span>93047 Regensburg</span> <br> <span>Tel.: +49 941 2986930</span> <br> <span>Fax: +49 941 29869316</span> <br> <span>Email: <EMAIL></span> <br> <span>Online: www.projekt29.de</span></p> \
  <p>If you are not satisfied with the response of our Data Protection Officer, you are entitled to submit a complaint to the data protection supervisory authority responsible for your Federal state.</p>

termsofuse-content = \
  <h2>1. Controlling heating/ventilation/air conditioning appliances online.</h2> \
  <p>Permission to use the Smartset communication service from Wolf GmbH free of charge is subject to the user's adherence to the conditions set out in this User Licence and Usage Agreement and applies to the following Wolf appliance types: BM, BM-2, RM-2, BWL-1-A, BWL-1-I, BWS-1, BWW-1, WPM-1, BWL-1S, BWL-1SB, CHC-SPLIT, CGB(K), CGB-2(K), CGS-2L, CGS-2R, CGW-2, CSZ-2, CGG-2, CGG-3, CGU-2, CHA, COB, COB-2, CWL, CWL-T, CWL-F, CWL-2, FGB, KM, KM-2, MGK-2, MM, MM-2, R1, R2, R3, R21, SM1, SM1-2, SM2, SM2-2, TOB, WOLF Power Systems, WRS-K, WRS-HKVS, WRS-IK, WRS-K Pool. Failure to adhere to the stipulations set out below will automatically result in the right to use the communication service being withdrawn.</p> \
  <h2>2. Right of use.</h2> \
  <p>The provider gives the user the simple, non-exclusive right to use the software and Smartset communication service (= software for controlling WOLF heating appliances, ventilation appliances and air conditioning appliances). The user is granted the right to use this communication service to control WOLF appliances.</p> \
  <h2>3. Functionality and end user's declaration of consent.</h2> \
  <p>By registering the serial number of their interface module and using the service via the web portal/app, users are aware that the Smartset communication service will control the following heating system functions and expressly give WOLF permission for this: adjusting the operating mode, time programs, parameters, standard settings. When using the software and communication service, users must abide by all instructions and security measures issued by the provider. Use of the communication service requires registration. Users must supply full and correct information as requested during the registration process. Users can register at www.wolf-smartset.com or in the Smartset app. To register, users must enter their name and email address and specify a user name and password. An email containing a confirmation code will then be sent to the email address provided. Registration is complete once the confirmation code has been activated. Users can also use their WOLF Profi login to register and log in.</p> \
  <h2>4. Safety measures.</h2> \
  <p>It is the end user's personal responsibility to provide adequate safety measures for protecting its internet access and terminal equipment (smart phone, tablet, PC, etc.) as well as its access data to the communication service against unauthorized use or misuse and against any consequences and/or consequential damages which may result from non-compliance with adequate safety measures. The end-user agrees to personally take all safety precautions necessary for the secure use of the communication service and for preventing its use by unauthorized third parties. This includes, among others, the secure handling of usage and access authorizations, as well as any identification and authentication measures within the scope of the communication service's use.</p> \
  <h2>5. Reservation of the right to make changes without notice and force majeure.</h2> \
  <p>The supplier reserves the right make changes to the communication service itself, or the manner of its provision and implementation, or the manner of its user guidance, etc., at any time and without prior notice. Availability of the communication service may be restricted for reasons of system maintenance. In case of events of force majeure such as e.g. the occurrence of technical problems which are beyond the supplier's control, there will be no provision or rendering of the communication service.</p> \
  <h2>6. Warranty and liability.</h2> \
  <p>The supplier shall be liable only for intentional acts and acts of gross negligence when providing and rendering the communication service. The supplier's liability for defects in title and quality shall be limited to defects which were fraudulent concealed. Moreover, the supplier's liability shall be limited to the actual amount equaling the current market value of the heating system. Product liability for the heater itself shall remain unaffected.</p> \
  <h2>7. Technical prerequisites for use of the communication service.</h2> \
  <h3>General:</h3> \
  <ul> \
    <li>WOLF heating/ventilation/air conditioning system with interface module ISM7i/ISM7e/Link home/Link pro, WLAN/LAN router</li> \
  </ul> \
  <h3>Internet connection requirements:</h3> \
  <ul> \
    <li>a reliable Internet connection (recommended: at least 1 Mbit/s upload and download speed, 500 MB data/month)</li> \
    <li>ISM7 FW1.xx: port 443 and port 45165 must be open</li> \
    <li>ISM7 FW2.xx / WOLF Link: port 443 and port 56154 must be open</li> \
  </ul> \
  <h3>System requirements to use the Smartset website:</h3> \
  <ul> \
    <li>an up-to-date browser (e.g. Google Chrome or Mozilla Firefox) with JavaScript, cookies, localStorage and sessionStorage enabled</li> \
  </ul> \
  <h3>System requirements to use the smartphone app:</h3> \
  <ul> \
    <li>iOS: iPhone or iPad with iOS 12.5.6 or higher</li> \
    <li>Android: smartphone or tablet with Android 4.2 or higher</li> \
  </ul> \
  <h3>System requirements to use the Windows application</h3> \
  <ul> \
    <li>With ISM7 FW1.xx: PC with Windows 7 operating system:</li> \
    <li>With ISM7 FW2.xx / WOLF Link: PC with Windows 7 or Windows 10 operating system</li> \
  </ul> \
  <h2>8. Final provisions.</h2> \
  <p>The invalidity, as the case may be, of individual provisions of this licensing and user agreement shall not affect the validity of the remaining provisions of the contract. In the event unforeseeable loopholes are found in the practical application of this contract, or in the event the invalidity of a provision becomes legally binding or concurred by both parties, the parties agree to fill this loophole and/or replace the invalid provision in a manner commensurate with the purposes of this contract. This contractual relationship is governed by the substantial law of the Federal Republic of Germany, to the exclusion of the United Nations Convention on Contracts for the International Sale of Goods (CISG). Sole place of venue shall be Munich unless another legal venue is mandatory by law.</p>

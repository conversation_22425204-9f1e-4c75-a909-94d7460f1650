meta-description = WOLF Link Einstellungen
overview = Übersicht
network = Netzwerk
settings = Einstellungen
general-info = Allgemeine Informationen
serial-number = Seriennummer
firmware-version = Firmware-Version
system-name = Anlagenname
eth-status = Ethernet
eth-mac = Ethernet MAC-Adresse
wifi-status = WLAN
wifi-mac = WLAN MAC-Adresse
wifi-ssid = WLAN SSID
ip-addr = IP-Adresse
ap = Access Point
ap-header = Access Point
ap-ssid = Access Point SSID
portal-status = Status Smartset-Portal
local-conn-status = Status lokale Verbindung
bus = Bus
ebus = eBus
modbus = ModBus
connected = Verbunden
not-connected = Nicht verbunden
log-on = Anmeldung
delayed-log-on = Verzögerte Anmeldung
delayed-log-on-2 = Verzögerte Anmeldung 2
try-connect-log-on = Verbinden und anmelden
ssl-connecting = SSL-Verbindungsaufbau
portal-response = Antwort von Portal erhalten
read-sysconfig = Anlagenkonfiguration auslesen
connected-locally = Lokal verbunden
disconnecting = Verbindung trennen
init = Init
init-connection = Verbindungsaufbau
debug-server-online = Debug-Server online
active = Aktiv
enabled = Aktiviert
disabled = Deaktiviert
portal-reboot-required = Neustart erforderlich, um Portalverbindung zu aktivieren
portal-not-activated = Portalverbindung noch nicht aktiviert, bitte in den <a href="/protect/settings.htm" style="color: #4444ff">Einstellungen</a> aktivieren
error-generic = Ein Fehler ist aufgetreten. Sind die Konfigurationsdaten korrekt?
go-back = Zurück
go-back-to-settings = Zurück zu den Einstellungen
invalid-password = Ungültiges Passwort
invalid-password-msg = Das Passwort muss mindestens 8 Zeichen lang sein. Es muss mindestens einen kleinen Buchstaben, mindestens einen großen Buchstaben und mindestens eine Zahl oder Sonderzeichen enthalten. Es darf keine Leerzeichen enthalten.
wifi-settings = WLAN-Einstellungen
ssid = SSID
password = Passwort
wifi-auth = Verschlüsselung
wifi-auth-none = Unverschlüsselt
connect = Verbinden
network-settings = Netzwerkeinstellungen
dhcp = Netzwerkeinstellungen automatisch übernehmen (DHCP)
subnet-mask = Subnetzmaske
standard-gateway = Standardgateway
dns-1 = Primärer DNS-Server
dns-2 = Sekundärer DNS-Server
show-advanced-settings = Einstellungen für Profis anzeigen
portal-host = Zielserveradresse
portal-port = Zielserverport
save-and-reboot = Speichern und neu starten
ap-enabled = Access Point aktiviert
save = Speichern
wps = Wi-Fi Protected Setup (WPS)
start-wps = WPS starten
invalid-ssid = Ungültige SSID
invalid-ip = Ungültige IPv4-Adresse für statische IP. IPv4-Adressen bestehen aus 4 Zahlen zwischen 0 und 255, die durch Punkte getrennt sind. Beispiel für korrekte IP: ************ (führende Nullen sind nicht erlaubt, bitte 42 statt 042 nutzen).
invalid-gateway = Ungültige IPv4-Adresse für Standardgateway. IPv4-Adressen bestehen aus 4 Zahlen zwischen 0 und 255, die durch Punkte getrennt sind. Beispiel für korrekte IP: *********** (führende Nullen sind nicht erlaubt, bitte 1 statt 001 nutzen)
invalid-dns-1 = Ungültige IPv4-Adresse für den primären DNS-Server. IPv4-Adressen bestehen aus 4 Zahlen zwischen 0 und 255, die durch Punkte getrennt sind. Beispiel für korrekte IP: *********** (führende Nullen sind nicht erlaubt, bitte 1 statt 001 nutzen)
invalid-dns-2 = Ungültige IPv4-Adresse für den sekundären DNS-Server. IPv4-Adressen bestehen aus 4 Zahlen zwischen 0 und 255, die durch Punkte getrennt sind. Beispiel für korrekte IP: *********** (führende Nullen sind nicht erlaubt, bitte 1 statt 001 nutzen)
invalid-subnet = Ungültige Subnetzmaske
invalid-portal-host = Ungültige Zielserveradresse
invalid-portal-port = Ungültiger Zielserverport
invalid-ap-ssid = Ungültige Access-Point-SSID
needs-rebooting = Das WOLF Link muss einen Neustart durchführen.
reboot = Neustart durchführen
reboot-title = Neustart
portal-connection = Portalverbindung
portal-enabled-not-active = Ein <a href="/protect/reboot.htm">Neustart</a> wird benötigt, um die Portalverbindung aufzubauen.
enable-portal-connection = Internetverbindung mit dem WOLF-Smartset-Portal aktivieren
agree-tos-privacy = Hiermit akzeptiere ich die <a href="/conditionsofuse.htm">Nutzungsbedingungen</a> und die <a href="/privacypolicy.htm">Datenschutzerklärung</a>.
save-settings = Einstellungen speichern
enter-new-password = Neues Passwort eingeben
repeat-password = Neues Passwort wiederholen
passwords-no-match = Passwort und Wiederholung stimmen nicht überein
password-length = Passwort muss zwischen 8 und 63 Buchstaben lang sein.
password-invalid-char = Ungültiges Zeichen im Passwort. Erlaubt sind: A-Z, a-z, 0-9 und Sonderzeichen.
password-does-not-meet-requirements = Passwort muss mindestens einen kleinen Buchstaben (a-z), mindestens einen großen Buchstaben (A-Z) und mindestens eine Zahl (0-9) oder Sonderzeichen enthalten.
invalid-system-name = Ungültiger Systemname, muss zwischen 1 und 32 Buchstaben lang sein
language = Sprache
lang-active-de = class="active"
lang-active-en = 
no-auth-required = Unverschlüsselt
auth-required = Verschlüsselt
rssi-0 = (Signalstärke: very bad)
rssi-1 = (Signalstärke: bad)
rssi-2 = (Signalstärke: ok)
rssi-3 = (Signalstärke: good)
rssi-4 = (Signalstärke: excellent)
available-wifi-networks = Verfügbare WLAN-Verbindungen:
custom-wifi-network = Manualle WLAN-Einrichtung
scanning-for-networks = WLAN-Netzwerke werden ermittelt, bitte warten...
wifi-scan-failed = Keine WLAN-Netzwerke gefunden
ap-sta-interference-notice = Achtung: WLAN-Verbindung wird erst aktiv, nachdem die Verbindung zum Access Point getrennt wurde.
hostname = Hostname
advanced-settings-notice = Diese Einstellungen sind nur für erfahrene Nutzer gedacht. Das Ändern dieser Einstellungen ohne gründliches Verständnis ihres Zwecks und ihrer Auswirkungen kann zu unerwarteten oder unerwünschten Ergebnissen führen.
invalid-hostname = Ungültiger Hostname. Erforderliche Länge: 1 - 30. Gültige Zeichen: a-z, A-Z, 0-9, -
ota = Automatische Updates
enable-ota = Automatische WOLF Link Sicherheits- und Funktionsupdates aktivieren

privacy-content = \
  <h1>WOLF Smartset Portal, WOLF Smartset Apps und Schnittstellenmodule ISM7 bzw. WOLF Link</h1> \
  <h2>Wir nehmen Datenschutz ernst</h2> \
  <p>Der Schutz Ihrer Privatsph&auml;re bei der Verarbeitung pers&ouml;nlicher Daten ist f&uuml;r uns ein wichtiges Anliegen. Wenn Sie unsere Webseite besuchen, speichern unsere Webserver standardm&auml;&szlig;ig die IP Ihres Internet Service Providers, die Webseite, von der aus Sie uns besuchen, die Webseiten, die Sie bei uns besuchen, sowie das Datum und die Dauer des Besuches. Diese Informationen sind f&uuml;r die technische &Uuml;bertragung der Webseiten und den sicheren Serverbetrieb zwingend erforderlich. Eine personalisierte Auswertung dieser Daten erfolgt nicht.</p> \
  <p>Sofern Sie uns Daten per Kontaktformular senden, werden diese Daten im Zuge der Datensicherung auf unseren Servern gespeichert. Ihre Daten werden von uns ausschlie&szlig;lich zur Bearbeitung Ihres Anliegens verwendet. Ihre Daten werden streng vertraulich behandelt. Eine Weitergabe an Dritte erfolgt nicht.</p> \
  <p>Verantwortliche Stelle im Sinne des Gesetzes ist</p> \
  <p> <span>Wolf GmbH</span> <br> <span>Industriestr. 1</span> <br> <span>84048 Mainburg</span> <br> <span>Telefon +49 8751/74-0</span> <br> <span>E-Mail <EMAIL></span></p> \
  <h2>Personenbezogene Daten</h2> \
  <p>Personenbezogene Daten sind Daten &uuml;ber Ihre Person. Diese beinhalten Ihren Namen, Ihre Adresse und Ihre E-Mail Adresse. Sie m&uuml;ssen auch keine personenbezogenen Daten preisgeben um unsere Internetseite besuchen zu k&ouml;nnen. In einigen F&auml;llen ben&ouml;tigen wir Ihren Namen und Adresse, sowie weitere Informationen um Ihnen die gew&uuml;nschte Dienstleistung anbieten zu k&ouml;nnen.</p> \
  <p>Das Gleiche gilt f&uuml;r den Fall, dass wir Sie auf Wunsch mit Informationsmaterial beliefern bzw. wenn wir Ihre Anfragen beantworten. In diesen F&auml;llen werden wir Sie immer darauf hinweisen. Au&szlig;erdem speichern wir nur die Daten, die Sie uns automatisch oder freiwillig &uuml;bermittelt haben.</p> \
  <p>Wenn Sie einen unserer Services nutzen, sammeln wir in der Regel nur die Daten die notwendig sind, um Ihnen unseren Service bieten zu k&ouml;nnen. M&ouml;glicherweise fragen wir Sie nach weiteren Informationen, die aber freiwilliger Natur sind. Wann immer wir personenbezogene Daten verarbeiten, tun wir dies um Ihnen unseren Service anbieten zu k&ouml;nnen oder um unsere kommerziellen Ziele zu verfolgen.</p> \
  <h2>Schnittstellenmodule</h2> \
  <p>Auf dem Schnittstellenmodul ISM7 / WOLF Link werden folgende Daten gespeichert:</p> \
  <ul> \
    <li>LAN MAC-Adresse (wird auch als eindeutige Seriennummer des Schnittstellenmoduls verwendet),</li> \
    <li>WLAN MAC-Adresse,</li> \
    <li>Netzwerkeinstellungen per DHCP automatisch beziehen, ob ja / nein,</li> \
    <li>Netzwerkeinstellungen f&uuml;r das Schnittstellenmodul (IP-Adresse, Standardgateway, Subnetzmaske, Prim&auml;rer DNS, Sekund&auml;rer DNS),</li> \
    <li>Name des Schnittstellenmoduls,</li> \
    <li>Passwort des Schnittstellenmoduls,</li> \
    <li>Internetverbindung zum WOLF Smartset Portal herstellen, ob ja / nein,</li> \
    <li>Daten zum WLAN Netzwerk mit dem sich das Schnittstellenmodul verbindet (WLAN Name, WLAN Passwort, WLAN Verschl&uuml;sselung).</li> \
  </ul> \
  <p>Das Schnittstellenmodul WOLF Link speichert zus&auml;tzlich sichtbare WLAN Netzwerke innerhalb seiner Umgebung.</p> \
  <p>Der Anlageneigent&uuml;mer kann entscheiden, ob er das Schnittstellenmodul nur lokal in seinem Heimnetzwerk bedienen m&ouml;chte (Bedienung m&ouml;glich per WOLF Smartset PC-Anwendung f&uuml;r Windows sowie WOLF Smartset mobile App f&uuml;r Android und iOS) oder &uuml;ber die Internetverbindung mit unserem WOLF Smartset Portal verbindet (Bedienung m&ouml;glich per WOLF Smartset Webseite sowie WOLF Smartset mobile App f&uuml;r Android und iOS; Internetverbindung erforderlich).</p> \
  <p>Die Internetverbindung ist in der Werkseinstellung deaktiviert und muss vom Anlageneigent&uuml;mer freigegeben werden. Dies geschieht &uuml;ber die Konfigurationsoberfl&auml;che des Schnittstellenmoduls oder &uuml;ber den Inbetriebnahme-Assistent.</p> \
  <h2>WOLF Smartset Portal (Internetverbindung)</h2> \
  <h3>Schnittstellenmodule, die sich mit dem WOLF Smartset Portal verbinden</h3> \
  <p>Zu jedem Schnittstellenmodul, das sich am WOLF Smartset Portal meldet, werden folgende Daten gelesen und gespeichert:</p> \
  <ul> \
    <li>Name des Schnittstellenmoduls,</li> \
    <li>Seriennummer des Schnittstellenmoduls,</li> \
    <li>Passwort des Schnittstellenmoduls,</li> \
    <li>Internet IP-Adresse und Port des Schnittstellenmoduls,</li> \
    <li>Firmwareversion des Schnittstellenmoduls,</li> \
    <li>Hardwareversion des Schnittstellenmoduls,</li> \
    <li>Typ des Schnittstellenmoduls (ISM7 oder WOLF Link),</li> \
    <li>Betriebsart des Schnittstellenmoduls (LAN oder WLAN),</li> \
    <li>Datum und Uhrzeit des WOLF Systems, in dem das Schnittstellenmodul integriert ist,</li> \
    <li>Erkannte WOLF Ger&auml;te innerhalb des WOLF Systems, in dem das Schnittstellenmodul integriert ist.</li> \
  </ul> \
  <p>Zu jedem Schnittstellenmodul, das sich am WOLF Smartset Portal meldet, werden folgende Daten gespeichert:</p> \
  <ul> \
    <li>Einzigartige, durch das WOLF Smartset Portal vergebene und einem bestimmten Schnittstellenmodul zugeordnete Nummer (ID),</li> \
    <li>Verbindungsstatus (ist das Schnittstellenmodul online oder offline),</li> \
    <li>Schnittstellenmodul gesperrt, ob ja / nein,</li> \
    <li>Datum und Uhrzeit, wann sich das Schnittstellenmodul das erste Mal mit dem WOLF Smartset Portal verbunden hat,</li> \
    <li>Datum und Uhrzeit des letzten erfolgreichen Verbindungsaufbaus,</li> \
    <li>Datum und Uhrzeit des letzten Verbindungsabbruchs,</li> \
    <li>Datum und Uhrzeit, wann zuletzt mit dem Schnittstellenmodul kommuniziert wurde,</li> \
    <li>Datum und Uhrzeit, wann sich zuletzt Daten zum Schnittstellenmodul ge&auml;ndert haben,</li> \
    <li>Anzahl erfolgreicher Verbindungsaufbauten,</li> \
    <li>Offset von Datum und Uhrzeit des Schnittstellenmoduls zu Datum und Uhrzeit des WOLF Smartset Portals.</li> \
  </ul> \
  <h2>Sicherheit</h2> \
  <p>Wir haben technische und administrative Sicherheitsvorkehrungen getroffen um Ihre personenbezogenen Daten gegen Verlust, Zerst&ouml;rung, Manipulation und unautorisierten Zugriff zu sch&uuml;tzen. All unsere Mitarbeiter sowie f&uuml;r uns t&auml;tige Dienstleister sind auf die g&uuml;ltigen Datenschutzgesetze verpflichtet.</p> \
  <p>Wann immer wir personenbezogene Daten sammeln und verarbeiten werden diese verschl&uuml;sselt bevor sie &uuml;bertragen werden. Das hei&szlig;t, dass Ihre Daten nicht von Dritten missbraucht werden k&ouml;nnen. Unsere Sicherheitsvorkehrungen unterliegen dabei einem st&auml;ndigen Verbesserungsprozess und unsere Datenschutzerkl&auml;rungen werden st&auml;ndig &uuml;berarbeitet. Bitte stellen Sie sicher, dass Ihnen die aktuellste Version vorliegt.</p> \
  <h2>Betroffenenrechte</h2> \
  <p>Bitte kontaktieren Sie uns jederzeit, wenn Sie sich informieren m&ouml;chten welche personenbezogenen Daten wir &uuml;ber Sie speichern bzw. wenn Sie diese berichtigen oder l&ouml;schen lassen wollen. Des Weiteren haben Sie das Recht auf Einschr&auml;nkung der Verarbeitung (Art. 18 DSGVO), ein Widerspruchsrechts gegen die Verarbeitung (Art. 21 DSGVO) sowie das Recht auf Daten&uuml;bertragbarkeit (Art. 20 DSGVO).</p> \
  <p>In diesen F&auml;llen wenden Sie sich bitte direkt an uns.</p> \
  <h2>&Auml;nderungen dieser Datenschutzerkl&auml;rung</h2> \
  <p>Wir behalten uns das Recht vor, unsere Datenschutzerkl&auml;rungen zu &auml;ndern falls dies aufgrund neuer Technologien notwendig sein sollte. Bitte stellen Sie sicher, dass Ihnen die aktuellste Version vorliegt. Werden an dieser Datenschutzerkl&auml;rung grundlegende &Auml;nderungen vorgenommen, geben wir diese auf unserer Website bekannt.</p> \
  <p>Alle Interessenten und Besucher unserer Internetseite erreichen uns in Datenschutzfragen unter:</p> \
  <p> <span>Herrn Christian Volkmer</span> <br> <span>Projekt 29 GmbH &amp; Co. KG</span> <br> <span>Ostengasse 14</span> <br> <span>93047 Regensburg</span> <br> <span>Tel.: +49 941 2986930</span> <br> <span>Fax: +49 941 29869316</span> <br> <span>E-Mail: <EMAIL></span> <br> <span>Internet: www.projekt29.de</span></p> \
  <p>Sollte unser Datenschutzbeauftragter Ihr Anliegen nicht zu Ihrer Zufriedenheit beantworten k&ouml;nnen, bleibt Ihnen in jedem Falle ihr Recht auf Beschwerde bei der f&uuml;r Ihr Bundesland zust&auml;ndigen Datenschutz-Aufsichtsbeh&ouml;rde.</p>

termsofuse-content = \
  <h2>1. Steuerung von Heizungs-/L&uuml;ftungs-/Klimager&auml;ten &uuml;ber das Internet.</h2> \
  <p>Die Einr&auml;umung der kostenfreien Nutzung des Smartset Kommunikationsdienstes der Fa. Wolf GmbH ist abh&auml;ngig von der Einhaltung der in dieser End-Nutzer Lizenz- und Nutzungsvereinbarung geregelten Voraussetzungen durch den Endnutzer und kann f&uuml;r nachfolgende Ger&auml;tetypen der Fa. WOLF genutzt werden: BM, BM-2, RM-2, BWL-1-A, BWL-1-I, BWS-1, BWW-1, WPM-1, BWL-1S, BWL-1SB, CHC-SPLIT, CGB(K), CGB-2(K), CGS-2L, CGS-2R, CGW-2, CSZ-2, CGG-2, CGG-3, CGU-2, CHA, COB, COB-2, CWL, CWL-T, CWL-F, CWL-2, FGB, KM, KM-2, MGK-2, MM, MM-2, R1, R2, R3, R21, SM1, SM1-2, SM2, SM2-2, TOB, WOLF Power Systems, WRS-K, WRS-HKVS, WRS-IK, WRS-K Pool. Die Nichteinhaltung nachfolgender Voraussetzungen hat den automatischen Wegfall der Nutzungsberechtigung f&uuml;r den Kommunikationsdienst zur Folge.</p> \
  <h2>2. Nutzungsrecht.</h2> \
  <p>Der Anbieter erteilt dem Endnutzer ein einfaches, nicht ausschliessliches Recht zur Nutzung der Software und des Kommunikationsdienstes Smartset (= Software zur Steuerung von Heizger&auml;ten, L&uuml;ftungsger&auml;ten und Klimager&auml;ten der Fa. WOLF). Die Nutzung des Kommunikationsdienstes zur Steuerung von Ger&auml;ten der Fa. WOLF wird dem Nutzer gestattet.</p> \
  <h2>3. Funktionalit&auml;t und Einwilligung des Endnutzers.</h2> \
  <p>Dem Nutzer ist bewusst und er erteilt mit Registrierung der Seriennummer seines Schnittstellenmoduls und Inbetriebnahme der Nutzung des Dienstes via Webportal/App der Fa. WOLF hiermit seine ausdr&uuml;ckliche Einwilligung, dass der Kommunikationsdienst Smartset folgende Funktionen seiner Heizung steuert: Anpassung Betriebsart, Zeitprogramme, Parameter, Grundeinstellungen. Der Endnutzer hat bei der Nutzung der Software und des Kommunikationsdienstes alle vom Anbieter hierzu gegebenen Hinweise und Sicherheitsma&szlig;nahmen zu beachten. Die Nutzung des Kommunikationsdienstes setzt eine Registrierung voraus. Der Endnutzer ist verpflichtet, die bei der Anmeldung abgefragten Daten richtig und vollst&auml;ndig mitzuteilen. Die Registrierung erfolgt unter www.wolf-smartset.com oder in der Smartset App. Zur Registrierung sind Name und E-Mail-Adresse einzugeben und ein Benutzername und Passwort festzulegen. Es wird eine E-Mail an die angegebene E-Mail-Adresse mit einem Best&auml;tigungscode verschickt. Nach Aktivieren des Best&auml;tigungscodes ist die Registrierung abgeschlossen. Falls vorhanden, kann der Endnutzer f&uuml;r die Registrierung und Anmeldung auch seinen WOLF Profi Login verwenden.</p> \
  <h2>4. Sicherheitsma&szlig;nahmen.</h2> \
  <p>Der Endnutzer ist selbst verantwortlich f&uuml;r hinreichende Sicherheitsma&szlig;nahmen, die seinen Internetzugang und sein Endger&auml;t (Smartphone, Tablet, PC etc.) sowie seine Zugangsdaten f&uuml;r den Kommunikationsdienst vor Missbrauch sch&uuml;tzen, sowie f&uuml;r Folgen bzw. Folgesch&auml;den, die sich aus der Nichteinhaltung hinreichender Sicherheitsmaßnahmen ergeben k&ouml;nnen. Der Endnutzer trifft die notwendigen Sicherheitsvorkehrungen, die f&uuml;r eine sichere Nutzung des Kommunikationsdienstes erforderlich sind, um die Nutzung durch Unbefugte zu verhindern selbst. Hierzu geh&ouml;rt z. B. der sichere Umgang mit Nutzungs- und Zugangsberechtigungen sowie etwaige Identifikations- und Authentifikations-Maßnahmen im Rahmen der Nutzung des Kommunikationsdienstes.</p> \
  <h2>5. &Auml;nderungsvorbehalt und h&ouml;here Gewalt.</h2> \
  <p>Der Anbieter beh&auml;lt sich vor, jederzeit &Auml;nderungen an dem Kommunikationsdienst selbst, der Art und Weise seiner Bereitstellung und Durchf&uuml;hrung, sowie an der Art der Benutzerf&uuml;hrung etc. vorzunehmen. Die Verf&uuml;gbarkeit des Kommunikationsdienstes kann aus Gr&uuml;nden der Systemwartung eingeschr&auml;nkt sein. Bei h&ouml;herer Gewalt wie etwa nicht beeinflussbaren technischen Problemen entf&auml;llt die Bereitstellung und Erbringung des Kommunikationsdienstes.</p> \
  <h2>6. Gew&auml;hrleistung und Haftung.</h2> \
  <p>Der Anbieter haftet bei der Bereitstellung und Erbringung der Leistungen des Kommunikationsdienstes nur f&uuml;r Vorsatz und grobe Fahrl&auml;ssigkeit. F&uuml;r Rechts- und Sachm&auml;ngel haftet der Anbieter nur bei arglistigem Verschweigen. Au&szlig;erdem ist die Haftung der H&ouml;he nach auf den Zeitwert der Heizung beschr&auml;nkt. Die Produkthaftung f&uuml;r das Heizungsger&auml;t selbst bleibt hiervon unber&uuml;hrt.</p> \
  <h2>7. Technische Voraussetzungen f&uuml;r die Nutzung des Kommunikationsdienstes.</h2> \
  <h3>Allgemein:</h3> \
  <ul> \
    <li>WOLF Heizungs-/L&uuml;ftungs-/Klimasystem mit Schnittstellenmodul ISM7i/ISM7e/Link home/Link pro, WLAN/LAN-Router</li> \
  </ul> \
  <h3>F&uuml;r die Internetverbindung zus&auml;tzlich:</h3> \
  <ul> \
    <li>Eine zuverl&auml;ssige Internetverbindung (Empfehlung: mindestens 1 Mbit/s Up- und Downloadgeschwindigkeit, 500 MB Datenvolumen/Monat)</li> \
    <li>ISM7 FW1.xx: Port 443 und Port 45165 darf nicht gesperrt sein</li> \
    <li>ISM7 FW2.xx / WOLF Link: Port 443 und Port 56154 darf nicht gesperrt sein</li> \
  </ul> \
  <h3>Systemvoraussetzungen f&uuml;r Nutzung der Smartset Website:</h3> \
  <ul> \
    <li>Einen aktuellen Internetbrowser (z.B. Google Chrome oder Mozilla Firefox) mit aktiviertem Javascript, Cookies, localStorage und sessionStorage</li> \
  </ul> \
  <h3>Systemvoraussetzungen f&uuml;r Nutzung der Smartphone App:</h3> \
  <ul> \
    <li>iOS: iPhone oder iPad mit Betriebssystem iOS 12.5.6 oder h&ouml;her</li> \
    <li>Android: Smartphone oder Tablet mit Betriebssystem Android 4.2 oder h&ouml;her</li> \
  </ul> \
  <h3>Systemvoraussetzungen f&uuml;r Nutzung der Windows Anwendung</h3> \
  <ul> \
    <li>In Verbindung mit ISM7 FW1.xx: PC mit Betriebssystem Windows 7</li> \
    <li>In Verbindung mit ISM7 FW2.xx / WOLF Link: PC mit Betriebssystem Windows 7 oder Windows 10</li> \
  </ul> \
  <h2>8. Schlussbestimmungen.</h2> \
  <p>Die etwaige Unwirksamkeit einzelner Bestimmungen dieser Lizenz- und Nutzungsvereinbarung beeintr&auml;chtigt nicht die G&uuml;ltigkeit des &uuml;brigen Vertragsinhaltes. Ergeben sich in der praktischen Anwendung dieses Vertrages nicht vorhergesehene L&uuml;cken oder wird die Unwirksamkeit einer Regelung rechtskr&auml;ftig oder von beiden Parteien &uuml;bereinstimmend festgestellt, so verpflichten sich diese, diese L&uuml;cke oder unwirksame Regelung in sachlicher, am Zweck des Vertrages orientierter angemessener Weise auszuf&uuml;llen bzw. zu ersetzen. Auf das Vertragsverh&auml;ltnis findet deutsches materielles Recht unter Ausschluss des UN-Kaufrechts Anwendung. Ausschlie&szlig;licher Gerichtsstand ist, sofern nicht eine Norm zwingend einen anderen Gerichtsstand anordnet M&uuml;nchen.</p>

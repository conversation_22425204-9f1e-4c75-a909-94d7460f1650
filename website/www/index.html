<!DOCTYPE html>

<html lang="en">

  <head>
    <title>WOLF Link</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="{{meta-description}}">
    <link rel="stylesheet" href="/style.css">
    <style>
      td { vertical-align: top; }
    </style>
  </head>

  <body>
    <nav>
      <img src="/wolf.svg" alt="WOLF Link" />
      <ul>
        <li><a href="/index.htm" class="active">{{overview}}</a></li>
        <li><a href="/protect/network.htm">{{network}}</a></li>
        <li><a href="/protect/settings.htm">{{settings}}</a></li>
      </ul>
    </nav>

    <main>
      <b>{{general-info}}</b>
      <table>
        <tr><td>- {{serial-number}}: &nbsp;</td><td><i id="status-serial"></i></td></tr>
        <tr><td>- {{firmware-version}}: &nbsp;</td><td><i id="status-firmware-version"></i></td></tr>
        <tr><td>- {{system-name}}: &nbsp;</td><td><i id="status-sysname"></i></td></tr>
      </table>
      <br>

      <b>{{network}}</b>
      <table>
        <tr><td>- {{eth-status}}: &nbsp;</td><td><i id="status-ethernet"></i></td></tr>
        <tr><td>- {{eth-mac}}: &nbsp;</td><td><i id="status-ethernet-mac"></i></td></tr>
        <tr><td>- {{wifi-status}}: &nbsp;</td><td><i id="status-wifi"></i></td></tr>
        <tr><td>- {{wifi-mac}}: &nbsp;</td><td><i id="status-wifi-mac"></i></td></tr>
        <tr><td>- {{wifi-ssid}}: &nbsp;</td><td><i id="status-wifi-ssid"></i></td></tr>
        <tr><td>- {{ip-addr}}: &nbsp;</td><td><i id="status-ip"></i></td></tr>
        <tr><td>- {{ap}}: &nbsp;</td><td><i id="status-ap"></i></td></tr>
        <tr id="status-ap-ssid-box"><td>- {{ap-ssid}}: &nbsp;</td><td><i id="status-ap-ssid"></i></td></tr>
        <tr><td>- {{portal-status}}: &nbsp;</td><td><i id="status-portal"></i></td></tr>
        <tr><td>- {{local-conn-status}}: &nbsp;</td><td><i id="status-direct-link"></i></td></tr>
      </table>
      <br>

      <b>{{bus}}</b>
      <table>
        <tr><td>- {{ebus}}: &nbsp;</td><td><i id="status-ebus"></i></td></tr>
        <tr><td>- {{modbus}}: &nbsp;</td><td><i id="status-modbus"></i></td></tr>
      </table>
    </main>

    <div id="lang-switcher">
      {{language}}:
      <a href="/lang.htm?lang=en&redir=/index.htm" {{lang-active-en}}>en</a> -
      <a href="/lang.htm?lang=de&redir=/index.htm" {{lang-active-de}}>de</a>
    </div>
  </body>

  <script>
    'use strict';

    const PORTAL_STATE = [
      /* 0 */  '{{log-on}}',
      /* 1 */  '{{delayed-log-on}}',
      /* 2 */  '{{delayed-log-on-2}}',
      /* 3 */  '{{try-connect-log-on}}',
      /* 4 */  '{{ssl-connecting}}',
      /* 5 */  '{{portal-response}}',
      /* 6 */  '{{read-sysconfig}}',
      /* 7 */  '{{connected-locally}}',
      /* 8 */  '{{disconnecting}}',
      /* 9 */  '{{not-connected}}',
      /* 10 */ '{{init}}',
      /* 11 */ '{{init-connection}}',
      /* 12 */ '{{connected}}',
    ];

    const LOCAL_CONNECTION_STATE = [
      /* 0 */  '{{log-on}}',
      /* 1 */  '{{delayed-log-on}}',
      /* 2 */  '{{delayed-log-on-2}}',
      /* 3 */  '{{try-connect-log-on}}',
      /* 4 */  '{{ssl-connecting}}',
      /* 5 */  '{{portal-response}}',
      /* 6 */  '{{read-sysconfig}}',
      /* 7 */  '{{connected-locally}}',
      /* 8 */  '{{disconnecting}}',
      /* 9 */  '{{not-connected}}',
      /* 10 */ '{{debug-server-online}}',
      /* 11 */ '{{init}}',
    ];

    const BUS_STATE = [
      /* 0 */ '{{connected}}',
      /* 1 */ '{{not-connected}}',
    ];

    function fetchStatus() {
      const xhttp = new XMLHttpRequest();
      xhttp.onreadystatechange = function () {
        if (this.readyState === 4 && this.status === 200) {
          updateStatus(this.responseXML);
        }
        if (this.readyState === 4) {
          setTimeout(fetchStatus, 1000);
        }
      };
      xhttp.open('GET', '/status.xml', true);
      xhttp.send();
    }

    function updateStatus(xml) {
      updateField(xml, 'info > serial', 'status-serial');
      updateField(xml, 'info > fwver', 'status-firmware-version');
      updateField(xml, 'info > serial', 'status-ethernet-mac', fancyMacAddress);
      updateField(xml, 'info > macwlan', 'status-wifi-mac', fancyMacAddress);
      updateField(xml, 'wlan > activessid', 'status-wifi-ssid');
      updateField(xml, 'network > activeip', 'status-ip');
      updateField(xml, 'portal > activesysname', 'status-sysname');
      updateField(xml, 'info > stateebus', 'status-ebus', function (value) { return BUS_STATE[value]; });
      updateField(xml, 'info > statemodbus', 'status-modbus', function (value) { return BUS_STATE[value]; });
      updateField(xml, 'info > statedirectlink', 'status-direct-link', function (value) { return LOCAL_CONNECTION_STATE[value]; });

      const modeElem = xml.querySelector('info > mode').childNodes[0];
      const mode = modeElem ? modeElem.nodeValue : null;
      document.getElementById('status-ethernet').textContent = mode === 'lan' ? '{{connected}}' : '{{not-connected}}';
      document.getElementById('status-wifi').textContent = mode === 'wlan' ? '{{connected}}' : '{{not-connected}}';

      const ipElem = xml.querySelector('network > activeip').childNodes[0];
      const ip = ipElem ? ipElem.nodeValue : null;
      const dhcpElem = xml.querySelector('network > activedhcp').childNodes[0];
      const dhcp = dhcpElem ? dhcpElem.nodeValue : null;
      const ipDhcpStr = (ip || '-') + (dhcp ? ' (DHCP)' : ' (static)');
      document.getElementById('status-ip').textContent = ipDhcpStr;

      const apmodeElem = xml.querySelector('info > apmode').childNodes[0];
      const apmode = apmodeElem ? apmodeElem.nodeValue : null;
      const activeapElem = xml.querySelector('info > activeap').childNodes[0];
      const activeap = activeapElem ? activeapElem.nodeValue : null;
      document.getElementById('status-ap').textContent = activeap === 'checked' ? '{{active}}' : apmode === 'checked' ? '{{enabled}}' : '{{disabled}}';
      if (activeap === 'checked') {
        document.getElementById('status-ap-ssid-box').style.display = 'table-row';
        updateField(xml, 'info > activeapssid', 'status-ap-ssid');
      } else {
        document.getElementById('status-ap-ssid-box').style.display = 'none';
      }
      const acon = xml.querySelector('info > acon').childNodes[0];
      const activeacon = xml.querySelector('info > activeacon').childNodes[0];
      if (acon && acon.nodeValue === 'checked' && activeacon && activeacon.nodeValue === 'checked') {
        updateField(xml, 'info > stateportal', 'status-portal', function (value) { return PORTAL_STATE[value]; });
      } else if (acon && acon.nodeValue === 'checked') {
        document.getElementById('status-portal').innerHTML = '<span style="color: red;">{{portal-reboot-required}}</span>';
      } else {
        document.getElementById('status-portal').innerHTML = '<span style="color: red;">{{portal-not-activated}}</span>';
      }
    }

    function updateField(xml, xmlPath, id, transform) {
      const children = xml.querySelector(xmlPath).childNodes;
      let value = children.length > 0 ? children[0].nodeValue : null;
      if (!value) value = '-';
      if (transform) value = transform(value);
      document.getElementById(id).textContent = value;
    }

    function fancyMacAddress(mac) {
      if (mac.length != 12) return mac;
      let buf = '';
      for (let i = 0; i < 6; i++) {
        buf += mac[i * 2];
        buf += mac[i * 2 + 1];
        if (i != 5) buf += ':';
      }
      return buf;
    }

    fetchStatus();
  </script>

</html>

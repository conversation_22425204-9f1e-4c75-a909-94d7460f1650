html, body, button, input, select {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Cantarell', 'Roboto', 'Ubuntu', 'Helvetica', 'Arial', sans-serif;
  font-size: 1em;
}

html, body {
  margin: 0;
  background-color: #eee;
  min-height: 100vh;
}

nav {
  background-color: #e10000;
  padding: 1em;
}

nav img {
  height: 2em;
}

nav ul {
  display: inline-block;
  list-style: none;
  margin: 0;
  padding: 0 0 0 2em;
}

nav li {
  display: inline-block;
}

nav a {
  color: white;
  text-decoration: none;
  font-weight: bold;
  margin-right: 1em;
  vertical-align: 0.6em;
}

nav a.active {
  border-bottom: solid 2px white;
  padding-bottom: 0.25em;
}

nav a:hover {
  color: #fdd;
  border-bottom-color: #fdd;
}

main {
  background-color: white;
  margin: 1em auto;
  max-width: 40em;
  padding: 1.5em;
}

input {
  border-radius: 0;
  border: solid 2px #bbb;
  padding: 0.2em 0.4em;
}

select {
  border-radius: 0;
  border: solid 2px #bbb;
  padding: 0.2em 0.4em;
  background-color: #eee;
}

button, input[type=submit] {
  border-radius: 0;
  border: solid 2px #bbb;
  padding: 0.2em 0.4em;
  background-color: #eee;
  cursor: pointer;
}

button:hover, input[type=submit]:hover {
  border-color: #e10000;
}

form {
  padding: 1em;
}

tr > td:nth-child(2n+1) {
  white-space: nowrap;
}

.submit {
  margin-top: .5em;
}

#dhcp-checkbox, #connection-checkbox, #ap-checkbox {
  margin-bottom: .5rem;
}

#advanced-settings {
  background: none;
  border: none;
  text-decoration: underline;
  color: #888;
}

#lang-switcher {
  color: #444;
  text-align: center;
  margin-bottom: 1rem;
}

#lang-switcher a {
  color: #444;
}

#lang-switcher a.active {
  color: #e10000;
}

#wifi-scan-div {
  margin-top: 1rem;
  padding: 1rem 1rem 0 1rem;
}

#wifi-scan {
  list-style: none;
  margin-left: -1rem;
}

#wifi-scan img {
  font-size: 0.7rem;
}

#wifi-scan button {
  border: none;
  padding: 0;
  background-color: transparent;
  text-decoration: underline;
}

@media screen and (max-width: 600px) {
  nav img {
    display: block;
    margin: 0 auto 1em auto;
  }

  nav ul {
    display: block;
    padding: 0;
    text-align: center;
  }

  main {
    margin: 0 auto;
  }

  html, body {
    background-color: white;
  }
}

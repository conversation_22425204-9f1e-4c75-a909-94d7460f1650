<!DOCTYPE html>

<html lang="en">

  <head>
    <title>WOLF Link</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="{{meta-description}}">
    <link rel="stylesheet" href="/style.css">
  </head>

  <body>
    <nav>
      <img src="/wolf.svg" alt="WOLF Link" />
      <ul>
        <li><a href="/index.htm">{{overview}}</a></li>
        <li><a href="/protect/network.htm" class="active">{{network}}</a></li>
        <li><a href="/protect/settings.htm">{{settings}}</a></li>
      </ul>
    </nav>

    <main>
      <div>
        <b>{{wifi-settings}}</b>

        <div id="wifi-scan-div">
          <b>{{available-wifi-networks}}</b>
          <p id="wifi-scan-msg">{{scanning-for-networks}}</p>
          <ul id="wifi-scan"></ul>
        </div>

        <form method="post" action="/protect/network.htm" id="wifi-config-form">
          <p><b>{{custom-wifi-network}}</b></p>
          <input type="hidden" name="name" value="wlanKickoff">
          <table>
            <tr><td>{{ssid}}: &nbsp;</td><td><input name="ssid" type="text" id="wifi-ssid"></td></tr>
            <tr><td>{{password}}: &nbsp;</td><td><input name="key" type="password" id="wifi-pass"></td></tr>
            <tr><td>{{wifi-auth}}: &nbsp;</td><td><select name="sec" id="wifi-auth">
              <option value="0">{{wifi-auth-none}}</option>
              <option value="1">WEP</option>
              <option value="2">WPA</option>
              <option value="3" selected="selected">WPA2 Personal</option>
              <option value="6">WPA3 Personal</option>
              <option value="4">WPA/WPA2 Personal</option>
              <option value="7">WPA2/WPA3 Personal</option>
              <!--<option value="5">WPA2 Enterprise</option>-->
              <!--<option value="8">WAPI Personal</option>-->
            </select></td></tr>
          </table>
          <button type="button" onclick="submitWifiConfig()" class="submit">{{connect}}</button>
          <div id="ap-sta-interference-notice" style="display: none; padding-top: 0.5rem; color: red;">{{ap-sta-interference-notice}}</div>
        </form>
      </div>

      <br>

      <div>
        <b>{{network-settings}}</b>
        <form method="post" action="/protect/network.htm" id="network-form">
          <input type="hidden" name="name" value="lanKickoff">
          <input type="hidden" name="dhcp" id="real-dhcp-field">
          <label>
            <input id="dhcp-checkbox" type="checkbox" onchange="toggleStaticIpConfig()" checked="checked">
            {{dhcp}}
          </label>
          <br>
          <table id="static-ip-config" style="display: none;">
            <tr><td>{{ip-addr}}: &nbsp;</td><td><input name="ip" type="text" id="config-ip"></td></tr>
            <tr><td>{{subnet-mask}}: &nbsp;</td><td><input name="sub" type="text" id="config-subnet"></td></tr>
            <tr><td>{{standard-gateway}}: &nbsp;</td><td><input name="gw" type="text" id="config-gateway"></td></tr>
            <tr><td>{{dns-1}}: &nbsp;</td><td><input name="dns1" type="text" id="config-dns-1"></td></tr>
            <tr><td>{{dns-2}}: &nbsp;</td><td><input name="dns2" type="text" id="config-dns-2"></td></tr>
          </table>
          <button type="button" onclick="toggleTargetServerConfig()" id="advanced-settings">{{show-advanced-settings}}</button>
          <br>
          <div id="target-server-config" style="display: none;">
            <span style="color: red;">{{advanced-settings-notice}}</span>
            <table>
              <tr><td>{{hostname}}: &nbsp;</td><td><input name="host" type="text" id="config-hostname"></td></tr>
              <tr><td>{{portal-host}}: &nbsp;</td><td><input name="wpip" type="text" id="config-portal-host"></td></tr>
              <tr><td>{{portal-port}}: &nbsp;</td><td><input name="port" type="text" id="config-portal-port"></td></tr>
            </table>
          </div>
          <button type="button" onclick="submitNetworkConfig()" class="submit">{{save-and-reboot}}</button>
        </form>
      </div>

      <br>

      <div>
        <b>{{ap-header}}</b>
        <form method="post" action="/protect/network.htm" id="ap-form">
          <input type="hidden" name="name" value="apKickoff">
          <input type="hidden" name="ap" id="real-ap-field">
          <label>
            <input id="ap-checkbox" type="checkbox" onchange="toggleAccessPointConfig()">
            {{ap-enabled}}
          </label>
          <br>
          <table id="ap-config" style="display: none;">
            <tr><td>{{ssid}}: &nbsp;</td><td><input id="config-ap-ssid" name="apssid" type="text"></td></tr>
          </table>
          <button type="button" onclick="submitApConfig()" class="submit">{{save}}</button>
        </form>
      </div>

      <br>

      <div>
        <b>{{wps}}</b>
        <form method="post" action="/protect/network.htm">
          <input type="hidden" name="name" value="wpsKickoff">
          <input type="submit" value="{{start-wps}}">
        </form>
      </div>
    </main>

    <div id="lang-switcher">
      {{language}}:
      <a href="/lang.htm?lang=en&redir=/protect/network.htm" {{lang-active-en}}>en</a> -
      <a href="/lang.htm?lang=de&redir=/protect/network.htm" {{lang-active-de}}>de</a>
    </div>
  </body>

  <script>
    'use strict';

    const isPrintableAsciiOnly = /^[\x20-\x7E]*$/;
    const isIpv4Address = /^(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)){3}$/;
    const isValidHostname = /^[a-zA-Z0-9\-]{1,30}$/;
    const isValidWepPassword = /^[0-9A-Fa-f]{10}(?:[0-9A-Fa-f]{16})?$/;
    const validSubnets = [ '***************', '***************', '***************', '***************', '***************', '***************', '***************', '***************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '*************', '***********', '***********', '***********', '***********', '***********', '***********', '***********', '***********', '*********', '*********', '*********', '*********', '240.0.0.0', '*********', '*********', '*********', '0.0.0.0' ]

    const AUTH_METHODS = [
      '{{wifi-auth-none}}',
      'WEP',
      'WPA',
      'WPA2 Personal',
      'WPA/WPA2 Personal',
      'WPA2 Enterprise',
      'WPA3 Personal',
      'WPA2/WPA3 Personal',
      'WAPI Personal',
    ];

    const UNSUPPORTED_AUTH_METHODS = [5, 8];

    function toggleStaticIpConfig() {
      const checked = document.getElementById('dhcp-checkbox').checked;
      const form = document.getElementById('static-ip-config');
      form.style.display = checked ? 'none' : 'block';
    }

    function toggleTargetServerConfig() {
      const ele = document.getElementById('target-server-config');
      ele.style.display = ele.style.display == 'none' ? 'block' : 'none';
    }

    function toggleAccessPointConfig() {
      const checked = document.getElementById('ap-checkbox').checked;
      const form = document.getElementById('ap-config');
      form.style.display = checked ? 'block' : 'none';
    }

    function getTrimmedValue(id) {
      const elem = document.getElementById(id);
      elem.value = elem.value.trim();
      return elem.value;
    }

    function submitWifiConfig() {
      const pass = getTrimmedValue('wifi-pass');
      const auth = getTrimmedValue('wifi-auth');

      if ((auth === "1" && !isValidWepPassword.test(pass)) || (auth !== "0" && (pass.length < 8 || pass.length > 63 || !isPrintableAsciiOnly.test(pass)))) {
        return alert('{{invalid-password}}');
      }

      const ssid = getTrimmedValue('wifi-ssid');
      if (ssid.length === 0 || ssid.length > 32 || !isPrintableAsciiOnly.test(ssid)) {
        return alert('{{invalid-ssid}}');
      }

      document.getElementById('wifi-config-form').submit();
    }

    function submitNetworkConfig() {
      if (!isIpv4Address.test(getTrimmedValue('config-ip'))) {
        return alert('{{invalid-ip}}');
      }
      if (!isIpv4Address.test(getTrimmedValue('config-gateway'))) {
        return alert('{{invalid-gateway}}');
      }
      if (!isIpv4Address.test(getTrimmedValue('config-dns-1'))) {
        return alert('{{invalid-dns-1}}');
      }
      if (!isIpv4Address.test(getTrimmedValue('config-dns-2'))) {
        return alert('{{invalid-dns-2}}');
      }
      if (validSubnets.indexOf(getTrimmedValue('config-subnet')) < 0) {
        return alert('{{invalid-subnet}}');
      }
      if (getTrimmedValue('config-portal-host').length === 0) {
        return alert('{{invalid-portal-host}}');
      }
      if (!isValidHostname.test(getTrimmedValue('config-hostname'))) {
        return alert('{{invalid-hostname}}');
      }
      const port = parseInt(getTrimmedValue('config-portal-port'))
      if (!port || port < 0 || port > 65535) {
        return alert('{{invalid-portal-port}}');
      }

      const dhcp = document.getElementById('dhcp-checkbox').checked;
      document.getElementById('real-dhcp-field').value = dhcp ? 'checked' : '';

      document.getElementById('network-form').submit();
    }

    function submitApConfig() {
      const ssid = getTrimmedValue('config-ap-ssid');
      if (ssid.length === 0 || ssid.length > 32 || !isPrintableAsciiOnly.test(ssid)) {
        return alert('{{invalid-ap-ssid}}');
      }

      const ap = document.getElementById('ap-checkbox').checked;
      document.getElementById('real-ap-field').value = ap ? 'checked' : '';

      document.getElementById('ap-form').submit();
    }

    function fetchStatus() {
      const xhttp = new XMLHttpRequest();
      xhttp.onreadystatechange = function () {
        if (this.readyState === 4 && this.status === 200) {
          updateStatus(this.responseXML);
        }
      };
      xhttp.open('GET', '/protect/status.xml', true);
      xhttp.send();
    }

    function updateStatus(xml) {
      updateField(xml, 'network > ip', 'config-ip');
      updateField(xml, 'network > sub', 'config-subnet');
      updateField(xml, 'network > gw', 'config-gateway');
      updateField(xml, 'network > dns1', 'config-dns-1');
      updateField(xml, 'network > dns2', 'config-dns-2');
      updateField(xml, 'network > host', 'config-hostname');
      updateField(xml, 'portal > wpip', 'config-portal-host');
      updateField(xml, 'portal > port', 'config-portal-port');
      updateField(xml, 'info > apssid', 'config-ap-ssid');
      updateField(xml, 'wlan > ssid', 'wifi-ssid');
      let dhcp = xml.querySelector('network > dhcp').childNodes[0];
      dhcp = (dhcp ? dhcp.nodeValue : '') === 'checked';
      document.getElementById('dhcp-checkbox').checked = dhcp;
      document.getElementById('static-ip-config').style.display = dhcp ? 'none' : 'block';
      let apmode = xml.querySelector('info > apmode').childNodes[0];
      apmode = (apmode ? apmode.nodeValue : '') === 'checked';
      document.getElementById('ap-checkbox').checked = apmode;
      document.getElementById('ap-config').style.display = apmode ? 'block' : 'none';
      let apConnected = xml.querySelector('info > apconnected').childNodes[0];
      apConnected = (apConnected ? apConnected.nodeValue : '') === 'checked';
      if (apConnected) document.getElementById('ap-sta-interference-notice').style.display = 'block';
    }

    function updateField(xml, xmlPath, id) {
      const children = xml.querySelector(xmlPath).childNodes;
      let value = children.length > 0 ? children[0].nodeValue : null;
      if (!value) value = '';
      document.getElementById(id).value = value;
    }

    function wifiScan() {
      const xhttp = new XMLHttpRequest();
      xhttp.onreadystatechange = function () {
        if (this.readyState === 4 && this.status === 200) {
          updateWifiScan(this.responseXML);
        }
      };
      xhttp.open('GET', '/protect/wifi-scan.xml', true);
      xhttp.send();
    }

    function updateWifiScan(xml) {
      const wifiScan = document.getElementById('wifi-scan');
      const wifiScanMsg = document.getElementById('wifi-scan-msg');
      const networks = xml.querySelector('wifi-scan').childNodes;
      if (!networks || networks.length === 0) {
        wifiScanMsg.textContent = '{{wifi-scan-failed}}';
        return;
      }
      wifiScanMsg.style.display = 'none';
      const coveredSsids = [];

      for (let i = 0; i < networks.length; i += 1) {
        const network = networks[i];
        const ssid = network.querySelector('ssid').childNodes[0].nodeValue;
        if (coveredSsids.indexOf(ssid) >= 0) continue;
        coveredSsids.push(ssid);
        let rssi = network.querySelector('rssi').childNodes[0].nodeValue;
        let authMethod = network.querySelector('auth-method').childNodes[0];
        authMethod = (authMethod && authMethod.nodeValue) ? Number(authMethod.nodeValue) : null;

        const rssiElem = document.createElement('img');
        rssiElem.title = rssi + ' dBm';
        rssi = Number(rssi);
        if (rssi <= -90) {
          rssiElem.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAAPCAMAAAAxmgQeAAAAPFBMVEX///++vr77+/vR0dH5+fn19fXv7+/Jycnp6enV1dXMzMzg4ODy8vLl5eXk5OTd3d3b29vGxsbCwsK/v7+s3UtZAAAAgElEQVQY012NSQ4DIQwE3W6DWWaf//81kFFIQvnkkkotb7QhE0tl8ZJrGCYWANd5ASjxUQfAZK21RODoKsOjqoUQrCWO3Mussjo6nkTzUysB1rXyBj/zAQzan8B7LJvaxsJNxWSgDpyAyw+adhPbk/xJiYwyKSEok9SFy6TafXkB/1kC2viMnQkAAAAASUVORK5CYII=';
          rssiElem.alt = '{{rssi-0}}';
        } else if (rssi <= -70) {
          rssiElem.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAAPCAMAAAAxmgQeAAAARVBMVEX////7+/u9vb2+vr7R0dH5+fnJycnv7+/p6enV1dXMzMz09PTg4ODb29v29vbGxsbDw8P19fXl5eXk5OTd3d2vr69gYGBYrOIcAAAAg0lEQVQY02XORw6DQBBE0erIRLJ9/6N6AIGNecsvlbqxowZ/ctHkKZZ8lS4x8ziMLJK6I1VhDUaABRWpaKJ4R2RT7q1NXOK2jITZeeMBFI81KbOWubSlnud70bz/Mqn052EjWzTpQjBcyFkGYccPCtVga8AtUn2tuCfQ+B7wiOGZ8PUBTbwDb6GwpP0AAAAASUVORK5CYII=';
          rssiElem.alt = '{{rssi-1}}';
        } else if (rssi <= -50) {
          rssiElem.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAAPCAMAAAAxmgQeAAAAYFBMVEX8/Pz////5+fm8vLz29vbz8/PR0dHGxsa+vr5fX1/s7Ozo6Ojg4ODU1NTMzMzKysrCwsK6urrv7+/c3NzOzs7k5OTX19e1tbV7e3tvb2+/v7+urq6kpKSjo6OCgoKAgICoOPddAAAAm0lEQVQY0z3O2Q7EIAgFUFCsddfunfX//3LQ1rkPJJxAAHoQkUvLBcqHmGLwCrrZJKXMOnON02VbkYsTgCDcMI5ztVC0BRCkiN2mcWOzA8GqZU1yCGHiBZ5ZSln86nlzaPeZJhlVe4eGB1UT3BDQ/j0/O6HAZlXpMOY05gC8rKF+K1AvjX9rmZ8zcNutTWaTEfou3HFN+o3OeOMPgHwFZILNErcAAAAASUVORK5CYII=';
          rssiElem.alt = '{{rssi-2}}';
        } else if (rssi <= -40) {
          rssiElem.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAAPCAMAAAAxmgQeAAAAZlBMVEX9/f3////7+/u9vb339/deXl7p6elhYWHu7u7GxsbOzs7IyMjCwsK1tbWNjY3g4ODd3d3W1tbU1NTS0tLKysq+vr7y8vKlpaWDg4N8fHx7e3vh4eHZ2dmvr6+Pj4+Ojo5ycnJwcHBTcZC7AAAAm0lEQVQY02WPRxIDMQgExwKlVdgcnf//SUv2Shd3waULKAYigVQnlwSQzdIOk5vGdvm6rMxERN56orkxv7kbUaMYAKuRqENyA1kDqMY3CjBu7pMzI8MdMrM7iMHkXcFR6meYw0PLyPleotdRIaOi7k+HVnDYXltgcS3/pT6kfmu5i/Jfxq4MXm3JcQbr7l3NVvDaVlfTK/w7VPcB7gAFtsgPCMsAAAAASUVORK5CYII=';
          rssiElem.alt = '{{rssi-3}}';
        } else {
          rssiElem.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAAPCAMAAAAxmgQeAAAAeFBMVEX5+fn////7+/tdXV39/f339/fx8fHZ2dmNjY2Kiorm5ubT09Ozs7OBgYFgYGDc3NzExMS/v795eXlxcXHz8/PNzc3KysrHx8esrKykpKSUlJR7e3t1dXVtbW1paWliYmLq6urh4eHg4OCurq6oqKiioqKYmJiPj48vG/9YAAAAqElEQVQY00WPiQ7EEBBAdQalRe/72nv//w93kG1fBHl5ycAAGDCCDuA88dCNVDHqdWv08x4dydwi4t7stNs0dGJC1IOkXvYK8QXUzbjmjGVd1WWcpzV+yOVfycoaPUufiDlNGCE1OmUqow5UIsyg0umMAXFTR5jhKURrbGONgEcSnaBVo9scLhDfJ4Qvy7eEdhr46QLpWPD/30gFW2EJl4tABmd3ydP9AOwLB0TOmjTsAAAAAElFTkSuQmCC';
          rssiElem.alt = '{{rssi-4}}';
        }

        const authElem = document.createElement('img');
        authElem.title = AUTH_METHODS[authMethod];
        authElem.src = authMethod === 0 ? 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQBAMAAADt3eJSAAAAHlBMVEX///+urq54eHjj4+Pe3t7b29vFxcXAwMC8vLy7u7vtNod4AAAAVElEQVQI12PABZhABHOjhIGSApBhmBompAQSmcbAABZgEGVgAgswiDAoKUIYTEqOIJpdUElRsADIYBRSEhQUADGAAmAGE1AAzAAKABlQAbBiBYi1AAQrB7DpuvOVAAAAAElFTkSuQmCC' : 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQBAMAAADt3eJSAAAAIVBMVEX///9dXV39/f3Gxsa8vLy2traBgYF3d3eLi4tgYGCKioodEfwXAAAAOUlEQVQI12PAC5QrpxuBaCbD1ghhBRCjSkmpHcwQVVIKBDNElJQcURhsgkCQAGQwghgCeBgIxXAAAKz5CPpNOZy7AAAAAElFTkSuQmCC';
        authElem.alt = authMethod === 0 ? '{{no-auth-required}}' : '{{auth-required}}';

        const ssidElem = document.createElement('button');
        ssidElem.textContent = ssid;
        if (UNSUPPORTED_AUTH_METHODS.includes(authMethod)) {
          ssidElem.style.textDecoration = "line-through";
          ssidElem.style.color = "#888";
        } else {
          ssidElem.onclick = function () {
            const ssidInputElem = document.getElementById('wifi-ssid');
            ssidInputElem.value = ssid;
            document.getElementById('wifi-auth').value = '' + authMethod;
            if (authMethod == 0) {
              submitWifiConfig();
            } else {
              document.getElementById('wifi-pass').focus();
            }
            ssidInputElem.scrollIntoView();
            setTimeout(function () { ssidInputElem.scrollIntoView(); }, 500); // soft keyboard on mobile phone shenanigans
          };
        }

        const elem = document.createElement('li');
        elem.appendChild(rssiElem);
        elem.appendChild(document.createTextNode(' '));
        elem.appendChild(authElem);
        elem.appendChild(document.createTextNode(' '));
        elem.appendChild(ssidElem);

        wifiScan.appendChild(elem);
      }
    }

    fetchStatus();
    setTimeout(wifiScan, 1000);

    for (const elem of document.getElementsByTagName('form')) {
      elem.addEventListener('keypress', function (e) {
        if ((e.keyCode || e.charCode) === 13) {
          e.preventDefault();
        }
      });
    }
  </script>

</html>

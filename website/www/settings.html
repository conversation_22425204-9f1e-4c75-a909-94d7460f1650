<!DOCTYPE html>

<html lang="en">

  <head>
    <title>WOLF Link</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="{{meta-description}}">
    <link rel="stylesheet" href="/style.css">
  </head>

  <body>
    <nav>
      <img src="/wolf.svg" alt="WOLF Link" />
      <ul>
        <li><a href="/index.htm">{{overview}}</a></li>
        <li><a href="/protect/network.htm">{{network}}</a></li>
        <li><a href="/protect/settings.htm" class="active">{{settings}}</a></li>
      </ul>
    </nav>

    <main>
      <div>
        <b>{{portal-connection}}</b>
        <form method="post" action="/protect/settings.htm" id="portal-enabled-form">
          <div style="display: none; margin: 0.5rem; color: red;" id="no-reboot-after-portal">
            {{portal-enabled-not-active}}
          </div>
          <input type="hidden" name="name" value="setKickoff">
          <input type="hidden" name="acon" value="" id="real-connection-input">
          <label>
            <input type="checkbox" id="connection-checkbox">
            {{enable-portal-connection}}
          </label>
          <br>
          {{agree-tos-privacy}}
          <br>
          <button type="button" onclick="submitPortalEnabled()" class="submit">{{save-settings}}</button>
        </form>
      </div>

      <br>

      <div>
        <b>{{ota}}</b>
        <form method="post" action="/protect/settings.htm" id="ota-enabled-form">
          <input type="hidden" name="name" value="setKickoff">
          <input type="hidden" name="ota" value="" id="real-ota-input">
          <label>
            <input type="checkbox" id="ota-checkbox">
            {{enable-ota}}
          </label>
          <br>
          <button type="button" onclick="submitOtaEnabled()" class="submit">{{save-settings}}</button>
        </form>
      </div>

      <br>

      <div>
        <b>{{password}}</b>
        <form method="post" action="/protect/settings.htm" id="pass-form">
          <input type="hidden" name="name" value="setKickoff">
          <table>
            <tr><td>{{enter-new-password}}: &nbsp;</td><td><input id="pass" name="pass" type="password"></td></tr>
            <tr><td>{{repeat-password}}: &nbsp;</td><td><input id="pass-repeat" type="password"></td></tr>
          </table>
          <button type="button" onclick="checkPassword()" class="submit">{{save}}</button>
        </form>
      </div>

      <br>

      <div>
        <b>{{system-name}}</b>
        <form method="post" action="/protect/settings.htm" id="sysname-form">
          <input type="hidden" name="name" value="sysnameKickoff">
          <table>
            <tr><td>{{system-name}}: &nbsp;</td><td><input name="sysname" type="text" id="system-name-input"></td></tr>
          </table>
          <button type="button" onclick="submitSystemName()" class="submit">{{save}}</button>
        </form>
      </div>

      <br>

      <div>
        <b>{{reboot-title}}</b>
        <form method="post" action="/protect/reboot.htm">
          <input type="hidden" name="name" value="reboKickoff">
          <input type="hidden" name="rebo" value="init">
          <input type="submit" value="{{reboot}}">
        </form>
      </div>
    </main>

    <div id="lang-switcher">
      {{language}}:
      <a href="/lang.htm?lang=en&redir=/protect/settings.htm" {{lang-active-en}}>en</a> -
      <a href="/lang.htm?lang=de&redir=/protect/settings.htm" {{lang-active-de}}>de</a>
    </div>
  </body>

  <script>
    'use strict';

    function checkPassword() {
      const pass = document.getElementById('pass').value;
      const passRepeat = document.getElementById('pass-repeat').value;

      if (pass !== passRepeat)
        return alert('{{passwords-no-match}}');
      if (pass.length < 8 || pass.length > 63)
        return alert('{{password-length}}');

      let lower = false, upper = false, special = false;
      for (let i = 0; i < pass.length; i++) {
        const char = pass.charCodeAt(i);
        if (65 <= char && char <= 90) upper = true; // 'A'..='Z'
        else if (97 <= char && char <= 122) lower = true; // 'a'..='z'
        else if ((32 <= char && char <= 64) || // ' '..='@', includes '0'..='9'
            (91 <= char && char <= 96) || // '['..='`'
            (123 <= char && char <= 126)) special = true; // '{'..='~'
        else return alert('{{password-invalid-char}}');
      }

      if (lower && upper && special) {
        document.getElementById('pass-form').submit();
      } else {
        return alert('{{password-does-not-meet-requirements}}');
      }
    }

    function submitPortalEnabled() {
      const enabled = document.getElementById('connection-checkbox').checked;
      document.getElementById('real-connection-input').value = enabled ? 'checked' : '';
      document.getElementById('portal-enabled-form').submit();
    }

    function submitOtaEnabled() {
      const enabled = document.getElementById('ota-checkbox').checked;
      document.getElementById('real-ota-input').value = enabled ? 'checked' : '';
      document.getElementById('ota-enabled-form').submit();
    }

    function submitSystemName() {
      const sysname = document.getElementById('system-name-input').value;
      if (sysname.length === 0 || sysname.length > 32) {
        return alert("{{invalid-system-name}}");
      }
      document.getElementById('sysname-form').submit();
    }

    function fetchStatus() {
      const xhttp = new XMLHttpRequest();
      xhttp.onreadystatechange = function () {
        if (this.readyState === 4 && this.status === 200) {
          updateStatus(this.responseXML);
        }
      };
      xhttp.open('GET', '/protect/status.xml', true);
      xhttp.send();
    }

    function updateStatus(xml) {
      const aconChildren = xml.querySelector('info > acon').childNodes;
      const acon = aconChildren.length > 0 ? aconChildren[0].nodeValue : null;
      const aconActiveChildren = xml.querySelector('info > activeacon').childNodes;
      const aconActive = aconActiveChildren.length > 0 ? aconActiveChildren[0].nodeValue : null;
      const otaChildren = xml.querySelector('info > ota').childNodes;
      const ota = otaChildren.length > 0 ? otaChildren[0].nodeValue : null;
      document.getElementById('connection-checkbox').checked = acon === 'checked';
      document.getElementById('ota-checkbox').checked = ota === 'checked';
      const systemName = xml.querySelector('portal > sysname').childNodes[0].nodeValue;
      document.getElementById('system-name-input').value = systemName;
      if (acon === 'checked' && aconActive !== 'checked') document.getElementById('no-reboot-after-portal').style.display = 'block';
    }

    fetchStatus();

    for (const elem of document.getElementsByTagName('form')) {
      elem.addEventListener('keypress', function (e) {
        if ((e.keyCode || e.charCode) === 13) {
          e.preventDefault();
        }
      });
    }
  </script>

</html>

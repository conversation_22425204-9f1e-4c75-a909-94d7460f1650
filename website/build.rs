#![feature(str_split_remainder)]

use std::{
    collections::HashMap,
    env, fs,
    path::{Path, PathBuf},
};

fn main() {
    let manifest_dir = PathBuf::from(env::var("CARGO_MANIFEST_DIR").unwrap());
    let dist_dir = manifest_dir.join("dist");
    let www_dir = manifest_dir.join("www");
    fs::create_dir_all(&dist_dir).unwrap();

    let translations = generate_translation_map();

    for entry in fs::read_dir(&www_dir).unwrap() {
        let entry = entry.unwrap();
        let input_file = entry.path();
        match input_file.extension() {
            Some(ext) if ext == "html" => minify_html_file(&dist_dir, input_file, &translations),
            Some(ext) if ext == "css" => minify_css_file(&dist_dir, input_file),
            _ => copy_file(&dist_dir, input_file),
        }
    }

    println!("cargo:rerun-if-changed=build.rs");
    println!("cargo:rerun-if-changed=www");
}

type TranslationMap = Vec<(String, HashMap<String, String>)>;

fn generate_translation_map() -> TranslationMap {
    let mut map = Vec::new();

    for entry in fs::read_dir("../website/trans").unwrap() {
        let entry = entry.unwrap();
        if entry.file_type().unwrap().is_file() {
            let input_file = entry.path();
            let lang = input_file.file_stem().unwrap().to_str().unwrap();
            let data = fs::read_to_string(&input_file).unwrap();
            let mut hash = HashMap::new();
            let mut lines = data.lines();
            let mut line_no = 0;
            while let Some(line) = lines.next() {
                line_no += 1;
                if line.trim().is_empty() {
                    continue;
                }
                let mut split = line.split('=');
                let key = split
                    .next()
                    .unwrap_or_else(|| panic!("{lang}.ini: invalid format in line {line_no}"))
                    .trim()
                    .to_string();
                let mut value = split
                    .remainder()
                    .unwrap_or_else(|| panic!("{lang}.ini: invalid format in line {line_no}"))
                    .trim()
                    .to_string();
                while value.ends_with('\\') {
                    let _ = value.pop().unwrap();
                    let next_line = lines
                        .next()
                        .unwrap_or_else(|| panic!("{lang}.ini: unexpected end of file"));
                    line_no += 1;
                    value.push_str(next_line.trim());
                }
                let _ = hash.insert(key, value);
            }
            map.push((lang.to_string(), hash));
        }
    }

    map
}

fn minify_html_file(dist_dir: &Path, input_file: PathBuf, translations: &TranslationMap) {
    let raw_input = fs::read_to_string(&input_file).unwrap();
    let filename = input_file.file_name().unwrap().to_str().unwrap();

    for lang in translations {
        let mut input = String::new();
        let mut i = 0;
        while i < raw_input.len() {
            let Some(fmtpos) = raw_input[i..].find("{{").map(|x| x + i) else {
                input.push_str(&raw_input[i..]);
                break;
            };
            let fmtend = i + raw_input[i..]
                .find("}}")
                .unwrap_or_else(|| panic!("{filename}: unclosed template literal"));
            let key = &raw_input[(fmtpos + 2)..fmtend];
            let subst = lang.1.get(key).unwrap_or_else(|| {
                panic!(
                    "{filename}: translation for key \"{key}\" not found (lang={})",
                    lang.0
                )
            });
            input.push_str(&raw_input[i..fmtpos]);
            input.push_str(subst);
            i = fmtend + 2;
        }

        let output = minifier::html::minify(&input);

        let output_file = get_output_file(dist_dir, &input_file, Some(&lang.0));
        fs::write(output_file, output).unwrap();
    }
}

fn minify_css_file(dist_dir: &Path, input_file: PathBuf) {
    let output_file = get_output_file(dist_dir, &input_file, None);
    let input = fs::read_to_string(input_file).unwrap();
    let output = minifier::css::minify(&input).unwrap().to_string();
    fs::write(output_file, output).expect("failed to write css file");
}

fn copy_file(dist_dir: &Path, input_file: PathBuf) {
    let output_file = get_output_file(dist_dir, &input_file, None);
    println!("output_file: {:?}", output_file);
    fs::copy(input_file, output_file).expect("failed to copy file");
}

fn get_output_file(dist_dir: &Path, input_file: &Path, lang: Option<&str>) -> PathBuf {
    let file_name = input_file.file_name().unwrap();
    println!("cargo:rerun-if-changed=www/{}", file_name.to_str().unwrap());
    let mut output_file = PathBuf::from(dist_dir);
    if let Some(lang) = lang {
        output_file.push(lang);
    };
    let _ = fs::create_dir_all(&output_file);
    output_file.push(file_name);
    output_file
}
